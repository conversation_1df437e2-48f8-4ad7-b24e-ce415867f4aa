      MODULE MOD_TYPES
        INTEGER, PARAMETER :: SP = SELECTED_REAL_KIND(6, 37)
        INTEGER, PARAMETER :: DP = SELECTED_REAL_KIND(15, 307)
      END MODULE
!
      MODULE F_GLOBALS
         USE MOD_TYPES
         IMPLICIT NONE
         INTEGER, PARAMETER :: N_MAX = 16
         INTEGER, PARAMETER :: I_MAX = 18
         INTEGER, PARAMETER :: J_MAX = 72
         REAL(SP) :: XREF
      END MODULE F_GLOBALS
!
       SUBROUTINE DUMMY ()
!
       USE F_GLOBALS
       USE MOD_TYPES
       IMPLICIT NONE
!
       REAL(SP) :: MINIMAL
       MINIMAL = 0.01*XREF
       RETURN
!
       END SUBROUTINE DUMMY
