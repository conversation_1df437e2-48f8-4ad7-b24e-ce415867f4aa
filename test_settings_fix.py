#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة الاستيراد في نظام الإعدادات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات"""
    print("🔧 اختبار إصلاح مشكلة الاستيراد")
    print("=" * 50)
    
    try:
        print("📦 اختبار استيراد QKeySequence...")
        from PyQt5.QtGui import QKeySequence
        print("✅ تم استيراد QKeySequence بنجاح من PyQt5.QtGui")
        
        print("📦 اختبار استيراد QKeySequenceEdit...")
        from PyQt5.QtWidgets import QKeySequenceEdit
        print("✅ تم استيراد QKeySequenceEdit بنجاح من PyQt5.QtWidgets")
        
        print("📦 اختبار إنشاء QKeySequence...")
        test_sequence = QKeySequence("Ctrl+T")
        print(f"✅ تم إنشاء QKeySequence: {test_sequence.toString()}")
        
        print("📦 اختبار استيراد مدير الإعدادات...")
        from utils.settings_manager import SettingsManager
        print("✅ تم استيراد SettingsManager بنجاح")
        
        print("📦 اختبار استيراد نماذج الإعدادات...")
        from database.settings_models import AppSettings, SUPPORTED_CURRENCIES
        print("✅ تم استيراد نماذج الإعدادات بنجاح")
        
        print("📦 اختبار استيراد نافذة الإعدادات...")
        from gui.settings_dialog import SettingsDialog
        print("✅ تم استيراد SettingsDialog بنجاح")
        
        print("")
        print("🎉 جميع الاستيرادات تعمل بشكل صحيح!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_settings_system():
    """اختبار نظام الإعدادات"""
    print("\n⚙️ اختبار نظام الإعدادات")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from sqlalchemy import create_engine
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        print("📊 تهيئة قاعدة البيانات...")
        from database.settings_models import init_settings_db
        init_settings_db(engine)
        print("✅ تم إنشاء جدول الإعدادات")
        
        print("🔧 تهيئة مدير الإعدادات...")
        from utils.settings_manager import init_settings_manager
        settings_manager = init_settings_manager(engine)
        print("✅ تم تهيئة مدير الإعدادات")
        
        print("📋 اختبار قراءة الإعدادات...")
        currency = settings_manager.get('currency', 'EGP')
        theme = settings_manager.get('theme', 'light')
        language = settings_manager.get('language', 'ar')
        print(f"✅ العملة: {currency}")
        print(f"✅ المظهر: {theme}")
        print(f"✅ اللغة: {language}")
        
        print("💰 اختبار تنسيق العملة...")
        formatted = settings_manager.format_currency(1234.56)
        print(f"✅ تنسيق العملة: {formatted}")
        
        print("⌨️ اختبار الاختصارات...")
        profit_shortcut = settings_manager.get_shortcut('profit_toggle')
        print(f"✅ اختصار المكسب: {profit_shortcut}")
        
        print("")
        print("🎉 نظام الإعدادات يعمل بشكل مثالي!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الإعدادات: {e}")
        return False

def main():
    print("🔧 اختبار إصلاح نظام الإعدادات")
    print("=" * 60)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    if imports_ok:
        # اختبار نظام الإعدادات
        system_ok = test_settings_system()
        
        if system_ok:
            print("\n🎯 النتيجة النهائية:")
            print("=" * 60)
            print("✅ تم إصلاح مشكلة الاستيراد بنجاح!")
            print("✅ QKeySequence يتم استيرادها من PyQt5.QtGui")
            print("✅ QKeySequenceEdit يتم استيرادها من PyQt5.QtWidgets")
            print("✅ نظام الإعدادات يعمل بشكل كامل")
            print("✅ جميع الوظائف متاحة")
            print("")
            print("🚀 يمكنك الآن فتح نافذة الإعدادات بدون أخطاء!")
            print("")
            print("📍 للوصول للإعدادات:")
            print("   القائمة → النظام → ⚙️ إعدادات التطبيق")
            print("")
            print("🎉 المشكلة تم حلها بالكامل!")
        else:
            print("\n❌ هناك مشكلة في نظام الإعدادات")
    else:
        print("\n❌ هناك مشكلة في الاستيرادات")

if __name__ == "__main__":
    main()
