# The PEP 484 type hints stub file for the QtBluetooth module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QBluetooth(PyQt5.sip.simplewrapper):

    class AttAccessConstraint(int):
        AttAuthorizationRequired = ... # type: QBluetooth.AttAccessConstraint
        AttAuthenticationRequired = ... # type: QBluetooth.AttAccessConstraint
        AttEncryptionRequired = ... # type: QBluetooth.AttAccessConstraint

    class Security(int):
        NoSecurity = ... # type: QBluetooth.Security
        Authorization = ... # type: QBluetooth.Security
        Authentication = ... # type: QBluetooth.Security
        Encryption = ... # type: QBluetooth.Security
        Secure = ... # type: QBluetooth.Security

    class SecurityFlags(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __xor__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __ior__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __or__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __iand__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __and__(self, f: typing.Union['QBluetooth.SecurityFlags', 'QBluetooth.Security']) -> 'QBluetooth.SecurityFlags': ...
        def __invert__(self) -> 'QBluetooth.SecurityFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class AttAccessConstraints(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __xor__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __ior__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __or__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __iand__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __and__(self, f: typing.Union['QBluetooth.AttAccessConstraints', 'QBluetooth.AttAccessConstraint']) -> 'QBluetooth.AttAccessConstraints': ...
        def __invert__(self) -> 'QBluetooth.AttAccessConstraints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...


class QBluetoothAddress(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, address: int) -> None: ...
    @typing.overload
    def __init__(self, address: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBluetoothAddress') -> None: ...

    def __ge__(self, other: 'QBluetoothAddress') -> bool: ...
    def toString(self) -> str: ...
    def toUInt64(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __lt__(self, other: 'QBluetoothAddress') -> bool: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...


class QBluetoothDeviceDiscoveryAgent(QtCore.QObject):

    class DiscoveryMethod(int):
        NoMethod = ... # type: QBluetoothDeviceDiscoveryAgent.DiscoveryMethod
        ClassicMethod = ... # type: QBluetoothDeviceDiscoveryAgent.DiscoveryMethod
        LowEnergyMethod = ... # type: QBluetoothDeviceDiscoveryAgent.DiscoveryMethod

    class InquiryType(int):
        GeneralUnlimitedInquiry = ... # type: QBluetoothDeviceDiscoveryAgent.InquiryType
        LimitedInquiry = ... # type: QBluetoothDeviceDiscoveryAgent.InquiryType

    class Error(int):
        NoError = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        InputOutputError = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        PoweredOffError = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        InvalidBluetoothAdapterError = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        UnsupportedPlatformError = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        UnsupportedDiscoveryMethod = ... # type: QBluetoothDeviceDiscoveryAgent.Error
        UnknownError = ... # type: QBluetoothDeviceDiscoveryAgent.Error

    class DiscoveryMethods(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __xor__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __ior__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __or__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __iand__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __and__(self, f: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __invert__(self) -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, deviceAdapter: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def supportedDiscoveryMethods() -> 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethods': ...
    def lowEnergyDiscoveryTimeout(self) -> int: ...
    def setLowEnergyDiscoveryTimeout(self, msTimeout: int) -> None: ...
    deviceUpdated: typing.ClassVar[QtCore.pyqtSignal]
    canceled: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    deviceDiscovered: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    @typing.overload
    def start(self) -> None: ...
    @typing.overload
    def start(self, method: typing.Union['QBluetoothDeviceDiscoveryAgent.DiscoveryMethods', 'QBluetoothDeviceDiscoveryAgent.DiscoveryMethod']) -> None: ...
    def discoveredDevices(self) -> typing.List['QBluetoothDeviceInfo']: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isActive(self) -> bool: ...
    def setInquiryType(self, type: 'QBluetoothDeviceDiscoveryAgent.InquiryType') -> None: ...
    def inquiryType(self) -> 'QBluetoothDeviceDiscoveryAgent.InquiryType': ...


class QBluetoothDeviceInfo(PyQt5.sip.wrapper):

    class Field(int):
        None_ = ... # type: QBluetoothDeviceInfo.Field
        RSSI = ... # type: QBluetoothDeviceInfo.Field
        ManufacturerData = ... # type: QBluetoothDeviceInfo.Field
        All = ... # type: QBluetoothDeviceInfo.Field

    class CoreConfiguration(int):
        UnknownCoreConfiguration = ... # type: QBluetoothDeviceInfo.CoreConfiguration
        LowEnergyCoreConfiguration = ... # type: QBluetoothDeviceInfo.CoreConfiguration
        BaseRateCoreConfiguration = ... # type: QBluetoothDeviceInfo.CoreConfiguration
        BaseRateAndLowEnergyCoreConfiguration = ... # type: QBluetoothDeviceInfo.CoreConfiguration

    class DataCompleteness(int):
        DataComplete = ... # type: QBluetoothDeviceInfo.DataCompleteness
        DataIncomplete = ... # type: QBluetoothDeviceInfo.DataCompleteness
        DataUnavailable = ... # type: QBluetoothDeviceInfo.DataCompleteness

    class ServiceClass(int):
        NoService = ... # type: QBluetoothDeviceInfo.ServiceClass
        PositioningService = ... # type: QBluetoothDeviceInfo.ServiceClass
        NetworkingService = ... # type: QBluetoothDeviceInfo.ServiceClass
        RenderingService = ... # type: QBluetoothDeviceInfo.ServiceClass
        CapturingService = ... # type: QBluetoothDeviceInfo.ServiceClass
        ObjectTransferService = ... # type: QBluetoothDeviceInfo.ServiceClass
        AudioService = ... # type: QBluetoothDeviceInfo.ServiceClass
        TelephonyService = ... # type: QBluetoothDeviceInfo.ServiceClass
        InformationService = ... # type: QBluetoothDeviceInfo.ServiceClass
        AllServices = ... # type: QBluetoothDeviceInfo.ServiceClass

    class MinorHealthClass(int):
        UncategorizedHealthDevice = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthBloodPressureMonitor = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthThermometer = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthWeightScale = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthGlucoseMeter = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthPulseOximeter = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthDataDisplay = ... # type: QBluetoothDeviceInfo.MinorHealthClass
        HealthStepCounter = ... # type: QBluetoothDeviceInfo.MinorHealthClass

    class MinorToyClass(int):
        UncategorizedToy = ... # type: QBluetoothDeviceInfo.MinorToyClass
        ToyRobot = ... # type: QBluetoothDeviceInfo.MinorToyClass
        ToyVehicle = ... # type: QBluetoothDeviceInfo.MinorToyClass
        ToyDoll = ... # type: QBluetoothDeviceInfo.MinorToyClass
        ToyController = ... # type: QBluetoothDeviceInfo.MinorToyClass
        ToyGame = ... # type: QBluetoothDeviceInfo.MinorToyClass

    class MinorWearableClass(int):
        UncategorizedWearableDevice = ... # type: QBluetoothDeviceInfo.MinorWearableClass
        WearableWristWatch = ... # type: QBluetoothDeviceInfo.MinorWearableClass
        WearablePager = ... # type: QBluetoothDeviceInfo.MinorWearableClass
        WearableJacket = ... # type: QBluetoothDeviceInfo.MinorWearableClass
        WearableHelmet = ... # type: QBluetoothDeviceInfo.MinorWearableClass
        WearableGlasses = ... # type: QBluetoothDeviceInfo.MinorWearableClass

    class MinorImagingClass(int):
        UncategorizedImagingDevice = ... # type: QBluetoothDeviceInfo.MinorImagingClass
        ImageDisplay = ... # type: QBluetoothDeviceInfo.MinorImagingClass
        ImageCamera = ... # type: QBluetoothDeviceInfo.MinorImagingClass
        ImageScanner = ... # type: QBluetoothDeviceInfo.MinorImagingClass
        ImagePrinter = ... # type: QBluetoothDeviceInfo.MinorImagingClass

    class MinorPeripheralClass(int):
        UncategorizedPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        KeyboardPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        PointingDevicePeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        KeyboardWithPointingDevicePeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        JoystickPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        GamepadPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        RemoteControlPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        SensingDevicePeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        DigitizerTabletPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass
        CardReaderPeripheral = ... # type: QBluetoothDeviceInfo.MinorPeripheralClass

    class MinorAudioVideoClass(int):
        UncategorizedAudioVideoDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        WearableHeadsetDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        HandsFreeDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        Microphone = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        Loudspeaker = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        Headphones = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        PortableAudioDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        CarAudio = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        SetTopBox = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        HiFiAudioDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        Vcr = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        VideoCamera = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        Camcorder = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        VideoMonitor = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        VideoDisplayAndLoudspeaker = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        VideoConferencing = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass
        GamingDevice = ... # type: QBluetoothDeviceInfo.MinorAudioVideoClass

    class MinorNetworkClass(int):
        NetworkFullService = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorOne = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorTwo = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorThree = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorFour = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorFive = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkLoadFactorSix = ... # type: QBluetoothDeviceInfo.MinorNetworkClass
        NetworkNoService = ... # type: QBluetoothDeviceInfo.MinorNetworkClass

    class MinorPhoneClass(int):
        UncategorizedPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass
        CellularPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass
        CordlessPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass
        SmartPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass
        WiredModemOrVoiceGatewayPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass
        CommonIsdnAccessPhone = ... # type: QBluetoothDeviceInfo.MinorPhoneClass

    class MinorComputerClass(int):
        UncategorizedComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        DesktopComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        ServerComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        LaptopComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        HandheldClamShellComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        HandheldComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass
        WearableComputer = ... # type: QBluetoothDeviceInfo.MinorComputerClass

    class MinorMiscellaneousClass(int):
        UncategorizedMiscellaneous = ... # type: QBluetoothDeviceInfo.MinorMiscellaneousClass

    class MajorDeviceClass(int):
        MiscellaneousDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        ComputerDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        PhoneDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        LANAccessDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        NetworkDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        AudioVideoDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        PeripheralDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        ImagingDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        WearableDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        ToyDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        HealthDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass
        UncategorizedDevice = ... # type: QBluetoothDeviceInfo.MajorDeviceClass

    class ServiceClasses(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __xor__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __ior__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __or__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __iand__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __and__(self, f: typing.Union['QBluetoothDeviceInfo.ServiceClasses', 'QBluetoothDeviceInfo.ServiceClass']) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __invert__(self) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class CoreConfigurations(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __xor__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __ior__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __or__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __iand__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __and__(self, f: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __invert__(self) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Fields(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __xor__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __ior__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __or__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __iand__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __and__(self, f: typing.Union['QBluetoothDeviceInfo.Fields', 'QBluetoothDeviceInfo.Field']) -> 'QBluetoothDeviceInfo.Fields': ...
        def __invert__(self) -> 'QBluetoothDeviceInfo.Fields': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, address: QBluetoothAddress, name: typing.Optional[str], classOfDevice: int) -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid', name: typing.Optional[str], classOfDevice: int) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBluetoothDeviceInfo') -> None: ...

    def setManufacturerData(self, manufacturerId: int, data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    @typing.overload
    def manufacturerData(self, manufacturerId: int) -> QtCore.QByteArray: ...
    @typing.overload
    def manufacturerData(self) -> typing.Dict[int, QtCore.QByteArray]: ...
    def manufacturerIds(self) -> typing.List[int]: ...
    def deviceUuid(self) -> 'QBluetoothUuid': ...
    def setDeviceUuid(self, uuid: 'QBluetoothUuid') -> None: ...
    def coreConfigurations(self) -> 'QBluetoothDeviceInfo.CoreConfigurations': ...
    def setCoreConfigurations(self, coreConfigs: typing.Union['QBluetoothDeviceInfo.CoreConfigurations', 'QBluetoothDeviceInfo.CoreConfiguration']) -> None: ...
    def serviceUuidsCompleteness(self) -> 'QBluetoothDeviceInfo.DataCompleteness': ...
    def serviceUuids(self) -> typing.Tuple[typing.List['QBluetoothUuid'], typing.Optional['QBluetoothDeviceInfo.DataCompleteness']]: ...
    @typing.overload
    def setServiceUuids(self, uuids: typing.Iterable['QBluetoothUuid'], completeness: 'QBluetoothDeviceInfo.DataCompleteness') -> None: ...
    @typing.overload
    def setServiceUuids(self, uuids: typing.Iterable['QBluetoothUuid']) -> None: ...
    def setRssi(self, signal: int) -> None: ...
    def rssi(self) -> int: ...
    def minorDeviceClass(self) -> int: ...
    def majorDeviceClass(self) -> 'QBluetoothDeviceInfo.MajorDeviceClass': ...
    def serviceClasses(self) -> 'QBluetoothDeviceInfo.ServiceClasses': ...
    def name(self) -> str: ...
    def address(self) -> QBluetoothAddress: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setCached(self, cached: bool) -> None: ...
    def isCached(self) -> bool: ...
    def isValid(self) -> bool: ...


class QBluetoothHostInfo(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBluetoothHostInfo') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def setAddress(self, address: QBluetoothAddress) -> None: ...
    def address(self) -> QBluetoothAddress: ...


class QBluetoothLocalDevice(QtCore.QObject):

    class Error(int):
        NoError = ... # type: QBluetoothLocalDevice.Error
        PairingError = ... # type: QBluetoothLocalDevice.Error
        UnknownError = ... # type: QBluetoothLocalDevice.Error

    class HostMode(int):
        HostPoweredOff = ... # type: QBluetoothLocalDevice.HostMode
        HostConnectable = ... # type: QBluetoothLocalDevice.HostMode
        HostDiscoverable = ... # type: QBluetoothLocalDevice.HostMode
        HostDiscoverableLimitedInquiry = ... # type: QBluetoothLocalDevice.HostMode

    class Pairing(int):
        Unpaired = ... # type: QBluetoothLocalDevice.Pairing
        Paired = ... # type: QBluetoothLocalDevice.Pairing
        AuthorizedPaired = ... # type: QBluetoothLocalDevice.Pairing

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, address: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    deviceDisconnected: typing.ClassVar[QtCore.pyqtSignal]
    deviceConnected: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    pairingDisplayConfirmation: typing.ClassVar[QtCore.pyqtSignal]
    pairingDisplayPinCode: typing.ClassVar[QtCore.pyqtSignal]
    pairingFinished: typing.ClassVar[QtCore.pyqtSignal]
    hostModeStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def pairingConfirmation(self, confirmation: bool) -> None: ...
    def connectedDevices(self) -> typing.List[QBluetoothAddress]: ...
    @staticmethod
    def allDevices() -> typing.List[QBluetoothHostInfo]: ...
    def address(self) -> QBluetoothAddress: ...
    def name(self) -> str: ...
    def powerOn(self) -> None: ...
    def hostMode(self) -> 'QBluetoothLocalDevice.HostMode': ...
    def setHostMode(self, mode: 'QBluetoothLocalDevice.HostMode') -> None: ...
    def pairingStatus(self, address: QBluetoothAddress) -> 'QBluetoothLocalDevice.Pairing': ...
    def requestPairing(self, address: QBluetoothAddress, pairing: 'QBluetoothLocalDevice.Pairing') -> None: ...
    def isValid(self) -> bool: ...


class QBluetoothServer(QtCore.QObject):

    class Error(int):
        NoError = ... # type: QBluetoothServer.Error
        UnknownError = ... # type: QBluetoothServer.Error
        PoweredOffError = ... # type: QBluetoothServer.Error
        InputOutputError = ... # type: QBluetoothServer.Error
        ServiceAlreadyRegisteredError = ... # type: QBluetoothServer.Error
        UnsupportedProtocolError = ... # type: QBluetoothServer.Error

    def __init__(self, serverType: 'QBluetoothServiceInfo.Protocol', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    newConnection: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    def serverType(self) -> 'QBluetoothServiceInfo.Protocol': ...
    def securityFlags(self) -> QBluetooth.SecurityFlags: ...
    def setSecurityFlags(self, security: typing.Union[QBluetooth.SecurityFlags, QBluetooth.Security]) -> None: ...
    def serverPort(self) -> int: ...
    def serverAddress(self) -> QBluetoothAddress: ...
    def nextPendingConnection(self) -> typing.Optional['QBluetoothSocket']: ...
    def hasPendingConnections(self) -> bool: ...
    def maxPendingConnections(self) -> int: ...
    def setMaxPendingConnections(self, numConnections: int) -> None: ...
    def isListening(self) -> bool: ...
    @typing.overload
    def listen(self, address: QBluetoothAddress = ..., port: int = ...) -> bool: ...
    @typing.overload
    def listen(self, uuid: 'QBluetoothUuid', serviceName: typing.Optional[str] = ...) -> 'QBluetoothServiceInfo': ...
    def close(self) -> None: ...


class QBluetoothServiceDiscoveryAgent(QtCore.QObject):

    class DiscoveryMode(int):
        MinimalDiscovery = ... # type: QBluetoothServiceDiscoveryAgent.DiscoveryMode
        FullDiscovery = ... # type: QBluetoothServiceDiscoveryAgent.DiscoveryMode

    class Error(int):
        NoError = ... # type: QBluetoothServiceDiscoveryAgent.Error
        InputOutputError = ... # type: QBluetoothServiceDiscoveryAgent.Error
        PoweredOffError = ... # type: QBluetoothServiceDiscoveryAgent.Error
        InvalidBluetoothAdapterError = ... # type: QBluetoothServiceDiscoveryAgent.Error
        UnknownError = ... # type: QBluetoothServiceDiscoveryAgent.Error

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, deviceAdapter: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    canceled: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    serviceDiscovered: typing.ClassVar[QtCore.pyqtSignal]
    def clear(self) -> None: ...
    def stop(self) -> None: ...
    def start(self, mode: 'QBluetoothServiceDiscoveryAgent.DiscoveryMode' = ...) -> None: ...
    def remoteAddress(self) -> QBluetoothAddress: ...
    def setRemoteAddress(self, address: QBluetoothAddress) -> bool: ...
    def uuidFilter(self) -> typing.List['QBluetoothUuid']: ...
    @typing.overload
    def setUuidFilter(self, uuids: typing.Iterable['QBluetoothUuid']) -> None: ...
    @typing.overload
    def setUuidFilter(self, uuid: 'QBluetoothUuid') -> None: ...
    def discoveredServices(self) -> typing.List['QBluetoothServiceInfo']: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isActive(self) -> bool: ...


class QBluetoothServiceInfo(PyQt5.sip.wrapper):

    class Protocol(int):
        UnknownProtocol = ... # type: QBluetoothServiceInfo.Protocol
        L2capProtocol = ... # type: QBluetoothServiceInfo.Protocol
        RfcommProtocol = ... # type: QBluetoothServiceInfo.Protocol

    class AttributeId(int):
        ServiceRecordHandle = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceClassIds = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceRecordState = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceId = ... # type: QBluetoothServiceInfo.AttributeId
        ProtocolDescriptorList = ... # type: QBluetoothServiceInfo.AttributeId
        BrowseGroupList = ... # type: QBluetoothServiceInfo.AttributeId
        LanguageBaseAttributeIdList = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceInfoTimeToLive = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceAvailability = ... # type: QBluetoothServiceInfo.AttributeId
        BluetoothProfileDescriptorList = ... # type: QBluetoothServiceInfo.AttributeId
        DocumentationUrl = ... # type: QBluetoothServiceInfo.AttributeId
        ClientExecutableUrl = ... # type: QBluetoothServiceInfo.AttributeId
        IconUrl = ... # type: QBluetoothServiceInfo.AttributeId
        AdditionalProtocolDescriptorList = ... # type: QBluetoothServiceInfo.AttributeId
        PrimaryLanguageBase = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceName = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceDescription = ... # type: QBluetoothServiceInfo.AttributeId
        ServiceProvider = ... # type: QBluetoothServiceInfo.AttributeId

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBluetoothServiceInfo') -> None: ...

    def serviceClassUuids(self) -> typing.List['QBluetoothUuid']: ...
    def serviceUuid(self) -> 'QBluetoothUuid': ...
    def setServiceUuid(self, uuid: 'QBluetoothUuid') -> None: ...
    def serviceAvailability(self) -> int: ...
    def setServiceAvailability(self, availability: int) -> None: ...
    def serviceProvider(self) -> str: ...
    def setServiceProvider(self, provider: typing.Optional[str]) -> None: ...
    def serviceDescription(self) -> str: ...
    def setServiceDescription(self, description: typing.Optional[str]) -> None: ...
    def serviceName(self) -> str: ...
    def setServiceName(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: 'QBluetoothUuid') -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: typing.Iterable[typing.Any]) -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: typing.Any) -> None: ...
    def unregisterService(self) -> bool: ...
    def registerService(self, localAdapter: QBluetoothAddress = ...) -> bool: ...
    def isRegistered(self) -> bool: ...
    def protocolDescriptor(self, protocol: 'QBluetoothUuid.ProtocolUuid') -> typing.List[typing.Any]: ...
    def serverChannel(self) -> int: ...
    def protocolServiceMultiplexer(self) -> int: ...
    def socketProtocol(self) -> 'QBluetoothServiceInfo.Protocol': ...
    def removeAttribute(self, attributeId: int) -> None: ...
    def contains(self, attributeId: int) -> bool: ...
    def attributes(self) -> typing.List[int]: ...
    def attribute(self, attributeId: int) -> typing.Any: ...
    def device(self) -> QBluetoothDeviceInfo: ...
    def setDevice(self, info: QBluetoothDeviceInfo) -> None: ...
    def isComplete(self) -> bool: ...
    def isValid(self) -> bool: ...


class QBluetoothSocket(QtCore.QIODevice):

    class SocketError(int):
        NoSocketError = ... # type: QBluetoothSocket.SocketError
        UnknownSocketError = ... # type: QBluetoothSocket.SocketError
        HostNotFoundError = ... # type: QBluetoothSocket.SocketError
        ServiceNotFoundError = ... # type: QBluetoothSocket.SocketError
        NetworkError = ... # type: QBluetoothSocket.SocketError
        UnsupportedProtocolError = ... # type: QBluetoothSocket.SocketError
        OperationError = ... # type: QBluetoothSocket.SocketError
        RemoteHostClosedError = ... # type: QBluetoothSocket.SocketError

    class SocketState(int):
        UnconnectedState = ... # type: QBluetoothSocket.SocketState
        ServiceLookupState = ... # type: QBluetoothSocket.SocketState
        ConnectingState = ... # type: QBluetoothSocket.SocketState
        ConnectedState = ... # type: QBluetoothSocket.SocketState
        BoundState = ... # type: QBluetoothSocket.SocketState
        ClosingState = ... # type: QBluetoothSocket.SocketState
        ListeningState = ... # type: QBluetoothSocket.SocketState

    @typing.overload
    def __init__(self, socketType: QBluetoothServiceInfo.Protocol, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def preferredSecurityFlags(self) -> QBluetooth.SecurityFlags: ...
    def setPreferredSecurityFlags(self, flags: typing.Union[QBluetooth.SecurityFlags, QBluetooth.Security]) -> None: ...
    def doDeviceDiscovery(self, service: QBluetoothServiceInfo, openMode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag]) -> None: ...
    def setSocketError(self, error: 'QBluetoothSocket.SocketError') -> None: ...
    def setSocketState(self, state: 'QBluetoothSocket.SocketState') -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> 'QBluetoothSocket.SocketState': ...
    def socketType(self) -> QBluetoothServiceInfo.Protocol: ...
    def socketDescriptor(self) -> int: ...
    def setSocketDescriptor(self, socketDescriptor: int, socketType: QBluetoothServiceInfo.Protocol, state: 'QBluetoothSocket.SocketState' = ..., mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> bool: ...
    def peerPort(self) -> int: ...
    def peerAddress(self) -> QBluetoothAddress: ...
    def peerName(self) -> str: ...
    def localPort(self) -> int: ...
    def localAddress(self) -> QBluetoothAddress: ...
    def localName(self) -> str: ...
    def disconnectFromService(self) -> None: ...
    @typing.overload
    def connectToService(self, service: QBluetoothServiceInfo, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...
    @typing.overload
    def connectToService(self, address: QBluetoothAddress, uuid: 'QBluetoothUuid', mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...
    @typing.overload
    def connectToService(self, address: QBluetoothAddress, port: int, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    def abort(self) -> None: ...


class QBluetoothTransferManager(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    finished: typing.ClassVar[QtCore.pyqtSignal]
    def put(self, request: 'QBluetoothTransferRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QBluetoothTransferReply']: ...


class QBluetoothTransferReply(QtCore.QObject):

    class TransferError(int):
        NoError = ... # type: QBluetoothTransferReply.TransferError
        UnknownError = ... # type: QBluetoothTransferReply.TransferError
        FileNotFoundError = ... # type: QBluetoothTransferReply.TransferError
        HostNotFoundError = ... # type: QBluetoothTransferReply.TransferError
        UserCanceledTransferError = ... # type: QBluetoothTransferReply.TransferError
        IODeviceNotReadableError = ... # type: QBluetoothTransferReply.TransferError
        ResourceBusyError = ... # type: QBluetoothTransferReply.TransferError
        SessionError = ... # type: QBluetoothTransferReply.TransferError

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setRequest(self, request: 'QBluetoothTransferRequest') -> None: ...
    def setManager(self, manager: typing.Optional[QBluetoothTransferManager]) -> None: ...
    transferProgress: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def abort(self) -> None: ...
    def request(self) -> 'QBluetoothTransferRequest': ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def manager(self) -> typing.Optional[QBluetoothTransferManager]: ...
    def isRunning(self) -> bool: ...
    def isFinished(self) -> bool: ...


class QBluetoothTransferRequest(PyQt5.sip.wrapper):

    class Attribute(int):
        DescriptionAttribute = ... # type: QBluetoothTransferRequest.Attribute
        TimeAttribute = ... # type: QBluetoothTransferRequest.Attribute
        TypeAttribute = ... # type: QBluetoothTransferRequest.Attribute
        LengthAttribute = ... # type: QBluetoothTransferRequest.Attribute
        NameAttribute = ... # type: QBluetoothTransferRequest.Attribute

    @typing.overload
    def __init__(self, address: QBluetoothAddress = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBluetoothTransferRequest') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def address(self) -> QBluetoothAddress: ...
    def setAttribute(self, code: 'QBluetoothTransferRequest.Attribute', value: typing.Any) -> None: ...
    def attribute(self, code: 'QBluetoothTransferRequest.Attribute', defaultValue: typing.Any = ...) -> typing.Any: ...


class QBluetoothUuid(QtCore.QUuid):

    class DescriptorType(int):
        UnknownDescriptorType = ... # type: QBluetoothUuid.DescriptorType
        CharacteristicExtendedProperties = ... # type: QBluetoothUuid.DescriptorType
        CharacteristicUserDescription = ... # type: QBluetoothUuid.DescriptorType
        ClientCharacteristicConfiguration = ... # type: QBluetoothUuid.DescriptorType
        ServerCharacteristicConfiguration = ... # type: QBluetoothUuid.DescriptorType
        CharacteristicPresentationFormat = ... # type: QBluetoothUuid.DescriptorType
        CharacteristicAggregateFormat = ... # type: QBluetoothUuid.DescriptorType
        ValidRange = ... # type: QBluetoothUuid.DescriptorType
        ExternalReportReference = ... # type: QBluetoothUuid.DescriptorType
        ReportReference = ... # type: QBluetoothUuid.DescriptorType
        EnvironmentalSensingConfiguration = ... # type: QBluetoothUuid.DescriptorType
        EnvironmentalSensingMeasurement = ... # type: QBluetoothUuid.DescriptorType
        EnvironmentalSensingTriggerSetting = ... # type: QBluetoothUuid.DescriptorType

    class CharacteristicType(int):
        DeviceName = ... # type: QBluetoothUuid.CharacteristicType
        Appearance = ... # type: QBluetoothUuid.CharacteristicType
        PeripheralPrivacyFlag = ... # type: QBluetoothUuid.CharacteristicType
        ReconnectionAddress = ... # type: QBluetoothUuid.CharacteristicType
        PeripheralPreferredConnectionParameters = ... # type: QBluetoothUuid.CharacteristicType
        ServiceChanged = ... # type: QBluetoothUuid.CharacteristicType
        AlertLevel = ... # type: QBluetoothUuid.CharacteristicType
        TxPowerLevel = ... # type: QBluetoothUuid.CharacteristicType
        DateTime = ... # type: QBluetoothUuid.CharacteristicType
        DayOfWeek = ... # type: QBluetoothUuid.CharacteristicType
        DayDateTime = ... # type: QBluetoothUuid.CharacteristicType
        ExactTime256 = ... # type: QBluetoothUuid.CharacteristicType
        DSTOffset = ... # type: QBluetoothUuid.CharacteristicType
        TimeZone = ... # type: QBluetoothUuid.CharacteristicType
        LocalTimeInformation = ... # type: QBluetoothUuid.CharacteristicType
        TimeWithDST = ... # type: QBluetoothUuid.CharacteristicType
        TimeAccuracy = ... # type: QBluetoothUuid.CharacteristicType
        TimeSource = ... # type: QBluetoothUuid.CharacteristicType
        ReferenceTimeInformation = ... # type: QBluetoothUuid.CharacteristicType
        TimeUpdateControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        TimeUpdateState = ... # type: QBluetoothUuid.CharacteristicType
        GlucoseMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        BatteryLevel = ... # type: QBluetoothUuid.CharacteristicType
        TemperatureMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        TemperatureType = ... # type: QBluetoothUuid.CharacteristicType
        IntermediateTemperature = ... # type: QBluetoothUuid.CharacteristicType
        MeasurementInterval = ... # type: QBluetoothUuid.CharacteristicType
        BootKeyboardInputReport = ... # type: QBluetoothUuid.CharacteristicType
        SystemID = ... # type: QBluetoothUuid.CharacteristicType
        ModelNumberString = ... # type: QBluetoothUuid.CharacteristicType
        SerialNumberString = ... # type: QBluetoothUuid.CharacteristicType
        FirmwareRevisionString = ... # type: QBluetoothUuid.CharacteristicType
        HardwareRevisionString = ... # type: QBluetoothUuid.CharacteristicType
        SoftwareRevisionString = ... # type: QBluetoothUuid.CharacteristicType
        ManufacturerNameString = ... # type: QBluetoothUuid.CharacteristicType
        IEEE1107320601RegulatoryCertificationDataList = ... # type: QBluetoothUuid.CharacteristicType
        CurrentTime = ... # type: QBluetoothUuid.CharacteristicType
        ScanRefresh = ... # type: QBluetoothUuid.CharacteristicType
        BootKeyboardOutputReport = ... # type: QBluetoothUuid.CharacteristicType
        BootMouseInputReport = ... # type: QBluetoothUuid.CharacteristicType
        GlucoseMeasurementContext = ... # type: QBluetoothUuid.CharacteristicType
        BloodPressureMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        IntermediateCuffPressure = ... # type: QBluetoothUuid.CharacteristicType
        HeartRateMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        BodySensorLocation = ... # type: QBluetoothUuid.CharacteristicType
        HeartRateControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        AlertStatus = ... # type: QBluetoothUuid.CharacteristicType
        RingerControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        RingerSetting = ... # type: QBluetoothUuid.CharacteristicType
        AlertCategoryIDBitMask = ... # type: QBluetoothUuid.CharacteristicType
        AlertCategoryID = ... # type: QBluetoothUuid.CharacteristicType
        AlertNotificationControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        UnreadAlertStatus = ... # type: QBluetoothUuid.CharacteristicType
        NewAlert = ... # type: QBluetoothUuid.CharacteristicType
        SupportedNewAlertCategory = ... # type: QBluetoothUuid.CharacteristicType
        SupportedUnreadAlertCategory = ... # type: QBluetoothUuid.CharacteristicType
        BloodPressureFeature = ... # type: QBluetoothUuid.CharacteristicType
        HIDInformation = ... # type: QBluetoothUuid.CharacteristicType
        ReportMap = ... # type: QBluetoothUuid.CharacteristicType
        HIDControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        Report = ... # type: QBluetoothUuid.CharacteristicType
        ProtocolMode = ... # type: QBluetoothUuid.CharacteristicType
        ScanIntervalWindow = ... # type: QBluetoothUuid.CharacteristicType
        PnPID = ... # type: QBluetoothUuid.CharacteristicType
        GlucoseFeature = ... # type: QBluetoothUuid.CharacteristicType
        RecordAccessControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        RSCMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        RSCFeature = ... # type: QBluetoothUuid.CharacteristicType
        SCControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        CSCMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        CSCFeature = ... # type: QBluetoothUuid.CharacteristicType
        SensorLocation = ... # type: QBluetoothUuid.CharacteristicType
        CyclingPowerMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        CyclingPowerVector = ... # type: QBluetoothUuid.CharacteristicType
        CyclingPowerFeature = ... # type: QBluetoothUuid.CharacteristicType
        CyclingPowerControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        LocationAndSpeed = ... # type: QBluetoothUuid.CharacteristicType
        Navigation = ... # type: QBluetoothUuid.CharacteristicType
        PositionQuality = ... # type: QBluetoothUuid.CharacteristicType
        LNFeature = ... # type: QBluetoothUuid.CharacteristicType
        LNControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        MagneticDeclination = ... # type: QBluetoothUuid.CharacteristicType
        Elevation = ... # type: QBluetoothUuid.CharacteristicType
        Pressure = ... # type: QBluetoothUuid.CharacteristicType
        Temperature = ... # type: QBluetoothUuid.CharacteristicType
        Humidity = ... # type: QBluetoothUuid.CharacteristicType
        TrueWindSpeed = ... # type: QBluetoothUuid.CharacteristicType
        TrueWindDirection = ... # type: QBluetoothUuid.CharacteristicType
        ApparentWindSpeed = ... # type: QBluetoothUuid.CharacteristicType
        ApparentWindDirection = ... # type: QBluetoothUuid.CharacteristicType
        GustFactor = ... # type: QBluetoothUuid.CharacteristicType
        PollenConcentration = ... # type: QBluetoothUuid.CharacteristicType
        UVIndex = ... # type: QBluetoothUuid.CharacteristicType
        Irradiance = ... # type: QBluetoothUuid.CharacteristicType
        Rainfall = ... # type: QBluetoothUuid.CharacteristicType
        WindChill = ... # type: QBluetoothUuid.CharacteristicType
        HeatIndex = ... # type: QBluetoothUuid.CharacteristicType
        DewPoint = ... # type: QBluetoothUuid.CharacteristicType
        DescriptorValueChanged = ... # type: QBluetoothUuid.CharacteristicType
        AerobicHeartRateLowerLimit = ... # type: QBluetoothUuid.CharacteristicType
        AerobicThreshold = ... # type: QBluetoothUuid.CharacteristicType
        Age = ... # type: QBluetoothUuid.CharacteristicType
        AnaerobicHeartRateLowerLimit = ... # type: QBluetoothUuid.CharacteristicType
        AnaerobicHeartRateUpperLimit = ... # type: QBluetoothUuid.CharacteristicType
        AnaerobicThreshold = ... # type: QBluetoothUuid.CharacteristicType
        AerobicHeartRateUpperLimit = ... # type: QBluetoothUuid.CharacteristicType
        DateOfBirth = ... # type: QBluetoothUuid.CharacteristicType
        DateOfThresholdAssessment = ... # type: QBluetoothUuid.CharacteristicType
        EmailAddress = ... # type: QBluetoothUuid.CharacteristicType
        FatBurnHeartRateLowerLimit = ... # type: QBluetoothUuid.CharacteristicType
        FatBurnHeartRateUpperLimit = ... # type: QBluetoothUuid.CharacteristicType
        FirstName = ... # type: QBluetoothUuid.CharacteristicType
        FiveZoneHeartRateLimits = ... # type: QBluetoothUuid.CharacteristicType
        Gender = ... # type: QBluetoothUuid.CharacteristicType
        HeartRateMax = ... # type: QBluetoothUuid.CharacteristicType
        Height = ... # type: QBluetoothUuid.CharacteristicType
        HipCircumference = ... # type: QBluetoothUuid.CharacteristicType
        LastName = ... # type: QBluetoothUuid.CharacteristicType
        MaximumRecommendedHeartRate = ... # type: QBluetoothUuid.CharacteristicType
        RestingHeartRate = ... # type: QBluetoothUuid.CharacteristicType
        SportTypeForAerobicAnaerobicThresholds = ... # type: QBluetoothUuid.CharacteristicType
        ThreeZoneHeartRateLimits = ... # type: QBluetoothUuid.CharacteristicType
        TwoZoneHeartRateLimits = ... # type: QBluetoothUuid.CharacteristicType
        VO2Max = ... # type: QBluetoothUuid.CharacteristicType
        WaistCircumference = ... # type: QBluetoothUuid.CharacteristicType
        Weight = ... # type: QBluetoothUuid.CharacteristicType
        DatabaseChangeIncrement = ... # type: QBluetoothUuid.CharacteristicType
        UserIndex = ... # type: QBluetoothUuid.CharacteristicType
        BodyCompositionFeature = ... # type: QBluetoothUuid.CharacteristicType
        BodyCompositionMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        WeightMeasurement = ... # type: QBluetoothUuid.CharacteristicType
        WeightScaleFeature = ... # type: QBluetoothUuid.CharacteristicType
        UserControlPoint = ... # type: QBluetoothUuid.CharacteristicType
        MagneticFluxDensity2D = ... # type: QBluetoothUuid.CharacteristicType
        MagneticFluxDensity3D = ... # type: QBluetoothUuid.CharacteristicType
        Language = ... # type: QBluetoothUuid.CharacteristicType
        BarometricPressureTrend = ... # type: QBluetoothUuid.CharacteristicType

    class ServiceClassUuid(int):
        ServiceDiscoveryServer = ... # type: QBluetoothUuid.ServiceClassUuid
        BrowseGroupDescriptor = ... # type: QBluetoothUuid.ServiceClassUuid
        PublicBrowseGroup = ... # type: QBluetoothUuid.ServiceClassUuid
        SerialPort = ... # type: QBluetoothUuid.ServiceClassUuid
        LANAccessUsingPPP = ... # type: QBluetoothUuid.ServiceClassUuid
        DialupNetworking = ... # type: QBluetoothUuid.ServiceClassUuid
        IrMCSync = ... # type: QBluetoothUuid.ServiceClassUuid
        ObexObjectPush = ... # type: QBluetoothUuid.ServiceClassUuid
        OBEXFileTransfer = ... # type: QBluetoothUuid.ServiceClassUuid
        IrMCSyncCommand = ... # type: QBluetoothUuid.ServiceClassUuid
        Headset = ... # type: QBluetoothUuid.ServiceClassUuid
        AudioSource = ... # type: QBluetoothUuid.ServiceClassUuid
        AudioSink = ... # type: QBluetoothUuid.ServiceClassUuid
        AV_RemoteControlTarget = ... # type: QBluetoothUuid.ServiceClassUuid
        AdvancedAudioDistribution = ... # type: QBluetoothUuid.ServiceClassUuid
        AV_RemoteControl = ... # type: QBluetoothUuid.ServiceClassUuid
        AV_RemoteControlController = ... # type: QBluetoothUuid.ServiceClassUuid
        HeadsetAG = ... # type: QBluetoothUuid.ServiceClassUuid
        PANU = ... # type: QBluetoothUuid.ServiceClassUuid
        NAP = ... # type: QBluetoothUuid.ServiceClassUuid
        GN = ... # type: QBluetoothUuid.ServiceClassUuid
        DirectPrinting = ... # type: QBluetoothUuid.ServiceClassUuid
        ReferencePrinting = ... # type: QBluetoothUuid.ServiceClassUuid
        ImagingResponder = ... # type: QBluetoothUuid.ServiceClassUuid
        ImagingAutomaticArchive = ... # type: QBluetoothUuid.ServiceClassUuid
        ImagingReferenceObjects = ... # type: QBluetoothUuid.ServiceClassUuid
        Handsfree = ... # type: QBluetoothUuid.ServiceClassUuid
        HandsfreeAudioGateway = ... # type: QBluetoothUuid.ServiceClassUuid
        DirectPrintingReferenceObjectsService = ... # type: QBluetoothUuid.ServiceClassUuid
        ReflectedUI = ... # type: QBluetoothUuid.ServiceClassUuid
        BasicPrinting = ... # type: QBluetoothUuid.ServiceClassUuid
        PrintingStatus = ... # type: QBluetoothUuid.ServiceClassUuid
        HumanInterfaceDeviceService = ... # type: QBluetoothUuid.ServiceClassUuid
        HardcopyCableReplacement = ... # type: QBluetoothUuid.ServiceClassUuid
        HCRPrint = ... # type: QBluetoothUuid.ServiceClassUuid
        HCRScan = ... # type: QBluetoothUuid.ServiceClassUuid
        SIMAccess = ... # type: QBluetoothUuid.ServiceClassUuid
        PhonebookAccessPCE = ... # type: QBluetoothUuid.ServiceClassUuid
        PhonebookAccessPSE = ... # type: QBluetoothUuid.ServiceClassUuid
        PhonebookAccess = ... # type: QBluetoothUuid.ServiceClassUuid
        HeadsetHS = ... # type: QBluetoothUuid.ServiceClassUuid
        MessageAccessServer = ... # type: QBluetoothUuid.ServiceClassUuid
        MessageNotificationServer = ... # type: QBluetoothUuid.ServiceClassUuid
        MessageAccessProfile = ... # type: QBluetoothUuid.ServiceClassUuid
        PnPInformation = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericNetworking = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericFileTransfer = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericAudio = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericTelephony = ... # type: QBluetoothUuid.ServiceClassUuid
        VideoSource = ... # type: QBluetoothUuid.ServiceClassUuid
        VideoSink = ... # type: QBluetoothUuid.ServiceClassUuid
        VideoDistribution = ... # type: QBluetoothUuid.ServiceClassUuid
        HDP = ... # type: QBluetoothUuid.ServiceClassUuid
        HDPSource = ... # type: QBluetoothUuid.ServiceClassUuid
        HDPSink = ... # type: QBluetoothUuid.ServiceClassUuid
        BasicImage = ... # type: QBluetoothUuid.ServiceClassUuid
        GNSS = ... # type: QBluetoothUuid.ServiceClassUuid
        GNSSServer = ... # type: QBluetoothUuid.ServiceClassUuid
        Display3D = ... # type: QBluetoothUuid.ServiceClassUuid
        Glasses3D = ... # type: QBluetoothUuid.ServiceClassUuid
        Synchronization3D = ... # type: QBluetoothUuid.ServiceClassUuid
        MPSProfile = ... # type: QBluetoothUuid.ServiceClassUuid
        MPSService = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericAccess = ... # type: QBluetoothUuid.ServiceClassUuid
        GenericAttribute = ... # type: QBluetoothUuid.ServiceClassUuid
        ImmediateAlert = ... # type: QBluetoothUuid.ServiceClassUuid
        LinkLoss = ... # type: QBluetoothUuid.ServiceClassUuid
        TxPower = ... # type: QBluetoothUuid.ServiceClassUuid
        CurrentTimeService = ... # type: QBluetoothUuid.ServiceClassUuid
        ReferenceTimeUpdateService = ... # type: QBluetoothUuid.ServiceClassUuid
        NextDSTChangeService = ... # type: QBluetoothUuid.ServiceClassUuid
        Glucose = ... # type: QBluetoothUuid.ServiceClassUuid
        HealthThermometer = ... # type: QBluetoothUuid.ServiceClassUuid
        DeviceInformation = ... # type: QBluetoothUuid.ServiceClassUuid
        HeartRate = ... # type: QBluetoothUuid.ServiceClassUuid
        PhoneAlertStatusService = ... # type: QBluetoothUuid.ServiceClassUuid
        BatteryService = ... # type: QBluetoothUuid.ServiceClassUuid
        BloodPressure = ... # type: QBluetoothUuid.ServiceClassUuid
        AlertNotificationService = ... # type: QBluetoothUuid.ServiceClassUuid
        HumanInterfaceDevice = ... # type: QBluetoothUuid.ServiceClassUuid
        ScanParameters = ... # type: QBluetoothUuid.ServiceClassUuid
        RunningSpeedAndCadence = ... # type: QBluetoothUuid.ServiceClassUuid
        CyclingSpeedAndCadence = ... # type: QBluetoothUuid.ServiceClassUuid
        CyclingPower = ... # type: QBluetoothUuid.ServiceClassUuid
        LocationAndNavigation = ... # type: QBluetoothUuid.ServiceClassUuid
        EnvironmentalSensing = ... # type: QBluetoothUuid.ServiceClassUuid
        BodyComposition = ... # type: QBluetoothUuid.ServiceClassUuid
        UserData = ... # type: QBluetoothUuid.ServiceClassUuid
        WeightScale = ... # type: QBluetoothUuid.ServiceClassUuid
        BondManagement = ... # type: QBluetoothUuid.ServiceClassUuid
        ContinuousGlucoseMonitoring = ... # type: QBluetoothUuid.ServiceClassUuid

    class ProtocolUuid(int):
        Sdp = ... # type: QBluetoothUuid.ProtocolUuid
        Udp = ... # type: QBluetoothUuid.ProtocolUuid
        Rfcomm = ... # type: QBluetoothUuid.ProtocolUuid
        Tcp = ... # type: QBluetoothUuid.ProtocolUuid
        TcsBin = ... # type: QBluetoothUuid.ProtocolUuid
        TcsAt = ... # type: QBluetoothUuid.ProtocolUuid
        Att = ... # type: QBluetoothUuid.ProtocolUuid
        Obex = ... # type: QBluetoothUuid.ProtocolUuid
        Ip = ... # type: QBluetoothUuid.ProtocolUuid
        Ftp = ... # type: QBluetoothUuid.ProtocolUuid
        Http = ... # type: QBluetoothUuid.ProtocolUuid
        Wsp = ... # type: QBluetoothUuid.ProtocolUuid
        Bnep = ... # type: QBluetoothUuid.ProtocolUuid
        Upnp = ... # type: QBluetoothUuid.ProtocolUuid
        Hidp = ... # type: QBluetoothUuid.ProtocolUuid
        HardcopyControlChannel = ... # type: QBluetoothUuid.ProtocolUuid
        HardcopyDataChannel = ... # type: QBluetoothUuid.ProtocolUuid
        HardcopyNotification = ... # type: QBluetoothUuid.ProtocolUuid
        Avctp = ... # type: QBluetoothUuid.ProtocolUuid
        Avdtp = ... # type: QBluetoothUuid.ProtocolUuid
        Cmtp = ... # type: QBluetoothUuid.ProtocolUuid
        UdiCPlain = ... # type: QBluetoothUuid.ProtocolUuid
        McapControlChannel = ... # type: QBluetoothUuid.ProtocolUuid
        McapDataChannel = ... # type: QBluetoothUuid.ProtocolUuid
        L2cap = ... # type: QBluetoothUuid.ProtocolUuid

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid.ProtocolUuid') -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid.ServiceClassUuid') -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid.CharacteristicType') -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid.DescriptorType') -> None: ...
    @typing.overload
    def __init__(self, uuid: int) -> None: ...
    @typing.overload
    def __init__(self, uuid: typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]) -> None: ...
    @typing.overload
    def __init__(self, uuid: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, uuid: 'QBluetoothUuid') -> None: ...
    @typing.overload
    def __init__(self, uuid: QtCore.QUuid) -> None: ...

    @staticmethod
    def descriptorToString(uuid: 'QBluetoothUuid.DescriptorType') -> str: ...
    @staticmethod
    def characteristicToString(uuid: 'QBluetoothUuid.CharacteristicType') -> str: ...
    @staticmethod
    def protocolToString(uuid: 'QBluetoothUuid.ProtocolUuid') -> str: ...
    @staticmethod
    def serviceClassToString(uuid: 'QBluetoothUuid.ServiceClassUuid') -> str: ...
    def toUInt128(self) -> typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]: ...
    def toUInt32(self) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toUInt16(self) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def minimumSize(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QLowEnergyAdvertisingData(PyQt5.sip.wrapper):

    class Discoverability(int):
        DiscoverabilityNone = ... # type: QLowEnergyAdvertisingData.Discoverability
        DiscoverabilityLimited = ... # type: QLowEnergyAdvertisingData.Discoverability
        DiscoverabilityGeneral = ... # type: QLowEnergyAdvertisingData.Discoverability

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyAdvertisingData') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyAdvertisingData') -> None: ...
    def rawData(self) -> QtCore.QByteArray: ...
    def setRawData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def services(self) -> typing.List[QBluetoothUuid]: ...
    def setServices(self, services: typing.Iterable[QBluetoothUuid]) -> None: ...
    def discoverability(self) -> 'QLowEnergyAdvertisingData.Discoverability': ...
    def setDiscoverability(self, mode: 'QLowEnergyAdvertisingData.Discoverability') -> None: ...
    def includePowerLevel(self) -> bool: ...
    def setIncludePowerLevel(self, doInclude: bool) -> None: ...
    def manufacturerData(self) -> QtCore.QByteArray: ...
    def manufacturerId(self) -> int: ...
    def setManufacturerData(self, id: int, data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    @staticmethod
    def invalidManufacturerId() -> int: ...
    def localName(self) -> str: ...
    def setLocalName(self, name: typing.Optional[str]) -> None: ...


class QLowEnergyAdvertisingParameters(PyQt5.sip.wrapper):

    class FilterPolicy(int):
        IgnoreWhiteList = ... # type: QLowEnergyAdvertisingParameters.FilterPolicy
        UseWhiteListForScanning = ... # type: QLowEnergyAdvertisingParameters.FilterPolicy
        UseWhiteListForConnecting = ... # type: QLowEnergyAdvertisingParameters.FilterPolicy
        UseWhiteListForScanningAndConnecting = ... # type: QLowEnergyAdvertisingParameters.FilterPolicy

    class Mode(int):
        AdvInd = ... # type: QLowEnergyAdvertisingParameters.Mode
        AdvScanInd = ... # type: QLowEnergyAdvertisingParameters.Mode
        AdvNonConnInd = ... # type: QLowEnergyAdvertisingParameters.Mode

    class AddressInfo(PyQt5.sip.wrapper):

        address = ... # type: QBluetoothAddress
        type = ... # type: 'QLowEnergyController.RemoteAddressType'

        @typing.overload
        def __init__(self, addr: QBluetoothAddress, t: 'QLowEnergyController.RemoteAddressType') -> None: ...
        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QLowEnergyAdvertisingParameters.AddressInfo') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyAdvertisingParameters') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyAdvertisingParameters') -> None: ...
    def maximumInterval(self) -> int: ...
    def minimumInterval(self) -> int: ...
    def setInterval(self, minimum: int, maximum: int) -> None: ...
    def filterPolicy(self) -> 'QLowEnergyAdvertisingParameters.FilterPolicy': ...
    def whiteList(self) -> typing.List['QLowEnergyAdvertisingParameters.AddressInfo']: ...
    def setWhiteList(self, whiteList: typing.Iterable['QLowEnergyAdvertisingParameters.AddressInfo'], policy: 'QLowEnergyAdvertisingParameters.FilterPolicy') -> None: ...
    def mode(self) -> 'QLowEnergyAdvertisingParameters.Mode': ...
    def setMode(self, mode: 'QLowEnergyAdvertisingParameters.Mode') -> None: ...


class QLowEnergyCharacteristic(PyQt5.sip.wrapper):

    class PropertyType(int):
        Unknown = ... # type: QLowEnergyCharacteristic.PropertyType
        Broadcasting = ... # type: QLowEnergyCharacteristic.PropertyType
        Read = ... # type: QLowEnergyCharacteristic.PropertyType
        WriteNoResponse = ... # type: QLowEnergyCharacteristic.PropertyType
        Write = ... # type: QLowEnergyCharacteristic.PropertyType
        Notify = ... # type: QLowEnergyCharacteristic.PropertyType
        Indicate = ... # type: QLowEnergyCharacteristic.PropertyType
        WriteSigned = ... # type: QLowEnergyCharacteristic.PropertyType
        ExtendedProperty = ... # type: QLowEnergyCharacteristic.PropertyType

    class PropertyTypes(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __xor__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __ior__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __or__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __iand__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __and__(self, f: typing.Union['QLowEnergyCharacteristic.PropertyTypes', 'QLowEnergyCharacteristic.PropertyType']) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __invert__(self) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyCharacteristic') -> None: ...

    def isValid(self) -> bool: ...
    def descriptors(self) -> typing.List['QLowEnergyDescriptor']: ...
    def descriptor(self, uuid: QBluetoothUuid) -> 'QLowEnergyDescriptor': ...
    def handle(self) -> int: ...
    def properties(self) -> 'QLowEnergyCharacteristic.PropertyTypes': ...
    def value(self) -> QtCore.QByteArray: ...
    def uuid(self) -> QBluetoothUuid: ...
    def name(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QLowEnergyCharacteristicData(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyCharacteristicData') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyCharacteristicData') -> None: ...
    def isValid(self) -> bool: ...
    def maximumValueLength(self) -> int: ...
    def minimumValueLength(self) -> int: ...
    def setValueLength(self, minimum: int, maximum: int) -> None: ...
    def writeConstraints(self) -> QBluetooth.AttAccessConstraints: ...
    def setWriteConstraints(self, constraints: typing.Union[QBluetooth.AttAccessConstraints, QBluetooth.AttAccessConstraint]) -> None: ...
    def readConstraints(self) -> QBluetooth.AttAccessConstraints: ...
    def setReadConstraints(self, constraints: typing.Union[QBluetooth.AttAccessConstraints, QBluetooth.AttAccessConstraint]) -> None: ...
    def addDescriptor(self, descriptor: 'QLowEnergyDescriptorData') -> None: ...
    def setDescriptors(self, descriptors: typing.Iterable['QLowEnergyDescriptorData']) -> None: ...
    def descriptors(self) -> typing.List['QLowEnergyDescriptorData']: ...
    def setProperties(self, properties: typing.Union[QLowEnergyCharacteristic.PropertyTypes, QLowEnergyCharacteristic.PropertyType]) -> None: ...
    def properties(self) -> QLowEnergyCharacteristic.PropertyTypes: ...
    def setValue(self, value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def value(self) -> QtCore.QByteArray: ...
    def setUuid(self, uuid: QBluetoothUuid) -> None: ...
    def uuid(self) -> QBluetoothUuid: ...


class QLowEnergyConnectionParameters(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyConnectionParameters') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyConnectionParameters') -> None: ...
    def supervisionTimeout(self) -> int: ...
    def setSupervisionTimeout(self, timeout: int) -> None: ...
    def latency(self) -> int: ...
    def setLatency(self, latency: int) -> None: ...
    def maximumInterval(self) -> float: ...
    def minimumInterval(self) -> float: ...
    def setIntervalRange(self, minimum: float, maximum: float) -> None: ...


class QLowEnergyController(QtCore.QObject):

    class Role(int):
        CentralRole = ... # type: QLowEnergyController.Role
        PeripheralRole = ... # type: QLowEnergyController.Role

    class RemoteAddressType(int):
        PublicAddress = ... # type: QLowEnergyController.RemoteAddressType
        RandomAddress = ... # type: QLowEnergyController.RemoteAddressType

    class ControllerState(int):
        UnconnectedState = ... # type: QLowEnergyController.ControllerState
        ConnectingState = ... # type: QLowEnergyController.ControllerState
        ConnectedState = ... # type: QLowEnergyController.ControllerState
        DiscoveringState = ... # type: QLowEnergyController.ControllerState
        DiscoveredState = ... # type: QLowEnergyController.ControllerState
        ClosingState = ... # type: QLowEnergyController.ControllerState
        AdvertisingState = ... # type: QLowEnergyController.ControllerState

    class Error(int):
        NoError = ... # type: QLowEnergyController.Error
        UnknownError = ... # type: QLowEnergyController.Error
        UnknownRemoteDeviceError = ... # type: QLowEnergyController.Error
        NetworkError = ... # type: QLowEnergyController.Error
        InvalidBluetoothAdapterError = ... # type: QLowEnergyController.Error
        ConnectionError = ... # type: QLowEnergyController.Error
        AdvertisingError = ... # type: QLowEnergyController.Error
        RemoteHostClosedError = ... # type: QLowEnergyController.Error
        AuthorizationError = ... # type: QLowEnergyController.Error

    @typing.overload
    def __init__(self, remoteDevice: QBluetoothDeviceInfo, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, remoteDevice: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, remoteDevice: QBluetoothAddress, localDevice: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def remoteDeviceUuid(self) -> QBluetoothUuid: ...
    connectionUpdated: typing.ClassVar[QtCore.pyqtSignal]
    def role(self) -> 'QLowEnergyController.Role': ...
    def requestConnectionUpdate(self, parameters: QLowEnergyConnectionParameters) -> None: ...
    def addService(self, service: 'QLowEnergyServiceData', parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QLowEnergyService']: ...
    def stopAdvertising(self) -> None: ...
    def startAdvertising(self, parameters: QLowEnergyAdvertisingParameters, advertisingData: QLowEnergyAdvertisingData, scanResponseData: QLowEnergyAdvertisingData = ...) -> None: ...
    @staticmethod
    def createPeripheral(parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QLowEnergyController']: ...
    @typing.overload
    @staticmethod
    def createCentral(remoteDevice: QBluetoothDeviceInfo, parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QLowEnergyController']: ...
    @typing.overload
    @staticmethod
    def createCentral(remoteDevice: QBluetoothAddress, localDevice: QBluetoothAddress, parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QLowEnergyController']: ...
    discoveryFinished: typing.ClassVar[QtCore.pyqtSignal]
    serviceDiscovered: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    def remoteName(self) -> str: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def createServiceObject(self, service: QBluetoothUuid, parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QLowEnergyService']: ...
    def services(self) -> typing.List[QBluetoothUuid]: ...
    def discoverServices(self) -> None: ...
    def disconnectFromDevice(self) -> None: ...
    def connectToDevice(self) -> None: ...
    def setRemoteAddressType(self, type: 'QLowEnergyController.RemoteAddressType') -> None: ...
    def remoteAddressType(self) -> 'QLowEnergyController.RemoteAddressType': ...
    def state(self) -> 'QLowEnergyController.ControllerState': ...
    def remoteAddress(self) -> QBluetoothAddress: ...
    def localAddress(self) -> QBluetoothAddress: ...


class QLowEnergyDescriptor(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyDescriptor') -> None: ...

    def type(self) -> QBluetoothUuid.DescriptorType: ...
    def name(self) -> str: ...
    def handle(self) -> int: ...
    def uuid(self) -> QBluetoothUuid: ...
    def value(self) -> QtCore.QByteArray: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QLowEnergyDescriptorData(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, uuid: QBluetoothUuid, value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyDescriptorData') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyDescriptorData') -> None: ...
    def writeConstraints(self) -> QBluetooth.AttAccessConstraints: ...
    def isWritable(self) -> bool: ...
    def setWritePermissions(self, writable: bool, constraints: typing.Union[QBluetooth.AttAccessConstraints, QBluetooth.AttAccessConstraint] = ...) -> None: ...
    def readConstraints(self) -> QBluetooth.AttAccessConstraints: ...
    def isReadable(self) -> bool: ...
    def setReadPermissions(self, readable: bool, constraints: typing.Union[QBluetooth.AttAccessConstraints, QBluetooth.AttAccessConstraint] = ...) -> None: ...
    def isValid(self) -> bool: ...
    def setUuid(self, uuid: QBluetoothUuid) -> None: ...
    def uuid(self) -> QBluetoothUuid: ...
    def setValue(self, value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def value(self) -> QtCore.QByteArray: ...


class QLowEnergyService(QtCore.QObject):

    class WriteMode(int):
        WriteWithResponse = ... # type: QLowEnergyService.WriteMode
        WriteWithoutResponse = ... # type: QLowEnergyService.WriteMode
        WriteSigned = ... # type: QLowEnergyService.WriteMode

    class ServiceState(int):
        InvalidService = ... # type: QLowEnergyService.ServiceState
        DiscoveryRequired = ... # type: QLowEnergyService.ServiceState
        DiscoveringServices = ... # type: QLowEnergyService.ServiceState
        ServiceDiscovered = ... # type: QLowEnergyService.ServiceState
        LocalService = ... # type: QLowEnergyService.ServiceState

    class ServiceError(int):
        NoError = ... # type: QLowEnergyService.ServiceError
        OperationError = ... # type: QLowEnergyService.ServiceError
        CharacteristicWriteError = ... # type: QLowEnergyService.ServiceError
        DescriptorWriteError = ... # type: QLowEnergyService.ServiceError
        CharacteristicReadError = ... # type: QLowEnergyService.ServiceError
        DescriptorReadError = ... # type: QLowEnergyService.ServiceError
        UnknownError = ... # type: QLowEnergyService.ServiceError

    class ServiceType(int):
        PrimaryService = ... # type: QLowEnergyService.ServiceType
        IncludedService = ... # type: QLowEnergyService.ServiceType

    class ServiceTypes(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __xor__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __ior__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __or__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __iand__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __and__(self, f: typing.Union['QLowEnergyService.ServiceTypes', 'QLowEnergyService.ServiceType']) -> 'QLowEnergyService.ServiceTypes': ...
        def __invert__(self) -> 'QLowEnergyService.ServiceTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    descriptorRead: typing.ClassVar[QtCore.pyqtSignal]
    characteristicRead: typing.ClassVar[QtCore.pyqtSignal]
    def readDescriptor(self, descriptor: QLowEnergyDescriptor) -> None: ...
    def readCharacteristic(self, characteristic: QLowEnergyCharacteristic) -> None: ...
    descriptorWritten: typing.ClassVar[QtCore.pyqtSignal]
    characteristicWritten: typing.ClassVar[QtCore.pyqtSignal]
    characteristicChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def writeDescriptor(self, descriptor: QLowEnergyDescriptor, newValue: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def writeCharacteristic(self, characteristic: QLowEnergyCharacteristic, newValue: typing.Union[QtCore.QByteArray, bytes, bytearray], mode: 'QLowEnergyService.WriteMode' = ...) -> None: ...
    @typing.overload
    def contains(self, characteristic: QLowEnergyCharacteristic) -> bool: ...
    @typing.overload
    def contains(self, descriptor: QLowEnergyDescriptor) -> bool: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def discoverDetails(self) -> None: ...
    def serviceName(self) -> str: ...
    def serviceUuid(self) -> QBluetoothUuid: ...
    def characteristics(self) -> typing.List[QLowEnergyCharacteristic]: ...
    def characteristic(self, uuid: QBluetoothUuid) -> QLowEnergyCharacteristic: ...
    def state(self) -> 'QLowEnergyService.ServiceState': ...
    def type(self) -> 'QLowEnergyService.ServiceTypes': ...
    def includedServices(self) -> typing.List[QBluetoothUuid]: ...


class QLowEnergyServiceData(PyQt5.sip.wrapper):

    class ServiceType(int):
        ServiceTypePrimary = ... # type: QLowEnergyServiceData.ServiceType
        ServiceTypeSecondary = ... # type: QLowEnergyServiceData.ServiceType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLowEnergyServiceData') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QLowEnergyServiceData') -> None: ...
    def isValid(self) -> bool: ...
    def addCharacteristic(self, characteristic: QLowEnergyCharacteristicData) -> None: ...
    def setCharacteristics(self, characteristics: typing.Iterable[QLowEnergyCharacteristicData]) -> None: ...
    def characteristics(self) -> typing.List[QLowEnergyCharacteristicData]: ...
    def addIncludedService(self, service: typing.Optional[QLowEnergyService]) -> None: ...
    def setIncludedServices(self, services: typing.Iterable[QLowEnergyService]) -> None: ...
    def includedServices(self) -> typing.List[QLowEnergyService]: ...
    def setUuid(self, uuid: QBluetoothUuid) -> None: ...
    def uuid(self) -> QBluetoothUuid: ...
    def setType(self, type: 'QLowEnergyServiceData.ServiceType') -> None: ...
    def type(self) -> 'QLowEnergyServiceData.ServiceType': ...
