# The PEP 484 type hints stub file for the QtWebEngineWidgets module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQtWebEngine.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtNetwork
from PyQt5 import QtGui
from PyQt5 import QtWidgets
from PyQt5 import QtPrintSupport
from PyQt5 import QtWebEngine<PERSON>ore
from PyQt5 import QtWebChannel

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt5.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt5.sip.Buffer, int, None]


class QWebEngineCertificateError(PyQt5.sipsimplewrapper):

    class Error(int):
        SslPinnedKeyNotInCertificateChain = ... # type: QWebEngineCertificateError.Error
        CertificateCommonNameInvalid = ... # type: QWebEngineCertificateError.Error
        CertificateDateInvalid = ... # type: QWebEngineCertificateError.Error
        CertificateAuthorityInvalid = ... # type: QWebEngineCertificateError.Error
        CertificateContainsErrors = ... # type: QWebEngineCertificateError.Error
        CertificateNoRevocationMechanism = ... # type: QWebEngineCertificateError.Error
        CertificateUnableToCheckRevocation = ... # type: QWebEngineCertificateError.Error
        CertificateRevoked = ... # type: QWebEngineCertificateError.Error
        CertificateInvalid = ... # type: QWebEngineCertificateError.Error
        CertificateWeakSignatureAlgorithm = ... # type: QWebEngineCertificateError.Error
        CertificateNonUniqueName = ... # type: QWebEngineCertificateError.Error
        CertificateWeakKey = ... # type: QWebEngineCertificateError.Error
        CertificateNameConstraintViolation = ... # type: QWebEngineCertificateError.Error
        CertificateValidityTooLong = ... # type: QWebEngineCertificateError.Error
        CertificateTransparencyRequired = ... # type: QWebEngineCertificateError.Error
        CertificateKnownInterceptionBlocked = ... # type: QWebEngineCertificateError.Error

    def __init__(self, other: 'QWebEngineCertificateError') -> None: ...

    def certificateChain(self) -> typing.List[QtNetwork.QSslCertificate]: ...
    def answered(self) -> bool: ...
    def ignoreCertificateError(self) -> None: ...
    def rejectCertificate(self) -> None: ...
    def deferred(self) -> bool: ...
    def defer(self) -> None: ...
    def errorDescription(self) -> str: ...
    def isOverridable(self) -> bool: ...
    def url(self) -> QtCore.QUrl: ...
    def error(self) -> 'QWebEngineCertificateError.Error': ...


class QWebEngineClientCertificateSelection(PyQt5.sipsimplewrapper):

    def __init__(self, a0: 'QWebEngineClientCertificateSelection') -> None: ...

    def certificates(self) -> typing.List[QtNetwork.QSslCertificate]: ...
    def selectNone(self) -> None: ...
    def select(self, certificate: QtNetwork.QSslCertificate) -> None: ...
    def host(self) -> QtCore.QUrl: ...


class QWebEngineContextMenuData(PyQt5.sipsimplewrapper):

    class EditFlag(int):
        CanUndo = ... # type: QWebEngineContextMenuData.EditFlag
        CanRedo = ... # type: QWebEngineContextMenuData.EditFlag
        CanCut = ... # type: QWebEngineContextMenuData.EditFlag
        CanCopy = ... # type: QWebEngineContextMenuData.EditFlag
        CanPaste = ... # type: QWebEngineContextMenuData.EditFlag
        CanDelete = ... # type: QWebEngineContextMenuData.EditFlag
        CanSelectAll = ... # type: QWebEngineContextMenuData.EditFlag
        CanTranslate = ... # type: QWebEngineContextMenuData.EditFlag
        CanEditRichly = ... # type: QWebEngineContextMenuData.EditFlag

    class MediaFlag(int):
        MediaInError = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaPaused = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaMuted = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaLoop = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaCanSave = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaHasAudio = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaCanToggleControls = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaControls = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaCanPrint = ... # type: QWebEngineContextMenuData.MediaFlag
        MediaCanRotate = ... # type: QWebEngineContextMenuData.MediaFlag

    class MediaType(int):
        MediaTypeNone = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypeImage = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypeVideo = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypeAudio = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypeCanvas = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypeFile = ... # type: QWebEngineContextMenuData.MediaType
        MediaTypePlugin = ... # type: QWebEngineContextMenuData.MediaType

    class MediaFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __xor__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __ior__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __or__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __iand__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __and__(self, f: typing.Union['QWebEngineContextMenuData.MediaFlags', 'QWebEngineContextMenuData.MediaFlag']) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __invert__(self) -> 'QWebEngineContextMenuData.MediaFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class EditFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __xor__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __ior__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __or__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __iand__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __and__(self, f: typing.Union['QWebEngineContextMenuData.EditFlags', 'QWebEngineContextMenuData.EditFlag']) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __invert__(self) -> 'QWebEngineContextMenuData.EditFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QWebEngineContextMenuData') -> None: ...

    def editFlags(self) -> 'QWebEngineContextMenuData.EditFlags': ...
    def mediaFlags(self) -> 'QWebEngineContextMenuData.MediaFlags': ...
    def spellCheckerSuggestions(self) -> typing.List[str]: ...
    def misspelledWord(self) -> str: ...
    def isContentEditable(self) -> bool: ...
    def mediaType(self) -> 'QWebEngineContextMenuData.MediaType': ...
    def mediaUrl(self) -> QtCore.QUrl: ...
    def linkUrl(self) -> QtCore.QUrl: ...
    def linkText(self) -> str: ...
    def selectedText(self) -> str: ...
    def position(self) -> QtCore.QPoint: ...
    def isValid(self) -> bool: ...


class QWebEngineDownloadItem(QtCore.QObject):

    class DownloadInterruptReason(int):
        NoReason = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileFailed = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileAccessDenied = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileNoSpace = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileNameTooLong = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileTooLarge = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileVirusInfected = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileTransientError = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileBlocked = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileSecurityCheckFailed = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileTooShort = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        FileHashMismatch = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        NetworkFailed = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        NetworkTimeout = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        NetworkDisconnected = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        NetworkServerDown = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        NetworkInvalidRequest = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerFailed = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerBadContent = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerUnauthorized = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerCertProblem = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerForbidden = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        ServerUnreachable = ... # type: QWebEngineDownloadItem.DownloadInterruptReason
        UserCanceled = ... # type: QWebEngineDownloadItem.DownloadInterruptReason

    class DownloadType(int):
        Attachment = ... # type: QWebEngineDownloadItem.DownloadType
        DownloadAttribute = ... # type: QWebEngineDownloadItem.DownloadType
        UserRequested = ... # type: QWebEngineDownloadItem.DownloadType
        SavePage = ... # type: QWebEngineDownloadItem.DownloadType

    class SavePageFormat(int):
        UnknownSaveFormat = ... # type: QWebEngineDownloadItem.SavePageFormat
        SingleHtmlSaveFormat = ... # type: QWebEngineDownloadItem.SavePageFormat
        CompleteHtmlSaveFormat = ... # type: QWebEngineDownloadItem.SavePageFormat
        MimeHtmlSaveFormat = ... # type: QWebEngineDownloadItem.SavePageFormat

    class DownloadState(int):
        DownloadRequested = ... # type: QWebEngineDownloadItem.DownloadState
        DownloadInProgress = ... # type: QWebEngineDownloadItem.DownloadState
        DownloadCompleted = ... # type: QWebEngineDownloadItem.DownloadState
        DownloadCancelled = ... # type: QWebEngineDownloadItem.DownloadState
        DownloadInterrupted = ... # type: QWebEngineDownloadItem.DownloadState

    def setDownloadFileName(self, fileName: typing.Optional[str]) -> None: ...
    def downloadFileName(self) -> str: ...
    def setDownloadDirectory(self, directory: typing.Optional[str]) -> None: ...
    def downloadDirectory(self) -> str: ...
    def suggestedFileName(self) -> str: ...
    def page(self) -> typing.Optional['QWebEnginePage']: ...
    def isSavePageDownload(self) -> bool: ...
    isPausedChanged: typing.ClassVar[QtCore.pyqtSignal]
    def resume(self) -> None: ...
    def pause(self) -> None: ...
    def isPaused(self) -> bool: ...
    def interruptReasonString(self) -> str: ...
    def interruptReason(self) -> 'QWebEngineDownloadItem.DownloadInterruptReason': ...
    def type(self) -> 'QWebEngineDownloadItem.DownloadType': ...
    def setSavePageFormat(self, format: 'QWebEngineDownloadItem.SavePageFormat') -> None: ...
    def savePageFormat(self) -> 'QWebEngineDownloadItem.SavePageFormat': ...
    def mimeType(self) -> str: ...
    downloadProgress: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def cancel(self) -> None: ...
    def accept(self) -> None: ...
    def isFinished(self) -> bool: ...
    def setPath(self, path: typing.Optional[str]) -> None: ...
    def path(self) -> str: ...
    def url(self) -> QtCore.QUrl: ...
    def receivedBytes(self) -> int: ...
    def totalBytes(self) -> int: ...
    def state(self) -> 'QWebEngineDownloadItem.DownloadState': ...
    def id(self) -> int: ...


class QWebEngineFullScreenRequest(PyQt5.sipsimplewrapper):

    def origin(self) -> QtCore.QUrl: ...
    def toggleOn(self) -> bool: ...
    def accept(self) -> None: ...
    def reject(self) -> None: ...


class QWebEngineHistoryItem(PyQt5.sipsimplewrapper):

    def __init__(self, other: 'QWebEngineHistoryItem') -> None: ...

    def swap(self, other: 'QWebEngineHistoryItem') -> None: ...
    def isValid(self) -> bool: ...
    def iconUrl(self) -> QtCore.QUrl: ...
    def lastVisited(self) -> QtCore.QDateTime: ...
    def title(self) -> str: ...
    def url(self) -> QtCore.QUrl: ...
    def originalUrl(self) -> QtCore.QUrl: ...


class QWebEngineHistory(PyQt5.sipsimplewrapper):

    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def currentItemIndex(self) -> int: ...
    def itemAt(self, i: int) -> QWebEngineHistoryItem: ...
    def forwardItem(self) -> QWebEngineHistoryItem: ...
    def currentItem(self) -> QWebEngineHistoryItem: ...
    def backItem(self) -> QWebEngineHistoryItem: ...
    def goToItem(self, item: QWebEngineHistoryItem) -> None: ...
    def forward(self) -> None: ...
    def back(self) -> None: ...
    def canGoForward(self) -> bool: ...
    def canGoBack(self) -> bool: ...
    def forwardItems(self, maxItems: int) -> typing.List[QWebEngineHistoryItem]: ...
    def backItems(self, maxItems: int) -> typing.List[QWebEngineHistoryItem]: ...
    def items(self) -> typing.List[QWebEngineHistoryItem]: ...
    def clear(self) -> None: ...


class QWebEnginePage(QtCore.QObject):

    class LifecycleState(int):
        Active = ... # type: QWebEnginePage.LifecycleState
        Frozen = ... # type: QWebEnginePage.LifecycleState
        Discarded = ... # type: QWebEnginePage.LifecycleState

    class RenderProcessTerminationStatus(int):
        NormalTerminationStatus = ... # type: QWebEnginePage.RenderProcessTerminationStatus
        AbnormalTerminationStatus = ... # type: QWebEnginePage.RenderProcessTerminationStatus
        CrashedTerminationStatus = ... # type: QWebEnginePage.RenderProcessTerminationStatus
        KilledTerminationStatus = ... # type: QWebEnginePage.RenderProcessTerminationStatus

    class NavigationType(int):
        NavigationTypeLinkClicked = ... # type: QWebEnginePage.NavigationType
        NavigationTypeTyped = ... # type: QWebEnginePage.NavigationType
        NavigationTypeFormSubmitted = ... # type: QWebEnginePage.NavigationType
        NavigationTypeBackForward = ... # type: QWebEnginePage.NavigationType
        NavigationTypeReload = ... # type: QWebEnginePage.NavigationType
        NavigationTypeRedirect = ... # type: QWebEnginePage.NavigationType
        NavigationTypeOther = ... # type: QWebEnginePage.NavigationType

    class JavaScriptConsoleMessageLevel(int):
        InfoMessageLevel = ... # type: QWebEnginePage.JavaScriptConsoleMessageLevel
        WarningMessageLevel = ... # type: QWebEnginePage.JavaScriptConsoleMessageLevel
        ErrorMessageLevel = ... # type: QWebEnginePage.JavaScriptConsoleMessageLevel

    class FileSelectionMode(int):
        FileSelectOpen = ... # type: QWebEnginePage.FileSelectionMode
        FileSelectOpenMultiple = ... # type: QWebEnginePage.FileSelectionMode

    class Feature(int):
        Notifications = ... # type: QWebEnginePage.Feature
        Geolocation = ... # type: QWebEnginePage.Feature
        MediaAudioCapture = ... # type: QWebEnginePage.Feature
        MediaVideoCapture = ... # type: QWebEnginePage.Feature
        MediaAudioVideoCapture = ... # type: QWebEnginePage.Feature
        MouseLock = ... # type: QWebEnginePage.Feature
        DesktopVideoCapture = ... # type: QWebEnginePage.Feature
        DesktopAudioVideoCapture = ... # type: QWebEnginePage.Feature

    class PermissionPolicy(int):
        PermissionUnknown = ... # type: QWebEnginePage.PermissionPolicy
        PermissionGrantedByUser = ... # type: QWebEnginePage.PermissionPolicy
        PermissionDeniedByUser = ... # type: QWebEnginePage.PermissionPolicy

    class WebWindowType(int):
        WebBrowserWindow = ... # type: QWebEnginePage.WebWindowType
        WebBrowserTab = ... # type: QWebEnginePage.WebWindowType
        WebDialog = ... # type: QWebEnginePage.WebWindowType
        WebBrowserBackgroundTab = ... # type: QWebEnginePage.WebWindowType

    class FindFlag(int):
        FindBackward = ... # type: QWebEnginePage.FindFlag
        FindCaseSensitively = ... # type: QWebEnginePage.FindFlag

    class WebAction(int):
        NoWebAction = ... # type: QWebEnginePage.WebAction
        Back = ... # type: QWebEnginePage.WebAction
        Forward = ... # type: QWebEnginePage.WebAction
        Stop = ... # type: QWebEnginePage.WebAction
        Reload = ... # type: QWebEnginePage.WebAction
        Cut = ... # type: QWebEnginePage.WebAction
        Copy = ... # type: QWebEnginePage.WebAction
        Paste = ... # type: QWebEnginePage.WebAction
        Undo = ... # type: QWebEnginePage.WebAction
        Redo = ... # type: QWebEnginePage.WebAction
        SelectAll = ... # type: QWebEnginePage.WebAction
        ReloadAndBypassCache = ... # type: QWebEnginePage.WebAction
        PasteAndMatchStyle = ... # type: QWebEnginePage.WebAction
        OpenLinkInThisWindow = ... # type: QWebEnginePage.WebAction
        OpenLinkInNewWindow = ... # type: QWebEnginePage.WebAction
        OpenLinkInNewTab = ... # type: QWebEnginePage.WebAction
        CopyLinkToClipboard = ... # type: QWebEnginePage.WebAction
        DownloadLinkToDisk = ... # type: QWebEnginePage.WebAction
        CopyImageToClipboard = ... # type: QWebEnginePage.WebAction
        CopyImageUrlToClipboard = ... # type: QWebEnginePage.WebAction
        DownloadImageToDisk = ... # type: QWebEnginePage.WebAction
        CopyMediaUrlToClipboard = ... # type: QWebEnginePage.WebAction
        ToggleMediaControls = ... # type: QWebEnginePage.WebAction
        ToggleMediaLoop = ... # type: QWebEnginePage.WebAction
        ToggleMediaPlayPause = ... # type: QWebEnginePage.WebAction
        ToggleMediaMute = ... # type: QWebEnginePage.WebAction
        DownloadMediaToDisk = ... # type: QWebEnginePage.WebAction
        InspectElement = ... # type: QWebEnginePage.WebAction
        ExitFullScreen = ... # type: QWebEnginePage.WebAction
        RequestClose = ... # type: QWebEnginePage.WebAction
        Unselect = ... # type: QWebEnginePage.WebAction
        SavePage = ... # type: QWebEnginePage.WebAction
        OpenLinkInNewBackgroundTab = ... # type: QWebEnginePage.WebAction
        ViewSource = ... # type: QWebEnginePage.WebAction
        ToggleBold = ... # type: QWebEnginePage.WebAction
        ToggleItalic = ... # type: QWebEnginePage.WebAction
        ToggleUnderline = ... # type: QWebEnginePage.WebAction
        ToggleStrikethrough = ... # type: QWebEnginePage.WebAction
        AlignLeft = ... # type: QWebEnginePage.WebAction
        AlignCenter = ... # type: QWebEnginePage.WebAction
        AlignRight = ... # type: QWebEnginePage.WebAction
        AlignJustified = ... # type: QWebEnginePage.WebAction
        Indent = ... # type: QWebEnginePage.WebAction
        Outdent = ... # type: QWebEnginePage.WebAction
        InsertOrderedList = ... # type: QWebEnginePage.WebAction
        InsertUnorderedList = ... # type: QWebEnginePage.WebAction

    class FindFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __xor__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __ior__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __or__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __iand__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __and__(self, f: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag']) -> 'QWebEnginePage.FindFlags': ...
        def __invert__(self) -> 'QWebEnginePage.FindFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, profile: typing.Optional['QWebEngineProfile'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    renderProcessPidChanged: typing.ClassVar[QtCore.pyqtSignal]
    def renderProcessPid(self) -> int: ...
    findTextFinished: typing.ClassVar[QtCore.pyqtSignal]
    recommendedStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    lifecycleStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def recommendedState(self) -> 'QWebEnginePage.LifecycleState': ...
    def setLifecycleState(self, state: 'QWebEnginePage.LifecycleState') -> None: ...
    def lifecycleState(self) -> 'QWebEnginePage.LifecycleState': ...
    def setUrlRequestInterceptor(self, interceptor: typing.Optional[QtWebEngineCore.QWebEngineUrlRequestInterceptor]) -> None: ...
    printRequested: typing.ClassVar[QtCore.pyqtSignal]
    selectClientCertificate: typing.ClassVar[QtCore.pyqtSignal]
    registerProtocolHandlerRequested: typing.ClassVar[QtCore.pyqtSignal]
    quotaRequested: typing.ClassVar[QtCore.pyqtSignal]
    def devToolsPage(self) -> typing.Optional['QWebEnginePage']: ...
    def setDevToolsPage(self, page: typing.Optional['QWebEnginePage']) -> None: ...
    def inspectedPage(self) -> typing.Optional['QWebEnginePage']: ...
    def setInspectedPage(self, page: typing.Optional['QWebEnginePage']) -> None: ...
    def download(self, url: QtCore.QUrl, filename: typing.Optional[str] = ...) -> None: ...
    def print(self, printer: typing.Optional[QtPrintSupport.QPrinter], resultCallback: typing.Callable[[bool], None]) -> None: ...
    def save(self, filePath: typing.Optional[str], format: QWebEngineDownloadItem.SavePageFormat = ...) -> None: ...
    def replaceMisspelledWord(self, replacement: typing.Optional[str]) -> None: ...
    pdfPrintingFinished: typing.ClassVar[QtCore.pyqtSignal]
    recentlyAudibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioMutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    scrollPositionChanged: typing.ClassVar[QtCore.pyqtSignal]
    iconChanged: typing.ClassVar[QtCore.pyqtSignal]
    def contextMenuData(self) -> QWebEngineContextMenuData: ...
    @typing.overload
    def printToPdf(self, filePath: typing.Optional[str], pageLayout: QtGui.QPageLayout = ...) -> None: ...
    @typing.overload
    def printToPdf(self, resultCallback: typing.Callable[[typing.Union[QtCore.QByteArray, bytes, bytearray]], None], pageLayout: QtGui.QPageLayout = ...) -> None: ...
    def recentlyAudible(self) -> bool: ...
    def setAudioMuted(self, muted: bool) -> None: ...
    def isAudioMuted(self) -> bool: ...
    def contentsSize(self) -> QtCore.QSizeF: ...
    def scrollPosition(self) -> QtCore.QPointF: ...
    def icon(self) -> QtGui.QIcon: ...
    renderProcessTerminated: typing.ClassVar[QtCore.pyqtSignal]
    fullScreenRequested: typing.ClassVar[QtCore.pyqtSignal]
    def setBackgroundColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def backgroundColor(self) -> QtGui.QColor: ...
    def acceptNavigationRequest(self, url: QtCore.QUrl, type: 'QWebEnginePage.NavigationType', isMainFrame: bool) -> bool: ...
    @typing.overload
    def setWebChannel(self, a0: typing.Optional[QtWebChannel.QWebChannel]) -> None: ...
    @typing.overload
    def setWebChannel(self, a0: typing.Optional[QtWebChannel.QWebChannel], worldId: int) -> None: ...
    def webChannel(self) -> typing.Optional[QtWebChannel.QWebChannel]: ...
    def scripts(self) -> 'QWebEngineScriptCollection': ...
    def profile(self) -> typing.Optional['QWebEngineProfile']: ...
    def certificateError(self, certificateError: QWebEngineCertificateError) -> bool: ...
    def javaScriptConsoleMessage(self, level: 'QWebEnginePage.JavaScriptConsoleMessageLevel', message: typing.Optional[str], lineNumber: int, sourceID: typing.Optional[str]) -> None: ...
    def javaScriptPrompt(self, securityOrigin: QtCore.QUrl, msg: typing.Optional[str], defaultValue: typing.Optional[str]) -> typing.Tuple[bool, typing.Optional[str]]: ...
    def javaScriptConfirm(self, securityOrigin: QtCore.QUrl, msg: typing.Optional[str]) -> bool: ...
    def javaScriptAlert(self, securityOrigin: QtCore.QUrl, msg: typing.Optional[str]) -> None: ...
    def chooseFiles(self, mode: 'QWebEnginePage.FileSelectionMode', oldFiles: typing.Iterable[typing.Optional[str]], acceptedMimeTypes: typing.Iterable[typing.Optional[str]]) -> typing.List[str]: ...
    def createWindow(self, type: 'QWebEnginePage.WebWindowType') -> typing.Optional['QWebEnginePage']: ...
    iconUrlChanged: typing.ClassVar[QtCore.pyqtSignal]
    urlChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleChanged: typing.ClassVar[QtCore.pyqtSignal]
    proxyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    authenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    featurePermissionRequestCanceled: typing.ClassVar[QtCore.pyqtSignal]
    featurePermissionRequested: typing.ClassVar[QtCore.pyqtSignal]
    windowCloseRequested: typing.ClassVar[QtCore.pyqtSignal]
    geometryChangeRequested: typing.ClassVar[QtCore.pyqtSignal]
    selectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    linkHovered: typing.ClassVar[QtCore.pyqtSignal]
    loadFinished: typing.ClassVar[QtCore.pyqtSignal]
    loadProgress: typing.ClassVar[QtCore.pyqtSignal]
    loadStarted: typing.ClassVar[QtCore.pyqtSignal]
    def settings(self) -> typing.Optional['QWebEngineSettings']: ...
    @typing.overload
    def runJavaScript(self, scriptSource: typing.Optional[str], worldId: int) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: typing.Optional[str], worldId: int, resultCallback: typing.Callable[..., None]) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: typing.Optional[str]) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: typing.Optional[str], resultCallback: typing.Callable[[typing.Any], None]) -> None: ...
    def setZoomFactor(self, factor: float) -> None: ...
    def zoomFactor(self) -> float: ...
    def iconUrl(self) -> QtCore.QUrl: ...
    def requestedUrl(self) -> QtCore.QUrl: ...
    def url(self) -> QtCore.QUrl: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def title(self) -> str: ...
    def toPlainText(self, resultCallback: typing.Callable[[typing.Optional[str]], None]) -> None: ...
    def toHtml(self, resultCallback: typing.Callable[[typing.Optional[str]], None]) -> None: ...
    def setContent(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray], mimeType: typing.Optional[str] = ..., baseUrl: QtCore.QUrl = ...) -> None: ...
    def setHtml(self, html: typing.Optional[str], baseUrl: QtCore.QUrl = ...) -> None: ...
    @typing.overload
    def load(self, url: QtCore.QUrl) -> None: ...
    @typing.overload
    def load(self, request: QtWebEngineCore.QWebEngineHttpRequest) -> None: ...
    def setFeaturePermission(self, securityOrigin: QtCore.QUrl, feature: 'QWebEnginePage.Feature', policy: 'QWebEnginePage.PermissionPolicy') -> None: ...
    def createStandardContextMenu(self) -> typing.Optional[QtWidgets.QMenu]: ...
    def findText(self, subString: typing.Optional[str], options: typing.Union['QWebEnginePage.FindFlags', 'QWebEnginePage.FindFlag'] = ..., resultCallback: typing.Callable[[bool], None] = ...) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def triggerAction(self, action: 'QWebEnginePage.WebAction', checked: bool = ...) -> None: ...
    def action(self, action: 'QWebEnginePage.WebAction') -> typing.Optional[QtWidgets.QAction]: ...
    def selectedText(self) -> str: ...
    def hasSelection(self) -> bool: ...
    def view(self) -> typing.Optional[QtWidgets.QWidget]: ...
    def setView(self, view: typing.Optional[QtWidgets.QWidget]) -> None: ...
    def history(self) -> typing.Optional[QWebEngineHistory]: ...


class QWebEngineProfile(QtCore.QObject):

    class PersistentCookiesPolicy(int):
        NoPersistentCookies = ... # type: QWebEngineProfile.PersistentCookiesPolicy
        AllowPersistentCookies = ... # type: QWebEngineProfile.PersistentCookiesPolicy
        ForcePersistentCookies = ... # type: QWebEngineProfile.PersistentCookiesPolicy

    class HttpCacheType(int):
        MemoryHttpCache = ... # type: QWebEngineProfile.HttpCacheType
        DiskHttpCache = ... # type: QWebEngineProfile.HttpCacheType
        NoCache = ... # type: QWebEngineProfile.HttpCacheType

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def clientCertificateStore(self) -> typing.Optional[QtWebEngineCore.QWebEngineClientCertificateStore]: ...
    def setNotificationPresenter(self, a0: typing.Callable[[QtWebEngineCore.QWebEngineNotification], None]) -> None: ...
    def setDownloadPath(self, path: typing.Optional[str]) -> None: ...
    def downloadPath(self) -> str: ...
    def isUsedForGlobalCertificateVerification(self) -> bool: ...
    def setUseForGlobalCertificateVerification(self, enabled: bool = ...) -> None: ...
    def isSpellCheckEnabled(self) -> bool: ...
    def setSpellCheckEnabled(self, enabled: bool) -> None: ...
    def spellCheckLanguages(self) -> typing.List[str]: ...
    def setSpellCheckLanguages(self, languages: typing.Iterable[typing.Optional[str]]) -> None: ...
    def clearHttpCache(self) -> None: ...
    def removeAllUrlSchemeHandlers(self) -> None: ...
    def removeUrlSchemeHandler(self, a0: typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]) -> None: ...
    def removeUrlScheme(self, scheme: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def installUrlSchemeHandler(self, scheme: typing.Union[QtCore.QByteArray, bytes, bytearray], a1: typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]) -> None: ...
    def urlSchemeHandler(self, a0: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]: ...
    def setRequestInterceptor(self, interceptor: typing.Optional[QtWebEngineCore.QWebEngineUrlRequestInterceptor]) -> None: ...
    def setUrlRequestInterceptor(self, interceptor: typing.Optional[QtWebEngineCore.QWebEngineUrlRequestInterceptor]) -> None: ...
    def cookieStore(self) -> typing.Optional[QtWebEngineCore.QWebEngineCookieStore]: ...
    def httpAcceptLanguage(self) -> str: ...
    def setHttpAcceptLanguage(self, httpAcceptLanguage: typing.Optional[str]) -> None: ...
    downloadRequested: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def defaultProfile() -> typing.Optional['QWebEngineProfile']: ...
    def scripts(self) -> typing.Optional['QWebEngineScriptCollection']: ...
    def settings(self) -> typing.Optional['QWebEngineSettings']: ...
    def visitedLinksContainsUrl(self, url: QtCore.QUrl) -> bool: ...
    def clearVisitedLinks(self, urls: typing.Iterable[QtCore.QUrl]) -> None: ...
    def clearAllVisitedLinks(self) -> None: ...
    def setHttpCacheMaximumSize(self, maxSize: int) -> None: ...
    def httpCacheMaximumSize(self) -> int: ...
    def setPersistentCookiesPolicy(self, a0: 'QWebEngineProfile.PersistentCookiesPolicy') -> None: ...
    def persistentCookiesPolicy(self) -> 'QWebEngineProfile.PersistentCookiesPolicy': ...
    def setHttpCacheType(self, a0: 'QWebEngineProfile.HttpCacheType') -> None: ...
    def httpCacheType(self) -> 'QWebEngineProfile.HttpCacheType': ...
    def setHttpUserAgent(self, userAgent: typing.Optional[str]) -> None: ...
    def httpUserAgent(self) -> str: ...
    def setCachePath(self, path: typing.Optional[str]) -> None: ...
    def cachePath(self) -> str: ...
    def setPersistentStoragePath(self, path: typing.Optional[str]) -> None: ...
    def persistentStoragePath(self) -> str: ...
    def isOffTheRecord(self) -> bool: ...
    def storageName(self) -> str: ...


class QWebEngineScript(PyQt5.sipsimplewrapper):

    class ScriptWorldId(int):
        MainWorld = ... # type: QWebEngineScript.ScriptWorldId
        ApplicationWorld = ... # type: QWebEngineScript.ScriptWorldId
        UserWorld = ... # type: QWebEngineScript.ScriptWorldId

    class InjectionPoint(int):
        Deferred = ... # type: QWebEngineScript.InjectionPoint
        DocumentReady = ... # type: QWebEngineScript.InjectionPoint
        DocumentCreation = ... # type: QWebEngineScript.InjectionPoint

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QWebEngineScript') -> None: ...

    def swap(self, other: 'QWebEngineScript') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setRunsOnSubFrames(self, on: bool) -> None: ...
    def runsOnSubFrames(self) -> bool: ...
    def setWorldId(self, a0: int) -> None: ...
    def worldId(self) -> int: ...
    def setInjectionPoint(self, a0: 'QWebEngineScript.InjectionPoint') -> None: ...
    def injectionPoint(self) -> 'QWebEngineScript.InjectionPoint': ...
    def setSourceCode(self, a0: typing.Optional[str]) -> None: ...
    def sourceCode(self) -> str: ...
    def setName(self, a0: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def isNull(self) -> bool: ...


class QWebEngineScriptCollection(PyQt5.sipsimplewrapper):

    def toList(self) -> typing.List[QWebEngineScript]: ...
    def clear(self) -> None: ...
    def remove(self, a0: QWebEngineScript) -> bool: ...
    @typing.overload
    def insert(self, a0: QWebEngineScript) -> None: ...
    @typing.overload
    def insert(self, list: typing.Iterable[QWebEngineScript]) -> None: ...
    def findScripts(self, name: typing.Optional[str]) -> typing.List[QWebEngineScript]: ...
    def findScript(self, name: typing.Optional[str]) -> QWebEngineScript: ...
    def contains(self, value: QWebEngineScript) -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def isEmpty(self) -> bool: ...


class QWebEngineSettings(PyQt5.sipsimplewrapper):

    class UnknownUrlSchemePolicy(int):
        DisallowUnknownUrlSchemes = ... # type: QWebEngineSettings.UnknownUrlSchemePolicy
        AllowUnknownUrlSchemesFromUserInteraction = ... # type: QWebEngineSettings.UnknownUrlSchemePolicy
        AllowAllUnknownUrlSchemes = ... # type: QWebEngineSettings.UnknownUrlSchemePolicy

    class FontSize(int):
        MinimumFontSize = ... # type: QWebEngineSettings.FontSize
        MinimumLogicalFontSize = ... # type: QWebEngineSettings.FontSize
        DefaultFontSize = ... # type: QWebEngineSettings.FontSize
        DefaultFixedFontSize = ... # type: QWebEngineSettings.FontSize

    class WebAttribute(int):
        AutoLoadImages = ... # type: QWebEngineSettings.WebAttribute
        JavascriptEnabled = ... # type: QWebEngineSettings.WebAttribute
        JavascriptCanOpenWindows = ... # type: QWebEngineSettings.WebAttribute
        JavascriptCanAccessClipboard = ... # type: QWebEngineSettings.WebAttribute
        LinksIncludedInFocusChain = ... # type: QWebEngineSettings.WebAttribute
        LocalStorageEnabled = ... # type: QWebEngineSettings.WebAttribute
        LocalContentCanAccessRemoteUrls = ... # type: QWebEngineSettings.WebAttribute
        XSSAuditingEnabled = ... # type: QWebEngineSettings.WebAttribute
        SpatialNavigationEnabled = ... # type: QWebEngineSettings.WebAttribute
        LocalContentCanAccessFileUrls = ... # type: QWebEngineSettings.WebAttribute
        HyperlinkAuditingEnabled = ... # type: QWebEngineSettings.WebAttribute
        ScrollAnimatorEnabled = ... # type: QWebEngineSettings.WebAttribute
        ErrorPageEnabled = ... # type: QWebEngineSettings.WebAttribute
        PluginsEnabled = ... # type: QWebEngineSettings.WebAttribute
        FullScreenSupportEnabled = ... # type: QWebEngineSettings.WebAttribute
        ScreenCaptureEnabled = ... # type: QWebEngineSettings.WebAttribute
        WebGLEnabled = ... # type: QWebEngineSettings.WebAttribute
        Accelerated2dCanvasEnabled = ... # type: QWebEngineSettings.WebAttribute
        AutoLoadIconsForPage = ... # type: QWebEngineSettings.WebAttribute
        TouchIconsEnabled = ... # type: QWebEngineSettings.WebAttribute
        FocusOnNavigationEnabled = ... # type: QWebEngineSettings.WebAttribute
        PrintElementBackgrounds = ... # type: QWebEngineSettings.WebAttribute
        AllowRunningInsecureContent = ... # type: QWebEngineSettings.WebAttribute
        AllowGeolocationOnInsecureOrigins = ... # type: QWebEngineSettings.WebAttribute
        AllowWindowActivationFromJavaScript = ... # type: QWebEngineSettings.WebAttribute
        ShowScrollBars = ... # type: QWebEngineSettings.WebAttribute
        PlaybackRequiresUserGesture = ... # type: QWebEngineSettings.WebAttribute
        WebRTCPublicInterfacesOnly = ... # type: QWebEngineSettings.WebAttribute
        JavascriptCanPaste = ... # type: QWebEngineSettings.WebAttribute
        DnsPrefetchEnabled = ... # type: QWebEngineSettings.WebAttribute
        PdfViewerEnabled = ... # type: QWebEngineSettings.WebAttribute

    class FontFamily(int):
        StandardFont = ... # type: QWebEngineSettings.FontFamily
        FixedFont = ... # type: QWebEngineSettings.FontFamily
        SerifFont = ... # type: QWebEngineSettings.FontFamily
        SansSerifFont = ... # type: QWebEngineSettings.FontFamily
        CursiveFont = ... # type: QWebEngineSettings.FontFamily
        FantasyFont = ... # type: QWebEngineSettings.FontFamily
        PictographFont = ... # type: QWebEngineSettings.FontFamily

    def resetUnknownUrlSchemePolicy(self) -> None: ...
    def setUnknownUrlSchemePolicy(self, policy: 'QWebEngineSettings.UnknownUrlSchemePolicy') -> None: ...
    def unknownUrlSchemePolicy(self) -> 'QWebEngineSettings.UnknownUrlSchemePolicy': ...
    def defaultTextEncoding(self) -> str: ...
    def setDefaultTextEncoding(self, encoding: typing.Optional[str]) -> None: ...
    def resetAttribute(self, attr: 'QWebEngineSettings.WebAttribute') -> None: ...
    def testAttribute(self, attr: 'QWebEngineSettings.WebAttribute') -> bool: ...
    def setAttribute(self, attr: 'QWebEngineSettings.WebAttribute', on: bool) -> None: ...
    def resetFontSize(self, type: 'QWebEngineSettings.FontSize') -> None: ...
    def fontSize(self, type: 'QWebEngineSettings.FontSize') -> int: ...
    def setFontSize(self, type: 'QWebEngineSettings.FontSize', size: int) -> None: ...
    def resetFontFamily(self, which: 'QWebEngineSettings.FontFamily') -> None: ...
    def fontFamily(self, which: 'QWebEngineSettings.FontFamily') -> str: ...
    def setFontFamily(self, which: 'QWebEngineSettings.FontFamily', family: typing.Optional[str]) -> None: ...
    @staticmethod
    def globalSettings() -> typing.Optional['QWebEngineSettings']: ...
    @staticmethod
    def defaultSettings() -> typing.Optional['QWebEngineSettings']: ...


class QWebEngineView(QtWidgets.QWidget):

    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...

    def closeEvent(self, a0: typing.Optional[QtGui.QCloseEvent]) -> None: ...
    def dropEvent(self, e: typing.Optional[QtGui.QDropEvent]) -> None: ...
    def dragMoveEvent(self, e: typing.Optional[QtGui.QDragMoveEvent]) -> None: ...
    def dragLeaveEvent(self, e: typing.Optional[QtGui.QDragLeaveEvent]) -> None: ...
    def dragEnterEvent(self, e: typing.Optional[QtGui.QDragEnterEvent]) -> None: ...
    iconChanged: typing.ClassVar[QtCore.pyqtSignal]
    def icon(self) -> QtGui.QIcon: ...
    def hideEvent(self, a0: typing.Optional[QtGui.QHideEvent]) -> None: ...
    def showEvent(self, a0: typing.Optional[QtGui.QShowEvent]) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def contextMenuEvent(self, a0: typing.Optional[QtGui.QContextMenuEvent]) -> None: ...
    def createWindow(self, type: QWebEnginePage.WebWindowType) -> typing.Optional['QWebEngineView']: ...
    renderProcessTerminated: typing.ClassVar[QtCore.pyqtSignal]
    iconUrlChanged: typing.ClassVar[QtCore.pyqtSignal]
    urlChanged: typing.ClassVar[QtCore.pyqtSignal]
    selectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleChanged: typing.ClassVar[QtCore.pyqtSignal]
    loadFinished: typing.ClassVar[QtCore.pyqtSignal]
    loadProgress: typing.ClassVar[QtCore.pyqtSignal]
    loadStarted: typing.ClassVar[QtCore.pyqtSignal]
    def reload(self) -> None: ...
    def forward(self) -> None: ...
    def back(self) -> None: ...
    def stop(self) -> None: ...
    def settings(self) -> typing.Optional[QWebEngineSettings]: ...
    def sizeHint(self) -> QtCore.QSize: ...
    def findText(self, subString: typing.Optional[str], options: typing.Union[QWebEnginePage.FindFlags, QWebEnginePage.FindFlag] = ..., resultCallback: typing.Callable[[bool], None] = ...) -> None: ...
    def setZoomFactor(self, factor: float) -> None: ...
    def zoomFactor(self) -> float: ...
    def triggerPageAction(self, action: QWebEnginePage.WebAction, checked: bool = ...) -> None: ...
    def pageAction(self, action: QWebEnginePage.WebAction) -> typing.Optional[QtWidgets.QAction]: ...
    def selectedText(self) -> str: ...
    def hasSelection(self) -> bool: ...
    def iconUrl(self) -> QtCore.QUrl: ...
    def url(self) -> QtCore.QUrl: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def title(self) -> str: ...
    def history(self) -> typing.Optional[QWebEngineHistory]: ...
    def setContent(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray], mimeType: typing.Optional[str] = ..., baseUrl: QtCore.QUrl = ...) -> None: ...
    def setHtml(self, html: typing.Optional[str], baseUrl: QtCore.QUrl = ...) -> None: ...
    @typing.overload
    def load(self, url: QtCore.QUrl) -> None: ...
    @typing.overload
    def load(self, request: QtWebEngineCore.QWebEngineHttpRequest) -> None: ...
    def setPage(self, page: typing.Optional[QWebEnginePage]) -> None: ...
    def page(self) -> typing.Optional[QWebEnginePage]: ...
