import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtPositioning 5.14'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "LocationSingleton"
        prototype: "QObject"
        exports: ["QtPositioning/QtPositioning 5.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method { name: "coordinate"; type: "QGeoCoordinate" }
        Method {
            name: "coordinate"
            type: "QGeoCoordinate"
            Parameter { name: "latitude"; type: "double" }
            Parameter { name: "longitude"; type: "double" }
            Parameter { name: "altitude"; type: "double" }
        }
        Method {
            name: "coordinate"
            type: "QGeoCoordinate"
            Parameter { name: "latitude"; type: "double" }
            Parameter { name: "longitude"; type: "double" }
        }
        Method { name: "shape"; type: "QGeoShape" }
        Method { name: "rectangle"; type: "QGeoRectangle" }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            Parameter { name: "center"; type: "QGeoCoordinate" }
            Parameter { name: "width"; type: "double" }
            Parameter { name: "height"; type: "double" }
        }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            Parameter { name: "topLeft"; type: "QGeoCoordinate" }
            Parameter { name: "bottomRight"; type: "QGeoCoordinate" }
        }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            Parameter { name: "coordinates"; type: "QVariantList" }
        }
        Method { name: "circle"; type: "QGeoCircle" }
        Method {
            name: "circle"
            type: "QGeoCircle"
            Parameter { name: "center"; type: "QGeoCoordinate" }
            Parameter { name: "radius"; type: "double" }
        }
        Method {
            name: "circle"
            type: "QGeoCircle"
            Parameter { name: "center"; type: "QGeoCoordinate" }
        }
        Method { name: "path"; type: "QGeoPath" }
        Method {
            name: "path"
            type: "QGeoPath"
            Parameter { name: "value"; type: "QJSValue" }
            Parameter { name: "width"; type: "double" }
        }
        Method {
            name: "path"
            type: "QGeoPath"
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method { name: "polygon"; type: "QGeoPolygon" }
        Method {
            name: "polygon"
            type: "QGeoPolygon"
            Parameter { name: "value"; type: "QVariantList" }
        }
        Method {
            name: "polygon"
            type: "QGeoPolygon"
            Parameter { name: "perimeter"; type: "QVariantList" }
            Parameter { name: "holes"; type: "QVariantList" }
        }
        Method {
            name: "shapeToCircle"
            type: "QGeoCircle"
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToRectangle"
            type: "QGeoRectangle"
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToPath"
            type: "QGeoPath"
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToPolygon"
            type: "QGeoPolygon"
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "mercatorToCoord"
            revision: 12
            type: "QGeoCoordinate"
            Parameter { name: "mercator"; type: "QPointF" }
        }
        Method {
            name: "coordToMercator"
            revision: 12
            type: "QPointF"
            Parameter { name: "coord"; type: "QGeoCoordinate" }
        }
    }
    Component {
        name: "QDeclarativeGeoAddress"
        prototype: "QObject"
        exports: ["QtPositioning/Address 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "address"; type: "QGeoAddress" }
        Property { name: "text"; type: "string" }
        Property { name: "country"; type: "string" }
        Property { name: "countryCode"; type: "string" }
        Property { name: "state"; type: "string" }
        Property { name: "county"; type: "string" }
        Property { name: "city"; type: "string" }
        Property { name: "district"; type: "string" }
        Property { name: "street"; type: "string" }
        Property { name: "postalCode"; type: "string" }
        Property { name: "isTextGenerated"; type: "bool"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeGeoLocation"
        prototype: "QObject"
        exports: ["QtPositioning/Location 5.0", "QtPositioning/Location 5.13"]
        exportMetaObjectRevisions: [0, 13]
        Property { name: "location"; type: "QGeoLocation" }
        Property { name: "address"; type: "QDeclarativeGeoAddress"; isPointer: true }
        Property { name: "coordinate"; type: "QGeoCoordinate" }
        Property { name: "boundingBox"; type: "QGeoRectangle" }
        Property { name: "extendedAttributes"; revision: 13; type: "QVariantMap" }
    }
    Component {
        name: "QDeclarativePluginParameter"
        prototype: "QObject"
        exports: ["QtPositioning/PluginParameter 5.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "value"; type: "QVariant" }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal { name: "initialized" }
    }
    Component {
        name: "QDeclarativePosition"
        prototype: "QObject"
        exports: [
            "QtPositioning/Position 5.0",
            "QtPositioning/Position 5.3",
            "QtPositioning/Position 5.4"
        ]
        exportMetaObjectRevisions: [0, 1, 2]
        Property { name: "latitudeValid"; type: "bool"; isReadonly: true }
        Property { name: "longitudeValid"; type: "bool"; isReadonly: true }
        Property { name: "altitudeValid"; type: "bool"; isReadonly: true }
        Property { name: "coordinate"; type: "QGeoCoordinate"; isReadonly: true }
        Property { name: "timestamp"; type: "QDateTime"; isReadonly: true }
        Property { name: "speed"; type: "double"; isReadonly: true }
        Property { name: "speedValid"; type: "bool"; isReadonly: true }
        Property { name: "horizontalAccuracy"; type: "double" }
        Property { name: "verticalAccuracy"; type: "double" }
        Property { name: "horizontalAccuracyValid"; type: "bool"; isReadonly: true }
        Property { name: "verticalAccuracyValid"; type: "bool"; isReadonly: true }
        Property { name: "directionValid"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "direction"; revision: 1; type: "double"; isReadonly: true }
        Property { name: "verticalSpeedValid"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "verticalSpeed"; revision: 1; type: "double"; isReadonly: true }
        Property { name: "magneticVariation"; revision: 2; type: "double"; isReadonly: true }
        Property { name: "magneticVariationValid"; revision: 2; type: "bool"; isReadonly: true }
        Signal { name: "directionValidChanged"; revision: 1 }
        Signal { name: "directionChanged"; revision: 1 }
        Signal { name: "verticalSpeedValidChanged"; revision: 1 }
        Signal { name: "verticalSpeedChanged"; revision: 1 }
        Signal { name: "magneticVariationChanged"; revision: 2 }
        Signal { name: "magneticVariationValidChanged"; revision: 2 }
    }
    Component {
        name: "QDeclarativePositionSource"
        defaultProperty: "parameters"
        prototype: "QObject"
        exports: ["QtPositioning/PositionSource 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "PositioningMethod"
            values: {
                "NoPositioningMethods": 0,
                "SatellitePositioningMethods": 255,
                "NonSatellitePositioningMethods": -256,
                "AllPositioningMethods": -1
            }
        }
        Enum {
            name: "PositioningMethods"
            values: {
                "NoPositioningMethods": 0,
                "SatellitePositioningMethods": 255,
                "NonSatellitePositioningMethods": -256,
                "AllPositioningMethods": -1
            }
        }
        Enum {
            name: "SourceError"
            values: {
                "AccessError": 0,
                "ClosedError": 1,
                "UnknownSourceError": 2,
                "NoError": 3,
                "SocketError": 100
            }
        }
        Property { name: "position"; type: "QDeclarativePosition"; isReadonly: true; isPointer: true }
        Property { name: "active"; type: "bool" }
        Property { name: "valid"; type: "bool"; isReadonly: true }
        Property { name: "nmeaSource"; type: "QUrl" }
        Property { name: "updateInterval"; type: "int" }
        Property { name: "supportedPositioningMethods"; type: "PositioningMethods"; isReadonly: true }
        Property { name: "preferredPositioningMethods"; type: "PositioningMethods" }
        Property { name: "sourceError"; type: "SourceError"; isReadonly: true }
        Property { name: "name"; type: "string" }
        Property {
            name: "parameters"
            revision: 14
            type: "QDeclarativePluginParameter"
            isList: true
            isReadonly: true
        }
        Signal { name: "validityChanged" }
        Signal { name: "updateTimeout" }
        Method { name: "update" }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "setBackendProperty"
            revision: 14
            type: "bool"
            Parameter { name: "name"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "backendProperty"
            revision: 14
            type: "QVariant"
            Parameter { name: "name"; type: "string" }
        }
    }
    Component {
        name: "QGeoShape"
        exports: ["QtPositioning/GeoShape 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ShapeType"
            values: {
                "UnknownType": 0,
                "RectangleType": 1,
                "CircleType": 2,
                "PathType": 3,
                "PolygonType": 4
            }
        }
        Property { name: "type"; type: "ShapeType"; isReadonly: true }
        Property { name: "isValid"; type: "bool"; isReadonly: true }
        Property { name: "isEmpty"; type: "bool"; isReadonly: true }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method { name: "boundingGeoRectangle"; type: "QGeoRectangle" }
        Method { name: "center"; type: "QGeoCoordinate" }
        Method {
            name: "extendShape"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method { name: "toString"; type: "string" }
    }
    Component {
        name: "QQuickGeoCoordinateAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: ["QtPositioning/CoordinateAnimation 5.3"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Direction"
            values: {
                "Shortest": 0,
                "West": 1,
                "East": 2
            }
        }
        Property { name: "from"; type: "QGeoCoordinate" }
        Property { name: "to"; type: "QGeoCoordinate" }
        Property { name: "direction"; type: "Direction" }
    }
}
