// qhboxplotmodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_5_8_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qhboxplotmodelmapper.h>
%End

    class QHBoxPlotModelMapper : QObject
    {
%TypeHeaderCode
#include <qhboxplotmodelmapper.h>
%End

    public:
        explicit QHBoxPlotModelMapper(QObject *parent /TransferThis/ = 0);
        QAbstractItemModel *model() const;
        void setModel(QAbstractItemModel *model /KeepReference/);
        QtCharts::QBoxPlotSeries *series() const;
        void setSeries(QtCharts::QBoxPlotSeries *series);
        int firstBoxSetRow() const;
        void setFirstBoxSetRow(int firstBoxSetRow);
        int lastBoxSetRow() const;
        void setLastBoxSetRow(int lastBoxSetRow);
        int firstColumn() const;
        void setFirstColumn(int firstColumn);
        int columnCount() const;
        void setColumnCount(int rowCount);

    signals:
        void seriesReplaced();
        void modelReplaced();
        void firstBoxSetRowChanged();
        void lastBoxSetRowChanged();
        void firstColumnChanged();
        void columnCountChanged();
    };
};

%End
