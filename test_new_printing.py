#!/usr/bin/env python3
"""
اختبار سريع لنظام الطباعة الجديد
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.quarter_invoice_printer import show_new_print_dialog

class TestPrintingWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نظام الطباعة الجديد")
        self.setGeometry(100, 100, 400, 300)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # زر اختبار الطباعة
        test_btn = QPushButton("اختبار طباعة الفاتورة رقم 1")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        test_btn.clicked.connect(self.test_printing)
        layout.addWidget(test_btn)
        
        # زر اختبار الفاتورة رقم 7 (الأحدث)
        test_btn2 = QPushButton("اختبار طباعة الفاتورة رقم 7")
        test_btn2.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        test_btn2.clicked.connect(lambda: self.test_printing(7))
        layout.addWidget(test_btn2)
        
        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def test_printing(self, invoice_id=1):
        """اختبار طباعة فاتورة"""
        try:
            show_new_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestPrintingWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
