#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لإضافة موردين تجريبيين إلى قاعدة البيانات
"""

import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from database.models import Supplier

def add_test_suppliers():
    """إضافة موردين تجريبيين لاختبار صفحة الموردين"""
    
    # إنشاء الاتصال بقاعدة البيانات
    db_path = os.path.join(os.getcwd(), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}')
    
    # قائمة الموردين التجريبيين
    test_suppliers = [
        {
            'name': 'شركة الأهرام للتوريدات',
            'phone': '***********',
            'address': 'القاهرة - مصر الجديدة',
            'balance': 5000.0
        },
        {
            'name': 'مؤسسة النيل التجارية',
            'phone': '***********',
            'address': 'الجيزة - المهندسين',
            'balance': -2500.0
        },
        {
            'name': 'شركة الدلتا للمواد الغذائية',
            'phone': '***********',
            'address': 'الإسكندرية - سيدي جابر',
            'balance': 0.0
        },
        {
            'name': 'مجموعة الصعيد للتجارة',
            'phone': '***********',
            'address': 'أسوان - شارع الكورنيش',
            'balance': 12000.0
        },
        {
            'name': 'شركة البحر الأحمر للاستيراد',
            'phone': '01666555444',
            'address': 'الغردقة - البحر الأحمر',
            'balance': -1500.0
        }
    ]
    
    try:
        with Session(engine) as session:
            # فحص الموردين الموجودين
            existing_suppliers = session.query(Supplier).all()
            print(f"عدد الموردين الموجودين: {len(existing_suppliers)}")
            
            # إضافة الموردين التجريبيين
            added_count = 0
            for supplier_data in test_suppliers:
                # التحقق من عدم وجود مورد بنفس الاسم
                existing = session.query(Supplier).filter(
                    Supplier.name == supplier_data['name']
                ).first()
                
                if not existing:
                    supplier = Supplier(
                        name=supplier_data['name'],
                        phone=supplier_data['phone'],
                        address=supplier_data['address'],
                        balance=supplier_data['balance']
                    )
                    session.add(supplier)
                    added_count += 1
                    print(f"تم إضافة المورد: {supplier_data['name']}")
                else:
                    print(f"المورد موجود بالفعل: {supplier_data['name']}")
            
            session.commit()
            print(f"\nتم إضافة {added_count} مورد جديد بنجاح!")
            
            # عرض جميع الموردين
            all_suppliers = session.query(Supplier).all()
            print(f"\nإجمالي الموردين في قاعدة البيانات: {len(all_suppliers)}")
            print("\nقائمة الموردين:")
            for supplier in all_suppliers:
                print(f"- الكود: {supplier.id}, الاسم: {supplier.name}, الرصيد: {supplier.balance}")
                
    except Exception as e:
        print(f"خطأ في إضافة الموردين: {e}")
        return False
    
    return True

def check_suppliers():
    """فحص الموردين الموجودين في قاعدة البيانات"""
    
    db_path = os.path.join(os.getcwd(), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}')
    
    try:
        with Session(engine) as session:
            suppliers = session.query(Supplier).all()
            print(f"عدد الموردين في قاعدة البيانات: {len(suppliers)}")
            
            if suppliers:
                print("\nقائمة الموردين:")
                for supplier in suppliers:
                    print(f"- الكود: {supplier.id}")
                    print(f"  الاسم: {supplier.name}")
                    print(f"  الهاتف: {supplier.phone or 'غير محدد'}")
                    print(f"  العنوان: {supplier.address or 'غير محدد'}")
                    print(f"  الرصيد: {supplier.balance}")
                    print("-" * 40)
            else:
                print("لا توجد موردين في قاعدة البيانات")
                
    except Exception as e:
        print(f"خطأ في فحص الموردين: {e}")

if __name__ == "__main__":
    print("=== اختبار صفحة الموردين ===")
    print("\n1. فحص الموردين الحاليين:")
    check_suppliers()
    
    print("\n2. إضافة موردين تجريبيين:")
    if add_test_suppliers():
        print("\n3. فحص الموردين بعد الإضافة:")
        check_suppliers()
    else:
        print("فشل في إضافة الموردين التجريبيين")
