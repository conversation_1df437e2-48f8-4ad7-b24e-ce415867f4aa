// qtexttospeech.sip generated by MetaSIP
//
// This file is part of the QtTextToSpeech Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_15_0 -)

class QTextToSpeech : QObject
{
%TypeHeaderCode
#include <qtexttospeech.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QTextToSpeech, &sipType_QTextToSpeech, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum State
    {
        Ready,
        Speaking,
        Paused,
        BackendError,
    };

    explicit QTextToSpeech(QObject *parent /TransferThis/ = 0);
    QTextToSpeech(const QString &engine, QObject *parent /TransferThis/ = 0);
    QTextToSpeech::State state() const;
    QVector<QLocale> availableLocales() const;
    QLocale locale() const;
    QVoice voice() const;
    QVector<QVoice> availableVoices() const;
    double rate() const;
    double pitch() const;
    double volume() const;
    static QStringList availableEngines();

public slots:
    void say(const QString &text);
    void stop();
    void pause();
    void resume();
    void setLocale(const QLocale &locale);
    void setRate(double rate);
    void setPitch(double pitch);
    void setVolume(double volume);
    void setVoice(const QVoice &voice);

signals:
    void stateChanged(QTextToSpeech::State state);
    void localeChanged(const QLocale &locale);
    void rateChanged(double rate);
    void pitchChanged(double pitch);
    void volumeChanged(double volume /Constrained/);
    void volumeChanged(int volume);
    void voiceChanged(const QVoice &voice);
};

%End
