// qquickwebengineprofile.sip generated by MetaSIP
//
// This file is part of the QtWebEngine Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_6_0 -)

class QQuickWebEngineProfile : public QObject
{
%TypeHeaderCode
#include <qquickwebengineprofile.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QQuickWebEngineProfile, &sipType_QQuickWebEngineProfile, -1, 1},
    #if QT_VERSION >= 0x050900
        {sipName_QQuickWebEngineScript, &sipType_QQuickWebEngineScript, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QQuickWebEngineProfile(QObject *parent /TransferThis/ = 0);
    virtual ~QQuickWebEngineProfile();

    enum HttpCacheType
    {
        MemoryHttpCache,
        DiskHttpCache,
%If (QtWebEngine_5_7_0 -)
        NoCache,
%End
    };

    enum PersistentCookiesPolicy
    {
        NoPersistentCookies,
        AllowPersistentCookies,
        ForcePersistentCookies,
    };

    QString storageName() const;
    void setStorageName(const QString &name);
    bool isOffTheRecord() const;
    void setOffTheRecord(bool offTheRecord);
    QString persistentStoragePath() const;
    void setPersistentStoragePath(const QString &path);
    QString cachePath() const;
    void setCachePath(const QString &path);
    QString httpUserAgent() const;
    void setHttpUserAgent(const QString &userAgent);
    QQuickWebEngineProfile::HttpCacheType httpCacheType() const;
    void setHttpCacheType(QQuickWebEngineProfile::HttpCacheType);
    QQuickWebEngineProfile::PersistentCookiesPolicy persistentCookiesPolicy() const;
    void setPersistentCookiesPolicy(QQuickWebEngineProfile::PersistentCookiesPolicy);
    int httpCacheMaximumSize() const;
    void setHttpCacheMaximumSize(int maxSize);
    QString httpAcceptLanguage() const;
    void setHttpAcceptLanguage(const QString &httpAcceptLanguage);
    QWebEngineCookieStore *cookieStore() const /Transfer/;
%If (QtWebEngine_5_13_0 -)
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
%End
    void setRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
    const QWebEngineUrlSchemeHandler *urlSchemeHandler(const QByteArray &) const;
    void installUrlSchemeHandler(const QByteArray &scheme, QWebEngineUrlSchemeHandler *);
    void removeUrlScheme(const QByteArray &scheme);
    void removeUrlSchemeHandler(QWebEngineUrlSchemeHandler *);
    void removeAllUrlSchemeHandlers();
%If (QtWebEngine_5_7_0 -)
    void clearHttpCache();
%End
    static QQuickWebEngineProfile *defaultProfile() /Transfer/;

signals:
    void storageNameChanged();
    void offTheRecordChanged();
    void persistentStoragePathChanged();
    void cachePathChanged();
    void httpUserAgentChanged();
    void httpCacheTypeChanged();
    void persistentCookiesPolicyChanged();
    void httpCacheMaximumSizeChanged();
    void httpAcceptLanguageChanged();

public:
%If (QtWebEngine_5_8_0 -)
    void setSpellCheckLanguages(const QStringList &languages);
%End
%If (QtWebEngine_5_8_0 -)
    QStringList spellCheckLanguages() const;
%End
%If (QtWebEngine_5_8_0 -)
    void setSpellCheckEnabled(bool enabled);
%End
%If (QtWebEngine_5_8_0 -)
    bool isSpellCheckEnabled() const;
%End

signals:
%If (QtWebEngine_5_8_0 -)
    void spellCheckLanguagesChanged();
%End
%If (QtWebEngine_5_8_0 -)
    void spellCheckEnabledChanged();
%End

public:
%If (QtWebEngine_5_13_0 -)
    void setUseForGlobalCertificateVerification(bool b);
%End
%If (QtWebEngine_5_13_0 -)
    bool isUsedForGlobalCertificateVerification() const;
%End
%If (QtWebEngine_5_13_0 -)
    QString downloadPath() const;
%End
%If (QtWebEngine_5_13_0 -)
    void setDownloadPath(const QString &path);
%End
%If (QtWebEngine_5_13_0 -)
%If (PyQt_SSL)
    QWebEngineClientCertificateStore *clientCertificateStore();
%End
%End

signals:
%If (QtWebEngine_5_13_0 -)
    void useForGlobalCertificateVerificationChanged();
%End
%If (QtWebEngine_5_13_0 -)
    void downloadPathChanged();
%End
%If (QtWebEngine_5_13_0 -)
    void presentNotification(QWebEngineNotification *notification);
%End
};

%End
