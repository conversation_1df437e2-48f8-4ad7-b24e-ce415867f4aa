import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "finalstate.h"
        name: "FinalState"
        defaultProperty: "children"
        prototype: "QFinalState"
        exports: ["QtQml.StateMachine/FinalState 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "children"; type: "QObject"; isList: true; isReadonly: true }
    }
    Component {
        file: "statemachineforeign.h"
        name: "QAbstractState"
        prototype: "QObject"
        exports: ["QtQml.StateMachine/QAbstractState 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "active"; type: "bool"; isReadonly: true }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "activeChanged"
            Parameter { name: "active"; type: "bool" }
        }
    }
    Component {
        name: "QAbstractTransition"
        prototype: "QObject"
        Enum {
            name: "TransitionType"
            values: ["ExternalTransition", "InternalTransition"]
        }
        Property { name: "sourceState"; type: "QState"; isReadonly: true; isPointer: true }
        Property { name: "targetState"; type: "QAbstractState"; isPointer: true }
        Property { name: "targetStates"; type: "QList<QAbstractState*>" }
        Property { name: "transitionType"; revision: 1; type: "TransitionType" }
        Signal { name: "triggered" }
    }
    Component { name: "QFinalState"; prototype: "QAbstractState" }
    Component {
        file: "statemachineforeign.h"
        name: "QHistoryState"
        prototype: "QAbstractState"
        exports: ["QtQml.StateMachine/HistoryState 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "HistoryType"
            values: ["ShallowHistory", "DeepHistory"]
        }
        Property { name: "defaultState"; type: "QAbstractState"; isPointer: true }
        Property { name: "defaultTransition"; type: "QAbstractTransition"; isPointer: true }
        Property { name: "historyType"; type: "HistoryType" }
    }
    Component {
        file: "statemachineforeign.h"
        name: "QSignalTransition"
        prototype: "QAbstractTransition"
        exports: [
            "QtQml.StateMachine/QSignalTransition 1.0",
            "QtQml.StateMachine/QSignalTransition 1.1"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Property { name: "senderObject"; type: "QObject"; isPointer: true }
        Property { name: "signal"; type: "QByteArray" }
    }
    Component {
        file: "statemachineforeign.h"
        name: "QState"
        prototype: "QAbstractState"
        exports: ["QtQml.StateMachine/QState 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ChildMode"
            values: ["ExclusiveStates", "ParallelStates"]
        }
        Enum {
            name: "RestorePolicy"
            values: ["DontRestoreProperties", "RestoreProperties"]
        }
        Property { name: "initialState"; type: "QAbstractState"; isPointer: true }
        Property { name: "errorState"; type: "QAbstractState"; isPointer: true }
        Property { name: "childMode"; type: "ChildMode" }
        Signal { name: "finished" }
        Signal { name: "propertiesAssigned" }
    }
    Component {
        name: "QStateMachine"
        prototype: "QState"
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "globalRestorePolicy"; type: "QState::RestorePolicy" }
        Property { name: "running"; type: "bool" }
        Property { name: "animated"; type: "bool" }
        Signal { name: "started" }
        Signal { name: "stopped" }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method { name: "_q_start" }
        Method { name: "_q_process" }
        Method { name: "_q_animationFinished" }
        Method {
            name: "_q_startDelayedEventTimer"
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_killDelayedEventTimer"
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
    }
    Component {
        file: "signaltransition.h"
        name: "SignalTransition"
        prototype: "QSignalTransition"
        exports: [
            "QtQml.StateMachine/SignalTransition 1.0",
            "QtQml.StateMachine/SignalTransition 1.1"
        ]
        exportMetaObjectRevisions: [0, 1]
        Property { name: "signal"; type: "QJSValue" }
        Property { name: "guard"; type: "QQmlScriptString" }
        Signal { name: "invokeYourself" }
        Signal { name: "qmlSignalChanged" }
        Method { name: "invoke" }
    }
    Component {
        file: "state.h"
        name: "State"
        defaultProperty: "children"
        prototype: "QState"
        exports: ["QtQml.StateMachine/State 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "children"; type: "QObject"; isList: true; isReadonly: true }
    }
    Component {
        file: "statemachine.h"
        name: "StateMachine"
        defaultProperty: "children"
        prototype: "QStateMachine"
        exports: ["QtQml.StateMachine/StateMachine 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "children"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "running"; type: "bool" }
        Signal { name: "qmlRunningChanged" }
        Method { name: "checkChildMode" }
    }
    Component {
        file: "timeouttransition.h"
        name: "TimeoutTransition"
        prototype: "QSignalTransition"
        exports: [
            "QtQml.StateMachine/TimeoutTransition 1.0",
            "QtQml.StateMachine/TimeoutTransition 1.1"
        ]
        exportMetaObjectRevisions: [0, 1]
        Property { name: "timeout"; type: "int" }
    }
}
