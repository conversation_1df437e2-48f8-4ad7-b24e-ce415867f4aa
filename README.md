# نظام المحاسبة العصري

نظام محاسبة متكامل مبني بلغة Python وواجهة PyQt5.

## المميزات

- إدارة المبيعات والمشتريات
- إدارة المخزون
- إدارة العملاء والموردين
- إدارة المستخدمين والصلاحيات
- نظام تفعيل متكامل
- واجهة مستخدم عصرية وسهلة الاستخدام
- تقارير متعددة
- طباعة الفواتير
- نظام مرتجعات المبيعات والمشتريات

## متطلبات التشغيل

- نظام تشغيل Windows 10 أو أحدث
- مساحة تخزين 500 ميجابايت على الأقل
- ذاكرة عشوائية 4 جيجابايت على الأقل

## طريقة التثبيت

1. قم بتحميل الملف التنفيذي من مجلد `dist`
2. قم بتشغيل الملف التنفيذي `نظام_المحاسبة_العصري.exe`
3. اتبع خطوات الإعداد الأولي للبرنامج

## بيانات الدخول الافتراضية

- اسم المستخدم: `sicoo`
- كلمة المرور: `sicoo123`

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشكلة، يرجى التواصل مع فريق الدعم الفني.

## الترخيص

جميع الحقوق محفوظة © 2024 