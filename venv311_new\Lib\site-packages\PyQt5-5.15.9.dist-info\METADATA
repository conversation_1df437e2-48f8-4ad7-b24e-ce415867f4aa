Metadata-Version: 2.1
Name: PyQt5
Version: 5.15.9
Requires-Python: >=3.7
Summary: Python bindings for the Qt cross platform application toolkit
Home-Page: https://www.riverbankcomputing.com/software/pyqt/
Author: Riverbank Computing Limited
Author-Email: <EMAIL>
License: GPL v3
Requires-Dist: PyQt5-sip (>=12.11, <13)
Requires-Dist: PyQt5-Qt5 (>=5.15.2)

PyQt5 - Comprehensive Python Bindings for Qt v5
===============================================

Qt is set of cross-platform C++ libraries that implement high-level APIs for
accessing many aspects of modern desktop and mobile systems.  These include
location and positioning services, multimedia, NFC and Bluetooth connectivity,
a Chromium based web browser, as well as traditional UI development.

PyQt5 is a comprehensive set of Python bindings for Qt v5.  It is implemented
as more than 35 extension modules and enables Python to be used as an
alternative application development language to C++ on all supported platforms
including iOS and Android.

PyQt5 may also be embedded in C++ based applications to allow users of those
applications to configure or enhance the functionality of those applications.


Author
------

PyQt5 is copyright (c) Riverbank Computing Limited.  Its homepage is
https://www.riverbankcomputing.com/software/pyqt/.

Support may be obtained from the PyQt mailing list at
https://www.riverbankcomputing.com/mailman/listinfo/pyqt/.


License
-------

PyQt5 is released under the GPL v3 license and under a commercial license that
allows for the development of proprietary applications.


Documentation
-------------

The documentation for the latest release can be found
`here <https://www.riverbankcomputing.com/static/Docs/PyQt5/>`__.


Installation
------------

The GPL version of PyQt5 can be installed from PyPI::

    pip install PyQt5

``pip`` will also build and install the bindings from the sdist package but
Qt's ``qmake`` tool must be on ``PATH``.

The ``sip-install`` tool will also install the bindings from the sdist package
but will allow you to configure many aspects of the installation.
