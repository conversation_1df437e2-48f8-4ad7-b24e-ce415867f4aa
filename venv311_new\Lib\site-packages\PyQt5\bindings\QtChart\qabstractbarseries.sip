// qabstractbarseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qabstractbarseries.h>
%End

    class QAbstractBarSeries : public QtCharts::QAbstractSeries /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qabstractbarseries.h>
%End

    public:
        virtual ~QAbstractBarSeries();
        void setBarWidth(qreal width);
        qreal barWidth() const;
        bool append(QtCharts::QBarSet *set /Transfer/);
        bool remove(QtCharts::QBarSet *set /TransferBack/);
        bool append(QList<QtCharts::QBarSet *> sets /Transfer/);
        bool insert(int index, QtCharts::QBarSet *set /Transfer/);
        int count() const /__len__/;
        QList<QtCharts::QBarSet *> barSets() const;
        void clear();
        void setLabelsVisible(bool visible = true);
        bool isLabelsVisible() const;
%If (QtChart_1_1_0 -)
        bool take(QtCharts::QBarSet *set);
%End

    protected:
%If (- QtChart_1_1_0)
        explicit QAbstractBarSeries(QObject *parent /TransferThis/ = 0);
%End

    signals:
        void clicked(int index, QtCharts::QBarSet *barset /ScopesStripped=1/);
%If (- QtChart_2_0_0)
        void hovered(bool status, QtCharts::QBarSet *barset /ScopesStripped=1/);
%End
%If (QtChart_1_3_1 -)
        void hovered(bool status, int index, QtCharts::QBarSet *barset /ScopesStripped=1/);
%End
        void countChanged();
        void labelsVisibleChanged();
        void barsetsAdded(QList<QtCharts::QBarSet *> sets /ScopesStripped=1/);
        void barsetsRemoved(QList<QtCharts::QBarSet *> sets /ScopesStripped=1/);

    public:
%If (QtChart_1_4_0 -)

        enum LabelsPosition
        {
            LabelsCenter,
            LabelsInsideEnd,
            LabelsInsideBase,
            LabelsOutsideEnd,
        };

%End
%If (QtChart_1_4_0 -)
        void setLabelsFormat(const QString &format);
%End
%If (QtChart_1_4_0 -)
        QString labelsFormat() const;
%End
%If (QtChart_1_4_0 -)
        void setLabelsPosition(QtCharts::QAbstractBarSeries::LabelsPosition position);
%End
%If (QtChart_1_4_0 -)
        QtCharts::QAbstractBarSeries::LabelsPosition labelsPosition() const;
%End

    signals:
%If (QtChart_1_4_0 -)
        void labelsFormatChanged(const QString &format);
%End
%If (QtChart_1_4_0 -)
        void labelsPositionChanged(QtCharts::QAbstractBarSeries::LabelsPosition position /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void pressed(int index, QtCharts::QBarSet *barset /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void released(int index, QtCharts::QBarSet *barset /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(int index, QtCharts::QBarSet *barset /ScopesStripped=1/);
%End

    public:
%If (QtChart_2_1_0 -)
        void setLabelsAngle(qreal angle);
%End
%If (QtChart_2_1_0 -)
        qreal labelsAngle() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void labelsAngleChanged(qreal angle);
%End

    public:
%If (QtChart_5_11_0 -)
        void setLabelsPrecision(int precision);
%End
%If (QtChart_5_11_0 -)
        int labelsPrecision() const;
%End

    signals:
%If (QtChart_5_11_0 -)
        void labelsPrecisionChanged(int precision);
%End
    };
};
