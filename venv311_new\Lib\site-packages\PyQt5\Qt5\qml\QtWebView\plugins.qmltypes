import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtWebView 1.14'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QQuickViewController"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Method {
            name: "onWindowChanged"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Method { name: "onVisibleChanged" }
    }
    Component {
        name: "QQuickWebView"
        defaultProperty: "data"
        prototype: "QQuickViewController"
        exports: [
            "QtWebView/WebView 1.0",
            "QtWebView/WebView 1.1",
            "QtWebView/WebView 1.14"
        ]
        exportMetaObjectRevisions: [0, 1, 14]
        Enum {
            name: "LoadStatus"
            values: {
                "LoadStartedStatus": 0,
                "LoadStoppedStatus": 1,
                "LoadSucceededStatus": 2,
                "LoadFailedStatus": 3
            }
        }
        Property { name: "httpUserAgent"; revision: 14; type: "string" }
        Property { name: "url"; type: "QUrl" }
        Property { name: "loading"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "loadProgress"; type: "int"; isReadonly: true }
        Property { name: "title"; type: "string"; isReadonly: true }
        Property { name: "canGoBack"; type: "bool"; isReadonly: true }
        Property { name: "canGoForward"; type: "bool"; isReadonly: true }
        Signal {
            name: "loadingChanged"
            revision: 1
            Parameter { name: "loadRequest"; type: "QQuickWebViewLoadRequest"; isPointer: true }
        }
        Signal { name: "httpUserAgentChanged"; revision: 14 }
        Method { name: "goBack" }
        Method { name: "goForward" }
        Method { name: "reload" }
        Method { name: "stop" }
        Method {
            name: "loadHtml"
            revision: 1
            Parameter { name: "html"; type: "string" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method {
            name: "loadHtml"
            revision: 1
            Parameter { name: "html"; type: "string" }
        }
        Method {
            name: "runJavaScript"
            revision: 1
            Parameter { name: "script"; type: "string" }
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            revision: 1
            Parameter { name: "script"; type: "string" }
        }
    }
    Component {
        name: "QQuickWebViewLoadRequest"
        prototype: "QObject"
        exports: ["QtWebView/WebViewLoadRequest 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "status"; type: "QQuickWebView::LoadStatus"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
    }
}
