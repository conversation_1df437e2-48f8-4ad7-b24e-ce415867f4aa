#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة مرتجع المبيعات للصفحة الرئيسية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("🏠 اختبار إضافة مرتجع المبيعات للصفحة الرئيسية")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("✅ التحديثات الجديدة:")
    print("")
    print("   🔄 إضافة بطاقة مرتجع المبيعات:")
    print("     • العنوان: 🔄 مرتجع المبيعات")
    print("     • الوصف: إرجاع منتجات من فواتير المبيعات")
    print("     • اللون: أحمر (#E74C3C)")
    print("     • الموقع: الصف الأول، العمود الثالث")
    print("")
    print("   📋 ترتيب البطاقات الجديد:")
    print("     الصف الأول:")
    print("     1️⃣ 📄 فاتورة جديدة (أخضر)")
    print("     2️⃣ 📋 عرض الفواتير (أزرق)")
    print("     3️⃣ 🔄 مرتجع المبيعات (أحمر) ← جديد!")
    print("")
    print("     الصف الثاني:")
    print("     4️⃣ 👤 إضافة عميل (بنفسجي)")
    print("     5️⃣ 📦 إضافة منتج (برتقالي)")
    print("     6️⃣ 🏢 إعدادات الشركة (بنفسجي غامق)")
    print("")
    print("     الصف الثالث:")
    print("     7️⃣ 📊 التقارير المالية (برتقالي ذهبي)")
    print("     8️⃣ 💾 النسخ الاحتياطي (رمادي)")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 تحقق من الصفحة الرئيسية:")
        print("")
        print("   ✅ يجب أن ترى بطاقة جديدة:")
        print("     • العنوان: 🔄 مرتجع المبيعات")
        print("     • اللون: أحمر جميل")
        print("     • الموقع: في الصف الأول")
        print("")
        print("   🖱️ اختبار الوظيفة:")
        print("     1️⃣ اضغط على بطاقة 'مرتجع المبيعات'")
        print("     2️⃣ يجب أن تفتح صفحة مرتجع المبيعات")
        print("     3️⃣ تحقق من وجود نظام الخصم النسبي")
        print("")
        print("   🎨 التصميم:")
        print("     • البطاقة بلون أحمر مميز")
        print("     • أيقونة 🔄 واضحة")
        print("     • نص وصفي مفيد")
        print("     • تأثير hover جميل")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 ملخص التحديث:")
    print("")
    print("   ✅ ما تم إضافته:")
    print("     • بطاقة مرتجع المبيعات في الصفحة الرئيسية")
    print("     • ربط مباشر بصفحة مرتجع المبيعات")
    print("     • تصميم متسق مع باقي البطاقات")
    print("     • لون مميز (أحمر) للتمييز")
    print("")
    print("   🎯 الفوائد:")
    print("     • وصول سريع لمرتجع المبيعات")
    print("     • تحسين تجربة المستخدم")
    print("     • تنظيم أفضل للوظائف")
    print("     • سهولة في الاستخدام")
    print("")
    print("🎉 الصفحة الرئيسية محدثة بنجاح!")

if __name__ == "__main__":
    main()
