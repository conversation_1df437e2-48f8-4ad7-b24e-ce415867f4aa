// qprinterinfo.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Printer)

class QPrinterInfo
{
%TypeHeaderCode
#include <qprinterinfo.h>
%End

public:
    QPrinterInfo();
    QPrinterInfo(const QPrinterInfo &src);
    explicit QPrinterInfo(const QPrinter &printer);
    ~QPrinterInfo();
    QString printerName() const;
    bool isNull() const;
    bool isDefault() const;
    QList<QPagedPaintDevice::PageSize> supportedPaperSizes() const;
%If (Qt_5_1_0 -)
    QList<QPair<QString, QSizeF>> supportedSizesWithNames() const;
%End
    static QList<QPrinterInfo> availablePrinters();
    static QPrinterInfo defaultPrinter();
    QString description() const;
    QString location() const;
    QString makeAndModel() const;
    static QPrinterInfo printerInfo(const QString &printerName);
%If (Qt_5_3_0 -)
    bool isRemote() const;
%End
%If (Qt_5_3_0 -)
    QPrinter::PrinterState state() const;
%End
%If (Qt_5_3_0 -)
    QList<QPageSize> supportedPageSizes() const;
%End
%If (Qt_5_3_0 -)
    QPageSize defaultPageSize() const;
%End
%If (Qt_5_3_0 -)
    bool supportsCustomPageSizes() const;
%End
%If (Qt_5_3_0 -)
    QPageSize minimumPhysicalPageSize() const;
%End
%If (Qt_5_3_0 -)
    QPageSize maximumPhysicalPageSize() const;
%End
%If (Qt_5_3_0 -)
    QList<int> supportedResolutions() const;
%End
%If (Qt_5_3_0 -)
    static QStringList availablePrinterNames();
%End
%If (Qt_5_3_0 -)
    static QString defaultPrinterName();
%End
%If (Qt_5_4_0 -)
    QPrinter::DuplexMode defaultDuplexMode() const;
%End
%If (Qt_5_4_0 -)
    QList<QPrinter::DuplexMode> supportedDuplexModes() const;
%End
%If (Qt_5_13_0 -)
    QPrinter::ColorMode defaultColorMode() const;
%End
%If (Qt_5_13_0 -)
    QList<QPrinter::ColorMode> supportedColorModes() const;
%End
};

%End
