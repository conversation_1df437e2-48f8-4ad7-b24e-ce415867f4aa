// qglobal.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qglobal.h>
%End

// PyQt version information.
int PYQT_VERSION;
const char *PYQT_VERSION_STR;

%ModuleCode
static int PYQT_VERSION = 0x050f09;
static const char *PYQT_VERSION_STR = "5.15.9";
%End
const int QT_VERSION;
const char *QT_VERSION_STR;
typedef signed char qint8 /PyInt/;
typedef unsigned char quint8 /PyInt/;
typedef short qint16;
typedef unsigned short quint16;
typedef int qint32;
typedef unsigned int quint32;
typedef long long qint64;
typedef unsigned long long quint64;
typedef qint64 qlonglong;
typedef quint64 qulonglong;
%If (PyQt_qreal_double)
typedef double qreal;
%End
%If (!PyQt_qreal_double)
typedef float qreal;
%End
typedef unsigned char uchar;
typedef unsigned short ushort;
typedef unsigned int uint;
typedef unsigned long ulong;
double qAbs(const double &t);
int qRound(qreal d);
qint64 qRound64(qreal d);
const char *qVersion();
bool qSharedBuild();
// Template definition for QFlags.
template<ENUM>
class QFlags /NoDefaultCtors, PyQtFlagsEnums="ENUM", TypeHintIn="Union[QFlags, ENUM]"/
{
public:
    // QFlags is supposed to be a more type safe version of an int (even though
    // Qt has cases where it expects multiple flag types to be or-ed together).
    // Because of the C++ int() operator and because type(ENUM) is a sub-type
    // of int, most of this is lost.  Therefore we only implement logical
    // operators that take int arguments.
    QFlags();
    QFlags(int f /TypeHint="QFlags"/);

    // This will never be called because the above ctor will be invoked first.
    // However it's required for sip to generate assignment helpers.
    QFlags(const QFlags &) /NoTypeHint/;

    operator int() const;

    // This is required for Python v3.8 and later.
    int __index__() const;
%MethodCode
        sipRes = sipCpp->operator QFlags::Int();
%End

    QFlags operator~() const;

    QFlags operator&(int f /TypeHint="QFlags"/) const;
    QFlags &operator&=(int f /TypeHint="QFlags"/);

    QFlags operator|(int f /TypeHint="QFlags"/) const;
    QFlags &operator|=(int f /TypeHint="QFlags"/);
%MethodCode
        *sipCpp = QFlags(*sipCpp | a0);
%End

    QFlags operator^(int f /TypeHint="QFlags"/) const;
    QFlags &operator^=(int f /TypeHint="QFlags"/);
%MethodCode
        *sipCpp = QFlags(*sipCpp ^ a0);
%End

    // These are necessary to prevent Python comparing object IDs.
    bool operator==(const QFlags &f) const;
%MethodCode
        sipRes = (sipCpp->operator QFlags::Int() == a0->operator QFlags::Int());
%End

    bool operator!=(const QFlags &f) const;
%MethodCode
        sipRes = (sipCpp->operator QFlags::Int() != a0->operator QFlags::Int());
%End

    int __bool__() const;
%MethodCode
        sipRes = (sipCpp->operator QFlags::Int() != 0);
%End

    long __hash__() const;
%MethodCode
        sipRes = sipCpp->operator QFlags::Int();
%End


%ConvertToTypeCode
// Allow an instance of the base enum whenever a QFlags is expected.

if (sipIsErr == NULL)
    return (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_ENUM)) ||
            sipCanConvertToType(sipPy, sipType_QFlags, SIP_NO_CONVERTORS));

if (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_ENUM)))
{
    *sipCppPtr = new QFlags(int(SIPLong_AsLong(sipPy)));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QFlags *>(sipConvertToType(sipPy, sipType_QFlags, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End
};
// Hook's into Qt's resource system.
%ModuleCode
QT_BEGIN_NAMESPACE
extern bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
extern bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
QT_END_NAMESPACE
%End

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qFuzzyCompare(double p1, double p2);
bool qIsNull(double d);
void qsrand(uint seed);
int qrand();
typedef void *QFunctionPointer;
// Mapped type for qintptr.
// Map qintptr onto sip.voidptr.  This means either an address (on Windows) or
// an integer file descriptor (on everything else) can be used.
%MappedType qintptr /TypeHint="PyQt5.sip.voidptr"/
{
%TypeHeaderCode
#include <QtGlobal>
%End

%ConvertToTypeCode
    qintptr ptr = (qintptr)sipConvertToVoidPtr(sipPy);

    if (!sipIsErr)
        return !PyErr_Occurred();

    // Mapped types deal with pointers, so create one on the heap.
    qintptr *heap = new qintptr;
    *heap = ptr;

    *sipCppPtr = heap;

    // Make sure the pointer doesn't leak.
    return SIP_TEMPORARY;
%End

%ConvertFromTypeCode
    return sipConvertFromVoidPtr((void *)*sipCpp);
%End
};
// Mapped type for quintptr.
// Map quintptr onto sip.voidptr.  This means either an address (on Windows) or
// an integer file descriptor (on everything else) can be used.
%MappedType quintptr /TypeHint="PyQt5.sip.voidptr"/
{
%TypeHeaderCode
#include <QtGlobal>
%End

%ConvertToTypeCode
    quintptr ptr = (quintptr)sipConvertToVoidPtr(sipPy);

    if (!sipIsErr)
        return !PyErr_Occurred();

    // Mapped types deal with pointers, so create one on the heap.
    quintptr *heap = new quintptr;
    *heap = ptr;

    *sipCppPtr = heap;

    // Make sure the pointer doesn't leak.
    return SIP_TEMPORARY;
%End

%ConvertFromTypeCode
    return sipConvertFromVoidPtr((void *)*sipCpp);
%End
};
// Implementations of pyqt[Set]PickleProtocol().
void pyqtSetPickleProtocol(SIP_PYOBJECT /TypeHint="Optional[int]"/);
%MethodCode
    Py_XDECREF(qpycore_pickle_protocol);
    qpycore_pickle_protocol = a0;
    Py_INCREF(qpycore_pickle_protocol);
%End

SIP_PYOBJECT pyqtPickleProtocol() /TypeHint="Optional[int]"/;
%MethodCode
    sipRes = qpycore_pickle_protocol;
    if (!sipRes)
        sipRes = Py_None;
    
    Py_INCREF(sipRes);
%End
%If (Qt_5_10_0 -)
QString qEnvironmentVariable(const char *varName);
%End
%If (Qt_5_10_0 -)
QString qEnvironmentVariable(const char *varName, const QString &defaultValue);
%End
