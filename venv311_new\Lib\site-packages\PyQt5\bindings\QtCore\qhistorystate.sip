// qhistorystate.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHistoryState : QAbstractState
{
%TypeHeaderCode
#include <qhistorystate.h>
%End

public:
    enum HistoryType
    {
        ShallowHistory,
        DeepHistory,
    };

    QHistoryState(QState *parent /TransferThis/ = 0);
    QHistoryState(QHistoryState::HistoryType type, QState *parent /TransferThis/ = 0);
    virtual ~QHistoryState();
    QAbstractState *defaultState() const;
    void setDefaultState(QAbstractState *state /KeepReference=0/);
    QHistoryState::HistoryType historyType() const;
    void setHistoryType(QHistoryState::HistoryType type);

protected:
    virtual void onEntry(QEvent *event);
    virtual void onExit(QEvent *event);
    virtual bool event(QEvent *e);

signals:
%If (Qt_5_4_0 -)
    void defaultStateChanged();
%End
%If (Qt_5_4_0 -)
    void historyTypeChanged();
%End

public:
%If (Qt_5_6_0 -)
    QAbstractTransition *defaultTransition() const;
%End
%If (Qt_5_6_0 -)
    void setDefaultTransition(QAbstractTransition *transition /KeepReference=1/);
%End

signals:
%If (Qt_5_6_0 -)
    void defaultTransitionChanged();
%End
};
