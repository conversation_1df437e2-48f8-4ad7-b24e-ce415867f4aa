// qwebengineview.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineView : public QWidget
{
%TypeHeaderCode
#include <qwebengineview.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if QT_VERSION >= 0x050500
        {sipName_QWebEngineProfile, &sipType_QWebEngineProfile, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
        {sipName_QWebEnginePage, &sipType_QWebEnginePage, -1, 2},
        {sipName_QWebEngineView, &sipType_QWebEngineView, -1, 3},
    #if QT_VERSION >= 0x050500
        {sipName_QWebEngineDownloadItem, &sipType_QWebEngineDownloadItem, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    explicit QWebEngineView(QWidget *parent /TransferThis/ = 0);
    virtual ~QWebEngineView();
    QWebEnginePage *page() const;
    void setPage(QWebEnginePage *page);
    void load(const QUrl &url);
%If (QtWebEngine_5_9_0 -)
    void load(const QWebEngineHttpRequest &request);
%End
    void setHtml(const QString &html, const QUrl &baseUrl = QUrl());
    void setContent(const QByteArray &data, const QString &mimeType = QString(), const QUrl &baseUrl = QUrl());
    QWebEngineHistory *history() const;
    QString title() const;
    void setUrl(const QUrl &url);
    QUrl url() const;
    QUrl iconUrl() const;
    bool hasSelection() const;
    QString selectedText() const;
    QAction *pageAction(QWebEnginePage::WebAction action) const;
    void triggerPageAction(QWebEnginePage::WebAction action, bool checked = false);
    qreal zoomFactor() const;
    void setZoomFactor(qreal factor);
    void findText(const QString &subString, QWebEnginePage::FindFlags options = QWebEnginePage::FindFlags(), SIP_PYCALLABLE resultCallback /AllowNone,TypeHint="Callable[[bool], None]"/ = 0);
%MethodCode
        // Make sure any callable doesn't get garbage collected until it is invoked.
        Py_XINCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->findText(*a0, *a1, [a2](bool arg) {
            if (a2)
            {
                SIP_BLOCK_THREADS
                
                PyObject *res;
        
                res = PyObject_CallFunctionObjArgs(a2, (arg ? Py_True : Py_False), NULL);
        
                Py_DECREF(a2);
        
                if (!res)
                    pyqt5_qtwebenginewidgets_err_print();
                else
                    Py_DECREF(res);
                
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

    virtual QSize sizeHint() const;
    QWebEngineSettings *settings() const;

public slots:
    void stop();
    void back();
    void forward();
    void reload();

signals:
    void loadStarted();
    void loadProgress(int progress);
    void loadFinished(bool);
    void titleChanged(const QString &title);
    void selectionChanged();
    void urlChanged(const QUrl &);
    void iconUrlChanged(const QUrl &);
%If (QtWebEngine_5_6_0 -)
    void renderProcessTerminated(QWebEnginePage::RenderProcessTerminationStatus terminationStatus, int exitCode);
%End

protected:
    virtual QWebEngineView *createWindow(QWebEnginePage::WebWindowType type);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual bool event(QEvent *);
%If (QtWebEngine_5_6_0 -)
    virtual void showEvent(QShowEvent *);
%End
%If (QtWebEngine_5_6_0 -)
    virtual void hideEvent(QHideEvent *);
%End

public:
%If (QtWebEngine_5_7_0 -)
    QIcon icon() const;
%End

signals:
%If (QtWebEngine_5_7_0 -)
    void iconChanged(const QIcon &);
%End

protected:
%If (QtWebEngine_5_7_0 -)
    virtual void dragEnterEvent(QDragEnterEvent *e);
%End
%If (QtWebEngine_5_7_0 -)
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
%End
%If (QtWebEngine_5_7_0 -)
    virtual void dragMoveEvent(QDragMoveEvent *e);
%End
%If (QtWebEngine_5_7_0 -)
    virtual void dropEvent(QDropEvent *e);
%End
%If (QtWebEngine_5_14_0 -)
    virtual void closeEvent(QCloseEvent *);
%End
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt5_qtwebenginewidgets_err_print_t)();
extern pyqt5_qtwebenginewidgets_err_print_t pyqt5_qtwebenginewidgets_err_print;
%End

%ModuleCode
// Imports from QtCore.
pyqt5_qtwebenginewidgets_err_print_t pyqt5_qtwebenginewidgets_err_print;

// Needed by the %PreInitialisationCode.
#include <QCoreApplication>
%End

%PreInitialisationCode
// QtWebEngineWidgets uses Q_COREAPP_STARTUP_FUNCTION so, in order to make sure
// things work (particularly on Windows) when we dynamically load the code, we
// check things have been done in the right order.
if (QCoreApplication::instance())
{
#if QT_VERSION >= 0x050400
    if (!QCoreApplication::testAttribute(Qt::AA_ShareOpenGLContexts))
    {
        PyErr_SetString(PyExc_ImportError,
                "QtWebEngineWidgets must be imported or Qt.AA_ShareOpenGLContexts must be set before a QCoreApplication instance is created");
        SIP_MODULE_RETURN(NULL);
    }
#else
    PyErr_SetString(PyExc_ImportError,
            "QtWebEngineWidgets must be imported before a QCoreApplication instance is created");
    SIP_MODULE_RETURN(NULL);
#endif
}
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qtwebenginewidgets_err_print = (pyqt5_qtwebenginewidgets_err_print_t)sipImportSymbol("pyqt5_err_print");
Q_ASSERT(pyqt5_qtwebenginewidgets_err_print);
%End
