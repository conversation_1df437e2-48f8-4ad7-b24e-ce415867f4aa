// qwebenginescriptcollection.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_5_0 -)

class QWebEngineScriptCollection
{
%TypeHeaderCode
#include <qwebenginescriptcollection.h>
%End

public:
    ~QWebEngineScriptCollection();
    bool isEmpty() const;
    int count() const /__len__/;
    bool contains(const QWebEngineScript &value) const;
    QWebEngineScript findScript(const QString &name) const;
    QList<QWebEngineScript> findScripts(const QString &name) const;
    void insert(const QWebEngineScript &);
    void insert(const QList<QWebEngineScript> &list);
    bool remove(const QWebEngineScript &);
    void clear();
    QList<QWebEngineScript> toList() const;

private:
    QWebEngineScriptCollection(const QWebEngineScriptCollection &);
};

%End
