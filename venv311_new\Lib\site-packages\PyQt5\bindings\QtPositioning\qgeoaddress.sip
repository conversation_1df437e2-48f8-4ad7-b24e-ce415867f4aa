// qgeoaddress.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoAddress
{
%TypeHeaderCode
#include <qgeoaddress.h>
%End

public:
    QGeoAddress();
    QGeoAddress(const QGeoAddress &other);
    ~QGeoAddress();
    bool operator==(const QGeoAddress &other) const;
    bool operator!=(const QGeoAddress &other) const;
    QString text() const;
    void setText(const QString &text);
    QString country() const;
    void setCountry(const QString &country);
    QString countryCode() const;
    void setCountryCode(const QString &countryCode);
    QString state() const;
    void setState(const QString &state);
    QString county() const;
    void setCounty(const QString &county);
    QString city() const;
    void setCity(const QString &city);
    QString district() const;
    void setDistrict(const QString &district);
    QString postalCode() const;
    void setPostalCode(const QString &postalCode);
    QString street() const;
    void setStreet(const QString &street);
    bool isEmpty() const;
    void clear();
    bool isTextGenerated() const;
};

%End
