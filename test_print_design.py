#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تصميم طباعة الفواتير
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from database.database import get_engine
from utils.advanced_invoice_printer import show_advanced_print_dialog

def main():
    print("🧾 اختبار تصميم طباعة الفواتير...")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    engine = get_engine()
    
    try:
        print("🔍 البحث عن آخر فاتورة...")
        
        # البحث عن آخر فاتورة
        from sqlalchemy.orm import Session
        from database.models import Transaction, TransactionType
        
        with Session(engine) as session:
            last_invoice = session.query(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).order_by(Transaction.id.desc()).first()
            
            if last_invoice:
                print(f"✅ تم العثور على الفاتورة رقم {last_invoice.id}")
                print("🖨️ فتح نافذة الطباعة...")
                
                # فتح نافذة الطباعة
                show_advanced_print_dialog(engine, last_invoice.id, None)
                
                print("✅ تم فتح نافذة الطباعة!")
                print("")
                print("🎯 تحقق من التصميم:")
                print("   ✅ يجب أن ترى إطارات سوداء")
                print("   ✅ تصميم منظم ومرتب")
                print("   ✅ معلومات الشركة واللوجو")
                print("   ✅ تفاصيل الفاتورة كاملة")
                print("")
                print("📋 اختبر كلا النوعين:")
                print("   🖨️ A4: للطباعة العادية")
                print("   🧾 رول: للطباعة على ورق صغير")
                
                app.exec_()
                
            else:
                print("❌ لا توجد فواتير في قاعدة البيانات")
                print("💡 قم بإنشاء فاتورة أولاً من صفحة المبيعات")
                
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")

if __name__ == "__main__":
    main()
