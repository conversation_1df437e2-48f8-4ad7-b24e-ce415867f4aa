// qtreewidgetitemiterator.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTreeWidgetItemIterator
{
%TypeHeaderCode
#include <qtreewidgetitemiterator.h>
%End

public:
    enum IteratorFlag
    {
        All,
        Hidden,
        NotHidden,
        Selected,
        Unselected,
        Selectable,
        NotSelectable,
        DragEnabled,
        DragDisabled,
        DropEnabled,
        DropDisabled,
        HasChildren,
        NoChildren,
        Checked,
        NotChecked,
        Enabled,
        Disabled,
        Editable,
        NotEditable,
        UserFlag,
    };

    typedef QFlags<QTreeWidgetItemIterator::IteratorFlag> IteratorFlags;
    QTreeWidgetItemIterator(const QTreeWidgetItemIterator &it);
    QTreeWidgetItemIterator(QTreeWidget *widget, QFlags<QTreeWidgetItemIterator::IteratorFlag> flags = All);
    QTreeWidgetItemIterator(QTreeWidgetItem *item, QFlags<QTreeWidgetItemIterator::IteratorFlag> flags = All);
    ~QTreeWidgetItemIterator();
    QTreeWidgetItem *value() const;
%MethodCode
        // SIP doesn't support operator* so this is a thin wrapper around it.
        sipRes = sipCpp->operator*();
%End

    QTreeWidgetItemIterator &operator+=(int n);
    QTreeWidgetItemIterator &operator-=(int n);
};

QFlags<QTreeWidgetItemIterator::IteratorFlag> operator|(QTreeWidgetItemIterator::IteratorFlag f1, QFlags<QTreeWidgetItemIterator::IteratorFlag> f2);
