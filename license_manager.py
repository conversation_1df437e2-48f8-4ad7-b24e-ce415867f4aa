#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التراخيص والأكواد
License Management System
"""

import os
import json
import hashlib
import platform
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import base64

class LicenseManager:
    def __init__(self):
        self.license_file = "license.dat"
        self.key = self._generate_machine_key()
        self.cipher = Fernet(self.key)
        
    def _generate_machine_key(self):
        """توليد مفتاح تشفير مرتبط بالجهاز"""
        # الحصول على معرف فريد للجهاز
        machine_id = platform.node() + platform.machine() + platform.processor()
        # تشفير المعرف
        key_material = hashlib.sha256(machine_id.encode()).digest()
        # تحويل إلى مفتاح Fernet صالح
        key = base64.urlsafe_b64encode(key_material)
        return key
    
    def get_machine_id(self):
        """الحصول على رقم الجهاز الفريد"""
        machine_info = f"{platform.node()}-{platform.machine()}-{uuid.getnode()}"
        machine_hash = hashlib.md5(machine_info.encode()).hexdigest()
        return machine_hash[:12].upper()
    
    def get_customer_code(self):
        """الحصول على كود العميل (يمكن تخصيصه لاحقاً)"""
        # يمكن ربطه بمعلومات العميل أو توليده عشوائياً
        machine_id = self.get_machine_id()
        customer_hash = hashlib.sha256(f"CUSTOMER_{machine_id}".encode()).hexdigest()
        return customer_hash[:9].upper()
    
    def create_initial_license(self, days=365):
        """إنشاء ترخيص أولي (للتجربة أو العميل الجديد)"""
        expiry_date = datetime.now() + timedelta(days=days)
        license_data = {
            "customer_code": self.get_customer_code(),
            "machine_id": self.get_machine_id(),
            "expiry_date": expiry_date.isoformat(),
            "license_type": "INITIAL",
            "created_date": datetime.now().isoformat()
        }
        
        self._save_license(license_data)
        return license_data
    
    def _save_license(self, license_data):
        """حفظ بيانات الترخيص مشفرة"""
        try:
            # تشفير البيانات
            json_data = json.dumps(license_data)
            encrypted_data = self.cipher.encrypt(json_data.encode())
            
            # حفظ في ملف
            with open(self.license_file, 'wb') as f:
                f.write(encrypted_data)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الترخيص: {e}")
            return False
    
    def _load_license(self):
        """تحميل بيانات الترخيص"""
        try:
            if not os.path.exists(self.license_file):
                return None
                
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            # فك التشفير
            decrypted_data = self.cipher.decrypt(encrypted_data)
            license_data = json.loads(decrypted_data.decode())
            return license_data
        except Exception as e:
            print(f"خطأ في تحميل الترخيص: {e}")
            return None
    
    def check_license(self):
        """فحص صحة الترخيص"""
        license_data = self._load_license()
        
        if not license_data:
            return {
                "valid": False,
                "status": "NO_LICENSE",
                "message": "لا يوجد ترخيص صالح"
            }
        
        # التحقق من تطابق الجهاز
        if license_data.get("machine_id") != self.get_machine_id():
            return {
                "valid": False,
                "status": "MACHINE_MISMATCH",
                "message": "الترخيص غير صالح لهذا الجهاز"
            }
        
        # التحقق من تاريخ الانتهاء
        try:
            expiry_date = datetime.fromisoformat(license_data["expiry_date"])
            current_date = datetime.now()
            
            if current_date > expiry_date:
                return {
                    "valid": False,
                    "status": "EXPIRED",
                    "message": "انتهت صلاحية الترخيص",
                    "expiry_date": expiry_date,
                    "customer_code": license_data.get("customer_code"),
                    "machine_id": license_data.get("machine_id")
                }
            
            # حساب الأيام المتبقية
            days_remaining = (expiry_date - current_date).days
            
            return {
                "valid": True,
                "status": "VALID",
                "message": "الترخيص صالح",
                "expiry_date": expiry_date,
                "days_remaining": days_remaining,
                "customer_code": license_data.get("customer_code"),
                "machine_id": license_data.get("machine_id")
            }
            
        except Exception as e:
            return {
                "valid": False,
                "status": "CORRUPTED",
                "message": f"ترخيص تالف: {e}"
            }
    
    def validate_renewal_code(self, renewal_code):
        """التحقق من صحة كود التجديد مع تاريخ انتهاء فعلي"""
        try:
            # تنسيق الكود: RENEW-YYYY-MM-DD-CUSTOMER-RANDOM-MACHINE
            parts = renewal_code.strip().upper().split('-')
            if len(parts) != 7 or parts[0] != "RENEW":
                return {
                    "valid": False,
                    "message": "تنسيق كود التجديد غير صحيح"
                }
            expiry_str = f"{parts[1]}-{parts[2]}-{parts[3]}"
            customer_part = parts[4]
            random_part = parts[5]
            machine_part = parts[6]
            expected_customer = self.get_customer_code()[:6].upper()
            if customer_part != expected_customer:
                return {
                    "valid": False,
                    "message": "كود التجديد غير صالح لهذا العميل"
                }
            expected_machine = self.get_machine_id()[:6].upper()
            if machine_part != expected_machine:
                return {
                    "valid": False,
                    "message": "كود التجديد غير صالح لهذا الجهاز"
                }
            from datetime import datetime
            try:
                new_expiry = datetime.strptime(expiry_str, "%Y-%m-%d")
            except Exception:
                return {
                    "valid": False,
                    "message": "تاريخ الانتهاء غير صحيح"
                }
            return {
                "valid": True,
                "message": "كود التجديد صحيح",
                "new_expiry": new_expiry
            }
        except Exception as e:
            return {
                "valid": False,
                "message": f"خطأ في التحقق من الكود: {e}"
            }
    
    def apply_renewal_code(self, renewal_code):
        """تطبيق كود التجديد"""
        validation = self.validate_renewal_code(renewal_code)
        
        if not validation["valid"]:
            return validation
        
        # إنشاء ترخيص جديد
        new_license_data = {
            "customer_code": self.get_customer_code(),
            "machine_id": self.get_machine_id(),
            "expiry_date": validation["new_expiry"].isoformat(),
            "license_type": "RENEWED",
            "renewal_code": renewal_code,
            "renewed_date": datetime.now().isoformat()
        }
        
        if self._save_license(new_license_data):
            return {
                "valid": True,
                "message": "تم تجديد الترخيص بنجاح",
                "new_expiry": validation["new_expiry"]
            }
        else:
            return {
                "valid": False,
                "message": "فشل في حفظ الترخيص الجديد"
            }

class LicenseCodeGenerator:
    """مولد أكواد التجديد (للمطور فقط)"""
    
    @staticmethod
    def generate_renewal_code(customer_code, machine_id, expiry_date=None):
        """توليد كود تجديد مع تاريخ انتهاء فعلي (YYYY-MM-DD)"""
        if expiry_date is None:
            from datetime import datetime, timedelta
            expiry_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d")
        customer_part = customer_code[:6].upper()
        machine_part = machine_id[:6].upper()
        random_part = hashlib.md5(f"{customer_code}{machine_id}{expiry_date}".encode()).hexdigest()[:6].upper()
        renewal_code = f"RENEW-{expiry_date}-{customer_part}-{random_part}-{machine_part}"
        return renewal_code
    
    @staticmethod
    def generate_multiple_codes(customers_data, year=None):
        """توليد أكواد متعددة"""
        codes = []
        for customer in customers_data:
            code = LicenseCodeGenerator.generate_renewal_code(
                customer["customer_code"],
                customer["machine_id"],
                year
            )
            codes.append({
                "customer_code": customer["customer_code"],
                "machine_id": customer["machine_id"],
                "renewal_code": code,
                "year": year or datetime.now().year + 1
            })
        return codes

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء مدير التراخيص
    license_manager = LicenseManager()
    
    # فحص الترخيص الحالي
    status = license_manager.check_license()
    print("حالة الترخيص:", status)
    
    # إذا لم يكن هناك ترخيص، إنشاء ترخيص أولي
    if not status["valid"] and status["status"] == "NO_LICENSE":
        print("إنشاء ترخيص أولي...")
        initial_license = license_manager.create_initial_license(days=30)  # 30 يوم تجربة
        print("تم إنشاء ترخيص أولي:", initial_license)
    
    # عرض معلومات الجهاز والعميل
    print(f"كود العميل: {license_manager.get_customer_code()}")
    print(f"رقم الجهاز: {license_manager.get_machine_id()}")
    
    # مثال على توليد كود تجديد (للمطور)
    renewal_code = LicenseCodeGenerator.generate_renewal_code(
        license_manager.get_customer_code(),
        license_manager.get_machine_id(),
        2025
    )
    print(f"كود التجديد المولد: {renewal_code}")
    
    # اختبار كود التجديد
    validation = license_manager.validate_renewal_code(renewal_code)
    print("نتيجة التحقق من الكود:", validation)
