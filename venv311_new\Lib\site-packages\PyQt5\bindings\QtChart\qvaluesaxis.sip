// qvaluesaxis.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (- QtChart_1_1_0)

namespace QtCharts
{
%TypeHeaderCode
#include <qvaluesaxis.h>
%End

    class QValuesAxis : public QtCharts::QAbstractAxis
    {
%TypeHeaderCode
#include <qvaluesaxis.h>
%End

    public:
        explicit QValuesAxis(QObject *parent /TransferThis/ = 0);
        virtual ~QValuesAxis();
        virtual QtCharts::QAbstractAxis::AxisType type() const;
        void setMin(qreal min);
        qreal min() const;
        void setMax(qreal max);
        qreal max() const;
        void setRange(qreal min, qreal max);
        void setTicksCount(int count);
        int ticksCount() const;
        void setNiceNumbersEnabled(bool enable = true);
        bool niceNumbersEnabled() const;

    signals:
        void minChanged(qreal min);
        void maxChanged(qreal max);
        void rangeChanged(qreal min, qreal max);

    private:
        QValuesAxis(const QtCharts::QValuesAxis &);
    };
};

%End
