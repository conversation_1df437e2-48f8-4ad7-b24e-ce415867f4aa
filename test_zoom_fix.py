#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة الزووم في نافذة طباعة الفواتير
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

def main():
    print("🔧 اختبار إصلاح مشكلة الزووم في نافذة الطباعة")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # الاتصال بقاعدة البيانات
    engine = create_engine('sqlite:///accounting.db', echo=False)
    
    try:
        print("🧾 فتح نافذة طباعة الفاتورة مع الإصلاحات الجديدة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        
        # فتح نافذة الطباعة للفاتورة رقم 1
        show_advanced_print_dialog(engine, 1, None)
        
        print("✅ تم فتح نافذة الطباعة!")
        print("")
        print("🎯 اختبر الآن:")
        print("   1. استخدم Ctrl + عجلة الماوس للزووم")
        print("   2. تحقق من عدم خروج البرنامج")
        print("   3. جرب الزووم المتكرر")
        print("   4. تحقق من استقرار النافذة")
        print("")
        print("🔧 الإصلاحات المطبقة:")
        print("   ✅ حدود آمنة لحجم الصورة")
        print("   ✅ معالجة أخطاء الذاكرة")
        print("   ✅ تنظيف آمن للعناصر")
        print("   ✅ خطوات زووم أصغر")
        print("   ✅ حماية من الأخطاء")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 ملخص الإصلاحات:")
    print("   🔧 حل مشكلة استنزاف الذاكرة عند الزووم")
    print("   🔧 إضافة حدود آمنة لحجم الصور")
    print("   🔧 تحسين معالجة الأخطاء")
    print("   🔧 تنظيف آمن للذاكرة عند الإغلاق")
    print("   🔧 خطوات زووم أصغر وأكثر سلاسة")

if __name__ == "__main__":
    main()
