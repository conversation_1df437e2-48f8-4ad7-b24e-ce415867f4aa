#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات للأدوات المنزلية فقط (بدون أجهزة كهربائية أو إضاءة)
"""

import pandas as pd
import random
import os

def create_home_products_database():
    """إنشاء قاعدة بيانات للأدوات المنزلية فقط"""
    
    # الشركات المصرية المعروفة في الأدوات المنزلية
    companies = [
        "الأهرام للبلاستيك", "العربية للأدوات المنزلية", "النصر للمنتجات المنزلية",
        "مصر للأدوات المنزلية", "القاهرة هوم", "الدلتا للمنتجات المنزلية",
        "الإسكندرية للأدوات المنزلية", "المصرية للبلاستيك", "النيل للمنتجات المنزلية",
        "الشرق الأوسط للأدوات المنزلية", "الجيزة هوم", "أسوان للمنتجات المنزلية",
        "المنصورة للأدوات المنزلية", "طنطا للمنتجات المنزلية", "الفيوم للأدوات المنزلية"
    ]
    
    # المنتجات حسب الفئات (بدون أجهزة كهربائية أو إضاءة)
    products_data = [
        # أدوات مطبخ
        {"name": "طقم أواني طبخ ستانلس ستيل", "category": "أدوات مطبخ", "price_range": (300, 2000), "unit": "طقم"},
        {"name": "طقم سكاكين مطبخ", "category": "أدوات مطبخ", "price_range": (100, 800), "unit": "طقم"},
        {"name": "لوح تقطيع خشبي", "category": "أدوات مطبخ", "price_range": (50, 300), "unit": "قطعة"},
        {"name": "لوح تقطيع بلاستيك", "category": "أدوات مطبخ", "price_range": (30, 150), "unit": "قطعة"},
        {"name": "مصفاة معدنية", "category": "أدوات مطبخ", "price_range": (30, 200), "unit": "قطعة"},
        {"name": "مقلاة تيفال", "category": "أدوات مطبخ", "price_range": (150, 800), "unit": "قطعة"},
        {"name": "حلة ضغط", "category": "أدوات مطبخ", "price_range": (400, 1500), "unit": "قطعة"},
        {"name": "طقم تقديم", "category": "أدوات مطبخ", "price_range": (200, 1000), "unit": "طقم"},
        {"name": "علب حفظ طعام بلاستيك", "category": "أدوات مطبخ", "price_range": (80, 400), "unit": "طقم"},
        {"name": "علب حفظ طعام زجاج", "category": "أدوات مطبخ", "price_range": (120, 600), "unit": "طقم"},
        {"name": "ترمس حراري", "category": "أدوات مطبخ", "price_range": (100, 500), "unit": "قطعة"},
        {"name": "إبريق شاي ستانلس ستيل", "category": "أدوات مطبخ", "price_range": (80, 400), "unit": "قطعة"},
        {"name": "إبريق شاي زجاج", "category": "أدوات مطبخ", "price_range": (60, 300), "unit": "قطعة"},
        {"name": "طقم أكواب شاي", "category": "أدوات مطبخ", "price_range": (50, 250), "unit": "طقم"},
        {"name": "طقم أطباق طعام", "category": "أدوات مطبخ", "price_range": (150, 800), "unit": "طقم"},
        {"name": "طقم ملاعق وشوك", "category": "أدوات مطبخ", "price_range": (80, 400), "unit": "طقم"},
        {"name": "مبشرة متعددة الاستخدامات", "category": "أدوات مطبخ", "price_range": (40, 200), "unit": "قطعة"},
        {"name": "مفتاح علب معدني", "category": "أدوات مطبخ", "price_range": (20, 80), "unit": "قطعة"},
        {"name": "ميزان مطبخ", "category": "أدوات مطبخ", "price_range": (100, 500), "unit": "قطعة"},
        {"name": "رف أطباق بلاستيك", "category": "أدوات مطبخ", "price_range": (80, 300), "unit": "قطعة"},
        
        # أدوات تنظيف
        {"name": "ممسحة أرضيات قطنية", "category": "أدوات تنظيف", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "ممسحة أرضيات مايكروفايبر", "category": "أدوات تنظيف", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "دلو تنظيف بلاستيك", "category": "أدوات تنظيف", "price_range": (30, 150), "unit": "قطعة"},
        {"name": "دلو تنظيف معدني", "category": "أدوات تنظيف", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "فرشاة تنظيف أرضيات", "category": "أدوات تنظيف", "price_range": (40, 150), "unit": "قطعة"},
        {"name": "فرشاة تنظيف حمامات", "category": "أدوات تنظيف", "price_range": (30, 120), "unit": "قطعة"},
        {"name": "منظف متعدد الأغراض", "category": "أدوات تنظيف", "price_range": (25, 80), "unit": "عبوة"},
        {"name": "منظف زجاج ومرايا", "category": "أدوات تنظيف", "price_range": (20, 60), "unit": "عبوة"},
        {"name": "مسحوق غسيل أطباق", "category": "أدوات تنظيف", "price_range": (15, 50), "unit": "عبوة"},
        {"name": "سائل غسيل أطباق", "category": "أدوات تنظيف", "price_range": (20, 70), "unit": "عبوة"},
        {"name": "معطر جو طبيعي", "category": "أدوات تنظيف", "price_range": (15, 60), "unit": "عبوة"},
        {"name": "إسفنجة تنظيف أطباق", "category": "أدوات تنظيف", "price_range": (10, 40), "unit": "عبوة"},
        {"name": "قفازات مطاطية", "category": "أدوات تنظيف", "price_range": (15, 50), "unit": "زوج"},
        {"name": "أكياس قمامة كبيرة", "category": "أدوات تنظيف", "price_range": (20, 80), "unit": "عبوة"},
        {"name": "أكياس قمامة صغيرة", "category": "أدوات تنظيف", "price_range": (15, 60), "unit": "عبوة"},
        {"name": "مكنسة يدوية", "category": "أدوات تنظيف", "price_range": (40, 150), "unit": "قطعة"},
        {"name": "جاروف قمامة", "category": "أدوات تنظيف", "price_range": (25, 100), "unit": "قطعة"},
        {"name": "قماش تنظيف مايكروفايبر", "category": "أدوات تنظيف", "price_range": (30, 120), "unit": "عبوة"},
        
        # أدوات حمام
        {"name": "ستارة حمام بلاستيك", "category": "أدوات حمام", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "ستارة حمام قماش", "category": "أدوات حمام", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "سجادة حمام قطنية", "category": "أدوات حمام", "price_range": (40, 200), "unit": "قطعة"},
        {"name": "سجادة حمام مطاطية", "category": "أدوات حمام", "price_range": (60, 250), "unit": "قطعة"},
        {"name": "رف حمام بلاستيك", "category": "أدوات حمام", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "رف حمام معدني", "category": "أدوات حمام", "price_range": (120, 500), "unit": "قطعة"},
        {"name": "موزع صابون بلاستيك", "category": "أدوات حمام", "price_range": (30, 120), "unit": "قطعة"},
        {"name": "موزع صابون زجاج", "category": "أدوات حمام", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "منشفة قطنية كبيرة", "category": "أدوات حمام", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "منشفة قطنية صغيرة", "category": "أدوات حمام", "price_range": (40, 150), "unit": "قطعة"},
        {"name": "سلة غسيل بلاستيك", "category": "أدوات حمام", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "سلة غسيل خوص", "category": "أدوات حمام", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "مرآة حمام صغيرة", "category": "أدوات حمام", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "مرآة حمام كبيرة", "category": "أدوات حمام", "price_range": (100, 400), "unit": "قطعة"},
        {"name": "حامل فرشاة أسنان", "category": "أدوات حمام", "price_range": (20, 80), "unit": "قطعة"},
        {"name": "كوب حمام بلاستيك", "category": "أدوات حمام", "price_range": (15, 60), "unit": "قطعة"},
        
        # أدوات غرف النوم
        {"name": "طقم ملايات قطنية", "category": "أدوات غرف النوم", "price_range": (200, 800), "unit": "طقم"},
        {"name": "طقم ملايات ساتان", "category": "أدوات غرف النوم", "price_range": (300, 1200), "unit": "طقم"},
        {"name": "مخدة قطنية", "category": "أدوات غرف النوم", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "مخدة فايبر", "category": "أدوات غرف النوم", "price_range": (60, 250), "unit": "قطعة"},
        {"name": "غطاء سرير قطني", "category": "أدوات غرف النوم", "price_range": (150, 600), "unit": "قطعة"},
        {"name": "غطاء سرير صوف", "category": "أدوات غرف النوم", "price_range": (300, 1000), "unit": "قطعة"},
        {"name": "كيس مخدة قطني", "category": "أدوات غرف النوم", "price_range": (30, 120), "unit": "قطعة"},
        {"name": "شرشف سرير قطني", "category": "أدوات غرف النوم", "price_range": (100, 400), "unit": "قطعة"},
        {"name": "منظم خزانة ملابس", "category": "أدوات غرف النوم", "price_range": (100, 500), "unit": "قطعة"},
        {"name": "علاقة ملابس خشبية", "category": "أدوات غرف النوم", "price_range": (20, 80), "unit": "عبوة"},
        {"name": "علاقة ملابس بلاستيك", "category": "أدوات غرف النوم", "price_range": (15, 60), "unit": "عبوة"},
        {"name": "صندوق تخزين ملابس", "category": "أدوات غرف النوم", "price_range": (80, 400), "unit": "قطعة"},
        
        # أدوات غرف المعيشة
        {"name": "غطاء كنبة قطني", "category": "أدوات غرف المعيشة", "price_range": (200, 800), "unit": "قطعة"},
        {"name": "غطاء كنبة مخمل", "category": "أدوات غرف المعيشة", "price_range": (300, 1200), "unit": "قطعة"},
        {"name": "خدادية كنبة", "category": "أدوات غرف المعيشة", "price_range": (50, 200), "unit": "قطعة"},
        {"name": "سجادة صالة صغيرة", "category": "أدوات غرف المعيشة", "price_range": (150, 600), "unit": "قطعة"},
        {"name": "سجادة صالة كبيرة", "category": "أدوات غرف المعيشة", "price_range": (400, 1500), "unit": "قطعة"},
        {"name": "ستائر نافذة قطنية", "category": "أدوات غرف المعيشة", "price_range": (100, 500), "unit": "زوج"},
        {"name": "ستائر نافذة حريرية", "category": "أدوات غرف المعيشة", "price_range": (200, 800), "unit": "زوج"},
        {"name": "طاولة تقديم خشبية", "category": "أدوات غرف المعيشة", "price_range": (300, 1200), "unit": "قطعة"},
        {"name": "طاولة تقديم معدنية", "category": "أدوات غرف المعيشة", "price_range": (200, 800), "unit": "قطعة"},
        {"name": "مزهرية زجاج", "category": "أدوات غرف المعيشة", "price_range": (50, 300), "unit": "قطعة"},
        {"name": "مزهرية سيراميك", "category": "أدوات غرف المعيشة", "price_range": (80, 400), "unit": "قطعة"},
        {"name": "إطار صور خشبي", "category": "أدوات غرف المعيشة", "price_range": (30, 150), "unit": "قطعة"},
        {"name": "إطار صور معدني", "category": "أدوات غرف المعيشة", "price_range": (40, 200), "unit": "قطعة"},
        
        # أدوات تنظيم وتخزين
        {"name": "صندوق تخزين بلاستيك صغير", "category": "أدوات تنظيم وتخزين", "price_range": (30, 120), "unit": "قطعة"},
        {"name": "صندوق تخزين بلاستيك كبير", "category": "أدوات تنظيم وتخزين", "price_range": (60, 250), "unit": "قطعة"},
        {"name": "صندوق تخزين خشبي", "category": "أدوات تنظيم وتخزين", "price_range": (100, 500), "unit": "قطعة"},
        {"name": "رف تخزين معدني", "category": "أدوات تنظيم وتخزين", "price_range": (200, 800), "unit": "قطعة"},
        {"name": "رف تخزين بلاستيك", "category": "أدوات تنظيم وتخزين", "price_range": (100, 400), "unit": "قطعة"},
        {"name": "سلة تخزين خوص", "category": "أدوات تنظيم وتخزين", "price_range": (80, 300), "unit": "قطعة"},
        {"name": "سلة تخزين بلاستيك", "category": "أدوات تنظيم وتخزين", "price_range": (40, 200), "unit": "قطعة"},
        {"name": "منظم أدراج", "category": "أدوات تنظيم وتخزين", "price_range": (50, 250), "unit": "قطعة"},
        {"name": "منظم خزانة مطبخ", "category": "أدوات تنظيم وتخزين", "price_range": (80, 400), "unit": "قطعة"},
        {"name": "حقيبة تخزين ملابس", "category": "أدوات تنظيم وتخزين", "price_range": (60, 300), "unit": "قطعة"},
        {"name": "علبة تخزين أحذية", "category": "أدوات تنظيم وتخزين", "price_range": (40, 200), "unit": "قطعة"},
        {"name": "منظم حقائب يد", "category": "أدوات تنظيم وتخزين", "price_range": (50, 250), "unit": "قطعة"}
    ]
    
    # إنشاء قائمة المنتجات النهائية
    final_products = []
    product_id = 1
    
    for product in products_data:
        for company in companies:
            # إنشاء 1-2 موديل لكل منتج من كل شركة
            models = ["عادي", "فاخر"]
            num_models = random.randint(1, 2)
            
            for i in range(num_models):
                model = models[i]
                
                # إنشاء الكود
                company_code = company.replace(" ", "").replace("للأدوات", "").replace("للمنتجات", "").replace("للبلاستيك", "")[:6].upper()
                product_code = f"{company_code}_{product_id:04d}"
                
                # إنشاء الاسم
                product_name = f"{product['name']} {company} - {model}"
                
                # تحديد الأسعار
                min_price, max_price = product['price_range']
                purchase_price = random.randint(min_price, max_price)
                sale_price = int(purchase_price * random.uniform(1.3, 1.8))
                
                # الكمية والحد الأدنى
                quantity = random.randint(20, 150)
                min_quantity = random.randint(5, 20)
                
                # الباركود
                barcode = f"629{random.randint(*********, *********)}"
                
                # الوصف
                descriptions = [
                    f"منتج عالي الجودة من {company}",
                    f"تصميم عملي ومتين من {company}",
                    f"منتج موثوق وعملي من {company}",
                    f"جودة ممتازة وسعر مناسب من {company}",
                    f"تصميم أنيق وعملي من {company}"
                ]
                
                final_product = {
                    "Code": product_code,
                    "Name": product_name,
                    "Description": random.choice(descriptions),
                    "Category": product['category'],
                    "Purchase_Price": purchase_price,
                    "Sale_Price": sale_price,
                    "Quantity": quantity,
                    "Min_Quantity": min_quantity,
                    "Unit": product['unit'],
                    "Barcode": barcode,
                    "Company": company,
                    "Model": model
                }
                
                final_products.append(final_product)
                product_id += 1
                
                # توقف عند 300 منتج
                if len(final_products) >= 300:
                    break
            
            if len(final_products) >= 300:
                break
        
        if len(final_products) >= 300:
            break
    
    return final_products

# إنشاء البيانات
print("🔄 جاري إنشاء قاعدة بيانات الأدوات المنزلية (بدون أجهزة كهربائية)...")
products = create_home_products_database()

# إنشاء DataFrame
df = pd.DataFrame(products)

# إنشاء مجلد البيانات
os.makedirs("sample_data", exist_ok=True)

# حفظ في Excel
excel_file = "sample_data/أدوات_منزلية_فقط.xlsx"
df.to_excel(excel_file, index=False, engine='openpyxl')

# حفظ في CSV
csv_file = "sample_data/أدوات_منزلية_فقط.csv"
df.to_csv(csv_file, index=False, encoding='utf-8-sig')

print(f"\n✅ تم إنشاء قاعدة البيانات بنجاح!")
print(f"📁 ملف Excel: {excel_file}")
print(f"📁 ملف CSV: {csv_file}")
print(f"📊 عدد المنتجات: {len(products)}")
print(f"🏢 عدد الشركات: {len(set(df['Company']))}")
print(f"📂 عدد الفئات: {len(set(df['Category']))}")

# عرض إحصائيات
print(f"\n📈 إحصائيات الأسعار:")
print(f"💰 متوسط سعر الشراء: {df['Purchase_Price'].mean():.0f} جنيه")
print(f"💰 متوسط سعر البيع: {df['Sale_Price'].mean():.0f} جنيه")
print(f"📦 إجمالي الكميات: {df['Quantity'].sum()} قطعة")

print(f"\n📂 الفئات المتضمنة:")
for category in sorted(set(df['Category'])):
    count = len(df[df['Category'] == category])
    print(f"   • {category}: {count} منتج")

print(f"\n🏭 الشركات المتضمنة:")
for company in sorted(set(df['Company'])):
    count = len(df[df['Company'] == company])
    print(f"   • {company}: {count} منتج")
