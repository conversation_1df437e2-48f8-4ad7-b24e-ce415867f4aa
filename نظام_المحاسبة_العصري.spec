# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('assets', 'assets'), ('company_settings.json', '.'), ('brand_settings.json', '.'), ('user_settings.json', '.'), ('theme_settings.json', '.'), ('accounting.db', '.'), ('utils', 'utils'), ('database', 'database'), ('images', 'images'), ('fonts', 'fonts'), ('gui', 'gui'), ('sample_data', 'sample_data'), ('README_للعميل_المحدث.md', '.'), ('file_version_info.txt', '.'), ('requirements.txt', '.'), ('LICENSE_SYSTEM_README.md', '.'), ('RESET_SYSTEM_README.md', '.'), ('export_config_updated.spec', '.'), ('main.py', '.'), ('main_simple.py', '.'), ('run_updated_app.py', '.'), ('license.dat', '.'), ('license_manager.py', '.'), ('license_ui.py', '.'), ('export_config.spec', '.'), ('accounting.spec', '.'), ('modern_accounts.spec', '.'), ('نظام_المحاسبة_العصري.spec', '.'), ('convert_csv_to_excel.py', '.'), ('create_home_products_only.py', '.'), ('create_products_db.py', '.'), ('create_test_company.py', '.'), ('update_contact_info.py', '.'), ('update_database_for_multiple_barcodes.py', '.'), ('update_database_for_returns.py', '.'), ('force_inventory_update.py', '.'), ('force_reload_inventory.py', '.'), ('generate_sample_excel.py', '.'), ('quick_start.py', '.'), ('quick_test.py', '.'), ('verify_clean_release.py', '.'), ('clean_for_release.py', '.'), ('final_test_complete_inventory.py', '.'), ('test_settings.db', '.'), ('backup_before_import_20250625_011738.db', '.'), ('backup_before_import_20250625_011817.db', '.'), ('backup_before_import_20250625_012312.db', '.'), ('backup_before_import_20250625_013424.db', '.'), ('backup_before_reset_20250623_060521.db', '.'), ('backup_before_reset_20250625_001745.db', '.'), ('backup_before_reset_20250625_001902.db', '.'), ('backup_before_reset_20250625_001948.db', '.'), ('backup_before_reset_20250625_003047.db', '.'), ('backup_before_reset_20250625_003051.db', '.'), ('backup_before_reset_20250625_072003.db', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='نظام_المحاسبة_العصري',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['assets\\icons.ico'],
)
