# 🔐 نظام التراخيص المدمج - برنامج المحاسبة

## 📋 نظرة عامة

تم دمج نظام ترخيص احترافي وآمن مع برنامج المحاسبة لضمان الحماية من القرصنة وتوفير دخل مستدام من التجديدات السنوية.

## 🎯 المميزات الرئيسية

### 🔒 الحماية والأمان
- **ترخيص مرتبط بالجهاز**: كل ترخيص مرتبط بجهاز محدد
- **تشفير متقدم**: جميع بيانات الترخيص مشفرة بقوة
- **فحص دوري**: فحص الترخيص كل 30 دقيقة
- **حماية من التلاعب**: لا يمكن تعديل ملفات الترخيص يدوياً

### 💰 النموذج التجاري
- **اشتراك سنوي**: ترخيص لمدة سنة واحدة
- **تجديد إجباري**: البرنامج يتوقف بعد انتهاء الصلاحية
- **دخل مستدام**: تجديدات سنوية مضمونة
- **تحكم كامل**: المطور يتحكم في جميع التراخيص

## 🛠️ الملفات المضافة

### ملفات النظام الأساسية
```
license_manager.py      # مدير التراخيص والتشفير
license_ui.py          # واجهة المستخدم للترخيص
code_generator_ui.py   # مولد الأكواد (للمطور فقط)
test_license_system.py # تطبيق اختبار النظام
requirements_license.txt # المكتبات المطلوبة
```

### ملفات التوثيق
```
docs/دليل_نظام_التراخيص.md    # دليل شامل للنظام
LICENSE_SYSTEM_README.md        # هذا الملف
test_integrated_license.py      # اختبار الدمج
run_license_demo.py            # عرض توضيحي
```

## 🔧 التعديلات على البرنامج الأصلي

### 1. main.py
```python
# إضافة فحص الترخيص في البداية
if LICENSE_SYSTEM_AVAILABLE:
    if not check_license_and_show_dialog():
        # إغلاق البرنامج إذا لم يتم تفعيل الترخيص
        return 1
```

### 2. gui/main_window.py
```python
# إضافة فحص دوري للترخيص
def setup_license_timer(self):
    self.license_timer = QTimer()
    self.license_timer.timeout.connect(self.check_license_status)
    self.license_timer.start(30 * 60 * 1000)  # كل 30 دقيقة

# إضافة خيار تجديد الترخيص في قائمة النظام
license_action = QAction("🔐 تجديد الترخيص", self)
```

## 🚀 كيفية الاستخدام

### للعميل (المستخدم النهائي)

#### 1. التشغيل الأول
```bash
python main.py
```
- سيتم إنشاء ترخيص تجريبي لمدة 30 يوم
- سيظهر كود العميل ورقم الجهاز

#### 2. تجديد الترخيص
1. اذهب إلى: **النظام > تجديد الترخيص**
2. أدخل كود التجديد المرسل من المطور
3. اضغط "تطبيق"

#### 3. التحذيرات
- تحذير قبل انتهاء الصلاحية بـ 7 أيام
- إغلاق تلقائي عند انتهاء الصلاحية

### للمطور

#### 1. توليد كود تجديد
```bash
python code_generator_ui.py
```
- أدخل كود العميل ورقم الجهاز
- اختر سنة التجديد
- انسخ الكود المولد

#### 2. إرسال الكود للعميل
- عبر واتساب، إيميل، أو أي وسيلة تواصل
- تأكد من الدفع قبل إرسال الكود

## 📊 سير العمل التجاري

### 1. العميل الجديد
```
تسطيب البرنامج → ترخيص تجريبي 30 يوم → تواصل للتجديد
```

### 2. تجديد الترخيص
```
انتهاء الصلاحية → تواصل العميل → دفع الرسوم → إرسال الكود → تفعيل
```

### 3. المتابعة
```
تذكير قبل الانتهاء → تواصل مسبق → تجديد سلس
```

## 🔑 أمثلة على الأكواد

### كود تجديد نموذجي
```
RENEW-2025-ABC123-XYZ789-DEF456
```

### تفسير الكود
- `RENEW`: نوع العملية (تجديد)
- `2025`: سنة التجديد
- `ABC123`: كود العميل
- `XYZ789`: كود أمان
- `DEF456`: رقم الجهاز (مختصر)

## 🛡️ الأمان والحماية

### مستويات الحماية
1. **تشفير AES-256**: لجميع بيانات الترخيص
2. **ربط بالجهاز**: كل ترخيص مرتبط بجهاز محدد
3. **توقيع رقمي**: للتأكد من صحة الأكواد
4. **فحص دوري**: للتأكد من استمرار صحة الترخيص

### منع القرصنة
- لا يمكن نسخ الترخيص لجهاز آخر
- لا يمكن تعديل تاريخ انتهاء الصلاحية
- لا يمكن تشغيل البرنامج بدون ترخيص صالح
- فحص مستمر لصحة الترخيص

## 💡 نصائح للمطور

### إدارة العملاء
1. **احتفظ بسجل** لجميع العملاء وأكوادهم
2. **راقب تواريخ الانتهاء** لتذكير العملاء
3. **قدم دعم سريع** لطلبات التجديد
4. **احفظ نسخة آمنة** من مولد الأكواد

### خدمة العملاء
1. **رد سريع** على طلبات التجديد
2. **تأكد من الدفع** قبل إرسال الكود
3. **قدم تعليمات واضحة** لإدخال الكود
4. **احفظ تاريخ كل تجديد** للمتابعة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### "نظام التراخيص غير متوفر"
```bash
pip install cryptography pyperclip
```

#### "كود التجديد غير صحيح"
- تأكد من إدخال الكود كاملاً
- تأكد من عدم وجود مسافات إضافية
- تأكد من أن الكود مخصص لهذا الجهاز

#### "انتهت صلاحية الترخيص"
- تواصل مع المطور للحصول على كود تجديد
- تأكد من دفع رسوم التجديد

## 📞 الدعم الفني

للحصول على الدعم الفني أو طلب تجديد الترخيص:
- تواصل مع المطور
- أرسل كود العميل ورقم الجهاز
- تأكد من دفع رسوم التجديد

---

## 🎉 الخلاصة

تم دمج نظام ترخيص احترافي وآمن مع برنامج المحاسبة بنجاح. النظام يوفر:

✅ **حماية قوية** ضد القرصنة والتلاعب  
✅ **دخل مستدام** من التجديدات السنوية  
✅ **سهولة استخدام** للعملاء والمطور  
✅ **تحكم كامل** في التراخيص والتجديدات  

**🚀 النظام جاهز للاستخدام والتوزيع التجاري!**
