// qsgsimpletexturenode.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGSimpleTextureNode : public QSGGeometryNode /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgsimpletexturenode.h>
%End

public:
    QSGSimpleTextureNode();
%If (Qt_5_4_0 -)
    virtual ~QSGSimpleTextureNode();
%End
    void setRect(const QRectF &rect);
    void setRect(qreal x, qreal y, qreal w, qreal h);
    QRectF rect() const;
    void setTexture(QSGTexture *texture);
    QSGTexture *texture() const;
    void setFiltering(QSGTexture::Filtering filtering);
    QSGTexture::Filtering filtering() const;
%If (Qt_5_2_0 -)

    enum TextureCoordinatesTransformFlag
    {
        NoTransform,
        MirrorHorizontally,
        MirrorVertically,
    };

%End
%If (Qt_5_2_0 -)
    typedef QFlags<QSGSimpleTextureNode::TextureCoordinatesTransformFlag> TextureCoordinatesTransformMode;
%End
%If (Qt_5_2_0 -)
    void setTextureCoordinatesTransform(QSGSimpleTextureNode::TextureCoordinatesTransformMode mode);
%End
%If (Qt_5_2_0 -)
    QSGSimpleTextureNode::TextureCoordinatesTransformMode textureCoordinatesTransform() const;
%End
%If (Qt_5_4_0 -)
    void setOwnsTexture(bool owns);
%End
%If (Qt_5_4_0 -)
    bool ownsTexture() const;
%End
%If (Qt_5_5_0 -)
    void setSourceRect(const QRectF &r);
%End
%If (Qt_5_5_0 -)
    void setSourceRect(qreal x, qreal y, qreal w, qreal h);
%End
%If (Qt_5_5_0 -)
    QRectF sourceRect() const;
%End
};

%If (Qt_5_2_0 -)
QFlags<QSGSimpleTextureNode::TextureCoordinatesTransformFlag> operator|(QSGSimpleTextureNode::TextureCoordinatesTransformFlag f1, QFlags<QSGSimpleTextureNode::TextureCoordinatesTransformFlag> f2);
%End
