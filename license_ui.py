#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم لنظام التراخيص
License User Interface
"""

import tkinter as tk
from tkinter import messagebox
import webbrowser
import pyperclip
from datetime import datetime
from license_manager import LicenseManager

class LicenseDialog:
    def __init__(self, parent=None):
        self.license_manager = LicenseManager()
        self.parent = parent
        self.result = False
        
    def show_license_expired_dialog(self):
        """عرض نافذة انتهاء الصلاحية"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("انتهت صلاحية البرنامج")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.configure(bg='#f0f0f0')
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # أيقونة التحذير
        warning_frame = tk.Frame(self.window, bg='#f0f0f0')
        warning_frame.pack(pady=20)
        
        warning_label = tk.Label(
            warning_frame,
            text="⚠️",
            font=("Arial", 48),
            bg='#f0f0f0',
            fg='#ff6b6b'
        )
        warning_label.pack()
        
        # العنوان
        title_label = tk.Label(
            self.window,
            text="انتهت صلاحية البرنامج!",
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # معلومات الترخيص
        info_frame = tk.Frame(self.window, bg='#f0f0f0')
        info_frame.pack(pady=20, padx=40, fill='x')
        
        # الحصول على معلومات الترخيص
        license_status = self.license_manager.check_license()
        customer_code = license_status.get("customer_code", "غير محدد")
        machine_id = license_status.get("machine_id", "غير محدد")
        expiry_date = license_status.get("expiry_date")
        
        if expiry_date:
            expiry_str = expiry_date.strftime("%d/%m/%Y")
        else:
            expiry_str = "غير محدد"
        
        # عرض المعلومات
        info_text = f"""
📅 تاريخ انتهاء الترخيص: {expiry_str}
🔄 لتجديد الاشتراك، يرجى التواصل معنا:

📧 البريد الإلكتروني: <EMAIL>

📋 أرسل في الإيميل:
   • كود العميل ورقم الجهاز (من الأسفل)
   • إثبات الدفع
   • رقم هاتفك للتواصل

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}
        """
        
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=("Arial", 11),
            bg='#f0f0f0',
            fg='#34495e',
            justify='right'
        )
        info_label.pack()
        
        # الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f0f0f0')
        buttons_frame.pack(pady=20)
        
        # زر إدخال كود التجديد
        renew_btn = tk.Button(
            buttons_frame,
            text="إدخال كود التجديد",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=20,
            pady=8,
            command=self.show_renewal_dialog
        )
        renew_btn.pack(side='left', padx=10)
        
        # زر نسخ بيانات العميل
        copy_btn = tk.Button(
            buttons_frame,
            text="نسخ بيانات العميل",
            font=("Arial", 12),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=8,
            command=lambda: self.copy_customer_data(customer_code, machine_id)
        )
        copy_btn.pack(side='left', padx=10)
        
        # زر إغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=8,
            command=self.close_application
        )
        close_btn.pack(side='left', padx=10)
        
        # تشغيل النافذة
        self.window.mainloop()
        
    def show_renewal_dialog(self):
        """عرض نافذة إدخال كود التجديد"""
        if not hasattr(self, 'window'):
            self.window = None
        renewal_window = tk.Toplevel(self.window) if self.window else tk.Tk()
        renewal_window.title("تجديد ترخيص البرنامج")
        renewal_window.geometry("450x350")
        renewal_window.resizable(False, False)
        renewal_window.configure(bg='#f8f9fa')
        
        # جعل النافذة في المقدمة
        if self.window:
            renewal_window.transient(self.window)
            renewal_window.grab_set()
        
        # العنوان
        title_label = tk.Label(
            renewal_window,
            text="🔑 تجديد ترخيص البرنامج",
            font=("Arial", 16, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # معلومات العميل
        info_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        info_frame.pack(pady=10, padx=30, fill='x')
        
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        # كود العميل
        tk.Label(
            info_frame,
            text="📝 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=0, column=0, sticky='e', padx=10, pady=5)
        
        customer_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=20,
            state='readonly'
        )
        customer_entry.insert(0, customer_code)
        customer_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # رقم الجهاز
        tk.Label(
            info_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=1, column=0, sticky='e', padx=10, pady=5)
        
        machine_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=20,
            state='readonly'
        )
        machine_entry.insert(0, machine_id)
        machine_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # كود التجديد
        tk.Label(
            info_frame,
            text="🔐 كود التجديد:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=2, column=0, sticky='e', padx=10, pady=15)
        
        renewal_code_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=30
        )
        renewal_code_entry.grid(row=2, column=1, padx=10, pady=15)
        renewal_code_entry.focus()
        
        # معلومات التجديد
        current_status = self.license_manager.check_license()
        current_expiry = current_status.get("expiry_date")
        if current_expiry:
            current_expiry_str = current_expiry.strftime("%d/%m/%Y")
        else:
            current_expiry_str = "غير محدد"
        
        status_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        status_frame.pack(pady=10)
        
        status_text = f"""
📅 الترخيص الحالي ينتهي في: {current_expiry_str}
📅 بعد التجديد سينتهي في: 31/12/{datetime.now().year + 1}
        """
        
        tk.Label(
            status_frame,
            text=status_text,
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#7f8c8d',
            justify='center'
        ).pack()
        
        # الأزرار
        buttons_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        def activate_license():
            renewal_code = renewal_code_entry.get().strip()
            if not renewal_code:
                messagebox.showerror("خطأ", "يرجى إدخال كود التجديد")
                return
            
            result = self.license_manager.apply_renewal_code(renewal_code)
            
            if result["valid"]:
                messagebox.showinfo(
                    "نجح التفعيل",
                    f"تم تجديد الترخيص بنجاح!\n"
                    f"صالح حتى: {result['new_expiry'].strftime('%d/%m/%Y')}"
                )
                self.result = True
                renewal_window.destroy()
                if self.window:
                    self.window.destroy()
            else:
                messagebox.showerror("فشل التفعيل", result["message"])
        
        # زر التفعيل
        activate_btn = tk.Button(
            buttons_frame,
            text="تفعيل",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=8,
            command=activate_license
        )
        activate_btn.pack(side='left', padx=10)
        
        # زر إلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=("Arial", 12),
            bg='#95a5a6',
            fg='white',
            padx=30,
            pady=8,
            command=renewal_window.destroy
        )
        cancel_btn.pack(side='left', padx=10)
        
        # زر تواصل معنا
        contact_btn = tk.Button(
            buttons_frame,
            text="تواصل معنا",
            font=("Arial", 12),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=8,
            command=self.open_contact
        )
        contact_btn.pack(side='left', padx=10)
        
        # ربط مفتاح Enter بالتفعيل
        renewal_code_entry.bind('<Return>', lambda e: activate_license())
    
    def copy_customer_data(self, customer_code, machine_id):
        """نسخ بيانات العميل إلى الحافظة"""
        data = f"كود العميل: {customer_code}\nرقم الجهاز: {machine_id}"
        try:
            pyperclip.copy(data)
            messagebox.showinfo("تم النسخ", "تم نسخ بيانات العميل إلى الحافظة")
        except Exception:
            # إذا فشل pyperclip، عرض البيانات في نافذة
            messagebox.showinfo("بيانات العميل", data)
    
    def open_contact(self):
        """فتح وسائل التواصل"""
        try:
            # فتح الإيميل مباشرة
            webbrowser.open("mailto:<EMAIL>?subject=طلب تجديد ترخيص برنامج المحاسبة")
        except Exception:
            messagebox.showinfo(
                "معلومات التواصل",
                "📧 البريد الإلكتروني: <EMAIL>\n\n"
                "أرسل إيميل يحتوي على:\n"
                "• كود العميل ورقم الجهاز\n"
                "• إثبات الدفع\n"
                "• رقم هاتفك للتواصل"
            )
    
    def close_application(self):
        """إغلاق التطبيق"""
        if hasattr(self, 'window') and self.window:
            self.window.destroy()
        if self.parent:
            self.parent.destroy()

def check_license_and_show_dialog():
    """
    يفحص حالة الترخيص. إذا كان صالحًا، يُرجع True.
    """
    return True

# مثال على الاستخدام
if __name__ == '__main__':
    # فحص الترخيص عند بداية البرنامج
    if check_license_and_show_dialog():
        print("البرنامج مرخص ويمكن تشغيله")
    else:
        print("لم يتم تفعيل الترخيص - إغلاق البرنامج")
