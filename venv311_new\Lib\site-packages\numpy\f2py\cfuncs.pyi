from typing import Final, <PERSON><PERSON>lias

from .__version__ import version

###

_NeedListDict: TypeAlias = dict[str, list[str]]
_NeedDict: TypeAlias = dict[str, str]

###

f2py_version: Final = version

outneeds: Final[_NeedListDict] = ...
needs: Final[_NeedListDict] = ...

includes0: Final[_NeedDict] = ...
includes: Final[_NeedDict] = ...
userincludes: Final[_NeedDict] = ...
typedefs: Final[_NeedDict] = ...
typedefs_generated: Final[_NeedDict] = ...
cppmacros: Final[_NeedDict] = ...
cfuncs: Final[_NeedDict] = ...
callbacks: Final[_NeedDict] = ...
f90modhooks: Final[_NeedDict] = ...
commonhooks: Final[_NeedDict] = ...

def errmess(s: str) -> None: ...
def buildcfuncs() -> None: ...
def get_needs() -> _NeedListDict: ...
def append_needs(need: str | list[str], flag: int = 1) -> _NeedListDict: ...
