#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام التراخيص
"""

try:
    from license_manager import LicenseManager, LicenseCodeGenerator
    
    print("🚀 اختبار نظام التراخيص")
    print("=" * 40)
    
    # إنشاء مدير التراخيص
    lm = LicenseManager()
    
    # عرض معلومات الجهاز
    customer_code = lm.get_customer_code()
    machine_id = lm.get_machine_id()
    
    print(f"🔑 كود العميل: {customer_code}")
    print(f"💻 رقم الجهاز: {machine_id}")
    
    # فحص الترخيص
    status = lm.check_license()
    
    if status["valid"]:
        print(f"✅ الترخيص صالح")
        print(f"📅 ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
        print(f"⏰ متبقي: {status['days_remaining']} يوم")
    else:
        print(f"❌ الترخيص غير صالح: {status['message']}")
        
        if status["status"] == "NO_LICENSE":
            print("🔄 إنشاء ترخيص تجريبي...")
            lm.create_initial_license(days=30)
            print("✅ تم إنشاء ترخيص تجريبي لمدة 30 يوم")
    
    # توليد كود تجديد
    print("\n🔑 توليد كود تجديد:")
    renewal_code = LicenseCodeGenerator.generate_renewal_code(
        customer_code, machine_id, 2025
    )
    print(f"📋 الكود: {renewal_code}")
    
    # اختبار الكود
    validation = lm.validate_renewal_code(renewal_code)
    if validation["valid"]:
        print("✅ الكود صحيح")
    else:
        print(f"❌ الكود خاطئ: {validation['message']}")
    
    print("\n🎉 انتهى الاختبار بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
