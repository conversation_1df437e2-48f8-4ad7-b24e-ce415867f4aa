// qserialportinfo.sip generated by MetaSIP
//
// This file is part of the QtSerialPort Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QSerialPortInfo
{
%TypeHeaderCode
#include <qserialportinfo.h>
%End

public:
    QSerialPortInfo();
    explicit QSerialPortInfo(const QSerialPort &port);
    explicit QSerialPortInfo(const QString &name);
    QSerialPortInfo(const QSerialPortInfo &other);
    ~QSerialPortInfo();
    void swap(QSerialPortInfo &other /Constrained/);
    QString portName() const;
    QString systemLocation() const;
    QString description() const;
    QString manufacturer() const;
    quint16 vendorIdentifier() const;
    quint16 productIdentifier() const;
    bool hasVendorIdentifier() const;
    bool hasProductIdentifier() const;
    bool isBusy() const;
    bool isValid() const;
    static QList<int> standardBaudRates();
    static QList<QSerialPortInfo> availablePorts();
    bool isNull() const;
%If (Qt_5_3_0 -)
    QString serialNumber() const;
%End
};

%End
