// qdesktopwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesktopWidget : public QWidget
{
%TypeHeaderCode
#include <qdesktopwidget.h>
%End

public:
    QDesktopWidget();
    virtual ~QDesktopWidget();
    bool isVirtualDesktop() const;
    int primaryScreen() const;
    int screenNumber(const QWidget *widget = 0) const;
    int screenNumber(const QPoint &) const;
    QWidget *screen(int screen = -1);
    int screenCount() const;
    const QRect screenGeometry(int screen = -1) const;
    const QRect screenGeometry(const QWidget *widget) const;
    const QRect screenGeometry(const QPoint &point) const;
    const QRect availableGeometry(int screen = -1) const;
    const QRect availableGeometry(const QWidget *widget) const;
    const QRect availableGeometry(const QPoint &point) const;

signals:
    void resized(int);
    void workAreaResized(int);
    void screenCountChanged(int);
%If (Qt_5_6_0 -)
    void primaryScreenChanged();
%End

protected:
    virtual void resizeEvent(QResizeEvent *e);
};
