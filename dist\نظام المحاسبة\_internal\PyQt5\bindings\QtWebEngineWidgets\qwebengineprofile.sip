// qwebengineprofile.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_5_0 -)

class QWebEngineProfile : public QObject
{
%TypeHeaderCode
#include <qwebengineprofile.h>
%End

%TypeCode
#if QT_VERSION >= 0x050d00
// For QWebEngineProfile.setNotificationPresenter().
#include <QWebEngineNotification>
#endif
%End

public:
    explicit QWebEngineProfile(QObject *parent /TransferThis/ = 0);
    QWebEngineProfile(const QString &name, QObject *parent /TransferThis/ = 0);
    virtual ~QWebEngineProfile();

    enum HttpCacheType
    {
        MemoryHttpCache,
        DiskHttpCache,
%If (QtWebEngine_5_7_0 -)
        NoCache,
%End
    };

    enum PersistentCookiesPolicy
    {
        NoPersistentCookies,
        AllowPersistentCookies,
        ForcePersistentCookies,
    };

    QString storageName() const;
    bool isOffTheRecord() const;
    QString persistentStoragePath() const;
    void setPersistentStoragePath(const QString &path);
    QString cachePath() const;
    void setCachePath(const QString &path);
    QString httpUserAgent() const;
    void setHttpUserAgent(const QString &userAgent);
    QWebEngineProfile::HttpCacheType httpCacheType() const;
    void setHttpCacheType(QWebEngineProfile::HttpCacheType);
    QWebEngineProfile::PersistentCookiesPolicy persistentCookiesPolicy() const;
    void setPersistentCookiesPolicy(QWebEngineProfile::PersistentCookiesPolicy);
    int httpCacheMaximumSize() const;
    void setHttpCacheMaximumSize(int maxSize);
    void clearAllVisitedLinks();
    void clearVisitedLinks(const QList<QUrl> &urls);
    bool visitedLinksContainsUrl(const QUrl &url) const;
    QWebEngineSettings *settings() const;
    QWebEngineScriptCollection *scripts() const;
    static QWebEngineProfile *defaultProfile() /Transfer/;

signals:
    void downloadRequested(QWebEngineDownloadItem *download);

public:
%If (QtWebEngine_5_6_0 -)
    void setHttpAcceptLanguage(const QString &httpAcceptLanguage);
%End
%If (QtWebEngine_5_6_0 -)
    QString httpAcceptLanguage() const;
%End
%If (QtWebEngine_5_6_0 -)
    QWebEngineCookieStore *cookieStore() /Transfer/;
%End
%If (QtWebEngine_5_13_0 -)
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
%End
%If (QtWebEngine_5_6_0 -)
    void setRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
%End
%If (QtWebEngine_5_6_0 -)
    const QWebEngineUrlSchemeHandler *urlSchemeHandler(const QByteArray &) const;
%End
%If (QtWebEngine_5_6_0 -)
    void installUrlSchemeHandler(const QByteArray &scheme, QWebEngineUrlSchemeHandler *);
%End
%If (QtWebEngine_5_6_0 -)
    void removeUrlScheme(const QByteArray &scheme);
%End
%If (QtWebEngine_5_6_0 -)
    void removeUrlSchemeHandler(QWebEngineUrlSchemeHandler *);
%End
%If (QtWebEngine_5_6_0 -)
    void removeAllUrlSchemeHandlers();
%End
%If (QtWebEngine_5_7_0 -)
    void clearHttpCache();
%End
%If (QtWebEngine_5_8_0 -)
    void setSpellCheckLanguages(const QStringList &languages);
%End
%If (QtWebEngine_5_8_0 -)
    QStringList spellCheckLanguages() const;
%End
%If (QtWebEngine_5_8_0 -)
    void setSpellCheckEnabled(bool enabled);
%End
%If (QtWebEngine_5_8_0 -)
    bool isSpellCheckEnabled() const;
%End

private:
%If (QtWebEngine_5_6_0 -)
    QWebEngineProfile(const QWebEngineProfile &);
%End

public:
%If (QtWebEngine_5_13_0 -)
    void setUseForGlobalCertificateVerification(bool enabled = true);
%End
%If (QtWebEngine_5_13_0 -)
    bool isUsedForGlobalCertificateVerification() const;
%End
%If (QtWebEngine_5_13_0 -)
    QString downloadPath() const;
%End
%If (QtWebEngine_5_13_0 -)
    void setDownloadPath(const QString &path);
%End
%If (QtWebEngine_5_13_0 -)
    void setNotificationPresenter(SIP_PYCALLABLE /AllowNone, KeepReference, TypeHint="Callable[[QWebEngineNotification], None]"/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->setNotificationPresenter([a0](std::unique_ptr<QWebEngineNotification> arg) {
            if (a0)
            {
                SIP_BLOCK_THREADS
                
                // We have to find the generated type structure because a name isn't
                // automatically generated as it isn't used anywhere else in the
                // module.
                PyObject *arg_obj = sipConvertFromNewType(arg.release(),
                        sipFindType("QWebEngineNotification"), NULL);
                
                if (arg_obj)
                {
                    PyObject *res = PyObject_CallFunctionObjArgs(a0, arg_obj, NULL);
        
                    Py_DECREF(arg_obj);
        
                    if (!res)
                        pyqt5_qtwebenginewidgets_err_print();
                    else
                        Py_DECREF(res);
                }
                else
                {
                    pyqt5_qtwebenginewidgets_err_print();
                }
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

%End
%If (QtWebEngine_5_13_0 -)
%If (PyQt_SSL)
    QWebEngineClientCertificateStore *clientCertificateStore();
%End
%End
};

%End
