// qpagesize.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_3_0 -)

class QPageSize
{
%TypeHeaderCode
#include <qpagesize.h>
%End

public:
    enum PageSizeId
    {
        A4,
        B5,
        Letter,
        Legal,
        Executive,
        A0,
        A1,
        A2,
        A3,
        A5,
        A6,
        A7,
        A8,
        A9,
        B0,
        B1,
        B10,
        B2,
        B3,
        B4,
        B6,
        B7,
        B8,
        B9,
        C5E,
        Comm10E,
        DLE,
        Folio,
        Ledger,
        Tabloid,
        Custom,
        A10,
        A3Extra,
        A4Extra,
        A4Plus,
        A4Small,
        A5Extra,
        B5Extra,
        JisB0,
        JisB1,
        JisB2,
        JisB3,
        JisB4,
        JisB5,
        JisB6,
        JisB7,
        JisB8,
        JisB9,
        JisB10,
        AnsiC,
        AnsiD,
        AnsiE,
        LegalExtra,
        LetterExtra,
        LetterPlus,
        LetterSmall,
        TabloidExtra,
        ArchA,
        ArchB,
        ArchC,
        ArchD,
        ArchE,
        Imperial7x9,
        Imperial8x10,
        Imperial9x11,
        Imperial9x12,
        Imperial10x11,
        Imperial10x13,
        Imperial10x14,
        Imperial12x11,
        Imperial15x11,
        ExecutiveStandard,
        Note,
        Quarto,
        Statement,
        SuperA,
        SuperB,
        Postcard,
        DoublePostcard,
        Prc16K,
        Prc32K,
        Prc32KBig,
        FanFoldUS,
        FanFoldGerman,
        FanFoldGermanLegal,
        EnvelopeB4,
        EnvelopeB5,
        EnvelopeB6,
        EnvelopeC0,
        EnvelopeC1,
        EnvelopeC2,
        EnvelopeC3,
        EnvelopeC4,
        EnvelopeC6,
        EnvelopeC65,
        EnvelopeC7,
        Envelope9,
        Envelope11,
        Envelope12,
        Envelope14,
        EnvelopeMonarch,
        EnvelopePersonal,
        EnvelopeChou3,
        EnvelopeChou4,
        EnvelopeInvite,
        EnvelopeItalian,
        EnvelopeKaku2,
        EnvelopeKaku3,
        EnvelopePrc1,
        EnvelopePrc2,
        EnvelopePrc3,
        EnvelopePrc4,
        EnvelopePrc5,
        EnvelopePrc6,
        EnvelopePrc7,
        EnvelopePrc8,
        EnvelopePrc9,
        EnvelopePrc10,
        EnvelopeYou4,
        NPageSize,
        NPaperSize,
        AnsiA,
        AnsiB,
        EnvelopeC5,
        EnvelopeDL,
        Envelope10,
        LastPageSize,
    };

    enum Unit
    {
        Millimeter,
        Point,
        Inch,
        Pica,
        Didot,
        Cicero,
    };

    enum SizeMatchPolicy
    {
        FuzzyMatch,
        FuzzyOrientationMatch,
        ExactMatch,
    };

    QPageSize();
    explicit QPageSize(QPageSize::PageSizeId pageSizeId);
    QPageSize(const QSize &pointSize, const QString &name = QString(), QPageSize::SizeMatchPolicy matchPolicy = QPageSize::FuzzyMatch);
    QPageSize(const QSizeF &size, QPageSize::Unit units, const QString &name = QString(), QPageSize::SizeMatchPolicy matchPolicy = QPageSize::FuzzyMatch);
    QPageSize(const QPageSize &other);
    ~QPageSize();
    void swap(QPageSize &other /Constrained/);
    bool isEquivalentTo(const QPageSize &other) const;
    bool isValid() const;
    QString key() const;
    QString name() const;
    QPageSize::PageSizeId id() const;
    int windowsId() const;
    QSizeF definitionSize() const;
    QPageSize::Unit definitionUnits() const;
    QSizeF size(QPageSize::Unit units) const;
    QSize sizePoints() const;
    QSize sizePixels(int resolution) const;
    QRectF rect(QPageSize::Unit units) const;
    QRect rectPoints() const;
    QRect rectPixels(int resolution) const;
    static QString key(QPageSize::PageSizeId pageSizeId);
    static QString name(QPageSize::PageSizeId pageSizeId);
    static QPageSize::PageSizeId id(const QSize &pointSize, QPageSize::SizeMatchPolicy matchPolicy = QPageSize::FuzzyMatch);
    static QPageSize::PageSizeId id(const QSizeF &size, QPageSize::Unit units, QPageSize::SizeMatchPolicy matchPolicy = QPageSize::FuzzyMatch);
    static QPageSize::PageSizeId id(int windowsId);
    static int windowsId(QPageSize::PageSizeId pageSizeId);
    static QSizeF definitionSize(QPageSize::PageSizeId pageSizeId);
    static QPageSize::Unit definitionUnits(QPageSize::PageSizeId pageSizeId);
    static QSizeF size(QPageSize::PageSizeId pageSizeId, QPageSize::Unit units);
    static QSize sizePoints(QPageSize::PageSizeId pageSizeId);
    static QSize sizePixels(QPageSize::PageSizeId pageSizeId, int resolution);
};

%End
%If (Qt_5_3_0 -)
bool operator==(const QPageSize &lhs, const QPageSize &rhs);
%End
%If (Qt_5_3_0 -)
bool operator!=(const QPageSize &lhs, const QPageSize &rhs);
%End
