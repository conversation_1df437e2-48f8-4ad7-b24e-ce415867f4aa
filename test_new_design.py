#!/usr/bin/env python3
"""
اختبار التصميم الجديد لطباعة الفواتير
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.new_design_invoice_printer import show_new_design_print_dialog

class TestNewDesignWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار التصميم الجديد للطباعة")
        self.setGeometry(100, 100, 500, 400)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_label = QPushButton("🎨 اختبار التصميم الجديد للطباعة")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # زر اختبار الفاتورة رقم 1
        test_btn1 = QPushButton("📋 اختبار الفاتورة رقم 1")
        test_btn1.setStyleSheet(self.get_button_style("#3498DB"))
        test_btn1.clicked.connect(lambda: self.test_printing(1))
        layout.addWidget(test_btn1)
        
        # زر اختبار الفاتورة رقم 7 (الأحدث)
        test_btn7 = QPushButton("📋 اختبار الفاتورة رقم 7")
        test_btn7.setStyleSheet(self.get_button_style("#27AE60"))
        test_btn7.clicked.connect(lambda: self.test_printing(7))
        layout.addWidget(test_btn7)
        
        # زر اختبار فاتورة عشوائية
        test_random_btn = QPushButton("🎲 اختبار فاتورة عشوائية")
        test_random_btn.setStyleSheet(self.get_button_style("#E67E22"))
        test_random_btn.clicked.connect(self.test_random_invoice)
        layout.addWidget(test_random_btn)
        
        # معلومات التصميم
        info_label = QPushButton("""
🎨 مميزات التصميم الجديد:
• ترويسة أنيقة مع اللوجو ومعلومات الشركة
• جدول منتجات أخضر جميل مع تدرجات
• تفاصيل الفاتورة في إطار أزرق منظم
• تصميم عصري ومتجاوب
• دعم طباعة A4 والرول
        """)
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background-color: #F8F9FA;
                color: #495057;
                font-size: 14px;
                padding: 15px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                text-align: left;
                margin: 10px 0;
            }
        """)
        layout.addWidget(info_label)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#E74C3C"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def get_button_style(self, color):
        """إرجاع نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
        """
    
    def test_printing(self, invoice_id):
        """اختبار طباعة فاتورة محددة"""
        try:
            show_new_design_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار الطباعة:\n{str(e)}")
    
    def test_random_invoice(self):
        """اختبار فاتورة عشوائية"""
        import random
        # اختيار رقم فاتورة عشوائي بين 1 و 10
        random_id = random.randint(1, 10)
        self.test_printing(random_id)

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم عام
    app.setStyleSheet("""
        QMainWindow {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        QWidget {
            font-family: 'Arial', 'Tahoma', sans-serif;
        }
    """)
    
    window = TestNewDesignWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
