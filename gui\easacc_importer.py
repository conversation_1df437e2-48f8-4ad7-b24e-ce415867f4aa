"""
أداة استيراد البيانات من برنامج EasAcc
"""

import os
import pandas as pd
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QFileDialog, QTableWidget, QTableWidgetItem,
                           QTabWidget, QWidget, QProgressBar, QTextEdit,
                           QMessageBox, QCheckBox, QGroupBox, QGridLayout,
                           QLineEdit, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from sqlalchemy.orm import sessionmaker
from database.models import Customer, Supplier, Product
import sqlite3

# محاولة استيراد pyodbc مع معالجة الخطأ
try:
    import pyodbc
    PYODBC_AVAILABLE = True
except ImportError:
    PYODBC_AVAILABLE = False
    print("تحذير: pyodbc غير متاح. سيتم دعم Excel و CSV فقط.")


class EasAccImporterDialog(QDialog):
    """نافذة استيراد البيانات من برنامج EasAcc"""
    
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.easacc_data = {}
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استيراد البيانات من برنامج EasAcc")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel("🔄 استيراد البيانات من برنامج EasAcc")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #3498db, stop:1 #2980b9);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # قسم اختيار قاعدة البيانات
        db_group = QGroupBox("📁 اختيار قاعدة بيانات EasAcc")
        db_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        db_layout = QGridLayout(db_group)
        
        # نوع قاعدة البيانات
        db_layout.addWidget(QLabel("نوع قاعدة البيانات:"), 0, 0)
        self.db_type_combo = QComboBox()
        self.db_type_combo.addItems([
            "Microsoft Access (.mdb/.accdb)",
            "SQL Server", 
            "Firebird (.fdb)",
            "Excel (.xlsx/.xls)",
            "CSV Files"
        ])
        db_layout.addWidget(self.db_type_combo, 0, 1)
        
        # مسار الملف
        db_layout.addWidget(QLabel("مسار قاعدة البيانات:"), 1, 0)
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setPlaceholderText("اختر ملف قاعدة البيانات...")
        db_layout.addWidget(self.db_path_edit, 1, 1)
        
        browse_btn = QPushButton("📂 استعراض")
        browse_btn.clicked.connect(self.browse_database)
        db_layout.addWidget(browse_btn, 1, 2)
        
        # زر الاتصال
        connect_btn = QPushButton("🔗 الاتصال بقاعدة البيانات")
        connect_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #229954, stop:1 #27ae60);
            }
        """)
        connect_btn.clicked.connect(self.connect_to_database)
        db_layout.addWidget(connect_btn, 2, 0, 1, 3)
        
        layout.addWidget(db_group)
        
        # تبويبات البيانات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #ddd;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
        """)
        
        # تبويب العملاء
        self.customers_tab = self.create_data_tab("العملاء", "customers")
        self.tabs.addTab(self.customers_tab, "👥 العملاء")
        
        # تبويب الموردين
        self.suppliers_tab = self.create_data_tab("الموردين", "suppliers")
        self.tabs.addTab(self.suppliers_tab, "🏢 الموردين")
        
        # تبويب المنتجات
        self.products_tab = self.create_data_tab("المنتجات", "products")
        self.tabs.addTab(self.products_tab, "📦 المنتجات")
        
        layout.addWidget(self.tabs)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة الرسائل
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                font-family: 'Courier New';
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # خيارات التعارض
        conflict_group = QGroupBox("⚠️ إدارة التعارضات")
        conflict_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(231, 76, 60, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #e74c3c;
            }
        """)
        conflict_layout = QGridLayout(conflict_group)

        # خيارات التعامل مع التعارضات
        conflict_layout.addWidget(QLabel("عند وجود بيانات مكررة:"), 0, 0)
        self.conflict_combo = QComboBox()
        self.conflict_combo.addItems([
            "تجاهل البيانات المكررة (الافتراضي)",
            "استبدال البيانات الموجودة",
            "دمج البيانات (الأرصدة والكميات)",
            "إظهار نافذة تأكيد لكل تعارض"
        ])
        conflict_layout.addWidget(self.conflict_combo, 0, 1)

        # خيار النسخ الاحتياطي
        self.backup_cb = QCheckBox("إنشاء نسخة احتياطية قبل الاستيراد")
        self.backup_cb.setChecked(True)
        self.backup_cb.setStyleSheet("color: #e74c3c; font-weight: bold;")
        conflict_layout.addWidget(self.backup_cb, 1, 0, 1, 2)

        layout.addWidget(conflict_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        import_btn = QPushButton("📥 استيراد البيانات المحددة")
        import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        import_btn.clicked.connect(self.import_selected_data)
        buttons_layout.addWidget(import_btn)
        
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_data_tab(self, title, data_type):
        """إنشاء تبويب لعرض البيانات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # خيارات الاستيراد
        options_layout = QHBoxLayout()
        
        select_all_cb = QCheckBox(f"تحديد جميع {title}")
        select_all_cb.setChecked(True)
        select_all_cb.stateChanged.connect(
            lambda state, dt=data_type: self.toggle_all_selection(dt, state)
        )
        options_layout.addWidget(select_all_cb)
        
        options_layout.addStretch()
        
        count_label = QLabel("0 عنصر")
        count_label.setObjectName(f"{data_type}_count")
        options_layout.addWidget(count_label)
        
        layout.addLayout(options_layout)
        
        # جدول البيانات
        table = QTableWidget()
        table.setObjectName(f"{data_type}_table")
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        layout.addWidget(table)
        
        return widget

    def update_customer_data(self, existing_customer, new_customer):
        """تحديث بيانات العميل الموجود"""
        existing_customer.phone = new_customer.phone or existing_customer.phone
        existing_customer.address = new_customer.address or existing_customer.address
        existing_customer.email = new_customer.email or existing_customer.email
        existing_customer.credit_limit = new_customer.credit_limit or existing_customer.credit_limit
        # لا نحدث الرصيد في وضع التحديث

    def merge_customer_data(self, existing_customer, new_customer):
        """دمج بيانات العميل (جمع الأرصدة)"""
        existing_customer.phone = new_customer.phone or existing_customer.phone
        existing_customer.address = new_customer.address or existing_customer.address
        existing_customer.email = new_customer.email or existing_customer.email
        existing_customer.credit_limit = max(new_customer.credit_limit or 0, existing_customer.credit_limit or 0)
        # جمع الأرصدة
        existing_customer.balance = (existing_customer.balance or 0) + (new_customer.balance or 0)

    def show_conflict_dialog(self, data_type, existing_name, new_data):
        """عرض نافذة تأكيد التعارض"""
        reply = QMessageBox.question(
            self, "تعارض في البيانات",
            f"يوجد {data_type} بنفس الاسم: {existing_name}\n\n"
            f"هل تريد استبدال البيانات الموجودة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes

    def browse_database(self):
        """استعراض ملف قاعدة البيانات"""
        db_type = self.db_type_combo.currentText()
        
        if "Access" in db_type:
            file_filter = "Access Database (*.mdb *.accdb)"
        elif "Excel" in db_type:
            file_filter = "Excel Files (*.xlsx *.xls)"
        elif "Firebird" in db_type:
            file_filter = "Firebird Database (*.fdb)"
        elif "CSV" in db_type:
            file_filter = "CSV Files (*.csv)"
        else:
            file_filter = "All Files (*.*)"
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف قاعدة البيانات", "", file_filter
        )
        
        if file_path:
            self.db_path_edit.setText(file_path)
            
    def log_message(self, message):
        """إضافة رسالة إلى سجل الأحداث"""
        self.log_text.append(f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}")
        
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات وتحميل البيانات"""
        db_path = self.db_path_edit.text().strip()
        if not db_path:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار ملف قاعدة البيانات")
            return
            
        if not os.path.exists(db_path):
            QMessageBox.critical(self, "خطأ", "الملف المحدد غير موجود")
            return
            
        self.log_message("بدء الاتصال بقاعدة البيانات...")
        
        try:
            db_type = self.db_type_combo.currentText()
            
            if "Access" in db_type:
                self.load_from_access(db_path)
            elif "Excel" in db_type:
                self.load_from_excel(db_path)
            elif "CSV" in db_type:
                self.load_from_csv(db_path)
            else:
                QMessageBox.warning(self, "تنبيه", "نوع قاعدة البيانات غير مدعوم حالياً")
                
        except Exception as e:
            self.log_message(f"خطأ في الاتصال: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات:\n{str(e)}")

    def load_from_access(self, db_path):
        """تحميل البيانات من قاعدة بيانات Access"""
        if not PYODBC_AVAILABLE:
            self.log_message("خطأ: مكتبة pyodbc غير متاحة")
            QMessageBox.critical(
                self, "خطأ",
                "مكتبة pyodbc غير مثبتة!\n\n"
                "لاستيراد بيانات Access، يرجى:\n"
                "1. تثبيت pyodbc: pip install pyodbc\n"
                "2. أو تصدير البيانات إلى Excel/CSV من برنامج EasAcc\n\n"
                "يمكنك استخدام Excel أو CSV كبديل."
            )
            return

        try:
            # محاولة الاتصال بقاعدة بيانات Access
            conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};"
            conn = pyodbc.connect(conn_str)

            self.log_message("تم الاتصال بقاعدة البيانات بنجاح")

            # تحميل العملاء
            self.load_customers_from_access(conn)

            # تحميل الموردين
            self.load_suppliers_from_access(conn)

            # تحميل المنتجات
            self.load_products_from_access(conn)

            conn.close()
            self.log_message("تم تحميل جميع البيانات بنجاح")

        except Exception as e:
            self.log_message(f"خطأ في تحميل بيانات Access: {str(e)}")
            QMessageBox.critical(
                self, "خطأ",
                f"فشل في الاتصال بقاعدة بيانات Access:\n{str(e)}\n\n"
                "تأكد من:\n"
                "1. تثبيت Microsoft Access Database Engine\n"
                "2. صحة مسار الملف\n"
                "3. أن الملف غير مفتوح في برنامج آخر"
            )

    def load_customers_from_access(self, conn):
        """تحميل العملاء من قاعدة بيانات Access"""
        try:
            # جداول العملاء المحتملة في EasAcc
            possible_tables = ['Customers', 'Customer', 'Clients', 'Client', 'العملاء']

            customers_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    customers_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول العملاء: {table_name}")
                    break
                except:
                    continue

            if customers_df is not None and not customers_df.empty:
                self.easacc_data['customers'] = customers_df
                self.populate_table('customers', customers_df)
                self.log_message(f"تم تحميل {len(customers_df)} عميل")
            else:
                self.log_message("لم يتم العثور على جدول العملاء")

        except Exception as e:
            self.log_message(f"خطأ في تحميل العملاء: {str(e)}")

    def load_suppliers_from_access(self, conn):
        """تحميل الموردين من قاعدة بيانات Access"""
        try:
            # جداول الموردين المحتملة في EasAcc
            possible_tables = ['Suppliers', 'Supplier', 'Vendors', 'Vendor', 'الموردين']

            suppliers_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    suppliers_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول الموردين: {table_name}")
                    break
                except:
                    continue

            if suppliers_df is not None and not suppliers_df.empty:
                self.easacc_data['suppliers'] = suppliers_df
                self.populate_table('suppliers', suppliers_df)
                self.log_message(f"تم تحميل {len(suppliers_df)} مورد")
            else:
                self.log_message("لم يتم العثور على جدول الموردين")

        except Exception as e:
            self.log_message(f"خطأ في تحميل الموردين: {str(e)}")

    def load_products_from_access(self, conn):
        """تحميل المنتجات من قاعدة بيانات Access"""
        try:
            # جداول المنتجات المحتملة في EasAcc
            possible_tables = ['Products', 'Product', 'Items', 'Item', 'الاصناف', 'المنتجات']

            products_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    products_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول المنتجات: {table_name}")
                    break
                except:
                    continue

            if products_df is not None and not products_df.empty:
                self.easacc_data['products'] = products_df
                self.populate_table('products', products_df)
                self.log_message(f"تم تحميل {len(products_df)} منتج")
            else:
                self.log_message("لم يتم العثور على جدول المنتجات")

        except Exception as e:
            self.log_message(f"خطأ في تحميل المنتجات: {str(e)}")

    def load_from_excel(self, file_path):
        """تحميل البيانات من ملف Excel"""
        try:
            # قراءة جميع الأوراق
            excel_file = pd.ExcelFile(file_path)
            self.log_message(f"تم العثور على {len(excel_file.sheet_names)} ورقة في الملف")

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)

                # تحديد نوع البيانات بناءً على اسم الورقة أو محتواها
                if any(keyword in sheet_name.lower() for keyword in ['customer', 'client', 'عميل', 'عملاء']):
                    self.easacc_data['customers'] = df
                    self.populate_table('customers', df)
                    self.log_message(f"تم تحميل {len(df)} عميل من ورقة {sheet_name}")

                elif any(keyword in sheet_name.lower() for keyword in ['supplier', 'vendor', 'مورد', 'موردين']):
                    self.easacc_data['suppliers'] = df
                    self.populate_table('suppliers', df)
                    self.log_message(f"تم تحميل {len(df)} مورد من ورقة {sheet_name}")

                elif any(keyword in sheet_name.lower() for keyword in ['product', 'item', 'منتج', 'صنف', 'اصناف']):
                    self.easacc_data['products'] = df
                    self.populate_table('products', df)
                    self.log_message(f"تم تحميل {len(df)} منتج من ورقة {sheet_name}")

        except Exception as e:
            self.log_message(f"خطأ في تحميل ملف Excel: {str(e)}")
            raise

    def load_from_csv(self, file_path):
        """تحميل البيانات من ملف CSV"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')

            # تحديد نوع البيانات بناءً على اسم الملف
            file_name = os.path.basename(file_path).lower()

            if any(keyword in file_name for keyword in ['customer', 'client', 'عميل', 'عملاء']):
                self.easacc_data['customers'] = df
                self.populate_table('customers', df)
                self.log_message(f"تم تحميل {len(df)} عميل من ملف CSV")

            elif any(keyword in file_name for keyword in ['supplier', 'vendor', 'مورد', 'موردين']):
                self.easacc_data['suppliers'] = df
                self.populate_table('suppliers', df)
                self.log_message(f"تم تحميل {len(df)} مورد من ملف CSV")

            elif any(keyword in file_name for keyword in ['product', 'item', 'منتج', 'صنف', 'اصناف']):
                self.easacc_data['products'] = df
                self.populate_table('products', df)
                self.log_message(f"تم تحميل {len(df)} منتج من ملف CSV")
            else:
                # إذا لم يتم تحديد النوع، اعرض خيارات للمستخدم
                self.show_data_type_selection(df)

        except Exception as e:
            self.log_message(f"خطأ في تحميل ملف CSV: {str(e)}")
            raise

    def populate_table(self, data_type, df):
        """ملء الجدول بالبيانات"""
        table = self.findChild(QTableWidget, f"{data_type}_table")
        count_label = self.findChild(QLabel, f"{data_type}_count")

        if table is None or df is None or df.empty:
            return

        # إعداد الجدول
        table.setRowCount(len(df))
        table.setColumnCount(len(df.columns))
        table.setHorizontalHeaderLabels(df.columns.tolist())

        # ملء البيانات
        for row in range(len(df)):
            for col in range(len(df.columns)):
                value = str(df.iloc[row, col]) if pd.notna(df.iloc[row, col]) else ""
                item = QTableWidgetItem(value)
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Checked)
                table.setItem(row, col, item)

        # تحديث العداد
        count_label.setText(f"{len(df)} عنصر")

        # تعديل حجم الأعمدة
        table.resizeColumnsToContents()

    def toggle_all_selection(self, data_type, state):
        """تحديد/إلغاء تحديد جميع العناصر"""
        table = self.findChild(QTableWidget, f"{data_type}_table")
        if table is None:
            return

        check_state = Qt.Checked if state == Qt.Checked else Qt.Unchecked

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item:
                item.setCheckState(check_state)

    def import_selected_data(self):
        """استيراد البيانات المحددة مع إدارة التعارضات"""
        try:
            # إنشاء نسخة احتياطية إذا كان مطلوب
            if self.backup_cb.isChecked():
                if not self.create_backup():
                    return

            Session = sessionmaker(bind=self.engine)

            with Session() as session:
                # استيراد العملاء
                if 'customers' in self.easacc_data:
                    self.log_message("بدء استيراد العملاء...")
                    self.import_customers(session)

                # استيراد الموردين
                if 'suppliers' in self.easacc_data:
                    self.log_message("بدء استيراد الموردين...")
                    self.import_suppliers(session)

                # استيراد المنتجات
                if 'products' in self.easacc_data:
                    self.log_message("بدء استيراد المنتجات...")
                    self.import_products(session)

                session.commit()

            self.log_message("تم استيراد جميع البيانات بنجاح!")
            QMessageBox.information(self, "نجح", "تم استيراد البيانات بنجاح!")

        except Exception as e:
            self.log_message(f"خطأ في الاستيراد: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في استيراد البيانات:\n{str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            from datetime import datetime

            # مسار قاعدة البيانات الحالية
            db_path = "accounting.db"

            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_before_import_{timestamp}.db"

            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)

            self.log_message(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True

        except Exception as e:
            self.log_message(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
            reply = QMessageBox.question(
                self, "تحذير",
                f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}\n\n"
                "هل تريد المتابعة بدون نسخة احتياطية؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            return reply == QMessageBox.Yes

    def import_customers(self, session):
        """استيراد العملاء مع إدارة التعارضات"""
        table = self.findChild(QTableWidget, "customers_table")
        df = self.easacc_data['customers']
        conflict_mode = self.conflict_combo.currentIndex()

        imported_count = 0
        updated_count = 0
        skipped_count = 0

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                try:
                    # استخراج بيانات العميل
                    customer_data = {}
                    for col in range(table.columnCount()):
                        header = table.horizontalHeaderItem(col).text()
                        value = table.item(row, col).text()
                        customer_data[header] = value

                    # تحويل البيانات إلى نموذج العميل
                    customer = self.convert_to_customer(customer_data)

                    # التحقق من وجود العميل مسبقاً
                    existing = session.query(Customer).filter(
                        Customer.name == customer.name
                    ).first()

                    if existing:
                        # معالجة التعارض
                        if conflict_mode == 0:  # تجاهل
                            skipped_count += 1
                            self.log_message(f"تم تجاهل العميل المكرر: {customer.name}")
                        elif conflict_mode == 1:  # استبدال
                            self.update_customer_data(existing, customer)
                            updated_count += 1
                            self.log_message(f"تم تحديث العميل: {customer.name}")
                        elif conflict_mode == 2:  # دمج
                            self.merge_customer_data(existing, customer)
                            updated_count += 1
                            self.log_message(f"تم دمج بيانات العميل: {customer.name}")
                        elif conflict_mode == 3:  # تأكيد
                            if self.show_conflict_dialog("عميل", existing.name, customer):
                                self.update_customer_data(existing, customer)
                                updated_count += 1
                            else:
                                skipped_count += 1
                    else:
                        session.add(customer)
                        imported_count += 1

                except Exception as e:
                    self.log_message(f"خطأ في استيراد العميل في الصف {row + 1}: {str(e)}")

        self.log_message(f"تم استيراد {imported_count} عميل جديد، تحديث {updated_count}، تجاهل {skipped_count}")

    def import_suppliers(self, session):
        """استيراد الموردين"""
        table = self.findChild(QTableWidget, "suppliers_table")
        df = self.easacc_data['suppliers']

        imported_count = 0

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                try:
                    # استخراج بيانات المورد
                    supplier_data = {}
                    for col in range(table.columnCount()):
                        header = table.horizontalHeaderItem(col).text()
                        value = table.item(row, col).text()
                        supplier_data[header] = value

                    # تحويل البيانات إلى نموذج المورد
                    supplier = self.convert_to_supplier(supplier_data)

                    # التحقق من عدم وجود المورد مسبقاً
                    existing = session.query(Supplier).filter(
                        Supplier.name == supplier.name
                    ).first()

                    if not existing:
                        session.add(supplier)
                        imported_count += 1

                except Exception as e:
                    self.log_message(f"خطأ في استيراد المورد في الصف {row + 1}: {str(e)}")

        self.log_message(f"تم استيراد {imported_count} مورد")

    def import_products(self, session):
        """استيراد المنتجات"""
        table = self.findChild(QTableWidget, "products_table")
        df = self.easacc_data['products']

        imported_count = 0

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                try:
                    # استخراج بيانات المنتج
                    product_data = {}
                    for col in range(table.columnCount()):
                        header = table.horizontalHeaderItem(col).text()
                        value = table.item(row, col).text()
                        product_data[header] = value

                    # تحويل البيانات إلى نموذج المنتج
                    product = self.convert_to_product(product_data)

                    # التحقق من عدم وجود المنتج مسبقاً
                    existing = session.query(Product).filter(
                        Product.name == product.name
                    ).first()

                    if not existing:
                        session.add(product)
                        imported_count += 1

                except Exception as e:
                    self.log_message(f"خطأ في استيراد المنتج في الصف {row + 1}: {str(e)}")

        self.log_message(f"تم استيراد {imported_count} منتج")

    def convert_to_customer(self, data):
        """تحويل بيانات العميل من EasAcc إلى نموذج العميل الجديد"""
        # خريطة الحقول المحتملة في EasAcc
        field_mapping = {
            'name': ['Name', 'CustomerName', 'Client_Name', 'اسم_العميل', 'الاسم'],
            'phone': ['Phone', 'Tel', 'Mobile', 'هاتف', 'تليفون', 'جوال'],
            'address': ['Address', 'عنوان', 'العنوان'],
            'email': ['Email', 'E_Mail', 'بريد_الكتروني'],
            'balance': ['Balance', 'رصيد', 'الرصيد', 'Current_Balance'],
            'credit_limit': ['Credit_Limit', 'حد_الائتمان', 'الحد_الائتماني']
        }

        customer = Customer()

        # تعيين القيم
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value:
                if field in ['balance', 'credit_limit']:
                    try:
                        setattr(customer, field, float(value.replace(',', '') if isinstance(value, str) else value))
                    except (ValueError, AttributeError):
                        setattr(customer, field, 0.0)
                else:
                    setattr(customer, field, str(value))

        # قيم افتراضية
        if not customer.name:
            customer.name = "عميل غير محدد"
        if not hasattr(customer, 'balance') or customer.balance is None:
            customer.balance = 0.0
        if not hasattr(customer, 'credit_limit') or customer.credit_limit is None:
            customer.credit_limit = 0.0

        return customer

    def convert_to_supplier(self, data):
        """تحويل بيانات المورد من EasAcc إلى نموذج المورد الجديد"""
        # خريطة الحقول المحتملة في EasAcc
        field_mapping = {
            'name': ['Name', 'SupplierName', 'Vendor_Name', 'اسم_المورد', 'الاسم'],
            'phone': ['Phone', 'Tel', 'Mobile', 'هاتف', 'تليفون', 'جوال'],
            'address': ['Address', 'عنوان', 'العنوان'],
            'email': ['Email', 'E_Mail', 'بريد_الكتروني'],
            'balance': ['Balance', 'رصيد', 'الرصيد', 'Current_Balance']
        }

        supplier = Supplier()

        # تعيين القيم
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value:
                if field == 'balance':
                    try:
                        setattr(supplier, field, float(value.replace(',', '') if isinstance(value, str) else value))
                    except (ValueError, AttributeError):
                        setattr(supplier, field, 0.0)
                else:
                    setattr(supplier, field, str(value))

        # قيم افتراضية
        if not supplier.name:
            supplier.name = "مورد غير محدد"
        if not hasattr(supplier, 'balance') or supplier.balance is None:
            supplier.balance = 0.0

        return supplier

    def convert_to_product(self, data):
        """تحويل بيانات المنتج من EasAcc إلى نموذج المنتج الجديد"""
        # خريطة الحقول المحتملة في EasAcc
        field_mapping = {
            'name': ['Name', 'ProductName', 'Item_Name', 'اسم_المنتج', 'الاسم', 'اسم_الصنف'],
            'code': ['Code', 'ProductCode', 'Item_Code', 'كود', 'الكود', 'كود_الصنف'],
            'description': ['Description', 'وصف', 'الوصف', 'تفاصيل'],
            'category': ['Category', 'فئة', 'الفئة', 'مجموعة'],
            'purchase_price': ['Purchase_Price', 'Cost', 'سعر_الشراء', 'التكلفة'],
            'sale_price': ['Sale_Price', 'Price', 'سعر_البيع', 'السعر'],
            'quantity': ['Quantity', 'Stock', 'كمية', 'الكمية', 'المخزون'],
            'unit': ['Unit', 'وحدة', 'الوحدة'],
            'barcode': ['Barcode', 'باركود', 'الباركود']
        }

        product = Product()

        # تعيين القيم
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value:
                if field in ['purchase_price', 'sale_price']:
                    try:
                        setattr(product, field, float(value.replace(',', '') if isinstance(value, str) else value))
                    except (ValueError, AttributeError):
                        setattr(product, field, 0.0)
                elif field == 'quantity':
                    try:
                        setattr(product, field, int(float(value.replace(',', '') if isinstance(value, str) else value)))
                    except (ValueError, AttributeError):
                        setattr(product, field, 0)
                else:
                    setattr(product, field, str(value))

        # قيم افتراضية
        if not product.name:
            product.name = "منتج غير محدد"
        if not product.code:
            product.code = f"PRD_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}"
        if not hasattr(product, 'purchase_price') or product.purchase_price is None:
            product.purchase_price = 0.0
        if not hasattr(product, 'sale_price') or product.sale_price is None:
            product.sale_price = 0.0
        if not hasattr(product, 'quantity') or product.quantity is None:
            product.quantity = 0
        if not product.unit:
            product.unit = "قطعة"

        return product

    def find_value_by_keys(self, data, possible_keys):
        """البحث عن قيمة باستخدام مفاتيح محتملة"""
        for key in possible_keys:
            if key in data and data[key] and str(data[key]).strip():
                return data[key]
        return None

    def show_data_type_selection(self, df):
        """عرض نافذة لاختيار نوع البيانات"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QComboBox, QPushButton, QHBoxLayout

        dialog = QDialog(self)
        dialog.setWindowTitle("تحديد نوع البيانات")
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        layout.addWidget(QLabel("الرجاء تحديد نوع البيانات في هذا الملف:"))

        combo = QComboBox()
        combo.addItems(["العملاء", "الموردين", "المنتجات"])
        layout.addWidget(combo)

        buttons_layout = QHBoxLayout()
        ok_btn = QPushButton("موافق")
        cancel_btn = QPushButton("إلغاء")

        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        if dialog.exec_() == QDialog.Accepted:
            data_type = combo.currentText()
            if data_type == "العملاء":
                self.easacc_data['customers'] = df
                self.populate_table('customers', df)
            elif data_type == "الموردين":
                self.easacc_data['suppliers'] = df
                self.populate_table('suppliers', df)
            elif data_type == "المنتجات":
                self.easacc_data['products'] = df
                self.populate_table('products', df)
