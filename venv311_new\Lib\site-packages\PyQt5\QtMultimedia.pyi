# The PEP 484 type hints stub file for the QtMultimedia module.
#
# Generated by SIP 6.7.7
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtNetwork

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., None], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt5.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt5.sip.Buffer, int, None]


class QAbstractVideoBuffer(PyQt5.sipsimplewrapper):

    class MapMode(int):
        NotMapped = ... # type: QAbstractVideoBuffer.MapMode
        ReadOnly = ... # type: QAbstractVideoBuffer.MapMode
        WriteOnly = ... # type: QAbstractVideoBuffer.MapMode
        ReadWrite = ... # type: QAbstractVideoBuffer.MapMode

    class HandleType(int):
        NoHandle = ... # type: QAbstractVideoBuffer.HandleType
        GLTextureHandle = ... # type: QAbstractVideoBuffer.HandleType
        XvShmImageHandle = ... # type: QAbstractVideoBuffer.HandleType
        CoreImageHandle = ... # type: QAbstractVideoBuffer.HandleType
        QPixmapHandle = ... # type: QAbstractVideoBuffer.HandleType
        EGLImageHandle = ... # type: QAbstractVideoBuffer.HandleType
        UserHandle = ... # type: QAbstractVideoBuffer.HandleType

    def __init__(self, type: 'QAbstractVideoBuffer.HandleType') -> None: ...

    def release(self) -> None: ...
    def handle(self) -> typing.Any: ...
    def unmap(self) -> None: ...
    def map(self, mode: 'QAbstractVideoBuffer.MapMode') -> typing.Tuple[PyQt5.sip.voidptr, int, int]: ...
    def mapMode(self) -> 'QAbstractVideoBuffer.MapMode': ...
    def handleType(self) -> 'QAbstractVideoBuffer.HandleType': ...


class QVideoFilterRunnable(PyQt5.sipsimplewrapper):

    class RunFlag(int):
        LastInChain = ... # type: QVideoFilterRunnable.RunFlag

    class RunFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __xor__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __ior__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __or__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __iand__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __and__(self, f: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFilterRunnable.RunFlags': ...
        def __invert__(self) -> 'QVideoFilterRunnable.RunFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVideoFilterRunnable') -> None: ...

    def run(self, input: 'QVideoFrame', surfaceFormat: 'QVideoSurfaceFormat', flags: typing.Union['QVideoFilterRunnable.RunFlags', 'QVideoFilterRunnable.RunFlag']) -> 'QVideoFrame': ...


class QAbstractVideoFilter(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def createFilterRunnable(self) -> QVideoFilterRunnable: ...
    def isActive(self) -> bool: ...


class QAbstractVideoSurface(QtCore.QObject):

    class Error(int):
        NoError = ... # type: QAbstractVideoSurface.Error
        UnsupportedFormatError = ... # type: QAbstractVideoSurface.Error
        IncorrectFormatError = ... # type: QAbstractVideoSurface.Error
        StoppedError = ... # type: QAbstractVideoSurface.Error
        ResourceError = ... # type: QAbstractVideoSurface.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    nativeResolutionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setNativeResolution(self, resolution: QtCore.QSize) -> None: ...
    def nativeResolution(self) -> QtCore.QSize: ...
    def setError(self, error: 'QAbstractVideoSurface.Error') -> None: ...
    supportedFormatsChanged: typing.ClassVar[QtCore.pyqtSignal]
    surfaceFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def error(self) -> 'QAbstractVideoSurface.Error': ...
    def present(self, frame: 'QVideoFrame') -> bool: ...
    def isActive(self) -> bool: ...
    def stop(self) -> None: ...
    def start(self, format: 'QVideoSurfaceFormat') -> bool: ...
    def surfaceFormat(self) -> 'QVideoSurfaceFormat': ...
    def nearestFormat(self, format: 'QVideoSurfaceFormat') -> 'QVideoSurfaceFormat': ...
    def isFormatSupported(self, format: 'QVideoSurfaceFormat') -> bool: ...
    def supportedPixelFormats(self, type: QAbstractVideoBuffer.HandleType = ...) -> typing.List['QVideoFrame.PixelFormat']: ...


class QAudio(PyQt5.sip.simplewrapper):

    class VolumeScale(int):
        LinearVolumeScale = ... # type: QAudio.VolumeScale
        CubicVolumeScale = ... # type: QAudio.VolumeScale
        LogarithmicVolumeScale = ... # type: QAudio.VolumeScale
        DecibelVolumeScale = ... # type: QAudio.VolumeScale

    class Role(int):
        UnknownRole = ... # type: QAudio.Role
        MusicRole = ... # type: QAudio.Role
        VideoRole = ... # type: QAudio.Role
        VoiceCommunicationRole = ... # type: QAudio.Role
        AlarmRole = ... # type: QAudio.Role
        NotificationRole = ... # type: QAudio.Role
        RingtoneRole = ... # type: QAudio.Role
        AccessibilityRole = ... # type: QAudio.Role
        SonificationRole = ... # type: QAudio.Role
        GameRole = ... # type: QAudio.Role
        CustomRole = ... # type: QAudio.Role

    class Mode(int):
        AudioInput = ... # type: QAudio.Mode
        AudioOutput = ... # type: QAudio.Mode

    class State(int):
        ActiveState = ... # type: QAudio.State
        SuspendedState = ... # type: QAudio.State
        StoppedState = ... # type: QAudio.State
        IdleState = ... # type: QAudio.State
        InterruptedState = ... # type: QAudio.State

    class Error(int):
        NoError = ... # type: QAudio.Error
        OpenError = ... # type: QAudio.Error
        IOError = ... # type: QAudio.Error
        UnderrunError = ... # type: QAudio.Error
        FatalError = ... # type: QAudio.Error

    def convertVolume(self, volume: float, from_: 'QAudio.VolumeScale', to: 'QAudio.VolumeScale') -> float: ...


class QAudioBuffer(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray], format: 'QAudioFormat', startTime: int = ...) -> None: ...
    @typing.overload
    def __init__(self, numFrames: int, format: 'QAudioFormat', startTime: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioBuffer') -> None: ...

    def data(self) -> PyQt5.sip.voidptr: ...
    def constData(self) -> PyQt5.sip.voidptr: ...
    def startTime(self) -> int: ...
    def duration(self) -> int: ...
    def byteCount(self) -> int: ...
    def sampleCount(self) -> int: ...
    def frameCount(self) -> int: ...
    def format(self) -> 'QAudioFormat': ...
    def isValid(self) -> bool: ...


class QMediaObject(QtCore.QObject):

    def __init__(self, parent: QtCore.QObject, service: 'QMediaService') -> None: ...

    def removePropertyWatch(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def addPropertyWatch(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    availabilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    notifyIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    def availableMetaData(self) -> typing.List[str]: ...
    def metaData(self, key: str) -> typing.Any: ...
    def isMetaDataAvailable(self) -> bool: ...
    def unbind(self, a0: QtCore.QObject) -> None: ...
    def bind(self, a0: QtCore.QObject) -> bool: ...
    def setNotifyInterval(self, milliSeconds: int) -> None: ...
    def notifyInterval(self) -> int: ...
    def service(self) -> 'QMediaService': ...
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...
    def isAvailable(self) -> bool: ...


class QAudioDecoder(QMediaObject):

    class Error(int):
        NoError = ... # type: QAudioDecoder.Error
        ResourceError = ... # type: QAudioDecoder.Error
        FormatError = ... # type: QAudioDecoder.Error
        AccessDeniedError = ... # type: QAudioDecoder.Error
        ServiceMissingError = ... # type: QAudioDecoder.Error

    class State(int):
        StoppedState = ... # type: QAudioDecoder.State
        DecodingState = ... # type: QAudioDecoder.State

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def unbind(self, a0: QtCore.QObject) -> None: ...
    def bind(self, a0: QtCore.QObject) -> bool: ...
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    formatChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    bufferReady: typing.ClassVar[QtCore.pyqtSignal]
    bufferAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def duration(self) -> int: ...
    def position(self) -> int: ...
    def bufferAvailable(self) -> bool: ...
    def read(self) -> QAudioBuffer: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def setAudioFormat(self, format: 'QAudioFormat') -> None: ...
    def audioFormat(self) -> 'QAudioFormat': ...
    def setSourceDevice(self, device: QtCore.QIODevice) -> None: ...
    def sourceDevice(self) -> QtCore.QIODevice: ...
    def setSourceFilename(self, fileName: str) -> None: ...
    def sourceFilename(self) -> str: ...
    def state(self) -> 'QAudioDecoder.State': ...
    @staticmethod
    def hasSupport(mimeType: str, codecs: typing.Iterable[str] = ...) -> 'QMultimedia.SupportEstimate': ...


class QMediaControl(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...


class QAudioDecoderControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    bufferAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    bufferReady: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    formatChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def duration(self) -> int: ...
    def position(self) -> int: ...
    def bufferAvailable(self) -> bool: ...
    def read(self) -> QAudioBuffer: ...
    def setAudioFormat(self, format: 'QAudioFormat') -> None: ...
    def audioFormat(self) -> 'QAudioFormat': ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setSourceDevice(self, device: QtCore.QIODevice) -> None: ...
    def sourceDevice(self) -> QtCore.QIODevice: ...
    def setSourceFilename(self, fileName: str) -> None: ...
    def sourceFilename(self) -> str: ...
    def state(self) -> QAudioDecoder.State: ...


class QAudioDeviceInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioDeviceInfo') -> None: ...

    def realm(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def supportedChannelCounts(self) -> typing.List[int]: ...
    def supportedSampleRates(self) -> typing.List[int]: ...
    @staticmethod
    def availableDevices(mode: QAudio.Mode) -> typing.List['QAudioDeviceInfo']: ...
    @staticmethod
    def defaultOutputDevice() -> 'QAudioDeviceInfo': ...
    @staticmethod
    def defaultInputDevice() -> 'QAudioDeviceInfo': ...
    def supportedSampleTypes(self) -> typing.List['QAudioFormat.SampleType']: ...
    def supportedByteOrders(self) -> typing.List['QAudioFormat.Endian']: ...
    def supportedSampleSizes(self) -> typing.List[int]: ...
    def supportedCodecs(self) -> typing.List[str]: ...
    def nearestFormat(self, format: 'QAudioFormat') -> 'QAudioFormat': ...
    def preferredFormat(self) -> 'QAudioFormat': ...
    def isFormatSupported(self, format: 'QAudioFormat') -> bool: ...
    def deviceName(self) -> str: ...
    def isNull(self) -> bool: ...


class QAudioEncoderSettingsControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setAudioSettings(self, settings: 'QAudioEncoderSettings') -> None: ...
    def audioSettings(self) -> 'QAudioEncoderSettings': ...
    def supportedSampleRates(self, settings: 'QAudioEncoderSettings') -> typing.Tuple[typing.List[int], bool]: ...
    def codecDescription(self, codecName: str) -> str: ...
    def supportedAudioCodecs(self) -> typing.List[str]: ...


class QAudioFormat(PyQt5.sipsimplewrapper):

    class Endian(int):
        BigEndian = ... # type: QAudioFormat.Endian
        LittleEndian = ... # type: QAudioFormat.Endian

    class SampleType(int):
        Unknown = ... # type: QAudioFormat.SampleType
        SignedInt = ... # type: QAudioFormat.SampleType
        UnSignedInt = ... # type: QAudioFormat.SampleType
        Float = ... # type: QAudioFormat.SampleType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioFormat') -> None: ...

    def bytesPerFrame(self) -> int: ...
    def durationForFrames(self, frameCount: int) -> int: ...
    def framesForDuration(self, duration: int) -> int: ...
    def framesForBytes(self, byteCount: int) -> int: ...
    def bytesForFrames(self, frameCount: int) -> int: ...
    def durationForBytes(self, byteCount: int) -> int: ...
    def bytesForDuration(self, duration: int) -> int: ...
    def channelCount(self) -> int: ...
    def setChannelCount(self, channelCount: int) -> None: ...
    def sampleRate(self) -> int: ...
    def setSampleRate(self, sampleRate: int) -> None: ...
    def sampleType(self) -> 'QAudioFormat.SampleType': ...
    def setSampleType(self, sampleType: 'QAudioFormat.SampleType') -> None: ...
    def byteOrder(self) -> 'QAudioFormat.Endian': ...
    def setByteOrder(self, byteOrder: 'QAudioFormat.Endian') -> None: ...
    def codec(self) -> str: ...
    def setCodec(self, codec: str) -> None: ...
    def sampleSize(self) -> int: ...
    def setSampleSize(self, sampleSize: int) -> None: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QAudioInput(QtCore.QObject):

    @typing.overload
    def __init__(self, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, audioDevice: QAudioDeviceInfo, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def volume(self) -> float: ...
    def setVolume(self, volume: float) -> None: ...
    notify: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> QAudio.State: ...
    def error(self) -> QAudio.Error: ...
    def elapsedUSecs(self) -> int: ...
    def processedUSecs(self) -> int: ...
    def notifyInterval(self) -> int: ...
    def setNotifyInterval(self, milliSeconds: int) -> None: ...
    def periodSize(self) -> int: ...
    def bytesReady(self) -> int: ...
    def bufferSize(self) -> int: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def resume(self) -> None: ...
    def suspend(self) -> None: ...
    def reset(self) -> None: ...
    def stop(self) -> None: ...
    @typing.overload
    def start(self, device: QtCore.QIODevice) -> None: ...
    @typing.overload
    def start(self) -> QtCore.QIODevice: ...
    def format(self) -> QAudioFormat: ...


class QAudioInputSelectorControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    availableInputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeInputChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setActiveInput(self, name: str) -> None: ...
    def activeInput(self) -> str: ...
    def defaultInput(self) -> str: ...
    def inputDescription(self, name: str) -> str: ...
    def availableInputs(self) -> typing.List[str]: ...


class QAudioOutput(QtCore.QObject):

    @typing.overload
    def __init__(self, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, audioDevice: QAudioDeviceInfo, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setCategory(self, category: str) -> None: ...
    def category(self) -> str: ...
    def volume(self) -> float: ...
    def setVolume(self, a0: float) -> None: ...
    notify: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> QAudio.State: ...
    def error(self) -> QAudio.Error: ...
    def elapsedUSecs(self) -> int: ...
    def processedUSecs(self) -> int: ...
    def notifyInterval(self) -> int: ...
    def setNotifyInterval(self, milliSeconds: int) -> None: ...
    def periodSize(self) -> int: ...
    def bytesFree(self) -> int: ...
    def bufferSize(self) -> int: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def resume(self) -> None: ...
    def suspend(self) -> None: ...
    def reset(self) -> None: ...
    def stop(self) -> None: ...
    @typing.overload
    def start(self, device: QtCore.QIODevice) -> None: ...
    @typing.overload
    def start(self) -> QtCore.QIODevice: ...
    def format(self) -> QAudioFormat: ...


class QAudioOutputSelectorControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    availableOutputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeOutputChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setActiveOutput(self, name: str) -> None: ...
    def activeOutput(self) -> str: ...
    def defaultOutput(self) -> str: ...
    def outputDescription(self, name: str) -> str: ...
    def availableOutputs(self) -> typing.List[str]: ...


class QAudioProbe(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    flush: typing.ClassVar[QtCore.pyqtSignal]
    audioBufferProbed: typing.ClassVar[QtCore.pyqtSignal]
    def isActive(self) -> bool: ...
    @typing.overload
    def setSource(self, source: QMediaObject) -> bool: ...
    @typing.overload
    def setSource(self, source: 'QMediaRecorder') -> bool: ...


class QMediaBindableInterface(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMediaBindableInterface') -> None: ...

    def setMediaObject(self, object: QMediaObject) -> bool: ...
    def mediaObject(self) -> QMediaObject: ...


class QMediaRecorder(QtCore.QObject, QMediaBindableInterface):

    class Error(int):
        NoError = ... # type: QMediaRecorder.Error
        ResourceError = ... # type: QMediaRecorder.Error
        FormatError = ... # type: QMediaRecorder.Error
        OutOfSpaceError = ... # type: QMediaRecorder.Error

    class Status(int):
        UnavailableStatus = ... # type: QMediaRecorder.Status
        UnloadedStatus = ... # type: QMediaRecorder.Status
        LoadingStatus = ... # type: QMediaRecorder.Status
        LoadedStatus = ... # type: QMediaRecorder.Status
        StartingStatus = ... # type: QMediaRecorder.Status
        RecordingStatus = ... # type: QMediaRecorder.Status
        PausedStatus = ... # type: QMediaRecorder.Status
        FinalizingStatus = ... # type: QMediaRecorder.Status

    class State(int):
        StoppedState = ... # type: QMediaRecorder.State
        RecordingState = ... # type: QMediaRecorder.State
        PausedState = ... # type: QMediaRecorder.State

    def __init__(self, mediaObject: QMediaObject, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMediaObject(self, object: QMediaObject) -> bool: ...
    availabilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataWritableChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    actualLocationChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setVolume(self, volume: float) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def stop(self) -> None: ...
    def pause(self) -> None: ...
    def record(self) -> None: ...
    def availableMetaData(self) -> typing.List[str]: ...
    def setMetaData(self, key: str, value: typing.Any) -> None: ...
    def metaData(self, key: str) -> typing.Any: ...
    def isMetaDataWritable(self) -> bool: ...
    def isMetaDataAvailable(self) -> bool: ...
    def setEncodingSettings(self, audio: 'QAudioEncoderSettings', video: 'QVideoEncoderSettings' = ..., container: str = ...) -> None: ...
    def setContainerFormat(self, container: str) -> None: ...
    def setVideoSettings(self, videoSettings: 'QVideoEncoderSettings') -> None: ...
    def setAudioSettings(self, audioSettings: 'QAudioEncoderSettings') -> None: ...
    def containerFormat(self) -> str: ...
    def videoSettings(self) -> 'QVideoEncoderSettings': ...
    def audioSettings(self) -> 'QAudioEncoderSettings': ...
    def supportedFrameRates(self, settings: 'QVideoEncoderSettings' = ...) -> typing.Tuple[typing.List[float], bool]: ...
    def supportedResolutions(self, settings: 'QVideoEncoderSettings' = ...) -> typing.Tuple[typing.List[QtCore.QSize], bool]: ...
    def videoCodecDescription(self, codecName: str) -> str: ...
    def supportedVideoCodecs(self) -> typing.List[str]: ...
    def supportedAudioSampleRates(self, settings: 'QAudioEncoderSettings' = ...) -> typing.Tuple[typing.List[int], bool]: ...
    def audioCodecDescription(self, codecName: str) -> str: ...
    def supportedAudioCodecs(self) -> typing.List[str]: ...
    def containerDescription(self, format: str) -> str: ...
    def supportedContainers(self) -> typing.List[str]: ...
    def volume(self) -> float: ...
    def isMuted(self) -> bool: ...
    def duration(self) -> int: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def status(self) -> 'QMediaRecorder.Status': ...
    def state(self) -> 'QMediaRecorder.State': ...
    def actualLocation(self) -> QtCore.QUrl: ...
    def setOutputLocation(self, location: QtCore.QUrl) -> bool: ...
    def outputLocation(self) -> QtCore.QUrl: ...
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...
    def isAvailable(self) -> bool: ...
    def mediaObject(self) -> QMediaObject: ...


class QAudioRecorder(QMediaRecorder):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    availableAudioInputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioInputChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setAudioInput(self, name: str) -> None: ...
    def audioInput(self) -> str: ...
    def audioInputDescription(self, name: str) -> str: ...
    def defaultAudioInput(self) -> str: ...
    def audioInputs(self) -> typing.List[str]: ...


class QAudioRoleControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    audioRoleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def supportedAudioRoles(self) -> typing.List[QAudio.Role]: ...
    def setAudioRole(self, role: QAudio.Role) -> None: ...
    def audioRole(self) -> QAudio.Role: ...


class QCamera(QMediaObject):

    class Position(int):
        UnspecifiedPosition = ... # type: QCamera.Position
        BackFace = ... # type: QCamera.Position
        FrontFace = ... # type: QCamera.Position

    class LockType(int):
        NoLock = ... # type: QCamera.LockType
        LockExposure = ... # type: QCamera.LockType
        LockWhiteBalance = ... # type: QCamera.LockType
        LockFocus = ... # type: QCamera.LockType

    class LockChangeReason(int):
        UserRequest = ... # type: QCamera.LockChangeReason
        LockAcquired = ... # type: QCamera.LockChangeReason
        LockFailed = ... # type: QCamera.LockChangeReason
        LockLost = ... # type: QCamera.LockChangeReason
        LockTemporaryLost = ... # type: QCamera.LockChangeReason

    class LockStatus(int):
        Unlocked = ... # type: QCamera.LockStatus
        Searching = ... # type: QCamera.LockStatus
        Locked = ... # type: QCamera.LockStatus

    class Error(int):
        NoError = ... # type: QCamera.Error
        CameraError = ... # type: QCamera.Error
        InvalidRequestError = ... # type: QCamera.Error
        ServiceMissingError = ... # type: QCamera.Error
        NotSupportedFeatureError = ... # type: QCamera.Error

    class CaptureMode(int):
        CaptureViewfinder = ... # type: QCamera.CaptureMode
        CaptureStillImage = ... # type: QCamera.CaptureMode
        CaptureVideo = ... # type: QCamera.CaptureMode

    class State(int):
        UnloadedState = ... # type: QCamera.State
        LoadedState = ... # type: QCamera.State
        ActiveState = ... # type: QCamera.State

    class Status(int):
        UnavailableStatus = ... # type: QCamera.Status
        UnloadedStatus = ... # type: QCamera.Status
        LoadingStatus = ... # type: QCamera.Status
        UnloadingStatus = ... # type: QCamera.Status
        LoadedStatus = ... # type: QCamera.Status
        StandbyStatus = ... # type: QCamera.Status
        StartingStatus = ... # type: QCamera.Status
        StoppingStatus = ... # type: QCamera.Status
        ActiveStatus = ... # type: QCamera.Status

    class CaptureModes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __xor__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __ior__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __or__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __iand__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __and__(self, f: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> 'QCamera.CaptureModes': ...
        def __invert__(self) -> 'QCamera.CaptureModes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class LockTypes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __xor__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __ior__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __or__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __iand__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __and__(self, f: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> 'QCamera.LockTypes': ...
        def __invert__(self) -> 'QCamera.LockTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FrameRateRange(PyQt5.sipsimplewrapper):

        maximumFrameRate = ... # type: float
        minimumFrameRate = ... # type: float

        @typing.overload
        def __init__(self, minimum: float, maximum: float) -> None: ...
        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QCamera.FrameRateRange') -> None: ...

        def __eq__(self, other: object): ...
        def __ne__(self, other: object): ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Union[QtCore.QByteArray, bytes, bytearray], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, cameraInfo: 'QCameraInfo', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, position: 'QCamera.Position', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def supportedViewfinderPixelFormats(self, settings: 'QCameraViewfinderSettings' = ...) -> typing.List['QVideoFrame.PixelFormat']: ...
    def supportedViewfinderFrameRateRanges(self, settings: 'QCameraViewfinderSettings' = ...) -> typing.List['QCamera.FrameRateRange']: ...
    def supportedViewfinderResolutions(self, settings: 'QCameraViewfinderSettings' = ...) -> typing.List[QtCore.QSize]: ...
    def supportedViewfinderSettings(self, settings: 'QCameraViewfinderSettings' = ...) -> typing.List['QCameraViewfinderSettings']: ...
    def setViewfinderSettings(self, settings: 'QCameraViewfinderSettings') -> None: ...
    def viewfinderSettings(self) -> 'QCameraViewfinderSettings': ...
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    lockStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    lockFailed: typing.ClassVar[QtCore.pyqtSignal]
    locked: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    captureModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def unlock(self) -> None: ...
    @typing.overload
    def unlock(self, locks: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> None: ...
    @typing.overload
    def searchAndLock(self) -> None: ...
    @typing.overload
    def searchAndLock(self, locks: typing.Union['QCamera.LockTypes', 'QCamera.LockType']) -> None: ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def unload(self) -> None: ...
    def load(self) -> None: ...
    def setCaptureMode(self, mode: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> None: ...
    @typing.overload
    def lockStatus(self) -> 'QCamera.LockStatus': ...
    @typing.overload
    def lockStatus(self, lock: 'QCamera.LockType') -> 'QCamera.LockStatus': ...
    def requestedLocks(self) -> 'QCamera.LockTypes': ...
    def supportedLocks(self) -> 'QCamera.LockTypes': ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def setViewfinder(self, viewfinder: QVideoWidget) -> None: ...
    @typing.overload
    def setViewfinder(self, viewfinder: QGraphicsVideoItem) -> None: ...
    @typing.overload
    def setViewfinder(self, surface: QAbstractVideoSurface) -> None: ...
    def imageProcessing(self) -> 'QCameraImageProcessing': ...
    def focus(self) -> 'QCameraFocus': ...
    def exposure(self) -> 'QCameraExposure': ...
    def isCaptureModeSupported(self, mode: typing.Union['QCamera.CaptureModes', 'QCamera.CaptureMode']) -> bool: ...
    def captureMode(self) -> 'QCamera.CaptureModes': ...
    def status(self) -> 'QCamera.Status': ...
    def state(self) -> 'QCamera.State': ...
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...
    @staticmethod
    def deviceDescription(device: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> str: ...
    @staticmethod
    def availableDevices() -> typing.List[QtCore.QByteArray]: ...


class QCameraCaptureBufferFormatControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    bufferFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setBufferFormat(self, format: 'QVideoFrame.PixelFormat') -> None: ...
    def bufferFormat(self) -> 'QVideoFrame.PixelFormat': ...
    def supportedBufferFormats(self) -> typing.List['QVideoFrame.PixelFormat']: ...


class QCameraCaptureDestinationControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    captureDestinationChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setCaptureDestination(self, destination: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> None: ...
    def captureDestination(self) -> 'QCameraImageCapture.CaptureDestinations': ...
    def isCaptureDestinationSupported(self, destination: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> bool: ...


class QCameraControl(QMediaControl):

    class PropertyChangeType(int):
        CaptureMode = ... # type: QCameraControl.PropertyChangeType
        ImageEncodingSettings = ... # type: QCameraControl.PropertyChangeType
        VideoEncodingSettings = ... # type: QCameraControl.PropertyChangeType
        Viewfinder = ... # type: QCameraControl.PropertyChangeType
        ViewfinderSettings = ... # type: QCameraControl.PropertyChangeType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    captureModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def canChangeProperty(self, changeType: 'QCameraControl.PropertyChangeType', status: QCamera.Status) -> bool: ...
    def isCaptureModeSupported(self, mode: typing.Union[QCamera.CaptureModes, QCamera.CaptureMode]) -> bool: ...
    def setCaptureMode(self, a0: typing.Union[QCamera.CaptureModes, QCamera.CaptureMode]) -> None: ...
    def captureMode(self) -> QCamera.CaptureModes: ...
    def status(self) -> QCamera.Status: ...
    def setState(self, state: QCamera.State) -> None: ...
    def state(self) -> QCamera.State: ...


class QCameraExposure(QtCore.QObject):

    class MeteringMode(int):
        MeteringMatrix = ... # type: QCameraExposure.MeteringMode
        MeteringAverage = ... # type: QCameraExposure.MeteringMode
        MeteringSpot = ... # type: QCameraExposure.MeteringMode

    class ExposureMode(int):
        ExposureAuto = ... # type: QCameraExposure.ExposureMode
        ExposureManual = ... # type: QCameraExposure.ExposureMode
        ExposurePortrait = ... # type: QCameraExposure.ExposureMode
        ExposureNight = ... # type: QCameraExposure.ExposureMode
        ExposureBacklight = ... # type: QCameraExposure.ExposureMode
        ExposureSpotlight = ... # type: QCameraExposure.ExposureMode
        ExposureSports = ... # type: QCameraExposure.ExposureMode
        ExposureSnow = ... # type: QCameraExposure.ExposureMode
        ExposureBeach = ... # type: QCameraExposure.ExposureMode
        ExposureLargeAperture = ... # type: QCameraExposure.ExposureMode
        ExposureSmallAperture = ... # type: QCameraExposure.ExposureMode
        ExposureAction = ... # type: QCameraExposure.ExposureMode
        ExposureLandscape = ... # type: QCameraExposure.ExposureMode
        ExposureNightPortrait = ... # type: QCameraExposure.ExposureMode
        ExposureTheatre = ... # type: QCameraExposure.ExposureMode
        ExposureSunset = ... # type: QCameraExposure.ExposureMode
        ExposureSteadyPhoto = ... # type: QCameraExposure.ExposureMode
        ExposureFireworks = ... # type: QCameraExposure.ExposureMode
        ExposureParty = ... # type: QCameraExposure.ExposureMode
        ExposureCandlelight = ... # type: QCameraExposure.ExposureMode
        ExposureBarcode = ... # type: QCameraExposure.ExposureMode
        ExposureModeVendor = ... # type: QCameraExposure.ExposureMode

    class FlashMode(int):
        FlashAuto = ... # type: QCameraExposure.FlashMode
        FlashOff = ... # type: QCameraExposure.FlashMode
        FlashOn = ... # type: QCameraExposure.FlashMode
        FlashRedEyeReduction = ... # type: QCameraExposure.FlashMode
        FlashFill = ... # type: QCameraExposure.FlashMode
        FlashTorch = ... # type: QCameraExposure.FlashMode
        FlashVideoLight = ... # type: QCameraExposure.FlashMode
        FlashSlowSyncFrontCurtain = ... # type: QCameraExposure.FlashMode
        FlashSlowSyncRearCurtain = ... # type: QCameraExposure.FlashMode
        FlashManual = ... # type: QCameraExposure.FlashMode

    class FlashModes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __xor__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __ior__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __or__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __iand__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __and__(self, f: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> 'QCameraExposure.FlashModes': ...
        def __invert__(self) -> 'QCameraExposure.FlashModes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    exposureCompensationChanged: typing.ClassVar[QtCore.pyqtSignal]
    isoSensitivityChanged: typing.ClassVar[QtCore.pyqtSignal]
    shutterSpeedRangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    shutterSpeedChanged: typing.ClassVar[QtCore.pyqtSignal]
    apertureRangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    apertureChanged: typing.ClassVar[QtCore.pyqtSignal]
    flashReady: typing.ClassVar[QtCore.pyqtSignal]
    def setAutoShutterSpeed(self) -> None: ...
    def setManualShutterSpeed(self, seconds: float) -> None: ...
    def setAutoAperture(self) -> None: ...
    def setManualAperture(self, aperture: float) -> None: ...
    def setAutoIsoSensitivity(self) -> None: ...
    def setManualIsoSensitivity(self, iso: int) -> None: ...
    def setExposureCompensation(self, ev: float) -> None: ...
    def setMeteringMode(self, mode: 'QCameraExposure.MeteringMode') -> None: ...
    def setExposureMode(self, mode: 'QCameraExposure.ExposureMode') -> None: ...
    def setFlashMode(self, mode: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> None: ...
    def supportedShutterSpeeds(self) -> typing.Tuple[typing.List[float], bool]: ...
    def supportedApertures(self) -> typing.Tuple[typing.List[float], bool]: ...
    def supportedIsoSensitivities(self) -> typing.Tuple[typing.List[int], bool]: ...
    def requestedShutterSpeed(self) -> float: ...
    def requestedAperture(self) -> float: ...
    def requestedIsoSensitivity(self) -> int: ...
    def shutterSpeed(self) -> float: ...
    def aperture(self) -> float: ...
    def isoSensitivity(self) -> int: ...
    def setSpotMeteringPoint(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def spotMeteringPoint(self) -> QtCore.QPointF: ...
    def isMeteringModeSupported(self, mode: 'QCameraExposure.MeteringMode') -> bool: ...
    def meteringMode(self) -> 'QCameraExposure.MeteringMode': ...
    def exposureCompensation(self) -> float: ...
    def isExposureModeSupported(self, mode: 'QCameraExposure.ExposureMode') -> bool: ...
    def exposureMode(self) -> 'QCameraExposure.ExposureMode': ...
    def isFlashReady(self) -> bool: ...
    def isFlashModeSupported(self, mode: typing.Union['QCameraExposure.FlashModes', 'QCameraExposure.FlashMode']) -> bool: ...
    def flashMode(self) -> 'QCameraExposure.FlashModes': ...
    def isAvailable(self) -> bool: ...


class QCameraExposureControl(QMediaControl):

    class ExposureParameter(int):
        ISO = ... # type: QCameraExposureControl.ExposureParameter
        Aperture = ... # type: QCameraExposureControl.ExposureParameter
        ShutterSpeed = ... # type: QCameraExposureControl.ExposureParameter
        ExposureCompensation = ... # type: QCameraExposureControl.ExposureParameter
        FlashPower = ... # type: QCameraExposureControl.ExposureParameter
        FlashCompensation = ... # type: QCameraExposureControl.ExposureParameter
        TorchPower = ... # type: QCameraExposureControl.ExposureParameter
        SpotMeteringPoint = ... # type: QCameraExposureControl.ExposureParameter
        ExposureMode = ... # type: QCameraExposureControl.ExposureParameter
        MeteringMode = ... # type: QCameraExposureControl.ExposureParameter
        ExtendedExposureParameter = ... # type: QCameraExposureControl.ExposureParameter

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    parameterRangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    actualValueChanged: typing.ClassVar[QtCore.pyqtSignal]
    requestedValueChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setValue(self, parameter: 'QCameraExposureControl.ExposureParameter', value: typing.Any) -> bool: ...
    def actualValue(self, parameter: 'QCameraExposureControl.ExposureParameter') -> typing.Any: ...
    def requestedValue(self, parameter: 'QCameraExposureControl.ExposureParameter') -> typing.Any: ...
    def supportedParameterRange(self, parameter: 'QCameraExposureControl.ExposureParameter') -> typing.Tuple[typing.List[typing.Any], bool]: ...
    def isParameterSupported(self, parameter: 'QCameraExposureControl.ExposureParameter') -> bool: ...


class QCameraFeedbackControl(QMediaControl):

    class EventType(int):
        ViewfinderStarted = ... # type: QCameraFeedbackControl.EventType
        ViewfinderStopped = ... # type: QCameraFeedbackControl.EventType
        ImageCaptured = ... # type: QCameraFeedbackControl.EventType
        ImageSaved = ... # type: QCameraFeedbackControl.EventType
        ImageError = ... # type: QCameraFeedbackControl.EventType
        RecordingStarted = ... # type: QCameraFeedbackControl.EventType
        RecordingInProgress = ... # type: QCameraFeedbackControl.EventType
        RecordingStopped = ... # type: QCameraFeedbackControl.EventType
        AutoFocusInProgress = ... # type: QCameraFeedbackControl.EventType
        AutoFocusLocked = ... # type: QCameraFeedbackControl.EventType
        AutoFocusFailed = ... # type: QCameraFeedbackControl.EventType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setEventFeedbackSound(self, a0: 'QCameraFeedbackControl.EventType', filePath: str) -> bool: ...
    def resetEventFeedback(self, a0: 'QCameraFeedbackControl.EventType') -> None: ...
    def setEventFeedbackEnabled(self, a0: 'QCameraFeedbackControl.EventType', a1: bool) -> bool: ...
    def isEventFeedbackEnabled(self, a0: 'QCameraFeedbackControl.EventType') -> bool: ...
    def isEventFeedbackLocked(self, a0: 'QCameraFeedbackControl.EventType') -> bool: ...


class QCameraFlashControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    flashReady: typing.ClassVar[QtCore.pyqtSignal]
    def isFlashReady(self) -> bool: ...
    def isFlashModeSupported(self, mode: typing.Union[QCameraExposure.FlashModes, QCameraExposure.FlashMode]) -> bool: ...
    def setFlashMode(self, mode: typing.Union[QCameraExposure.FlashModes, QCameraExposure.FlashMode]) -> None: ...
    def flashMode(self) -> QCameraExposure.FlashModes: ...


class QCameraFocusZone(PyQt5.sipsimplewrapper):

    class FocusZoneStatus(int):
        Invalid = ... # type: QCameraFocusZone.FocusZoneStatus
        Unused = ... # type: QCameraFocusZone.FocusZoneStatus
        Selected = ... # type: QCameraFocusZone.FocusZoneStatus
        Focused = ... # type: QCameraFocusZone.FocusZoneStatus

    def __init__(self, other: 'QCameraFocusZone') -> None: ...

    def status(self) -> 'QCameraFocusZone.FocusZoneStatus': ...
    def area(self) -> QtCore.QRectF: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QCameraFocus(QtCore.QObject):

    class FocusPointMode(int):
        FocusPointAuto = ... # type: QCameraFocus.FocusPointMode
        FocusPointCenter = ... # type: QCameraFocus.FocusPointMode
        FocusPointFaceDetection = ... # type: QCameraFocus.FocusPointMode
        FocusPointCustom = ... # type: QCameraFocus.FocusPointMode

    class FocusMode(int):
        ManualFocus = ... # type: QCameraFocus.FocusMode
        HyperfocalFocus = ... # type: QCameraFocus.FocusMode
        InfinityFocus = ... # type: QCameraFocus.FocusMode
        AutoFocus = ... # type: QCameraFocus.FocusMode
        ContinuousFocus = ... # type: QCameraFocus.FocusMode
        MacroFocus = ... # type: QCameraFocus.FocusMode

    class FocusModes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __xor__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __ior__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __or__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __iand__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __and__(self, f: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> 'QCameraFocus.FocusModes': ...
        def __invert__(self) -> 'QCameraFocus.FocusModes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    maximumDigitalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumOpticalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusZonesChanged: typing.ClassVar[QtCore.pyqtSignal]
    digitalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    opticalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    def zoomTo(self, opticalZoom: float, digitalZoom: float) -> None: ...
    def digitalZoom(self) -> float: ...
    def opticalZoom(self) -> float: ...
    def maximumDigitalZoom(self) -> float: ...
    def maximumOpticalZoom(self) -> float: ...
    def focusZones(self) -> typing.List[QCameraFocusZone]: ...
    def setCustomFocusPoint(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def customFocusPoint(self) -> QtCore.QPointF: ...
    def isFocusPointModeSupported(self, a0: 'QCameraFocus.FocusPointMode') -> bool: ...
    def setFocusPointMode(self, mode: 'QCameraFocus.FocusPointMode') -> None: ...
    def focusPointMode(self) -> 'QCameraFocus.FocusPointMode': ...
    def isFocusModeSupported(self, mode: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> bool: ...
    def setFocusMode(self, mode: typing.Union['QCameraFocus.FocusModes', 'QCameraFocus.FocusMode']) -> None: ...
    def focusMode(self) -> 'QCameraFocus.FocusModes': ...
    def isAvailable(self) -> bool: ...


class QCameraFocusControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    focusZonesChanged: typing.ClassVar[QtCore.pyqtSignal]
    customFocusPointChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusPointModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def focusZones(self) -> typing.List[QCameraFocusZone]: ...
    def setCustomFocusPoint(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def customFocusPoint(self) -> QtCore.QPointF: ...
    def isFocusPointModeSupported(self, mode: QCameraFocus.FocusPointMode) -> bool: ...
    def setFocusPointMode(self, mode: QCameraFocus.FocusPointMode) -> None: ...
    def focusPointMode(self) -> QCameraFocus.FocusPointMode: ...
    def isFocusModeSupported(self, mode: typing.Union[QCameraFocus.FocusModes, QCameraFocus.FocusMode]) -> bool: ...
    def setFocusMode(self, mode: typing.Union[QCameraFocus.FocusModes, QCameraFocus.FocusMode]) -> None: ...
    def focusMode(self) -> QCameraFocus.FocusModes: ...


class QCameraImageCapture(QtCore.QObject, QMediaBindableInterface):

    class CaptureDestination(int):
        CaptureToFile = ... # type: QCameraImageCapture.CaptureDestination
        CaptureToBuffer = ... # type: QCameraImageCapture.CaptureDestination

    class DriveMode(int):
        SingleImageCapture = ... # type: QCameraImageCapture.DriveMode

    class Error(int):
        NoError = ... # type: QCameraImageCapture.Error
        NotReadyError = ... # type: QCameraImageCapture.Error
        ResourceError = ... # type: QCameraImageCapture.Error
        OutOfSpaceError = ... # type: QCameraImageCapture.Error
        NotSupportedFeatureError = ... # type: QCameraImageCapture.Error
        FormatError = ... # type: QCameraImageCapture.Error

    class CaptureDestinations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __xor__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __ior__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __or__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __iand__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __and__(self, f: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __invert__(self) -> 'QCameraImageCapture.CaptureDestinations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, mediaObject: QMediaObject, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMediaObject(self, a0: QMediaObject) -> bool: ...
    imageSaved: typing.ClassVar[QtCore.pyqtSignal]
    imageAvailable: typing.ClassVar[QtCore.pyqtSignal]
    imageMetadataAvailable: typing.ClassVar[QtCore.pyqtSignal]
    imageCaptured: typing.ClassVar[QtCore.pyqtSignal]
    imageExposed: typing.ClassVar[QtCore.pyqtSignal]
    captureDestinationChanged: typing.ClassVar[QtCore.pyqtSignal]
    bufferFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    readyForCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def cancelCapture(self) -> None: ...
    def capture(self, file: str = ...) -> int: ...
    def setCaptureDestination(self, destination: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> None: ...
    def captureDestination(self) -> 'QCameraImageCapture.CaptureDestinations': ...
    def isCaptureDestinationSupported(self, destination: typing.Union['QCameraImageCapture.CaptureDestinations', 'QCameraImageCapture.CaptureDestination']) -> bool: ...
    def setBufferFormat(self, format: 'QVideoFrame.PixelFormat') -> None: ...
    def bufferFormat(self) -> 'QVideoFrame.PixelFormat': ...
    def supportedBufferFormats(self) -> typing.List['QVideoFrame.PixelFormat']: ...
    def setEncodingSettings(self, settings: 'QImageEncoderSettings') -> None: ...
    def encodingSettings(self) -> 'QImageEncoderSettings': ...
    def supportedResolutions(self, settings: 'QImageEncoderSettings' = ...) -> typing.Tuple[typing.List[QtCore.QSize], bool]: ...
    def imageCodecDescription(self, codecName: str) -> str: ...
    def supportedImageCodecs(self) -> typing.List[str]: ...
    def isReadyForCapture(self) -> bool: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def mediaObject(self) -> QMediaObject: ...
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...
    def isAvailable(self) -> bool: ...


class QCameraImageCaptureControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    error: typing.ClassVar[QtCore.pyqtSignal]
    imageSaved: typing.ClassVar[QtCore.pyqtSignal]
    imageAvailable: typing.ClassVar[QtCore.pyqtSignal]
    imageMetadataAvailable: typing.ClassVar[QtCore.pyqtSignal]
    imageCaptured: typing.ClassVar[QtCore.pyqtSignal]
    imageExposed: typing.ClassVar[QtCore.pyqtSignal]
    readyForCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def cancelCapture(self) -> None: ...
    def capture(self, fileName: str) -> int: ...
    def setDriveMode(self, mode: QCameraImageCapture.DriveMode) -> None: ...
    def driveMode(self) -> QCameraImageCapture.DriveMode: ...
    def isReadyForCapture(self) -> bool: ...


class QCameraImageProcessing(QtCore.QObject):

    class ColorFilter(int):
        ColorFilterNone = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterGrayscale = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterNegative = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterSolarize = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterSepia = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterPosterize = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterWhiteboard = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterBlackboard = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterAqua = ... # type: QCameraImageProcessing.ColorFilter
        ColorFilterVendor = ... # type: QCameraImageProcessing.ColorFilter

    class WhiteBalanceMode(int):
        WhiteBalanceAuto = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceManual = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceSunlight = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceCloudy = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceShade = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceTungsten = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceFluorescent = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceFlash = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceSunset = ... # type: QCameraImageProcessing.WhiteBalanceMode
        WhiteBalanceVendor = ... # type: QCameraImageProcessing.WhiteBalanceMode

    def setBrightness(self, value: float) -> None: ...
    def brightness(self) -> float: ...
    def isColorFilterSupported(self, filter: 'QCameraImageProcessing.ColorFilter') -> bool: ...
    def setColorFilter(self, filter: 'QCameraImageProcessing.ColorFilter') -> None: ...
    def colorFilter(self) -> 'QCameraImageProcessing.ColorFilter': ...
    def setDenoisingLevel(self, value: float) -> None: ...
    def denoisingLevel(self) -> float: ...
    def setSharpeningLevel(self, value: float) -> None: ...
    def sharpeningLevel(self) -> float: ...
    def setSaturation(self, value: float) -> None: ...
    def saturation(self) -> float: ...
    def setContrast(self, value: float) -> None: ...
    def contrast(self) -> float: ...
    def setManualWhiteBalance(self, colorTemperature: float) -> None: ...
    def manualWhiteBalance(self) -> float: ...
    def isWhiteBalanceModeSupported(self, mode: 'QCameraImageProcessing.WhiteBalanceMode') -> bool: ...
    def setWhiteBalanceMode(self, mode: 'QCameraImageProcessing.WhiteBalanceMode') -> None: ...
    def whiteBalanceMode(self) -> 'QCameraImageProcessing.WhiteBalanceMode': ...
    def isAvailable(self) -> bool: ...


class QCameraImageProcessingControl(QMediaControl):

    class ProcessingParameter(int):
        WhiteBalancePreset = ... # type: QCameraImageProcessingControl.ProcessingParameter
        ColorTemperature = ... # type: QCameraImageProcessingControl.ProcessingParameter
        Contrast = ... # type: QCameraImageProcessingControl.ProcessingParameter
        Saturation = ... # type: QCameraImageProcessingControl.ProcessingParameter
        Brightness = ... # type: QCameraImageProcessingControl.ProcessingParameter
        Sharpening = ... # type: QCameraImageProcessingControl.ProcessingParameter
        Denoising = ... # type: QCameraImageProcessingControl.ProcessingParameter
        ContrastAdjustment = ... # type: QCameraImageProcessingControl.ProcessingParameter
        SaturationAdjustment = ... # type: QCameraImageProcessingControl.ProcessingParameter
        BrightnessAdjustment = ... # type: QCameraImageProcessingControl.ProcessingParameter
        SharpeningAdjustment = ... # type: QCameraImageProcessingControl.ProcessingParameter
        DenoisingAdjustment = ... # type: QCameraImageProcessingControl.ProcessingParameter
        ColorFilter = ... # type: QCameraImageProcessingControl.ProcessingParameter
        ExtendedParameter = ... # type: QCameraImageProcessingControl.ProcessingParameter

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setParameter(self, parameter: 'QCameraImageProcessingControl.ProcessingParameter', value: typing.Any) -> None: ...
    def parameter(self, parameter: 'QCameraImageProcessingControl.ProcessingParameter') -> typing.Any: ...
    def isParameterValueSupported(self, parameter: 'QCameraImageProcessingControl.ProcessingParameter', value: typing.Any) -> bool: ...
    def isParameterSupported(self, a0: 'QCameraImageProcessingControl.ProcessingParameter') -> bool: ...


class QCameraInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    @typing.overload
    def __init__(self, camera: QCamera) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCameraInfo') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def availableCameras(position: QCamera.Position = ...) -> typing.List['QCameraInfo']: ...
    @staticmethod
    def defaultCamera() -> 'QCameraInfo': ...
    def orientation(self) -> int: ...
    def position(self) -> QCamera.Position: ...
    def description(self) -> str: ...
    def deviceName(self) -> str: ...
    def isNull(self) -> bool: ...


class QCameraInfoControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def cameraOrientation(self, deviceName: str) -> int: ...
    def cameraPosition(self, deviceName: str) -> QCamera.Position: ...


class QCameraLocksControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    lockStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def unlock(self, locks: typing.Union[QCamera.LockTypes, QCamera.LockType]) -> None: ...
    def searchAndLock(self, locks: typing.Union[QCamera.LockTypes, QCamera.LockType]) -> None: ...
    def lockStatus(self, lock: QCamera.LockType) -> QCamera.LockStatus: ...
    def supportedLocks(self) -> QCamera.LockTypes: ...


class QCameraViewfinderSettings(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCameraViewfinderSettings') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def setPixelAspectRatio(self, ratio: QtCore.QSize) -> None: ...
    @typing.overload
    def setPixelAspectRatio(self, horizontal: int, vertical: int) -> None: ...
    def pixelAspectRatio(self) -> QtCore.QSize: ...
    def setPixelFormat(self, format: 'QVideoFrame.PixelFormat') -> None: ...
    def pixelFormat(self) -> 'QVideoFrame.PixelFormat': ...
    def setMaximumFrameRate(self, rate: float) -> None: ...
    def maximumFrameRate(self) -> float: ...
    def setMinimumFrameRate(self, rate: float) -> None: ...
    def minimumFrameRate(self) -> float: ...
    @typing.overload
    def setResolution(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int) -> None: ...
    def resolution(self) -> QtCore.QSize: ...
    def isNull(self) -> bool: ...
    def swap(self, other: 'QCameraViewfinderSettings') -> None: ...


class QCameraViewfinderSettingsControl(QMediaControl):

    class ViewfinderParameter(int):
        Resolution = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter
        PixelAspectRatio = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter
        MinimumFrameRate = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter
        MaximumFrameRate = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter
        PixelFormat = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter
        UserParameter = ... # type: QCameraViewfinderSettingsControl.ViewfinderParameter

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setViewfinderParameter(self, parameter: 'QCameraViewfinderSettingsControl.ViewfinderParameter', value: typing.Any) -> None: ...
    def viewfinderParameter(self, parameter: 'QCameraViewfinderSettingsControl.ViewfinderParameter') -> typing.Any: ...
    def isViewfinderParameterSupported(self, parameter: 'QCameraViewfinderSettingsControl.ViewfinderParameter') -> bool: ...


class QCameraViewfinderSettingsControl2(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setViewfinderSettings(self, settings: QCameraViewfinderSettings) -> None: ...
    def viewfinderSettings(self) -> QCameraViewfinderSettings: ...
    def supportedViewfinderSettings(self) -> typing.List[QCameraViewfinderSettings]: ...


class QCameraZoomControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    currentDigitalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    currentOpticalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    requestedDigitalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    requestedOpticalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumDigitalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumOpticalZoomChanged: typing.ClassVar[QtCore.pyqtSignal]
    def zoomTo(self, optical: float, digital: float) -> None: ...
    def currentDigitalZoom(self) -> float: ...
    def currentOpticalZoom(self) -> float: ...
    def requestedDigitalZoom(self) -> float: ...
    def requestedOpticalZoom(self) -> float: ...
    def maximumDigitalZoom(self) -> float: ...
    def maximumOpticalZoom(self) -> float: ...


class QCustomAudioRoleControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    customAudioRoleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def supportedCustomAudioRoles(self) -> typing.List[str]: ...
    def setCustomAudioRole(self, role: str) -> None: ...
    def customAudioRole(self) -> str: ...


class QImageEncoderControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setImageSettings(self, settings: 'QImageEncoderSettings') -> None: ...
    def imageSettings(self) -> 'QImageEncoderSettings': ...
    def supportedResolutions(self, settings: 'QImageEncoderSettings') -> typing.Tuple[typing.List[QtCore.QSize], bool]: ...
    def imageCodecDescription(self, codec: str) -> str: ...
    def supportedImageCodecs(self) -> typing.List[str]: ...


class QMediaAudioProbeControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    flush: typing.ClassVar[QtCore.pyqtSignal]
    audioBufferProbed: typing.ClassVar[QtCore.pyqtSignal]


class QMediaAvailabilityControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    availabilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...


class QMediaContainerControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def containerDescription(self, formatMimeType: str) -> str: ...
    def setContainerFormat(self, format: str) -> None: ...
    def containerFormat(self) -> str: ...
    def supportedContainers(self) -> typing.List[str]: ...


class QMediaContent(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, contentUrl: QtCore.QUrl) -> None: ...
    @typing.overload
    def __init__(self, contentRequest: QtNetwork.QNetworkRequest) -> None: ...
    @typing.overload
    def __init__(self, contentResource: 'QMediaResource') -> None: ...
    @typing.overload
    def __init__(self, resources: typing.Iterable['QMediaResource']) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMediaContent') -> None: ...
    @typing.overload
    def __init__(self, playlist: 'QMediaPlaylist', contentUrl: QtCore.QUrl = ...) -> None: ...

    def request(self) -> QtNetwork.QNetworkRequest: ...
    def playlist(self) -> 'QMediaPlaylist': ...
    def resources(self) -> typing.List['QMediaResource']: ...
    def canonicalResource(self) -> 'QMediaResource': ...
    def canonicalRequest(self) -> QtNetwork.QNetworkRequest: ...
    def canonicalUrl(self) -> QtCore.QUrl: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QAudioEncoderSettings(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioEncoderSettings') -> None: ...

    def setEncodingOptions(self, options: typing.Dict[str, typing.Any]) -> None: ...
    def setEncodingOption(self, option: str, value: typing.Any) -> None: ...
    def encodingOptions(self) -> typing.Dict[str, typing.Any]: ...
    def encodingOption(self, option: str) -> typing.Any: ...
    def setQuality(self, quality: 'QMultimedia.EncodingQuality') -> None: ...
    def quality(self) -> 'QMultimedia.EncodingQuality': ...
    def setSampleRate(self, rate: int) -> None: ...
    def sampleRate(self) -> int: ...
    def setChannelCount(self, channels: int) -> None: ...
    def channelCount(self) -> int: ...
    def setBitRate(self, bitrate: int) -> None: ...
    def bitRate(self) -> int: ...
    def setCodec(self, codec: str) -> None: ...
    def codec(self) -> str: ...
    def setEncodingMode(self, a0: 'QMultimedia.EncodingMode') -> None: ...
    def encodingMode(self) -> 'QMultimedia.EncodingMode': ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QVideoEncoderSettings(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QVideoEncoderSettings') -> None: ...

    def setEncodingOptions(self, options: typing.Dict[str, typing.Any]) -> None: ...
    def setEncodingOption(self, option: str, value: typing.Any) -> None: ...
    def encodingOptions(self) -> typing.Dict[str, typing.Any]: ...
    def encodingOption(self, option: str) -> typing.Any: ...
    def setQuality(self, quality: 'QMultimedia.EncodingQuality') -> None: ...
    def quality(self) -> 'QMultimedia.EncodingQuality': ...
    def setBitRate(self, bitrate: int) -> None: ...
    def bitRate(self) -> int: ...
    def setFrameRate(self, rate: float) -> None: ...
    def frameRate(self) -> float: ...
    @typing.overload
    def setResolution(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int) -> None: ...
    def resolution(self) -> QtCore.QSize: ...
    def setCodec(self, a0: str) -> None: ...
    def codec(self) -> str: ...
    def setEncodingMode(self, a0: 'QMultimedia.EncodingMode') -> None: ...
    def encodingMode(self) -> 'QMultimedia.EncodingMode': ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QImageEncoderSettings(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QImageEncoderSettings') -> None: ...

    def setEncodingOptions(self, options: typing.Dict[str, typing.Any]) -> None: ...
    def setEncodingOption(self, option: str, value: typing.Any) -> None: ...
    def encodingOptions(self) -> typing.Dict[str, typing.Any]: ...
    def encodingOption(self, option: str) -> typing.Any: ...
    def setQuality(self, quality: 'QMultimedia.EncodingQuality') -> None: ...
    def quality(self) -> 'QMultimedia.EncodingQuality': ...
    @typing.overload
    def setResolution(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int) -> None: ...
    def resolution(self) -> QtCore.QSize: ...
    def setCodec(self, a0: str) -> None: ...
    def codec(self) -> str: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QMediaGaplessPlaybackControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    advancedToNextMedia: typing.ClassVar[QtCore.pyqtSignal]
    nextMediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    crossfadeTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setCrossfadeTime(self, crossfadeTime: float) -> None: ...
    def crossfadeTime(self) -> float: ...
    def isCrossfadeSupported(self) -> bool: ...
    def setNextMedia(self, media: QMediaContent) -> None: ...
    def nextMedia(self) -> QMediaContent: ...


class QMediaMetaData(PyQt5.sip.simplewrapper):

    AlbumArtist = ... # type: str
    AlbumTitle = ... # type: str
    AudioBitRate = ... # type: str
    AudioCodec = ... # type: str
    Author = ... # type: str
    AverageLevel = ... # type: str
    CameraManufacturer = ... # type: str
    CameraModel = ... # type: str
    Category = ... # type: str
    ChannelCount = ... # type: str
    ChapterNumber = ... # type: str
    Comment = ... # type: str
    Composer = ... # type: str
    Conductor = ... # type: str
    Contrast = ... # type: str
    ContributingArtist = ... # type: str
    Copyright = ... # type: str
    CoverArtImage = ... # type: str
    CoverArtUrlLarge = ... # type: str
    CoverArtUrlSmall = ... # type: str
    Date = ... # type: str
    DateTimeDigitized = ... # type: str
    DateTimeOriginal = ... # type: str
    Description = ... # type: str
    DeviceSettingDescription = ... # type: str
    DigitalZoomRatio = ... # type: str
    Director = ... # type: str
    Duration = ... # type: str
    Event = ... # type: str
    ExposureBiasValue = ... # type: str
    ExposureMode = ... # type: str
    ExposureProgram = ... # type: str
    ExposureTime = ... # type: str
    FNumber = ... # type: str
    Flash = ... # type: str
    FocalLength = ... # type: str
    FocalLengthIn35mmFilm = ... # type: str
    GPSAltitude = ... # type: str
    GPSAreaInformation = ... # type: str
    GPSDOP = ... # type: str
    GPSImgDirection = ... # type: str
    GPSImgDirectionRef = ... # type: str
    GPSLatitude = ... # type: str
    GPSLongitude = ... # type: str
    GPSMapDatum = ... # type: str
    GPSProcessingMethod = ... # type: str
    GPSSatellites = ... # type: str
    GPSSpeed = ... # type: str
    GPSStatus = ... # type: str
    GPSTimeStamp = ... # type: str
    GPSTrack = ... # type: str
    GPSTrackRef = ... # type: str
    GainControl = ... # type: str
    Genre = ... # type: str
    ISOSpeedRatings = ... # type: str
    Keywords = ... # type: str
    Language = ... # type: str
    LeadPerformer = ... # type: str
    LightSource = ... # type: str
    Lyrics = ... # type: str
    MediaType = ... # type: str
    MeteringMode = ... # type: str
    Mood = ... # type: str
    Orientation = ... # type: str
    ParentalRating = ... # type: str
    PeakValue = ... # type: str
    PixelAspectRatio = ... # type: str
    PosterImage = ... # type: str
    PosterUrl = ... # type: str
    Publisher = ... # type: str
    RatingOrganization = ... # type: str
    Resolution = ... # type: str
    SampleRate = ... # type: str
    Saturation = ... # type: str
    SceneCaptureType = ... # type: str
    Sharpness = ... # type: str
    Size = ... # type: str
    SubTitle = ... # type: str
    Subject = ... # type: str
    SubjectDistance = ... # type: str
    ThumbnailImage = ... # type: str
    Title = ... # type: str
    TrackCount = ... # type: str
    TrackNumber = ... # type: str
    UserRating = ... # type: str
    VideoBitRate = ... # type: str
    VideoCodec = ... # type: str
    VideoFrameRate = ... # type: str
    WhiteBalance = ... # type: str
    Writer = ... # type: str
    Year = ... # type: str


class QMediaNetworkAccessControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    configurationChanged: typing.ClassVar[QtCore.pyqtSignal]
    def currentConfiguration(self) -> QtNetwork.QNetworkConfiguration: ...
    def setConfigurations(self, configuration: typing.Iterable[QtNetwork.QNetworkConfiguration]) -> None: ...


class QMediaPlayer(QMediaObject):

    class Error(int):
        NoError = ... # type: QMediaPlayer.Error
        ResourceError = ... # type: QMediaPlayer.Error
        FormatError = ... # type: QMediaPlayer.Error
        NetworkError = ... # type: QMediaPlayer.Error
        AccessDeniedError = ... # type: QMediaPlayer.Error
        ServiceMissingError = ... # type: QMediaPlayer.Error

    class Flag(int):
        LowLatency = ... # type: QMediaPlayer.Flag
        StreamPlayback = ... # type: QMediaPlayer.Flag
        VideoSurface = ... # type: QMediaPlayer.Flag

    class MediaStatus(int):
        UnknownMediaStatus = ... # type: QMediaPlayer.MediaStatus
        NoMedia = ... # type: QMediaPlayer.MediaStatus
        LoadingMedia = ... # type: QMediaPlayer.MediaStatus
        LoadedMedia = ... # type: QMediaPlayer.MediaStatus
        StalledMedia = ... # type: QMediaPlayer.MediaStatus
        BufferingMedia = ... # type: QMediaPlayer.MediaStatus
        BufferedMedia = ... # type: QMediaPlayer.MediaStatus
        EndOfMedia = ... # type: QMediaPlayer.MediaStatus
        InvalidMedia = ... # type: QMediaPlayer.MediaStatus

    class State(int):
        StoppedState = ... # type: QMediaPlayer.State
        PlayingState = ... # type: QMediaPlayer.State
        PausedState = ... # type: QMediaPlayer.State

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __xor__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __ior__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __or__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __iand__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __and__(self, f: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag']) -> 'QMediaPlayer.Flags': ...
        def __invert__(self) -> 'QMediaPlayer.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ..., flags: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag'] = ...) -> None: ...

    customAudioRoleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def supportedCustomAudioRoles(self) -> typing.List[str]: ...
    def setCustomAudioRole(self, audioRole: str) -> None: ...
    def customAudioRole(self) -> str: ...
    audioRoleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def supportedAudioRoles(self) -> typing.List[QAudio.Role]: ...
    def setAudioRole(self, audioRole: QAudio.Role) -> None: ...
    def audioRole(self) -> QAudio.Role: ...
    def unbind(self, a0: QtCore.QObject) -> None: ...
    def bind(self, a0: QtCore.QObject) -> bool: ...
    networkConfigurationChanged: typing.ClassVar[QtCore.pyqtSignal]
    playbackRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    seekableChanged: typing.ClassVar[QtCore.pyqtSignal]
    bufferStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    currentMediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setNetworkConfigurations(self, configurations: typing.Iterable[QtNetwork.QNetworkConfiguration]) -> None: ...
    def setPlaylist(self, playlist: 'QMediaPlaylist') -> None: ...
    def setMedia(self, media: QMediaContent, stream: typing.Optional[QtCore.QIODevice] = ...) -> None: ...
    def setPlaybackRate(self, rate: float) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setVolume(self, volume: int) -> None: ...
    def setPosition(self, position: int) -> None: ...
    def stop(self) -> None: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def availability(self) -> 'QMultimedia.AvailabilityStatus': ...
    def currentNetworkConfiguration(self) -> QtNetwork.QNetworkConfiguration: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def playbackRate(self) -> float: ...
    def isSeekable(self) -> bool: ...
    def bufferStatus(self) -> int: ...
    def isVideoAvailable(self) -> bool: ...
    def isAudioAvailable(self) -> bool: ...
    def isMuted(self) -> bool: ...
    def volume(self) -> int: ...
    def position(self) -> int: ...
    def duration(self) -> int: ...
    def mediaStatus(self) -> 'QMediaPlayer.MediaStatus': ...
    def state(self) -> 'QMediaPlayer.State': ...
    def currentMedia(self) -> QMediaContent: ...
    def playlist(self) -> 'QMediaPlaylist': ...
    def mediaStream(self) -> QtCore.QIODevice: ...
    def media(self) -> QMediaContent: ...
    @typing.overload
    def setVideoOutput(self, a0: QVideoWidget) -> None: ...
    @typing.overload
    def setVideoOutput(self, a0: QGraphicsVideoItem) -> None: ...
    @typing.overload
    def setVideoOutput(self, surface: QAbstractVideoSurface) -> None: ...
    @typing.overload
    def setVideoOutput(self, surfaces: typing.Iterable[QAbstractVideoSurface]) -> None: ...
    @staticmethod
    def supportedMimeTypes(flags: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag'] = ...) -> typing.List[str]: ...
    @staticmethod
    def hasSupport(mimeType: str, codecs: typing.Iterable[str] = ..., flags: typing.Union['QMediaPlayer.Flags', 'QMediaPlayer.Flag'] = ...) -> 'QMultimedia.SupportEstimate': ...


class QMediaPlayerControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    error: typing.ClassVar[QtCore.pyqtSignal]
    playbackRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    availablePlaybackRangesChanged: typing.ClassVar[QtCore.pyqtSignal]
    seekableChanged: typing.ClassVar[QtCore.pyqtSignal]
    bufferStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def setMedia(self, media: QMediaContent, stream: QtCore.QIODevice) -> None: ...
    def mediaStream(self) -> QtCore.QIODevice: ...
    def media(self) -> QMediaContent: ...
    def setPlaybackRate(self, rate: float) -> None: ...
    def playbackRate(self) -> float: ...
    def availablePlaybackRanges(self) -> 'QMediaTimeRange': ...
    def isSeekable(self) -> bool: ...
    def isVideoAvailable(self) -> bool: ...
    def isAudioAvailable(self) -> bool: ...
    def bufferStatus(self) -> int: ...
    def setMuted(self, mute: bool) -> None: ...
    def isMuted(self) -> bool: ...
    def setVolume(self, volume: int) -> None: ...
    def volume(self) -> int: ...
    def setPosition(self, position: int) -> None: ...
    def position(self) -> int: ...
    def duration(self) -> int: ...
    def mediaStatus(self) -> QMediaPlayer.MediaStatus: ...
    def state(self) -> QMediaPlayer.State: ...


class QMediaPlaylist(QtCore.QObject, QMediaBindableInterface):

    class Error(int):
        NoError = ... # type: QMediaPlaylist.Error
        FormatError = ... # type: QMediaPlaylist.Error
        FormatNotSupportedError = ... # type: QMediaPlaylist.Error
        NetworkError = ... # type: QMediaPlaylist.Error
        AccessDeniedError = ... # type: QMediaPlaylist.Error

    class PlaybackMode(int):
        CurrentItemOnce = ... # type: QMediaPlaylist.PlaybackMode
        CurrentItemInLoop = ... # type: QMediaPlaylist.PlaybackMode
        Sequential = ... # type: QMediaPlaylist.PlaybackMode
        Loop = ... # type: QMediaPlaylist.PlaybackMode
        Random = ... # type: QMediaPlaylist.PlaybackMode

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMediaObject(self, object: QMediaObject) -> bool: ...
    loadFailed: typing.ClassVar[QtCore.pyqtSignal]
    loaded: typing.ClassVar[QtCore.pyqtSignal]
    mediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaRemoved: typing.ClassVar[QtCore.pyqtSignal]
    mediaAboutToBeRemoved: typing.ClassVar[QtCore.pyqtSignal]
    mediaInserted: typing.ClassVar[QtCore.pyqtSignal]
    mediaAboutToBeInserted: typing.ClassVar[QtCore.pyqtSignal]
    currentMediaChanged: typing.ClassVar[QtCore.pyqtSignal]
    playbackModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    currentIndexChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setCurrentIndex(self, index: int) -> None: ...
    def previous(self) -> None: ...
    def next(self) -> None: ...
    def shuffle(self) -> None: ...
    def moveMedia(self, from_: int, to: int) -> bool: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QMediaPlaylist.Error': ...
    @typing.overload
    def save(self, location: QtCore.QUrl, format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def save(self, device: QtCore.QIODevice, format: str) -> bool: ...
    @typing.overload
    def load(self, request: QtNetwork.QNetworkRequest, format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def load(self, location: QtCore.QUrl, format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def load(self, device: QtCore.QIODevice, format: typing.Optional[str] = ...) -> None: ...
    def clear(self) -> bool: ...
    @typing.overload
    def removeMedia(self, pos: int) -> bool: ...
    @typing.overload
    def removeMedia(self, start: int, end: int) -> bool: ...
    @typing.overload
    def insertMedia(self, index: int, content: QMediaContent) -> bool: ...
    @typing.overload
    def insertMedia(self, index: int, items: typing.Iterable[QMediaContent]) -> bool: ...
    @typing.overload
    def addMedia(self, content: QMediaContent) -> bool: ...
    @typing.overload
    def addMedia(self, items: typing.Iterable[QMediaContent]) -> bool: ...
    def isReadOnly(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def mediaCount(self) -> int: ...
    def media(self, index: int) -> QMediaContent: ...
    def previousIndex(self, steps: int = ...) -> int: ...
    def nextIndex(self, steps: int = ...) -> int: ...
    def currentMedia(self) -> QMediaContent: ...
    def currentIndex(self) -> int: ...
    def setPlaybackMode(self, mode: 'QMediaPlaylist.PlaybackMode') -> None: ...
    def playbackMode(self) -> 'QMediaPlaylist.PlaybackMode': ...
    def mediaObject(self) -> QMediaObject: ...


class QMediaRecorderControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setVolume(self, volume: float) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setState(self, state: QMediaRecorder.State) -> None: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    actualLocationChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def applySettings(self) -> None: ...
    def volume(self) -> float: ...
    def isMuted(self) -> bool: ...
    def duration(self) -> int: ...
    def status(self) -> QMediaRecorder.Status: ...
    def state(self) -> QMediaRecorder.State: ...
    def setOutputLocation(self, location: QtCore.QUrl) -> bool: ...
    def outputLocation(self) -> QtCore.QUrl: ...


class QMediaResource(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, url: QtCore.QUrl, mimeType: str = ...) -> None: ...
    @typing.overload
    def __init__(self, request: QtNetwork.QNetworkRequest, mimeType: str = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMediaResource') -> None: ...

    @typing.overload
    def setResolution(self, resolution: QtCore.QSize) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int) -> None: ...
    def resolution(self) -> QtCore.QSize: ...
    def setVideoBitRate(self, rate: int) -> None: ...
    def videoBitRate(self) -> int: ...
    def setChannelCount(self, channels: int) -> None: ...
    def channelCount(self) -> int: ...
    def setSampleRate(self, frequency: int) -> None: ...
    def sampleRate(self) -> int: ...
    def setAudioBitRate(self, rate: int) -> None: ...
    def audioBitRate(self) -> int: ...
    def setDataSize(self, size: int) -> None: ...
    def dataSize(self) -> int: ...
    def setVideoCodec(self, codec: str) -> None: ...
    def videoCodec(self) -> str: ...
    def setAudioCodec(self, codec: str) -> None: ...
    def audioCodec(self) -> str: ...
    def setLanguage(self, language: str) -> None: ...
    def language(self) -> str: ...
    def mimeType(self) -> str: ...
    def request(self) -> QtNetwork.QNetworkRequest: ...
    def url(self) -> QtCore.QUrl: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isNull(self) -> bool: ...


class QMediaService(QtCore.QObject):

    def __init__(self, parent: QtCore.QObject) -> None: ...

    def releaseControl(self, control: QMediaControl) -> None: ...
    def requestControl(self, name: str) -> QMediaControl: ...


class QMediaStreamsControl(QMediaControl):

    class StreamType(int):
        UnknownStream = ... # type: QMediaStreamsControl.StreamType
        VideoStream = ... # type: QMediaStreamsControl.StreamType
        AudioStream = ... # type: QMediaStreamsControl.StreamType
        SubPictureStream = ... # type: QMediaStreamsControl.StreamType
        DataStream = ... # type: QMediaStreamsControl.StreamType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    activeStreamsChanged: typing.ClassVar[QtCore.pyqtSignal]
    streamsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setActive(self, streamNumber: int, state: bool) -> None: ...
    def isActive(self, streamNumber: int) -> bool: ...
    def metaData(self, streamNumber: int, key: str) -> typing.Any: ...
    def streamType(self, streamNumber: int) -> 'QMediaStreamsControl.StreamType': ...
    def streamCount(self) -> int: ...


class QMediaTimeInterval(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, start: int, end: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMediaTimeInterval') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def translated(self, offset: int) -> 'QMediaTimeInterval': ...
    def normalized(self) -> 'QMediaTimeInterval': ...
    def isNormal(self) -> bool: ...
    def contains(self, time: int) -> bool: ...
    def end(self) -> int: ...
    def start(self) -> int: ...


class QMediaTimeRange(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, start: int, end: int) -> None: ...
    @typing.overload
    def __init__(self, a0: QMediaTimeInterval) -> None: ...
    @typing.overload
    def __init__(self, range: 'QMediaTimeRange') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, a0: 'QMediaTimeRange', a1: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    def __sub__(self, a0: 'QMediaTimeRange', a1: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    def clear(self) -> None: ...
    @typing.overload
    def __isub__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    @typing.overload
    def __isub__(self, a0: QMediaTimeInterval) -> 'QMediaTimeRange': ...
    @typing.overload
    def __iadd__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    @typing.overload
    def __iadd__(self, a0: QMediaTimeInterval) -> 'QMediaTimeRange': ...
    def removeTimeRange(self, a0: 'QMediaTimeRange') -> None: ...
    @typing.overload
    def removeInterval(self, start: int, end: int) -> None: ...
    @typing.overload
    def removeInterval(self, interval: QMediaTimeInterval) -> None: ...
    def addTimeRange(self, a0: 'QMediaTimeRange') -> None: ...
    @typing.overload
    def addInterval(self, start: int, end: int) -> None: ...
    @typing.overload
    def addInterval(self, interval: QMediaTimeInterval) -> None: ...
    def contains(self, time: int) -> bool: ...
    def isContinuous(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def intervals(self) -> typing.List[QMediaTimeInterval]: ...
    def latestTime(self) -> int: ...
    def earliestTime(self) -> int: ...


class QMediaVideoProbeControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    flush: typing.ClassVar[QtCore.pyqtSignal]
    videoFrameProbed: typing.ClassVar[QtCore.pyqtSignal]


class QMetaDataReaderControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    metaDataAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    def availableMetaData(self) -> typing.List[str]: ...
    def metaData(self, key: str) -> typing.Any: ...
    def isMetaDataAvailable(self) -> bool: ...


class QMetaDataWriterControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    metaDataAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    writableChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    def availableMetaData(self) -> typing.List[str]: ...
    def setMetaData(self, key: str, value: typing.Any) -> None: ...
    def metaData(self, key: str) -> typing.Any: ...
    def isMetaDataAvailable(self) -> bool: ...
    def isWritable(self) -> bool: ...


class QMultimedia(PyQt5.sip.simplewrapper):

    class AvailabilityStatus(int):
        Available = ... # type: QMultimedia.AvailabilityStatus
        ServiceMissing = ... # type: QMultimedia.AvailabilityStatus
        Busy = ... # type: QMultimedia.AvailabilityStatus
        ResourceError = ... # type: QMultimedia.AvailabilityStatus

    class EncodingMode(int):
        ConstantQualityEncoding = ... # type: QMultimedia.EncodingMode
        ConstantBitRateEncoding = ... # type: QMultimedia.EncodingMode
        AverageBitRateEncoding = ... # type: QMultimedia.EncodingMode
        TwoPassEncoding = ... # type: QMultimedia.EncodingMode

    class EncodingQuality(int):
        VeryLowQuality = ... # type: QMultimedia.EncodingQuality
        LowQuality = ... # type: QMultimedia.EncodingQuality
        NormalQuality = ... # type: QMultimedia.EncodingQuality
        HighQuality = ... # type: QMultimedia.EncodingQuality
        VeryHighQuality = ... # type: QMultimedia.EncodingQuality

    class SupportEstimate(int):
        NotSupported = ... # type: QMultimedia.SupportEstimate
        MaybeSupported = ... # type: QMultimedia.SupportEstimate
        ProbablySupported = ... # type: QMultimedia.SupportEstimate
        PreferredService = ... # type: QMultimedia.SupportEstimate


class QRadioData(QtCore.QObject, QMediaBindableInterface):

    class ProgramType(int):
        Undefined = ... # type: QRadioData.ProgramType
        News = ... # type: QRadioData.ProgramType
        CurrentAffairs = ... # type: QRadioData.ProgramType
        Information = ... # type: QRadioData.ProgramType
        Sport = ... # type: QRadioData.ProgramType
        Education = ... # type: QRadioData.ProgramType
        Drama = ... # type: QRadioData.ProgramType
        Culture = ... # type: QRadioData.ProgramType
        Science = ... # type: QRadioData.ProgramType
        Varied = ... # type: QRadioData.ProgramType
        PopMusic = ... # type: QRadioData.ProgramType
        RockMusic = ... # type: QRadioData.ProgramType
        EasyListening = ... # type: QRadioData.ProgramType
        LightClassical = ... # type: QRadioData.ProgramType
        SeriousClassical = ... # type: QRadioData.ProgramType
        OtherMusic = ... # type: QRadioData.ProgramType
        Weather = ... # type: QRadioData.ProgramType
        Finance = ... # type: QRadioData.ProgramType
        ChildrensProgrammes = ... # type: QRadioData.ProgramType
        SocialAffairs = ... # type: QRadioData.ProgramType
        Religion = ... # type: QRadioData.ProgramType
        PhoneIn = ... # type: QRadioData.ProgramType
        Travel = ... # type: QRadioData.ProgramType
        Leisure = ... # type: QRadioData.ProgramType
        JazzMusic = ... # type: QRadioData.ProgramType
        CountryMusic = ... # type: QRadioData.ProgramType
        NationalMusic = ... # type: QRadioData.ProgramType
        OldiesMusic = ... # type: QRadioData.ProgramType
        FolkMusic = ... # type: QRadioData.ProgramType
        Documentary = ... # type: QRadioData.ProgramType
        AlarmTest = ... # type: QRadioData.ProgramType
        Alarm = ... # type: QRadioData.ProgramType
        Talk = ... # type: QRadioData.ProgramType
        ClassicRock = ... # type: QRadioData.ProgramType
        AdultHits = ... # type: QRadioData.ProgramType
        SoftRock = ... # type: QRadioData.ProgramType
        Top40 = ... # type: QRadioData.ProgramType
        Soft = ... # type: QRadioData.ProgramType
        Nostalgia = ... # type: QRadioData.ProgramType
        Classical = ... # type: QRadioData.ProgramType
        RhythmAndBlues = ... # type: QRadioData.ProgramType
        SoftRhythmAndBlues = ... # type: QRadioData.ProgramType
        Language = ... # type: QRadioData.ProgramType
        ReligiousMusic = ... # type: QRadioData.ProgramType
        ReligiousTalk = ... # type: QRadioData.ProgramType
        Personality = ... # type: QRadioData.ProgramType
        Public = ... # type: QRadioData.ProgramType
        College = ... # type: QRadioData.ProgramType

    class Error(int):
        NoError = ... # type: QRadioData.Error
        ResourceError = ... # type: QRadioData.Error
        OpenError = ... # type: QRadioData.Error
        OutOfRangeError = ... # type: QRadioData.Error

    def __init__(self, mediaObject: QMediaObject, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMediaObject(self, a0: QMediaObject) -> bool: ...
    alternativeFrequenciesEnabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    radioTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    programTypeNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    programTypeChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationIdChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setAlternativeFrequenciesEnabled(self, enabled: bool) -> None: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isAlternativeFrequenciesEnabled(self) -> bool: ...
    def radioText(self) -> str: ...
    def stationName(self) -> str: ...
    def programTypeName(self) -> str: ...
    def programType(self) -> 'QRadioData.ProgramType': ...
    def stationId(self) -> str: ...
    def availability(self) -> QMultimedia.AvailabilityStatus: ...
    def mediaObject(self) -> QMediaObject: ...


class QRadioDataControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    alternativeFrequenciesEnabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    radioTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    programTypeNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    programTypeChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationIdChanged: typing.ClassVar[QtCore.pyqtSignal]
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isAlternativeFrequenciesEnabled(self) -> bool: ...
    def setAlternativeFrequenciesEnabled(self, enabled: bool) -> None: ...
    def radioText(self) -> str: ...
    def stationName(self) -> str: ...
    def programTypeName(self) -> str: ...
    def programType(self) -> QRadioData.ProgramType: ...
    def stationId(self) -> str: ...


class QRadioTuner(QMediaObject):

    class SearchMode(int):
        SearchFast = ... # type: QRadioTuner.SearchMode
        SearchGetStationId = ... # type: QRadioTuner.SearchMode

    class StereoMode(int):
        ForceStereo = ... # type: QRadioTuner.StereoMode
        ForceMono = ... # type: QRadioTuner.StereoMode
        Auto = ... # type: QRadioTuner.StereoMode

    class Error(int):
        NoError = ... # type: QRadioTuner.Error
        ResourceError = ... # type: QRadioTuner.Error
        OpenError = ... # type: QRadioTuner.Error
        OutOfRangeError = ... # type: QRadioTuner.Error

    class Band(int):
        AM = ... # type: QRadioTuner.Band
        FM = ... # type: QRadioTuner.Band
        SW = ... # type: QRadioTuner.Band
        LW = ... # type: QRadioTuner.Band
        FM2 = ... # type: QRadioTuner.Band

    class State(int):
        ActiveState = ... # type: QRadioTuner.State
        StoppedState = ... # type: QRadioTuner.State

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    antennaConnectedChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationFound: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    signalStrengthChanged: typing.ClassVar[QtCore.pyqtSignal]
    searchingChanged: typing.ClassVar[QtCore.pyqtSignal]
    stereoStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    frequencyChanged: typing.ClassVar[QtCore.pyqtSignal]
    bandChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setVolume(self, volume: int) -> None: ...
    def setFrequency(self, frequency: int) -> None: ...
    def setBand(self, band: 'QRadioTuner.Band') -> None: ...
    def cancelSearch(self) -> None: ...
    def searchAllStations(self, searchMode: 'QRadioTuner.SearchMode' = ...) -> None: ...
    def searchBackward(self) -> None: ...
    def searchForward(self) -> None: ...
    def radioData(self) -> QRadioData: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isAntennaConnected(self) -> bool: ...
    def isSearching(self) -> bool: ...
    def isMuted(self) -> bool: ...
    def volume(self) -> int: ...
    def signalStrength(self) -> int: ...
    def stereoMode(self) -> 'QRadioTuner.StereoMode': ...
    def setStereoMode(self, mode: 'QRadioTuner.StereoMode') -> None: ...
    def isStereo(self) -> bool: ...
    def frequencyRange(self, band: 'QRadioTuner.Band') -> typing.Tuple[int, int]: ...
    def frequencyStep(self, band: 'QRadioTuner.Band') -> int: ...
    def frequency(self) -> int: ...
    def isBandSupported(self, b: 'QRadioTuner.Band') -> bool: ...
    def band(self) -> 'QRadioTuner.Band': ...
    def state(self) -> 'QRadioTuner.State': ...
    def availability(self) -> QMultimedia.AvailabilityStatus: ...


class QRadioTunerControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    antennaConnectedChanged: typing.ClassVar[QtCore.pyqtSignal]
    stationFound: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    signalStrengthChanged: typing.ClassVar[QtCore.pyqtSignal]
    searchingChanged: typing.ClassVar[QtCore.pyqtSignal]
    stereoStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    frequencyChanged: typing.ClassVar[QtCore.pyqtSignal]
    bandChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def cancelSearch(self) -> None: ...
    def searchAllStations(self, searchMode: QRadioTuner.SearchMode = ...) -> None: ...
    def searchBackward(self) -> None: ...
    def searchForward(self) -> None: ...
    def isAntennaConnected(self) -> bool: ...
    def isSearching(self) -> bool: ...
    def setMuted(self, muted: bool) -> None: ...
    def isMuted(self) -> bool: ...
    def setVolume(self, volume: int) -> None: ...
    def volume(self) -> int: ...
    def signalStrength(self) -> int: ...
    def setStereoMode(self, mode: QRadioTuner.StereoMode) -> None: ...
    def stereoMode(self) -> QRadioTuner.StereoMode: ...
    def isStereo(self) -> bool: ...
    def setFrequency(self, frequency: int) -> None: ...
    def frequencyRange(self, b: QRadioTuner.Band) -> typing.Tuple[int, int]: ...
    def frequencyStep(self, b: QRadioTuner.Band) -> int: ...
    def frequency(self) -> int: ...
    def isBandSupported(self, b: QRadioTuner.Band) -> bool: ...
    def setBand(self, b: QRadioTuner.Band) -> None: ...
    def band(self) -> QRadioTuner.Band: ...
    def state(self) -> QRadioTuner.State: ...


class QSound(QtCore.QObject):

    class Loop(int):
        Infinite = ... # type: QSound.Loop

    def __init__(self, filename: str, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def stop(self) -> None: ...
    def isFinished(self) -> bool: ...
    def fileName(self) -> str: ...
    def setLoops(self, a0: int) -> None: ...
    def loopsRemaining(self) -> int: ...
    def loops(self) -> int: ...
    @typing.overload
    @staticmethod
    def play(filename: str) -> None: ...
    @typing.overload
    def play(self) -> None: ...


class QSoundEffect(QtCore.QObject):

    class Status(int):
        Null = ... # type: QSoundEffect.Status
        Loading = ... # type: QSoundEffect.Status
        Ready = ... # type: QSoundEffect.Status
        Error = ... # type: QSoundEffect.Status

    class Loop(int):
        Infinite = ... # type: QSoundEffect.Loop

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, audioDevice: QAudioDeviceInfo, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def stop(self) -> None: ...
    def play(self) -> None: ...
    categoryChanged: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    playingChanged: typing.ClassVar[QtCore.pyqtSignal]
    loadedChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    loopsRemainingChanged: typing.ClassVar[QtCore.pyqtSignal]
    loopCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setCategory(self, category: str) -> None: ...
    def category(self) -> str: ...
    def status(self) -> 'QSoundEffect.Status': ...
    def isPlaying(self) -> bool: ...
    def isLoaded(self) -> bool: ...
    def setMuted(self, muted: bool) -> None: ...
    def isMuted(self) -> bool: ...
    def setVolume(self, volume: float) -> None: ...
    def volume(self) -> float: ...
    def setLoopCount(self, loopCount: int) -> None: ...
    def loopsRemaining(self) -> int: ...
    def loopCount(self) -> int: ...
    def setSource(self, url: QtCore.QUrl) -> None: ...
    def source(self) -> QtCore.QUrl: ...
    @staticmethod
    def supportedMimeTypes() -> typing.List[str]: ...


class QVideoDeviceSelectorControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    devicesChanged: typing.ClassVar[QtCore.pyqtSignal]
    selectedDeviceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setSelectedDevice(self, index: int) -> None: ...
    def selectedDevice(self) -> int: ...
    def defaultDevice(self) -> int: ...
    def deviceDescription(self, index: int) -> str: ...
    def deviceName(self, index: int) -> str: ...
    def deviceCount(self) -> int: ...


class QVideoEncoderSettingsControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setVideoSettings(self, settings: QVideoEncoderSettings) -> None: ...
    def videoSettings(self) -> QVideoEncoderSettings: ...
    def videoCodecDescription(self, codec: str) -> str: ...
    def supportedVideoCodecs(self) -> typing.List[str]: ...
    def supportedFrameRates(self, settings: QVideoEncoderSettings) -> typing.Tuple[typing.List[float], bool]: ...
    def supportedResolutions(self, settings: QVideoEncoderSettings) -> typing.Tuple[typing.List[QtCore.QSize], bool]: ...


class QVideoFrame(PyQt5.sipsimplewrapper):

    class PixelFormat(int):
        Format_Invalid = ... # type: QVideoFrame.PixelFormat
        Format_ARGB32 = ... # type: QVideoFrame.PixelFormat
        Format_ARGB32_Premultiplied = ... # type: QVideoFrame.PixelFormat
        Format_RGB32 = ... # type: QVideoFrame.PixelFormat
        Format_RGB24 = ... # type: QVideoFrame.PixelFormat
        Format_RGB565 = ... # type: QVideoFrame.PixelFormat
        Format_RGB555 = ... # type: QVideoFrame.PixelFormat
        Format_ARGB8565_Premultiplied = ... # type: QVideoFrame.PixelFormat
        Format_BGRA32 = ... # type: QVideoFrame.PixelFormat
        Format_BGRA32_Premultiplied = ... # type: QVideoFrame.PixelFormat
        Format_BGR32 = ... # type: QVideoFrame.PixelFormat
        Format_BGR24 = ... # type: QVideoFrame.PixelFormat
        Format_BGR565 = ... # type: QVideoFrame.PixelFormat
        Format_BGR555 = ... # type: QVideoFrame.PixelFormat
        Format_BGRA5658_Premultiplied = ... # type: QVideoFrame.PixelFormat
        Format_AYUV444 = ... # type: QVideoFrame.PixelFormat
        Format_AYUV444_Premultiplied = ... # type: QVideoFrame.PixelFormat
        Format_YUV444 = ... # type: QVideoFrame.PixelFormat
        Format_YUV420P = ... # type: QVideoFrame.PixelFormat
        Format_YV12 = ... # type: QVideoFrame.PixelFormat
        Format_UYVY = ... # type: QVideoFrame.PixelFormat
        Format_YUYV = ... # type: QVideoFrame.PixelFormat
        Format_NV12 = ... # type: QVideoFrame.PixelFormat
        Format_NV21 = ... # type: QVideoFrame.PixelFormat
        Format_IMC1 = ... # type: QVideoFrame.PixelFormat
        Format_IMC2 = ... # type: QVideoFrame.PixelFormat
        Format_IMC3 = ... # type: QVideoFrame.PixelFormat
        Format_IMC4 = ... # type: QVideoFrame.PixelFormat
        Format_Y8 = ... # type: QVideoFrame.PixelFormat
        Format_Y16 = ... # type: QVideoFrame.PixelFormat
        Format_Jpeg = ... # type: QVideoFrame.PixelFormat
        Format_CameraRaw = ... # type: QVideoFrame.PixelFormat
        Format_AdobeDng = ... # type: QVideoFrame.PixelFormat
        Format_ABGR32 = ... # type: QVideoFrame.PixelFormat
        Format_YUV422P = ... # type: QVideoFrame.PixelFormat
        Format_User = ... # type: QVideoFrame.PixelFormat

    class FieldType(int):
        ProgressiveFrame = ... # type: QVideoFrame.FieldType
        TopField = ... # type: QVideoFrame.FieldType
        BottomField = ... # type: QVideoFrame.FieldType
        InterlacedFrame = ... # type: QVideoFrame.FieldType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, buffer: QAbstractVideoBuffer, size: QtCore.QSize, format: 'QVideoFrame.PixelFormat') -> None: ...
    @typing.overload
    def __init__(self, bytes: int, size: QtCore.QSize, bytesPerLine: int, format: 'QVideoFrame.PixelFormat') -> None: ...
    @typing.overload
    def __init__(self, image: QtGui.QImage) -> None: ...
    @typing.overload
    def __init__(self, other: 'QVideoFrame') -> None: ...

    def image(self) -> QtGui.QImage: ...
    def buffer(self) -> QAbstractVideoBuffer: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def planeCount(self) -> int: ...
    def setMetaData(self, key: str, value: typing.Any) -> None: ...
    def metaData(self, key: str) -> typing.Any: ...
    def availableMetaData(self) -> typing.Dict[str, typing.Any]: ...
    @staticmethod
    def imageFormatFromPixelFormat(format: 'QVideoFrame.PixelFormat') -> QtGui.QImage.Format: ...
    @staticmethod
    def pixelFormatFromImageFormat(format: QtGui.QImage.Format) -> 'QVideoFrame.PixelFormat': ...
    def setEndTime(self, time: int) -> None: ...
    def endTime(self) -> int: ...
    def setStartTime(self, time: int) -> None: ...
    def startTime(self) -> int: ...
    def handle(self) -> typing.Any: ...
    def mappedBytes(self) -> int: ...
    @typing.overload
    def bits(self) -> PyQt5.sip.voidptr: ...
    @typing.overload
    def bits(self, plane: int) -> PyQt5.sip.voidptr: ...
    @typing.overload
    def bytesPerLine(self) -> int: ...
    @typing.overload
    def bytesPerLine(self, plane: int) -> int: ...
    def unmap(self) -> None: ...
    def map(self, mode: QAbstractVideoBuffer.MapMode) -> bool: ...
    def mapMode(self) -> QAbstractVideoBuffer.MapMode: ...
    def isWritable(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def isMapped(self) -> bool: ...
    def setFieldType(self, a0: 'QVideoFrame.FieldType') -> None: ...
    def fieldType(self) -> 'QVideoFrame.FieldType': ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def size(self) -> QtCore.QSize: ...
    def handleType(self) -> QAbstractVideoBuffer.HandleType: ...
    def pixelFormat(self) -> 'QVideoFrame.PixelFormat': ...
    def isValid(self) -> bool: ...


class QVideoProbe(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    flush: typing.ClassVar[QtCore.pyqtSignal]
    videoFrameProbed: typing.ClassVar[QtCore.pyqtSignal]
    def isActive(self) -> bool: ...
    @typing.overload
    def setSource(self, source: QMediaObject) -> bool: ...
    @typing.overload
    def setSource(self, source: QMediaRecorder) -> bool: ...


class QVideoRendererControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setSurface(self, surface: QAbstractVideoSurface) -> None: ...
    def surface(self) -> QAbstractVideoSurface: ...


class QVideoSurfaceFormat(PyQt5.sipsimplewrapper):

    class YCbCrColorSpace(int):
        YCbCr_Undefined = ... # type: QVideoSurfaceFormat.YCbCrColorSpace
        YCbCr_BT601 = ... # type: QVideoSurfaceFormat.YCbCrColorSpace
        YCbCr_BT709 = ... # type: QVideoSurfaceFormat.YCbCrColorSpace
        YCbCr_xvYCC601 = ... # type: QVideoSurfaceFormat.YCbCrColorSpace
        YCbCr_xvYCC709 = ... # type: QVideoSurfaceFormat.YCbCrColorSpace
        YCbCr_JPEG = ... # type: QVideoSurfaceFormat.YCbCrColorSpace

    class Direction(int):
        TopToBottom = ... # type: QVideoSurfaceFormat.Direction
        BottomToTop = ... # type: QVideoSurfaceFormat.Direction

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, format: QVideoFrame.PixelFormat, type: QAbstractVideoBuffer.HandleType = ...) -> None: ...
    @typing.overload
    def __init__(self, format: 'QVideoSurfaceFormat') -> None: ...

    def setMirrored(self, mirrored: bool) -> None: ...
    def isMirrored(self) -> bool: ...
    def setProperty(self, name: str, value: typing.Any) -> None: ...
    def property(self, name: str) -> typing.Any: ...
    def propertyNames(self) -> typing.List[QtCore.QByteArray]: ...
    def sizeHint(self) -> QtCore.QSize: ...
    def setYCbCrColorSpace(self, colorSpace: 'QVideoSurfaceFormat.YCbCrColorSpace') -> None: ...
    def yCbCrColorSpace(self) -> 'QVideoSurfaceFormat.YCbCrColorSpace': ...
    @typing.overload
    def setPixelAspectRatio(self, ratio: QtCore.QSize) -> None: ...
    @typing.overload
    def setPixelAspectRatio(self, width: int, height: int) -> None: ...
    def pixelAspectRatio(self) -> QtCore.QSize: ...
    def setFrameRate(self, rate: float) -> None: ...
    def frameRate(self) -> float: ...
    def setScanLineDirection(self, direction: 'QVideoSurfaceFormat.Direction') -> None: ...
    def scanLineDirection(self) -> 'QVideoSurfaceFormat.Direction': ...
    def setViewport(self, viewport: QtCore.QRect) -> None: ...
    def viewport(self) -> QtCore.QRect: ...
    def frameHeight(self) -> int: ...
    def frameWidth(self) -> int: ...
    @typing.overload
    def setFrameSize(self, size: QtCore.QSize) -> None: ...
    @typing.overload
    def setFrameSize(self, width: int, height: int) -> None: ...
    def frameSize(self) -> QtCore.QSize: ...
    def handleType(self) -> QAbstractVideoBuffer.HandleType: ...
    def pixelFormat(self) -> QVideoFrame.PixelFormat: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QVideoWindowControl(QMediaControl):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    nativeSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    saturationChanged: typing.ClassVar[QtCore.pyqtSignal]
    hueChanged: typing.ClassVar[QtCore.pyqtSignal]
    contrastChanged: typing.ClassVar[QtCore.pyqtSignal]
    brightnessChanged: typing.ClassVar[QtCore.pyqtSignal]
    fullScreenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setSaturation(self, saturation: int) -> None: ...
    def saturation(self) -> int: ...
    def setHue(self, hue: int) -> None: ...
    def hue(self) -> int: ...
    def setContrast(self, contrast: int) -> None: ...
    def contrast(self) -> int: ...
    def setBrightness(self, brightness: int) -> None: ...
    def brightness(self) -> int: ...
    def setAspectRatioMode(self, mode: QtCore.Qt.AspectRatioMode) -> None: ...
    def aspectRatioMode(self) -> QtCore.Qt.AspectRatioMode: ...
    def nativeSize(self) -> QtCore.QSize: ...
    def repaint(self) -> None: ...
    def setFullScreen(self, fullScreen: bool) -> None: ...
    def isFullScreen(self) -> bool: ...
    def setDisplayRect(self, rect: QtCore.QRect) -> None: ...
    def displayRect(self) -> QtCore.QRect: ...
    def setWinId(self, id: PyQt5.sip.voidptr) -> None: ...
    def winId(self) -> PyQt5.sip.voidptr: ...
