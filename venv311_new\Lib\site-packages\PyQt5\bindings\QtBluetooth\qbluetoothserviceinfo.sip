// qbluetoothserviceinfo.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothServiceInfo
{
%TypeHeaderCode
#include <qbluetoothserviceinfo.h>
%End

public:
    enum AttributeId
    {
        ServiceRecordHandle,
        ServiceClassIds,
        ServiceRecordState,
        ServiceId,
        ProtocolDescriptorList,
        BrowseGroupList,
        LanguageBaseAttributeIdList,
        ServiceInfoTimeToLive,
        ServiceAvailability,
        BluetoothProfileDescriptorList,
        DocumentationUrl,
        ClientExecutableUrl,
        IconUrl,
        AdditionalProtocolDescriptorList,
        PrimaryLanguageBase,
        ServiceName,
        ServiceDescription,
        ServiceProvider,
    };

    enum Protocol
    {
        UnknownProtocol,
        L2capProtocol,
        RfcommProtocol,
    };

    QBluetoothServiceInfo();
    QBluetoothServiceInfo(const QBluetoothServiceInfo &other);
    ~QBluetoothServiceInfo();
    bool isValid() const;
    bool isComplete() const;
    void setDevice(const QBluetoothDeviceInfo &info);
    QBluetoothDeviceInfo device() const;
    QVariant attribute(quint16 attributeId) const;
    QList<quint16> attributes() const;
    bool contains(quint16 attributeId) const;
    void removeAttribute(quint16 attributeId);
    QBluetoothServiceInfo::Protocol socketProtocol() const;
    int protocolServiceMultiplexer() const;
    int serverChannel() const;
    QBluetoothServiceInfo::Sequence protocolDescriptor(QBluetoothUuid::ProtocolUuid protocol) const;
    bool isRegistered() const;
    bool registerService(const QBluetoothAddress &localAdapter = QBluetoothAddress());
    bool unregisterService();
    void setAttribute(quint16 attributeId, const QBluetoothUuid &value);
    void setAttribute(quint16 attributeId, const QBluetoothServiceInfo::Sequence &value);
    void setAttribute(quint16 attributeId, const QVariant &value);
    void setServiceName(const QString &name);
    QString serviceName() const;
    void setServiceDescription(const QString &description);
    QString serviceDescription() const;
    void setServiceProvider(const QString &provider);
    QString serviceProvider() const;
    void setServiceAvailability(quint8 availability);
    quint8 serviceAvailability() const;
    void setServiceUuid(const QBluetoothUuid &uuid);
    QBluetoothUuid serviceUuid() const;
    QList<QBluetoothUuid> serviceClassUuids() const;
};

%End
