#!/usr/bin/env python3
"""
تشغيل التطبيق مع التحديثات الجديدة
"""

import sys
import os
import shutil
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clear_cache():
    """حذف ملفات cache"""
    cache_dirs = [
        "gui/__pycache__",
        "__pycache__",
        "database/__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✅ تم حذف {cache_dir}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {cache_dir}: {e}")

def main():
    print("🚀 تشغيل التطبيق مع التحديثات الجديدة...")
    print("=" * 70)
    
    # حذف ملفات cache
    print("🧹 حذف ملفات cache...")
    clear_cache()
    
    # تشغيل التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        print("🖥️ تشغيل الواجهة الرئيسية...")
        
        # استيراد مع إعادة تحميل
        import importlib
        
        # إعادة تحميل الوحدات إذا كانت محملة
        modules_to_reload = [
            "gui.inventory",
            "gui.main_window"
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
                print(f"🔄 تم إعادة تحميل {module_name}")
        
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 التحديثات المطبقة:")
        print("")
        print("   📦 في قسم المخزون:")
        print("     ✅ تبويب 'المخزون' - عرض المنتجات")
        print("     ✅ تبويب 'إدارة الأصناف' - تعديل وحذف")
        print("     ✅ عمود 'المكسب' في كلا الجدولين")
        print("     ✅ لون أخضر فاتح لعمود المكسب")
        print("     ✅ حسابات صحيحة: المكسب = الكمية × سعر البيع")
        print("")
        print("   ➕ عند إضافة منتج جديد:")
        print("     ✅ حقول القيمة الإجمالية والمكسب")
        print("     ✅ تحديث فوري عند تغيير الأرقام")
        print("     ✅ ألوان مميزة (أزرق وأخضر)")
        print("")
        print("   ✏️ عند تعديل منتج:")
        print("     ✅ أزرار تعديل وحذف في تبويب 'إدارة الأصناف'")
        print("     ✅ تحديث فوري للقيم المحسوبة")
        print("     ✅ حفظ التغييرات بنجاح")
        print("")
        print("🔍 خطوات التحقق:")
        print("1️⃣ اذهب لقائمة 'المخزون' من الشريط العلوي")
        print("2️⃣ ابحث عن تبويبين: 'المخزون' و 'إدارة الأصناف'")
        print("3️⃣ انقر على تبويب 'إدارة الأصناف'")
        print("4️⃣ لاحظ عمود 'المكسب' في الجدول")
        print("5️⃣ اضغط 'إضافة صنف جديد'")
        print("6️⃣ أدخل البيانات ولاحظ التحديث الفوري:")
        print("     • الكمية: 10")
        print("     • سعر الشراء: 50")
        print("     • سعر البيع: 75")
        print("     • القيمة الإجمالية: 500.00 ريال")
        print("     • المكسب المتوقع: 750.00 ريال")
        print("7️⃣ احفظ المنتج")
        print("8️⃣ اضغط 'تعديل' لأي منتج")
        print("9️⃣ غير الأرقام ولاحظ التحديث الفوري")
        print("")
        print("⚠️ إذا لم تظهر التحديثات:")
        print("   • تأكد من أنك في قسم 'المخزون' وليس قسم آخر")
        print("   • ابحث عن التبويبات في أعلى الصفحة")
        print("   • أغلق البرنامج وأعد تشغيله")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    
    print("🎯 انتهى التشغيل")
    print("=" * 70)
    print("📊 ملخص التحديثات:")
    print("")
    print("   ✅ تم تطبيق جميع التحديثات:")
    print("     📊 عمود المكسب في الجداول")
    print("     📋 تبويب إدارة الأصناف")
    print("     ⚡ التحديث التلقائي للقيم")
    print("     🎨 واجهة محسنة وملونة")
    print("")
    print("🎉 نظام المخزون محدث ومكتمل!")

if __name__ == "__main__":
    main()
