// qabstractitemdelegate.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractItemDelegate : public QObject
{
%TypeHeaderCode
#include <qabstractitemdelegate.h>
%End

public:
    enum EndEditHint
    {
        NoHint,
        EditNextItem,
        EditPreviousItem,
        SubmitModelCache,
        RevertModelCache,
    };

    explicit QAbstractItemDelegate(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractItemDelegate();
    virtual void paint(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const = 0;
    virtual QSize sizeHint(const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const = 0;
    virtual QWidget *createEditor(QWidget *parent /TransferThis/, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const /Factory/;
    virtual void setEditorData(QWidget *editor, const QModelIndex &index) const;
    virtual void setModelData(QWidget *editor, QAbstractItemModel *model /KeepReference/, const QModelIndex &index) const;
    virtual void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    virtual void destroyEditor(QWidget *editor, const QModelIndex &index) const;
    virtual bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index);
    virtual bool helpEvent(QHelpEvent *event, QAbstractItemView *view, const QStyleOptionViewItem &option, const QModelIndex &index);

signals:
    void commitData(QWidget *editor);
    void closeEditor(QWidget *editor, QAbstractItemDelegate::EndEditHint hint = QAbstractItemDelegate::NoHint);
    void sizeHintChanged(const QModelIndex &);
};
