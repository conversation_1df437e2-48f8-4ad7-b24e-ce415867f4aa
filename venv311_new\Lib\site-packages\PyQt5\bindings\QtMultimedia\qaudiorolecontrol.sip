// qaudiorolecontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_6_0 -)

class QAudioRoleControl : QMediaControl
{
%TypeHeaderCode
#include <qaudiorolecontrol.h>
%End

public:
    virtual ~QAudioRoleControl();
    virtual QAudio::Role audioRole() const = 0;
    virtual void setAudioRole(QAudio::Role role) = 0;
    virtual QList<QAudio::Role> supportedAudioRoles() const = 0;

signals:
    void audioRoleChanged(QAudio::Role role);

protected:
    explicit QAudioRoleControl(QObject *parent /TransferThis/ = 0);
};

%End
