#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التراخيص المدمج مع برنامج المحاسبة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_license_integration():
    """اختبار دمج نظام التراخيص"""
    print("🧪 اختبار دمج نظام التراخيص مع برنامج المحاسبة")
    print("=" * 60)
    
    try:
        # اختبار استيراد نظام التراخيص
        print("1. اختبار استيراد نظام التراخيص...")
        from license_manager import LicenseManager
        from license_ui import check_license_and_show_dialog
        print("   ✅ تم استيراد نظام التراخيص بنجاح")
        
        # اختبار إنشاء مدير التراخيص
        print("2. اختبار إنشاء مدير التراخيص...")
        license_manager = LicenseManager()
        print("   ✅ تم إنشاء مدير التراخيص بنجاح")
        
        # اختبار الحصول على معلومات الجهاز
        print("3. اختبار الحصول على معلومات الجهاز...")
        customer_code = license_manager.get_customer_code()
        machine_id = license_manager.get_machine_id()
        print(f"   🔑 كود العميل: {customer_code}")
        print(f"   💻 رقم الجهاز: {machine_id}")
        print("   ✅ تم الحصول على معلومات الجهاز بنجاح")
        
        # اختبار فحص الترخيص
        print("4. اختبار فحص الترخيص...")
        status = license_manager.check_license()
        
        if status["valid"]:
            print("   ✅ الترخيص صالح")
            print(f"   📅 ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   ⏰ متبقي: {status['days_remaining']} يوم")
        else:
            print(f"   ⚠️ الترخيص غير صالح: {status['message']}")
            
            if status["status"] == "NO_LICENSE":
                print("   🔄 إنشاء ترخيص تجريبي...")
                license_manager.create_initial_license(days=30)
                print("   ✅ تم إنشاء ترخيص تجريبي لمدة 30 يوم")
        
        # اختبار استيراد النافذة الرئيسية
        print("5. اختبار استيراد النافذة الرئيسية...")
        from gui.main_window import MainWindow
        print("   ✅ تم استيراد النافذة الرئيسية بنجاح")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام التراخيص مدمج بنجاح مع برنامج المحاسبة")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return False

def test_main_app_with_license():
    """اختبار تشغيل التطبيق الرئيسي مع نظام التراخيص"""
    print("\n🚀 اختبار تشغيل التطبيق الرئيسي...")
    print("=" * 60)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("1. تم إنشاء تطبيق Qt بنجاح")
        
        # استيراد الدالة الرئيسية
        from main import main
        print("2. تم استيراد الدالة الرئيسية بنجاح")
        
        print("3. ✅ التطبيق جاهز للتشغيل مع نظام التراخيص")
        print("   📝 ملاحظة: لتشغيل التطبيق الكامل، استخدم: python main.py")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التطبيق: {e}")
        return False

def show_integration_summary():
    """عرض ملخص الدمج"""
    print("\n" + "=" * 60)
    print("📋 ملخص دمج نظام التراخيص")
    print("=" * 60)
    
    print("🔧 الملفات المعدلة:")
    print("   • main.py - إضافة فحص الترخيص في البداية")
    print("   • gui/main_window.py - إضافة فحص دوري وخيار التجديد")
    
    print("\n🔐 المميزات المضافة:")
    print("   • فحص الترخيص عند بدء التشغيل")
    print("   • فحص دوري كل 30 دقيقة")
    print("   • تحذير قبل انتهاء الصلاحية بـ 7 أيام")
    print("   • خيار تجديد الترخيص من قائمة النظام")
    print("   • إغلاق تلقائي عند انتهاء الصلاحية")
    
    print("\n🎯 كيفية الاستخدام:")
    print("   1. تشغيل البرنامج: python main.py")
    print("   2. سيتم فحص الترخيص تلقائياً")
    print("   3. للتجديد: النظام > تجديد الترخيص")
    print("   4. إدخال كود التجديد المرسل من المطور")
    
    print("\n💰 النموذج التجاري:")
    print("   • ترخيص سنوي لكل عميل")
    print("   • تجديد إجباري بعد السنة")
    print("   • حماية قوية ضد القرصنة")
    print("   • دخل مستدام ومضمون")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔐 اختبار نظام التراخيص المدمج")
    print("=" * 60)
    
    # اختبار الدمج
    integration_success = test_license_integration()
    
    if integration_success:
        # اختبار التطبيق الرئيسي
        app_success = test_main_app_with_license()
        
        if app_success:
            # عرض الملخص
            show_integration_summary()
            
            print("\n🎉 نجح دمج نظام التراخيص بالكامل!")
            print("🚀 البرنامج جاهز للاستخدام والتوزيع")
        else:
            print("\n❌ فشل في اختبار التطبيق الرئيسي")
    else:
        print("\n❌ فشل في اختبار دمج نظام التراخيص")
        print("💡 تأكد من وجود جميع ملفات نظام التراخيص")

if __name__ == "__main__":
    main()
