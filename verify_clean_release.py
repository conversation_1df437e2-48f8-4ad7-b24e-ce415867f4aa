#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 سكريبت التحقق من نظافة البيانات للإصدار النهائي
يتحقق من أن البرنامج نظيف وجاهز للتوزيع
"""

import os
import sqlite3
from datetime import datetime

def check_database_tables():
    """فحص محتويات جداول قاعدة البيانات"""
    db_path = "accounting.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص محتويات قاعدة البيانات:")
        print("-" * 40)
        
        # الجداول التي يجب أن تكون فارغة
        tables_should_be_empty = [
            ("products", "المنتجات"),
            ("customers", "العملاء"), 
            ("suppliers", "الموردين"),
            ("transactions", "الفواتير"),
            ("transaction_items", "عناصر الفواتير"),
            ("product_barcodes", "باركودات المنتجات"),
            ("audit_log", "سجل العمليات"),
            ("notifications", "الإشعارات")
        ]
        
        all_clean = True
        
        for table, description in tables_should_be_empty:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                if count == 0:
                    print(f"✅ {description}: فارغ ({count} سجل)")
                else:
                    print(f"❌ {description}: يحتوي على {count} سجل - يجب تنظيفه!")
                    all_clean = False
                    
            except sqlite3.Error as e:
                print(f"⚠️ خطأ في فحص {description}: {e}")
        
        # فحص جدول المستخدمين (يجب أن يحتوي على المدير الافتراضي فقط)
        try:
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            if users_count == 1:
                cursor.execute("SELECT username FROM users")
                username = cursor.fetchone()[0]
                if username == "sicoo":
                    print(f"✅ المستخدمين: حساب المدير الافتراضي فقط ({username})")
                else:
                    print(f"⚠️ المستخدمين: حساب غير متوقع ({username})")
            else:
                print(f"❌ المستخدمين: {users_count} حساب - يجب أن يكون حساب واحد فقط!")
                all_clean = False
                
        except sqlite3.Error as e:
            print(f"⚠️ خطأ في فحص المستخدمين: {e}")
        
        conn.close()
        return all_clean
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_company_settings():
    """فحص إعدادات الشركة"""
    try:
        conn = sqlite3.connect("accounting.db")
        cursor = conn.cursor()
        
        print("\n🏢 فحص إعدادات الشركة:")
        print("-" * 40)
        
        # الإعدادات التي يجب أن تكون فارغة
        settings_should_be_empty = [
            'company_name',
            'owner_name',
            'phone', 
            'email',
            'address',
            'tax_number',
            'commercial_register',
            'company_logo'
        ]
        
        all_clean = True
        
        for setting in settings_should_be_empty:
            cursor.execute("SELECT value FROM company_settings WHERE key = ?", (setting,))
            result = cursor.fetchone()
            
            if result is None or not result[0]:
                print(f"✅ {setting}: فارغ")
            else:
                print(f"❌ {setting}: يحتوي على قيمة - يجب تنظيفه!")
                all_clean = False
        
        conn.close()
        return all_clean
        
    except Exception as e:
        print(f"❌ خطأ في فحص إعدادات الشركة: {e}")
        return False

def check_developer_assets():
    """فحص أصول الشركة المطورة"""
    print("\n🎨 فحص أصول الشركة المطورة:")
    print("-" * 40)
    
    # الملفات التي يجب أن تكون موجودة
    required_files = [
        ("assets/icons.ico", "لوجو الشركة المطورة"),
        ("main.py", "الملف الرئيسي"),
        ("database/database.py", "نظام قاعدة البيانات"),
        ("gui/main_window.py", "النافذة الرئيسية")
    ]
    
    all_present = True
    
    for file_path, description in required_files:
        if os.path.exists(file_path):
            print(f"✅ {description}: موجود")
        else:
            print(f"❌ {description}: مفقود!")
            all_present = False
    
    return all_present

def check_temp_files():
    """فحص الملفات المؤقتة"""
    print("\n🗂️ فحص الملفات المؤقتة:")
    print("-" * 40)
    
    # الملفات التي يجب ألا تكون موجودة
    temp_files_should_not_exist = [
        "user_settings.json",
        "app_settings.json",
        "backup_settings.json", 
        "temp_invoice.pdf",
        "debug.log",
        "error.log"
    ]
    
    # المجلدات التي يجب ألا تكون موجودة
    temp_dirs_should_not_exist = [
        "__pycache__",
        ".pytest_cache", 
        "temp",
        "logs"
    ]
    
    all_clean = True
    
    for file_name in temp_files_should_not_exist:
        if not os.path.exists(file_name):
            print(f"✅ {file_name}: غير موجود (جيد)")
        else:
            print(f"❌ {file_name}: موجود - يجب حذفه!")
            all_clean = False
    
    for dir_name in temp_dirs_should_not_exist:
        if not os.path.exists(dir_name):
            print(f"✅ {dir_name}/: غير موجود (جيد)")
        else:
            print(f"❌ {dir_name}/: موجود - يجب حذفه!")
            all_clean = False
    
    return all_clean

def generate_release_report():
    """إنشاء تقرير حالة الإصدار"""
    print("\n" + "=" * 60)
    print("📋 تقرير حالة الإصدار النهائي")
    print("=" * 60)
    
    # فحص جميع المكونات
    db_clean = check_database_tables()
    company_clean = check_company_settings()
    assets_present = check_developer_assets()
    temp_clean = check_temp_files()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية:")
    print("=" * 60)
    
    if db_clean and company_clean and assets_present and temp_clean:
        print("🎉 البرنامج جاهز للإصدار النهائي!")
        print("✅ جميع البيانات التجريبية محذوفة")
        print("✅ لوجو الشركة المطورة محفوظ")
        print("✅ الملفات الأساسية موجودة")
        print("✅ لا توجد ملفات مؤقتة")
        print("\n🚀 يمكن توزيع البرنامج على العملاء الآن!")
        return True
    else:
        print("❌ البرنامج غير جاهز للإصدار!")
        print("\n🔧 المشاكل التي تحتاج إصلاح:")
        
        if not db_clean:
            print("   • قاعدة البيانات تحتوي على بيانات تجريبية")
        if not company_clean:
            print("   • إعدادات الشركة تحتوي على بيانات تجريبية")
        if not assets_present:
            print("   • ملفات أساسية مفقودة")
        if not temp_clean:
            print("   • ملفات مؤقتة موجودة")
            
        print("\n💡 قم بتشغيل clean_for_release.py لحل هذه المشاكل")
        return False

def main():
    """الدالة الرئيسية للفحص"""
    print("🔍 فحص نظافة البرنامج للإصدار النهائي")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء تقرير شامل
    is_ready = generate_release_report()
    
    # حفظ التقرير في ملف
    report_file = f"release_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    print(f"\n📄 تم حفظ التقرير في: {report_file}")
    
    return is_ready

if __name__ == "__main__":
    main()
