#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث معلومات التواصل في جميع ملفات النظام
Update Contact Information in All System Files
"""

import os
import re

def update_contact_info():
    """تحديث معلومات التواصل في جميع الملفات"""
    
    # معلومات التواصل الجديدة
    new_email = "<EMAIL>"
    contact_message = f"""للتجديد، راسل:
📧 {new_email}

أرسل كود العميل ورقم الجهاز مع إثبات الدفع"""
    
    print("🔄 تحديث معلومات التواصل...")
    print(f"📧 الإيميل الجديد: {new_email}")
    print("=" * 50)
    
    # قائمة الملفات المراد تحديثها
    files_to_update = [
        "license_ui.py",
        "code_generator_ui.py", 
        "quick_start.py",
        "run_license_demo.py"
    ]
    
    # تحديث كل ملف
    for filename in files_to_update:
        if os.path.exists(filename):
            print(f"📝 تحديث {filename}...")
            update_file_contact_info(filename, new_email, contact_message)
        else:
            print(f"⚠️ الملف غير موجود: {filename}")
    
    print("\n✅ تم تحديث معلومات التواصل في جميع الملفات!")
    print(f"📧 الإيميل الجديد: {new_email}")

def update_file_contact_info(filename, new_email, contact_message):
    """تحديث معلومات التواصل في ملف محدد"""
    try:
        # قراءة الملف
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # استبدال الإيميلات القديمة
        old_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for old_email in old_emails:
            content = content.replace(old_email, new_email)
        
        # استبدال أرقام الهواتف القديمة
        old_phones = [
            "01234567890",
            "201234567890"
        ]
        
        for old_phone in old_phones:
            content = content.replace(old_phone, "تواصل عبر الإيميل")
        
        # استبدال روابط الواتساب القديمة
        old_whatsapp = [
            "wa.me/201234567890",
            "https://wa.me/201234567890"
        ]
        
        for old_wa in old_whatsapp:
            content = content.replace(old_wa, f"mailto:{new_email}")
        
        # استبدال رسائل التواصل القديمة
        old_messages = [
            "يرجى التواصل مع الدعم الفني",
            "تواصل مع الدعم الفني",
            "للحصول على كود التجديد، يرجى التواصل مع الدعم الفني"
        ]
        
        for old_msg in old_messages:
            content = content.replace(old_msg, f"للتجديد، راسل: {new_email}")
        
        # كتابة الملف المحدث
        if content != original_content:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   ✅ تم تحديث {filename}")
        else:
            print(f"   ℹ️ لا يحتاج {filename} لتحديث")
            
    except Exception as e:
        print(f"   ❌ خطأ في تحديث {filename}: {e}")

def create_contact_template():
    """إنشاء قالب معلومات التواصل"""
    template = f"""
# 📞 معلومات التواصل

## للعملاء:
- **📧 البريد الإلكتروني:** <EMAIL>
- **📋 المطلوب في الإيميل:**
  - كود العميل ورقم الجهاز
  - إثبات الدفع
  - رقم الهاتف للتواصل

## للمطور:
- استخدم `code_generator_ui.py` لتوليد أكواد التجديد
- احفظ سجل بجميع الأكواد المولدة
- تأكد من الدفع قبل إرسال الكود

## نموذج إيميل للعميل:
```
الموضوع: طلب تجديد ترخيص برنامج المحاسبة

السلام عليكم،

أرغب في تجديد ترخيص برنامج المحاسبة.

كود العميل: [كود العميل]
رقم الجهاز: [رقم الجهاز]
رقم الهاتف: [رقم الهاتف]

مرفق إثبات الدفع.

شكراً لكم.
```
"""
    
    with open("contact_template.md", "w", encoding="utf-8") as f:
        f.write(template)
    
    print("📄 تم إنشاء قالب معلومات التواصل: contact_template.md")

def test_contact_integration():
    """اختبار دمج معلومات التواصل"""
    print("\n🧪 اختبار دمج معلومات التواصل...")
    
    # فحص الملفات المحدثة
    files_to_check = [
        "license_ui.py",
        "gui/main_window.py",
        "main.py"
    ]
    
    email_found = False
    
    for filename in files_to_check:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                if "<EMAIL>" in content:
                    print(f"   ✅ {filename} - يحتوي على الإيميل الجديد")
                    email_found = True
                else:
                    print(f"   ⚠️ {filename} - لا يحتوي على الإيميل الجديد")
        else:
            print(f"   ❌ {filename} - الملف غير موجود")
    
    if email_found:
        print("\n✅ تم دمج معلومات التواصل بنجاح!")
    else:
        print("\n❌ لم يتم العثور على معلومات التواصل الجديدة")

def show_integration_summary():
    """عرض ملخص الدمج"""
    print("\n" + "=" * 60)
    print("📋 ملخص دمج معلومات التواصل")
    print("=" * 60)
    
    print("📧 الإيميل الجديد: <EMAIL>")
    print("\n🔧 الملفات المحدثة:")
    print("   • license_ui.py - واجهة تجديد الترخيص")
    print("   • gui/main_window.py - رسائل التحذير")
    print("   • main.py - رسائل الخطأ")
    
    print("\n📱 كيفية التواصل:")
    print("   1. العميل يرسل إيميل لـ <EMAIL>")
    print("   2. يرفق كود العميل ورقم الجهاز")
    print("   3. يرفق إثبات الدفع")
    print("   4. المطور يولد كود التجديد")
    print("   5. المطور يرسل الكود للعميل")
    
    print("\n💡 نصائح:")
    print("   • رد سريع على الإيميلات")
    print("   • تأكد من الدفع قبل إرسال الكود")
    print("   • احفظ سجل بجميع العملاء والأكواد")
    print("   • قدم دعم فني ممتاز")

def main():
    """الدالة الرئيسية"""
    print("📧 تحديث معلومات التواصل في نظام التراخيص")
    print("=" * 60)
    
    # تحديث معلومات التواصل
    update_contact_info()
    
    # إنشاء قالب التواصل
    create_contact_template()
    
    # اختبار الدمج
    test_contact_integration()
    
    # عرض الملخص
    show_integration_summary()
    
    print("\n🎉 تم تحديث معلومات التواصل بنجاح!")
    print("📧 الإيميل الجديد: <EMAIL>")

if __name__ == "__main__":
    main()
