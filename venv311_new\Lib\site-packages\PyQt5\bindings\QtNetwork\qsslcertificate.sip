// qsslcertificate.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

class QSslCertificate
{
%TypeHeaderCode
#include <qsslcertificate.h>
%End

public:
    enum SubjectInfo
    {
        Organization,
        CommonName,
        LocalityName,
        OrganizationalUnitName,
        CountryName,
        StateOrProvinceName,
        DistinguishedNameQualifier,
        SerialNumber,
        EmailAddress,
    };

    QSslCertificate(QIODevice *device, QSsl::EncodingFormat format = QSsl::Pem) /ReleaseGIL/;
    QSslCertificate(const QByteArray &data = QByteArray(), QSsl::EncodingFormat format = QSsl::Pem);
    QSslCertificate(const QSslCertificate &other);
    ~QSslCertificate();
    bool operator==(const QSslCertificate &other) const;
    bool operator!=(const QSslCertificate &other) const;
    bool isNull() const;
    void clear();
    QByteArray version() const;
    QByteArray serialNumber() const;
    QByteArray digest(QCryptographicHash::Algorithm algorithm = QCryptographicHash::Md5) const;
    QStringList issuerInfo(QSslCertificate::SubjectInfo info) const;
    QStringList issuerInfo(const QByteArray &attribute) const;
    QStringList subjectInfo(QSslCertificate::SubjectInfo info) const;
    QStringList subjectInfo(const QByteArray &attribute) const;
    QMultiMap<QSsl::AlternativeNameEntryType, QString> subjectAlternativeNames() const;
    QDateTime effectiveDate() const;
    QDateTime expiryDate() const;
    QSslKey publicKey() const;
    QByteArray toPem() const;
    QByteArray toDer() const;
    static QList<QSslCertificate> fromPath(const QString &path, QSsl::EncodingFormat format = QSsl::Pem, QRegExp::PatternSyntax syntax = QRegExp::FixedString);
    static QList<QSslCertificate> fromDevice(QIODevice *device, QSsl::EncodingFormat format = QSsl::Pem);
    static QList<QSslCertificate> fromData(const QByteArray &data, QSsl::EncodingFormat format = QSsl::Pem);
    Qt::HANDLE handle() const;
    void swap(QSslCertificate &other /Constrained/);
    bool isBlacklisted() const;
    QList<QByteArray> subjectInfoAttributes() const;
    QList<QByteArray> issuerInfoAttributes() const;
    QList<QSslCertificateExtension> extensions() const;
    QString toText() const;
    static QList<QSslError> verify(QList<QSslCertificate> certificateChain, const QString &hostName = QString());
%If (Qt_5_4_0 -)
    bool isSelfSigned() const;
%End
%If (Qt_5_4_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
%If (Qt_5_4_0 -)
    static bool importPkcs12(QIODevice *device, QSslKey *key, QSslCertificate *certificate, QList<QSslCertificate> *caCertificates = 0, const QByteArray &passPhrase = QByteArray()) /ReleaseGIL/;
%End
%If (Qt_5_12_0 -)
    QString issuerDisplayName() const;
%End
%If (Qt_5_12_0 -)
    QString subjectDisplayName() const;
%End
%If (Qt_5_15_0 -)

    enum class PatternSyntax
    {
        RegularExpression,
        Wildcard,
        FixedString,
    };

%End
};

%End
