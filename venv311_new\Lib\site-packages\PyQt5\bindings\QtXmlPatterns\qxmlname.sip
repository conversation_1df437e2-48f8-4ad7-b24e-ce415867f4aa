// qxmlname.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlName
{
%TypeHeaderCode
#include <qxmlname.h>
%End

public:
    QXmlName();
    QXmlName(QXmlNamePool &namePool, const QString &localName, const QString &namespaceUri = QString(), const QString &prefix = QString());
%If (Qt_5_9_0 -)
    QXmlName(const QXmlName &other);
%End
    QString namespaceUri(const QXmlNamePool &query) const;
    QString prefix(const QXmlNamePool &query) const;
    QString localName(const QXmlNamePool &query) const;
    QString toClarkName(const QXmlNamePool &query) const;
    bool operator==(const QXmlName &other) const;
    bool operator!=(const QXmlName &other) const;
    bool isNull() const;
    static bool isNCName(const QString &candidate);
    static QXmlName fromClarkName(const QString &clarkName, const QXmlNamePool &namePool);
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};
