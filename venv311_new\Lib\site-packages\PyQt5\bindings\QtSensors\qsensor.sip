// qsensor.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)
typedef quint64 qtimestamp;
%End
%If (Qt_5_1_0 -)
typedef QList<QPair<int, int>> qrangelist;
%End
%If (Qt_5_1_0 -)

struct qoutputrange
{
%TypeHeaderCode
#include <qsensor.h>
%End

    qreal minimum;
    qreal maximum;
    qreal accuracy;
};

%End
%If (Qt_5_1_0 -)
typedef QList<qoutputrange> qoutputrangelist;
%End
%If (Qt_5_1_0 -)

class QSensorReading : QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsensor.h>
%End

public:
    virtual ~QSensorReading();
    quint64 timestamp() const;
    void setTimestamp(quint64 timestamp);
    int valueCount() const;
    QVariant value(int index) const;
};

%End
%If (Qt_5_1_0 -)

class QSensorFilter
{
%TypeHeaderCode
#include <qsensor.h>
%End

public:
    virtual bool filter(QSensorReading *reading) = 0;

protected:
    QSensorFilter();
    virtual ~QSensorFilter();
};

%End
%If (Qt_5_1_0 -)

class QSensor : QObject
{
%TypeHeaderCode
#include <qsensor.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QSensor, &sipType_QSensor, 2, 1},
        {sipName_QSensorReading, &sipType_QSensorReading, 21, -1},
        {sipName_QAccelerometer, &sipType_QAccelerometer, -1, 3},
        {sipName_QAltimeter, &sipType_QAltimeter, -1, 4},
        {sipName_QAmbientLightSensor, &sipType_QAmbientLightSensor, -1, 5},
        {sipName_QAmbientTemperatureSensor, &sipType_QAmbientTemperatureSensor, -1, 6},
        {sipName_QCompass, &sipType_QCompass, -1, 7},
    #if QT_VERSION >= 0x050400
        {sipName_QDistanceSensor, &sipType_QDistanceSensor, -1, 8},
    #else
        {0, 0, -1, 8},
    #endif
        {sipName_QGyroscope, &sipType_QGyroscope, -1, 9},
        {sipName_QHolsterSensor, &sipType_QHolsterSensor, -1, 10},
    #if QT_VERSION >= 0x050900
        {sipName_QHumiditySensor, &sipType_QHumiditySensor, -1, 11},
    #else
        {0, 0, -1, 11},
    #endif
        {sipName_QIRProximitySensor, &sipType_QIRProximitySensor, -1, 12},
    #if QT_VERSION >= 0x050900
        {sipName_QLidSensor, &sipType_QLidSensor, -1, 13},
    #else
        {0, 0, -1, 13},
    #endif
        {sipName_QLightSensor, &sipType_QLightSensor, -1, 14},
        {sipName_QMagnetometer, &sipType_QMagnetometer, -1, 15},
        {sipName_QOrientationSensor, &sipType_QOrientationSensor, -1, 16},
        {sipName_QPressureSensor, &sipType_QPressureSensor, -1, 17},
        {sipName_QProximitySensor, &sipType_QProximitySensor, -1, 18},
        {sipName_QRotationSensor, &sipType_QRotationSensor, -1, 19},
        {sipName_QTapSensor, &sipType_QTapSensor, -1, 20},
        {sipName_QTiltSensor, &sipType_QTiltSensor, -1, -1},
        {sipName_QAccelerometerReading, &sipType_QAccelerometerReading, -1, 22},
        {sipName_QAltimeterReading, &sipType_QAltimeterReading, -1, 23},
        {sipName_QAmbientLightReading, &sipType_QAmbientLightReading, -1, 24},
        {sipName_QAmbientTemperatureReading, &sipType_QAmbientTemperatureReading, -1, 25},
        {sipName_QCompassReading, &sipType_QCompassReading, -1, 26},
    #if QT_VERSION >= 0x050400
        {sipName_QDistanceReading, &sipType_QDistanceReading, -1, 27},
    #else
        {0, 0, -1, 27},
    #endif
        {sipName_QGyroscopeReading, &sipType_QGyroscopeReading, -1, 28},
        {sipName_QHolsterReading, &sipType_QHolsterReading, -1, 29},
    #if QT_VERSION >= 0x050900
        {sipName_QHumidityReading, &sipType_QHumidityReading, -1, 30},
    #else
        {0, 0, -1, 30},
    #endif
        {sipName_QIRProximityReading, &sipType_QIRProximityReading, -1, 31},
    #if QT_VERSION >= 0x050900
        {sipName_QLidReading, &sipType_QLidReading, -1, 32},
    #else
        {0, 0, -1, 32},
    #endif
        {sipName_QLightReading, &sipType_QLightReading, -1, 33},
        {sipName_QMagnetometerReading, &sipType_QMagnetometerReading, -1, 34},
        {sipName_QOrientationReading, &sipType_QOrientationReading, -1, 35},
        {sipName_QPressureReading, &sipType_QPressureReading, -1, 36},
        {sipName_QProximityReading, &sipType_QProximityReading, -1, 37},
        {sipName_QRotationReading, &sipType_QRotationReading, -1, 38},
        {sipName_QTapReading, &sipType_QTapReading, -1, 39},
        {sipName_QTiltReading, &sipType_QTiltReading, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Feature
    {
        Buffering,
        AlwaysOn,
        GeoValues,
        FieldOfView,
        AccelerationMode,
        SkipDuplicates,
        AxesOrientation,
%If (Qt_5_2_0 -)
        PressureSensorTemperature,
%End
    };

    enum AxesOrientationMode
    {
        FixedOrientation,
        AutomaticOrientation,
        UserOrientation,
    };

    QSensor(const QByteArray &type, QObject *parent /TransferThis/ = 0);
    virtual ~QSensor();
    QByteArray identifier() const;
    void setIdentifier(const QByteArray &identifier);
    QByteArray type() const;
    bool connectToBackend();
    bool isConnectedToBackend() const;
    bool isBusy() const;
    void setActive(bool active);
    bool isActive() const;
    bool isAlwaysOn() const;
    void setAlwaysOn(bool alwaysOn);
    bool skipDuplicates() const;
    void setSkipDuplicates(bool skipDuplicates);
    qrangelist availableDataRates() const;
    int dataRate() const;
    void setDataRate(int rate);
    qoutputrangelist outputRanges() const;
    int outputRange() const;
    void setOutputRange(int index);
    QString description() const;
    int error() const;
    void addFilter(QSensorFilter *filter);
    void removeFilter(QSensorFilter *filter);
    QList<QSensorFilter *> filters() const;
    QSensorReading *reading() const;
    static QList<QByteArray> sensorTypes();
    static QList<QByteArray> sensorsForType(const QByteArray &type);
    static QByteArray defaultSensorForType(const QByteArray &type);
    bool isFeatureSupported(QSensor::Feature feature) const;
    QSensor::AxesOrientationMode axesOrientationMode() const;
    void setAxesOrientationMode(QSensor::AxesOrientationMode axesOrientationMode);
    int currentOrientation() const;
    void setCurrentOrientation(int currentOrientation);
    int userOrientation() const;
    void setUserOrientation(int userOrientation);
    int maxBufferSize() const;
    void setMaxBufferSize(int maxBufferSize);
    int efficientBufferSize() const;
    void setEfficientBufferSize(int efficientBufferSize);
    int bufferSize() const;
    void setBufferSize(int bufferSize);

public slots:
    bool start();
    void stop();

signals:
    void busyChanged();
    void activeChanged();
    void readingChanged();
    void sensorError(int error);
    void availableSensorsChanged();
    void alwaysOnChanged();
    void dataRateChanged();
    void skipDuplicatesChanged(bool skipDuplicates);
    void axesOrientationModeChanged(QSensor::AxesOrientationMode axesOrientationMode /ScopesStripped=1/);
    void currentOrientationChanged(int currentOrientation);
    void userOrientationChanged(int userOrientation);
    void maxBufferSizeChanged(int maxBufferSize);
    void efficientBufferSizeChanged(int efficientBufferSize);
    void bufferSizeChanged(int bufferSize);
};

%End
