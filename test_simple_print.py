#!/usr/bin/env python3
"""
اختبار بسيط لنظام الطباعة المتقدم
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار بسيط - الطباعة المتقدمة")
        self.setGeometry(200, 200, 500, 400)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_label = QPushButton("🎨 اختبار الطباعة المتقدمة")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #27AE60, #2ECC71);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات
        info_label = QPushButton("""
✅ النظام الجديد يستخدم QPainter للرسم المباشر
✅ ألوان زاهية محفوظة 100%
✅ تدرجات لونية جميلة
✅ طباعة وحفظ PDF بجودة عالية

🎯 اضغط على الأزرار أدناه للاختبار
        """)
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background: #F8F9FA;
                color: #2C3E50;
                font-size: 14px;
                padding: 15px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                text-align: left;
                margin: 10px 0;
            }
        """)
        layout.addWidget(info_label)
        
        # أزرار الاختبار
        test_btn1 = QPushButton("🧾 اختبار فاتورة رقم 1")
        test_btn1.setStyleSheet(self.get_button_style("#3498DB"))
        test_btn1.clicked.connect(lambda: self.test_invoice(1))
        layout.addWidget(test_btn1)
        
        test_btn7 = QPushButton("🧾 اختبار فاتورة رقم 7")
        test_btn7.setStyleSheet(self.get_button_style("#E67E22"))
        test_btn7.clicked.connect(lambda: self.test_invoice(7))
        layout.addWidget(test_btn7)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#E74C3C"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: {color}DD;
            }}
        """
    
    def test_invoice(self, invoice_id):
        """اختبار طباعة الفاتورة"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎨 اختبار بسيط للطباعة المتقدمة")
    print("=" * 50)
    print("✅ تم تحميل النظام بنجاح")
    print("🎯 افتح النافذة للاختبار")
    
    window = SimpleTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
