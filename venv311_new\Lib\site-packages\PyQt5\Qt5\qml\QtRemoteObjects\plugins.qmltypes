import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtRemoteObjects 5.15'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QRemoteObjectAbstractPersistedStore"
        prototype: "QObject"
        exports: ["QtRemoteObjects/PersistedStore 5.12"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QRemoteObjectHost"
        prototype: "QRemoteObjectHostBase"
        exports: ["QtRemoteObjects/Host 5.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "hostUrl"; type: "QUrl" }
    }
    Component {
        name: "QRemoteObjectHostBase"
        prototype: "QRemoteObjectNode"
        Enum {
            name: "AllowedSchemas"
            values: {
                "BuiltInSchemasOnly": 0,
                "AllowExternalRegistration": 1
            }
        }
        Method {
            name: "enableRemoting"
            type: "bool"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "enableRemoting"
            type: "bool"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "disableRemoting"
            type: "bool"
            Parameter { name: "remoteObject"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        name: "QRemoteObjectNode"
        prototype: "QObject"
        exports: ["QtRemoteObjects/Node 5.12"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ErrorCode"
            values: {
                "NoError": 0,
                "RegistryNotAcquired": 1,
                "RegistryAlreadyHosted": 2,
                "NodeIsNoServer": 3,
                "ServerAlreadyCreated": 4,
                "UnintendedRegistryHosting": 5,
                "OperationNotValidOnClientNode": 6,
                "SourceNotRegistered": 7,
                "MissingObjectName": 8,
                "HostUrlInvalid": 9,
                "ProtocolMismatch": 10,
                "ListenFailed": 11
            }
        }
        Property { name: "registryUrl"; type: "QUrl" }
        Property {
            name: "persistedStore"
            type: "QRemoteObjectAbstractPersistedStore"
            isPointer: true
        }
        Property { name: "heartbeatInterval"; type: "int" }
        Signal {
            name: "remoteObjectAdded"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "remoteObjectRemoved"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QRemoteObjectNode::ErrorCode" }
        }
        Signal {
            name: "heartbeatIntervalChanged"
            Parameter { name: "heartbeatInterval"; type: "int" }
        }
        Method {
            name: "connectToNode"
            type: "bool"
            Parameter { name: "address"; type: "QUrl" }
        }
    }
    Component {
        name: "QRemoteObjectSettingsStore"
        prototype: "QRemoteObjectAbstractPersistedStore"
        exports: ["QtRemoteObjects/SettingsStore 5.12"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QtQmlRemoteObjects"
        prototype: "QObject"
        exports: ["QtRemoteObjects/QtRemoteObjects 5.14"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method {
            name: "watch"
            type: "QJSValue"
            Parameter { name: "reply"; type: "QRemoteObjectPendingCall" }
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "watch"
            type: "QJSValue"
            Parameter { name: "reply"; type: "QRemoteObjectPendingCall" }
        }
    }
}
