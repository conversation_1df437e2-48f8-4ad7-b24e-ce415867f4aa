#!/usr/bin/env python3
"""
اختبار المسافات المحسنة في طباعة الرول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار المسافات المحسنة في طباعة الرول...")
    print("=" * 60)
    print("🚀 تحسينات المسافات الجديدة:")
    print("   • مسافة كبيرة بين الترويسة ومعلومات الفاتورة: 30px")
    print("   • مسافة أكبر بين أسطر معلومات الفاتورة: 25px")
    print("   • مسافة كبيرة قبل الخط الفاصل: 35px")
    print("   • مسافة أكبر بعد الخط الفاصل: 25px")
    print("   • مسافة أكبر بين اسم المنتج وتفاصيله: 18px")
    print("   • مسافة أكبر بين المنتجات: 28px + 10px إضافية")
    print("   • مسافة كبيرة قبل خط فاصل الإجمالي: 20px")
    print("   • مسافة أكبر بعد خط فاصل الإجمالي: 25px")
    print("=" * 60)
    print("🎯 النتيجة المتوقعة:")
    print("   ✅ فصل واضح بين معلومات البراند وتفاصيل الفاتورة")
    print("   ✅ فصل واضح بين تفاصيل العميل والمنتجات")
    print("   ✅ مسافات مريحة للعين بين جميع الأقسام")
    print("   ✅ تنظيم احترافي للمحتوى")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع المسافات المحسنة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من المسافات في طباعة الرول:")
        print("   • اختر نوع الطابعة: 'رول'")
        print("   • لاحظ المسافة الكبيرة بين الترويسة ومعلومات الفاتورة")
        print("   • تحقق من الفصل الواضح بين تفاصيل العميل والمنتجات")
        print("   • لاحظ المسافات المريحة بين المنتجات")
        print("   • تحقق من الفصل الواضح قبل الإجمالي")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة المسافات:")
    print("   قبل التحسين:")
    print("     ❌ مسافات صغيرة (15px)")
    print("     ❌ نصوص متداخلة")
    print("     ❌ صعوبة في التمييز بين الأقسام")
    print("")
    print("   بعد التحسين:")
    print("     ✅ مسافات كبيرة (25-35px)")
    print("     ✅ فصل واضح بين الأقسام")
    print("     ✅ سهولة في القراءة والفهم")
    print("     ✅ تنظيم احترافي")

if __name__ == "__main__":
    main()
