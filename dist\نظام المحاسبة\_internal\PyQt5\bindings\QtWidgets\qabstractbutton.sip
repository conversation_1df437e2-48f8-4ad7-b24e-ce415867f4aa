// qabstractbutton.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractButton : public QWidget
{
%TypeHeaderCode
#include <qabstractbutton.h>
%End

public:
    explicit QAbstractButton(QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractButton();
    void setAutoRepeatDelay(int);
    int autoRepeatDelay() const;
    void setAutoRepeatInterval(int);
    int autoRepeatInterval() const;
    void setText(const QString &text);
    QString text() const;
    void setIcon(const QIcon &icon);
    QIcon icon() const;
    QSize iconSize() const;
    void setShortcut(const QKeySequence &key);
    QKeySequence shortcut() const;
    void setCheckable(bool);
    bool isCheckable() const;
    bool isChecked() const;
    void setDown(bool);
    bool isDown() const;
    void setAutoRepeat(bool);
    bool autoRepeat() const;
    void setAutoExclusive(bool);
    bool autoExclusive() const;
    QButtonGroup *group() const;

public slots:
    void setIconSize(const QSize &size);
    void animateClick(int msecs = 100);
    void click();
    void toggle();
    void setChecked(bool);

signals:
    void pressed();
    void released();
    void clicked(bool checked = false);
    void toggled(bool checked);

protected:
    virtual void paintEvent(QPaintEvent *e) = 0;
    virtual bool hitButton(const QPoint &pos) const;
    virtual void checkStateSet();
    virtual void nextCheckState();
    virtual bool event(QEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void keyReleaseEvent(QKeyEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void changeEvent(QEvent *e);
    virtual void timerEvent(QTimerEvent *e);
};
