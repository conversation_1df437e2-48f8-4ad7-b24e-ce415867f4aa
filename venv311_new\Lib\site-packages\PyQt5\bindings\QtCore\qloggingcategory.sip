// qloggingcategory.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QLoggingCategory
{
%TypeHeaderCode
#include <qloggingcategory.h>
%End

public:
    explicit QLoggingCategory(const char *category);
    QLoggingCategory(const char *category, QtMsgType severityLevel);
    ~QLoggingCategory();
    bool isEnabled(QtMsgType type) const;
    void setEnabled(QtMsgType type, bool enable);
    bool isDebugEnabled() const;
    bool isInfoEnabled() const;
    bool isWarningEnabled() const;
    bool isCriticalEnabled() const;
    const char *categoryName() const;
    QLoggingCategory &operator()();
    static QLoggingCategory *defaultCategory();
    static void setFilterRules(const QString &rules);

private:
    QLoggingCategory(const QLoggingCategory &);
};

%End
