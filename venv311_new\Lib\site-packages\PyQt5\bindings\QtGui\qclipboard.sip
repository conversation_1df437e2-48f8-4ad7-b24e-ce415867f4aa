// qclipboard.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QClipboard : QObject
{
%TypeHeaderCode
#include <qclipboard.h>
%End

    explicit QClipboard(QObject *parent /TransferThis/);
    virtual ~QClipboard();

public:
    enum Mode
    {
        Clipboard,
        Selection,
        FindBuffer,
    };

    void clear(QClipboard::Mode mode = QClipboard::Clipboard);
    bool supportsFindBuffer() const;
    bool supportsSelection() const;
    bool ownsClipboard() const;
    bool ownsFindBuffer() const;
    bool ownsSelection() const;
    QString text(QClipboard::Mode mode = QClipboard::Clipboard) const;
    SIP_PYTUPLE text(const QString &subtype, QClipboard::Mode mode = QClipboard::Clipboard) const /TypeHint="Tuple[QString, QString]"/;
%MethodCode
        QString *text;
        QString *subtype = new QString(*a0);
        
        Py_BEGIN_ALLOW_THREADS
        text = new QString(sipCpp->text(*subtype, a1));
        Py_END_ALLOW_THREADS
        
        PyObject *text_obj = sipConvertFromNewType(text, sipType_QString, NULL);
        PyObject *subtype_obj = sipConvertFromNewType(subtype, sipType_QString, NULL);
        
        if (text_obj && subtype_obj)
            sipRes = PyTuple_Pack(2, text_obj, subtype_obj);
        
        Py_XDECREF(text_obj);
        Py_XDECREF(subtype_obj);
%End

    void setText(const QString &, QClipboard::Mode mode = QClipboard::Clipboard);
    const QMimeData *mimeData(QClipboard::Mode mode = QClipboard::Clipboard) const;
    void setMimeData(QMimeData *data /GetWrapper/, QClipboard::Mode mode = QClipboard::Clipboard);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setMimeData(a0, a1);
        Py_END_ALLOW_THREADS
        
        // Transfer ownership to C++ and make sure the Python object stays alive by
        // giving it a reference to itself.  The cycle will be broken by QMimeData's
        // virtual dtor.  The reason we don't do the obvious and just use /Transfer/ is
        // that the QClipboard Python object we would transfer ownership to is likely
        // to be garbage collected immediately afterwards.
        sipTransferTo(a0Wrapper, a0Wrapper);
%End

    QImage image(QClipboard::Mode mode = QClipboard::Clipboard) const;
    QPixmap pixmap(QClipboard::Mode mode = QClipboard::Clipboard) const;
    void setImage(const QImage &, QClipboard::Mode mode = QClipboard::Clipboard);
    void setPixmap(const QPixmap &, QClipboard::Mode mode = QClipboard::Clipboard);

signals:
    void changed(QClipboard::Mode mode);
    void dataChanged();
    void findBufferChanged();
    void selectionChanged();

private:
    QClipboard(const QClipboard &);
};
