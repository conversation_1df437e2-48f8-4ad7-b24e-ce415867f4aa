// qvpiemodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qvpiemodelmapper.h>
%End

    class QVPieModelMapper : QObject
    {
%TypeHeaderCode
#include <qvpiemodelmapper.h>
%End

    public:
        explicit QVPieModelMapper(QObject *parent /TransferThis/ = 0);
        int valuesColumn() const;
        void setValuesColumn(int valuesColumn);
        int labelsColumn() const;
        void setLabelsColumn(int labelsColumn);
        QAbstractItemModel *model() const;
        void setModel(QAbstractItemModel *model /KeepReference/);
        QtCharts::QPieSeries *series() const;
        void setSeries(QtCharts::QPieSeries *series);
        int firstRow() const;
        void setFirstRow(int firstRow);
        int rowCount() const;
        void setRowCount(int rowCount);

    signals:
        void seriesReplaced();
        void modelReplaced();
        void valuesColumnChanged();
        void labelsColumnChanged();
        void firstRowChanged();
        void rowCountChanged();
    };
};
