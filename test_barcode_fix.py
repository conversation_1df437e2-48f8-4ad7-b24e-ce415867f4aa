#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح أزرار مسح الباركود
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("🔧 اختبار إصلاح أزرار مسح الباركود")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("✅ تم إصلاح الخطأ في استدعاء نافذة مسح الباركود")
    print("")
    print("🔧 الإصلاح المطبق:")
    print("   قبل: BarcodeScannerDialog(self, scan_type='camera')")
    print("   بعد: BarcodeScannerDialog(self, 'camera')")
    print("")
    print("🎯 الآن الأزرار تعمل بشكل صحيح!")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 اختبر الآن:")
        print("   1️⃣ اذهب للمخزون")
        print("   2️⃣ اضغط 'إضافة منتج جديد'")
        print("   3️⃣ اضغط أي زر 📷 أو 🔍")
        print("   4️⃣ يجب أن تفتح نافذة مسح الباركود")
        print("")
        print("🎉 الأزرار تعمل الآن بشكل مثالي!")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
