// qaudiooutput.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioOutput : QObject
{
%TypeHeaderCode
#include <qaudiooutput.h>
%End

public:
    QAudioOutput(const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    QAudioOutput(const QAudioDeviceInfo &audioDevice, const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    virtual ~QAudioOutput();
    QAudioFormat format() const;
    void start(QIODevice *device);
    QIODevice *start();
    void stop();
    void reset();
    void suspend();
    void resume();
    void setBufferSize(int bytes);
    int bufferSize() const;
    int bytesFree() const;
    int periodSize() const;
    void setNotifyInterval(int milliSeconds);
    int notifyInterval() const;
    qint64 processedUSecs() const;
    qint64 elapsedUSecs() const;
    QAudio::Error error() const;
    QAudio::State state() const;

signals:
    void stateChanged(QAudio::State);
    void notify();

public:
    void setVolume(qreal);
    qreal volume() const;
    QString category() const;
    void setCategory(const QString &category);
};
