// qwebenginescript.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_5_0 -)

class QWebEngineScript
{
%TypeHeaderCode
#include <qwebenginescript.h>
%End

public:
    enum InjectionPoint
    {
        Deferred,
        DocumentReady,
        DocumentCreation,
    };

    enum ScriptWorldId
    {
        MainWorld,
        ApplicationWorld,
        UserWorld,
    };

    QWebEngineScript();
    QWebEngineScript(const QWebEngineScript &other);
    ~QWebEngineScript();
    bool isNull() const;
    QString name() const;
    void setName(const QString &);
    QString sourceCode() const;
    void setSourceCode(const QString &);
    QWebEngineScript::InjectionPoint injectionPoint() const;
    void setInjectionPoint(QWebEngineScript::InjectionPoint);
    quint32 worldId() const;
    void setWorldId(quint32);
    bool runsOnSubFrames() const;
    void setRunsOnSubFrames(bool on);
    bool operator==(const QWebEngineScript &other) const;
    bool operator!=(const QWebEngineScript &other) const;
    void swap(QWebEngineScript &other /Constrained/);
};

%End
