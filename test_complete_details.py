#!/usr/bin/env python3
"""
اختبار تفاصيل الفاتورة الكاملة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار تفاصيل الفاتورة الكاملة...")
    print("=" * 60)
    print("📋 التحسينات المضافة:")
    print("   A4:")
    print("     ✅ إضافة حالة الفاتورة (مدفوعة/جزئية/غير مدفوعة)")
    print("     ✅ عرض المبلغ المتبقي دائماً")
    print("     ✅ أيقونات واضحة للحالة:")
    print("       • ✅ مدفوعة (عندما المتبقي = 0)")
    print("       • ⚠️ جزئية (عندما المدفوع > 0 والمتبقي > 0)")
    print("       • ❌ غير مدفوعة (عندما المدفوع = 0)")
    print("     ✅ ترتيب أفضل للمعلومات")
    print("")
    print("   الرول:")
    print("     ✅ إضافة قسم تفاصيل الفاتورة كامل")
    print("     ✅ المجموع الفرعي")
    print("     ✅ المبلغ المدفوع")
    print("     ✅ المبلغ المتبقي")
    print("     ✅ حالة الفاتورة مع أيقونة")
    print("     ✅ الخصم (إذا وجد)")
    print("     ✅ تصميم مناسب للرول (ارتفاع 120px)")
    print("     ✅ خط مناسب للمساحة الضيقة")
    print("=" * 60)
    print("🎨 التصميم:")
    print("   • خلفية بيضاء مع حدود سوداء")
    print("   • عنوان واضح: '📋 تفاصيل الفاتورة'")
    print("   • معلومات منظمة في سطور")
    print("   • أيقونات مميزة لكل معلومة")
    print("   • مسافات مناسبة بين العناصر")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع التفاصيل الكاملة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من التفاصيل الجديدة:")
        print("   A4:")
        print("     • ابحث عن قسم 'تفاصيل الفاتورة'")
        print("     • تحقق من وجود حالة الفاتورة")
        print("     • لاحظ المبلغ المتبقي")
        print("     • تحقق من الأيقونات")
        print("   الرول:")
        print("     • ابحث عن قسم 'تفاصيل الفاتورة' الجديد")
        print("     • تحقق من جميع المعلومات المالية")
        print("     • لاحظ حالة الفاتورة مع الأيقونة")
        print("     • تحقق من التنسيق المناسب للرول")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة التفاصيل:")
    print("   قبل التحسين:")
    print("     A4:")
    print("       ❌ حالة الفاتورة مفقودة")
    print("       ❌ المتبقي يظهر أحياناً فقط")
    print("       ❌ لا توجد أيقونات واضحة")
    print("     الرول:")
    print("       ❌ لا يوجد قسم تفاصيل أصلاً")
    print("       ❌ معلومات مالية مفقودة")
    print("       ❌ لا توجد حالة الفاتورة")
    print("")
    print("   بعد التحسين:")
    print("     A4:")
    print("       ✅ حالة الفاتورة واضحة مع أيقونة")
    print("       ✅ المتبقي يظهر دائماً")
    print("       ✅ أيقونات مميزة لكل حالة")
    print("       ✅ ترتيب منطقي للمعلومات")
    print("     الرول:")
    print("       ✅ قسم تفاصيل كامل ومنظم")
    print("       ✅ جميع المعلومات المالية")
    print("       ✅ حالة الفاتورة مع أيقونة")
    print("       ✅ تصميم مناسب للمساحة الضيقة")
    print("")
    print("🎯 الفوائد:")
    print("   ✅ معلومات شاملة في كلا النوعين")
    print("   ✅ وضوح حالة الدفع")
    print("   ✅ سهولة متابعة المستحقات")
    print("   ✅ مظهر احترافي موحد")
    print("   ✅ تجربة مستخدم محسنة")

if __name__ == "__main__":
    main()
