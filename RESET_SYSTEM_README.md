# 🔄 ميزة إعادة ضبط النظام

## نظرة عامة
تم إضافة ميزة "إعادة ضبط النظام" في تبويب النظام ضمن إعدادات التطبيق. هذه الميزة متاحة للمدير فقط وتتطلب كلمة مرور للوصول إليها.

## المميزات

### 🔐 حماية بكلمة مرور
- تتطلب كلمة مرور المدير للوصول
- كلمة المرور الافتراضية: `sicoo123`
- تحقق من صلاحيات المستخدم

### 📋 خيارات الإعادة ضبط
يمكن للمدير اختيار ما يريد إعادة ضبطه:

1. **🗑️ حذف جميع الفواتير والمرتجعات**
   - حذف جميع فواتير المبيعات
   - حذف جميع فواتير المشتريات
   - حذف جميع المرتجعات

2. **🗑️ حذف جميع المنتجات**
   - حذف جميع المنتجات من قاعدة البيانات
   - حذف معلومات الأسعار والوحدات

3. **📦 إعادة تعيين المخزون (صفر)**
   - تعيين كمية جميع المنتجات إلى صفر
   - الاحتفاظ بالمنتجات مع إعادة تعيين الكميات

4. **👥 حذف جميع العملاء**
   - حذف جميع بيانات العملاء
   - حذف سجلات الحسابات

5. **🏢 حذف جميع الموردين**
   - حذف جميع بيانات الموردين
   - حذف سجلات الحسابات

6. **📋 حذف سجل العمليات**
   - حذف سجل جميع العمليات
   - حذف سجل تسجيل الدخول والخروج

### 💾 النسخ الاحتياطي التلقائي
- إنشاء نسخة احتياطية تلقائياً قبل الإعادة ضبط
- اسم الملف: `backup_before_reset_YYYYMMDD_HHMMSS.db`
- يمكن استعادة البيانات من النسخة الاحتياطية

### ⚠️ التحذيرات والتأكيدات
- تحذير واضح أن العملية لا يمكن التراجع عنها
- تأكيد مزدوج قبل تنفيذ العملية
- عرض تفصيلي للخيارات المحددة

### 🔄 واجهة مستخدم متقدمة
- تصميم عصري وجذاب
- شريط تقدم أثناء العملية
- رسائل واضحة لكل خطوة
- أزرار تحكم سهلة الاستخدام

## كيفية الاستخدام

### الوصول للميزة
1. افتح التطبيق وسجل دخول كمدير
2. اذهب إلى: **النظام** → **إعدادات التطبيق**
3. انتقل لتبويب **🔄 إعادة ضبط** (متاح للمدير فقط)

### خطوات الاستخدام
1. **أدخل كلمة مرور المدير**
2. **اختر الخيارات المطلوبة** للإعادة ضبط
3. **اضغط "إعادة ضبط النظام"**
4. **تأكد من العملية** في نافذة التأكيد
5. **انتظر انتهاء العملية** مع مراقبة التقدم

### ملاحظات مهمة
- ⚠️ **تحذير**: العملية لا يمكن التراجع عنها
- 💾 **نسخة احتياطية**: يتم إنشاؤها تلقائياً
- 🔐 **أمان**: تتطلب صلاحيات المدير
- ⏱️ **الوقت**: قد تستغرق بضع دقائق حسب حجم البيانات

## الأمان والحماية

### التحقق من الصلاحيات
```python
def check_admin_permission(self):
    """التحقق من صلاحيات المدير"""
    try:
        parent = self.parent()
        if parent and hasattr(parent, 'check_permission'):
            return parent.check_permission("إدارة_المستخدمين")
        else:
            return True
    except:
        return True
```

### التحقق من كلمة المرور
```python
def verify_admin_password(self):
    """التحقق من كلمة مرور المدير"""
    password = self.password_edit.text().strip()
    admin_password = "sicoo123"
    
    if password == admin_password:
        return True
    else:
        QMessageBox.warning(self, "خطأ", "كلمة المرور غير صحيحة!")
        return False
```

## الملفات المضافة

### `gui/reset_system_dialog.py`
- النافذة الرئيسية لإعادة ضبط النظام
- واجهة المستخدم المتقدمة
- منطق التحقق من الصلاحيات

### `gui/settings_dialog.py`
- إضافة تبويب إعادة ضبط النظام
- التحقق من صلاحيات المدير
- دمج الميزة مع نظام الإعدادات

## التقنيات المستخدمة

### SQLAlchemy
```python
# حذف البيانات من قاعدة البيانات
session.execute(text("DELETE FROM transactions"))
session.execute(text("DELETE FROM transaction_items"))
session.execute(text("DELETE FROM products"))
session.execute(text("UPDATE products SET quantity = 0"))
```

### QThread
```python
class ResetWorker(QThread):
    """عامل منفصل لتنفيذ عملية الإعادة ضبط"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
```

### PyQt5
- واجهة مستخدم متقدمة
- شريط تقدم
- رسائل تأكيد
- تصميم عصري

## الاستخدامات المحتملة

### 🏢 الشركات الجديدة
- بدء تشغيل نظيف للنظام
- إزالة بيانات الاختبار

### 🔄 إعادة هيكلة البيانات
- تنظيف البيانات القديمة
- إعادة تنظيم قاعدة البيانات

### 🧪 بيئة الاختبار
- إعادة تعيين النظام للاختبار
- تجربة الميزات الجديدة

### 📊 التقارير السنوية
- بداية سنة مالية جديدة
- إعادة تعيين الإحصائيات

## الدعم والمساعدة

### في حالة المشاكل
1. تحقق من كلمة المرور
2. تأكد من صلاحيات المدير
3. راجع النسخة الاحتياطية
4. اتصل بالدعم الفني

### معلومات الاتصال
- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: 01010101010

---

**ملاحظة**: هذه الميزة مصممة للمديرين فقط ويجب استخدامها بحذر شديد. 