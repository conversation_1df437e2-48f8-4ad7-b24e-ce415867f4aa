// QtMultimediamod.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtMultimedia, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip
%Import QtNetwork/QtNetworkmod.sip

%Copying
Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt5.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper

%Include qabstractvideobuffer.sip
%Include qabstractvideofilter.sip
%Include qabstractvideosurface.sip
%Include qaudio.sip
%Include qaudiobuffer.sip
%Include qaudiodecoder.sip
%Include qaudiodecodercontrol.sip
%Include qaudiodeviceinfo.sip
%Include qaudioencodersettingscontrol.sip
%Include qaudioformat.sip
%Include qaudioinput.sip
%Include qaudioinputselectorcontrol.sip
%Include qaudiooutput.sip
%Include qaudiooutputselectorcontrol.sip
%Include qaudioprobe.sip
%Include qaudiorecorder.sip
%Include qaudiorolecontrol.sip
%Include qcamera.sip
%Include qcameracapturebufferformatcontrol.sip
%Include qcameracapturedestinationcontrol.sip
%Include qcameracontrol.sip
%Include qcameraexposure.sip
%Include qcameraexposurecontrol.sip
%Include qcamerafeedbackcontrol.sip
%Include qcameraflashcontrol.sip
%Include qcamerafocus.sip
%Include qcamerafocuscontrol.sip
%Include qcameraimagecapture.sip
%Include qcameraimagecapturecontrol.sip
%Include qcameraimageprocessing.sip
%Include qcameraimageprocessingcontrol.sip
%Include qcamerainfo.sip
%Include qcamerainfocontrol.sip
%Include qcameralockscontrol.sip
%Include qcameraviewfindersettings.sip
%Include qcameraviewfindersettingscontrol.sip
%Include qcamerazoomcontrol.sip
%Include qcustomaudiorolecontrol.sip
%Include qimageencodercontrol.sip
%Include qmediaaudioprobecontrol.sip
%Include qmediaavailabilitycontrol.sip
%Include qmediabindableinterface.sip
%Include qmediacontainercontrol.sip
%Include qmediacontent.sip
%Include qmediacontrol.sip
%Include qmediaencodersettings.sip
%Include qmediagaplessplaybackcontrol.sip
%Include qmediametadata.sip
%Include qmedianetworkaccesscontrol.sip
%Include qmediaobject.sip
%Include qmediaplayer.sip
%Include qmediaplayercontrol.sip
%Include qmediaplaylist.sip
%Include qmediarecorder.sip
%Include qmediarecordercontrol.sip
%Include qmediaresource.sip
%Include qmediaservice.sip
%Include qmediastreamscontrol.sip
%Include qmediatimerange.sip
%Include qmediavideoprobecontrol.sip
%Include qmetadatareadercontrol.sip
%Include qmetadatawritercontrol.sip
%Include qmultimedia.sip
%Include qradiodata.sip
%Include qradiodatacontrol.sip
%Include qradiotuner.sip
%Include qradiotunercontrol.sip
%Include qsound.sip
%Include qsoundeffect.sip
%Include qvideodeviceselectorcontrol.sip
%Include qvideoencodersettingscontrol.sip
%Include qvideoframe.sip
%Include qvideoprobe.sip
%Include qvideorenderercontrol.sip
%Include qvideosurfaceformat.sip
%Include qvideowindowcontrol.sip
%Include qpymultimedia_qlist.sip
