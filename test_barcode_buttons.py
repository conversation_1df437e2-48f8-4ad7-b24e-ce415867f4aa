#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار أزرار مسح الباركود الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("📱 اختبار أزرار مسح الباركود الجديدة")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("🎯 الميزات الجديدة المضافة:")
    print("")
    print("   📷 أزرار الكاميرا (📷):")
    print("     • لون أزرق مميز (#17A2B8)")
    print("     • حجم 40×40 بكسل")
    print("     • تأثير hover جميل")
    print("     • نص توضيحي: 'مسح الباركود بالكاميرا'")
    print("")
    print("   🔍 أزرار الجهاز (🔍):")
    print("     • لون أخضر مميز (#28A745)")
    print("     • حجم 40×40 بكسل")
    print("     • تأثير hover جميل")
    print("     • نص توضيحي: 'مسح الباركود بالجهاز'")
    print("")
    print("   📊 التخطيط المحسن:")
    print("     • كل حقل باركود له أزراره الخاصة")
    print("     • ترتيب أفقي: [حقل النص] [📷] [🔍]")
    print("     • مسافات مناسبة بين العناصر")
    print("     • تصميم متناسق وجميل")
    print("")
    print("   🔧 الوظائف:")
    print("     • دالة scan_barcode_for_field()")
    print("     • دعم الكاميرا والجهاز")
    print("     • رسائل نجاح وخطأ واضحة")
    print("     • حماية من الأخطاء")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار أزرار مسح الباركود:")
        print("")
        print("   📦 فتح نافذة إضافة منتج:")
        print("     1️⃣ اذهب لقسم 'المخزون'")
        print("     2️⃣ اضغط 'إضافة منتج جديد'")
        print("     3️⃣ لاحظ الأزرار الجديدة بجانب كل حقل باركود")
        print("")
        print("   👀 ما يجب أن تراه:")
        print("     ✅ 🔵 الباركود الأساسي: [حقل] [📷] [🔍]")
        print("     ✅ 🔸 باركود إضافي 1: [حقل] [📷] [🔍]")
        print("     ✅ 🔸 باركود إضافي 2: [حقل] [📷] [🔍]")
        print("     ✅ 🔸 باركود إضافي 3: [حقل] [📷] [🔍]")
        print("")
        print("   🔍 اختبار الأزرار:")
        print("     1️⃣ مرر الماوس فوق أي زر:")
        print("        📷 الأزرق يصبح أغمق")
        print("        🔍 الأخضر يصبح أغمق")
        print("")
        print("     2️⃣ اضغط على زر الكاميرا 📷:")
        print("        • يفتح نافذة مسح بالكاميرا")
        print("        • أو رسالة خطأ إذا لم تكن متوفرة")
        print("")
        print("     3️⃣ اضغط على زر الجهاز 🔍:")
        print("        • يفتح نافذة مسح بالجهاز")
        print("        • أو رسالة خطأ إذا لم تكن متوفرة")
        print("")
        print("   📝 اختبار كامل:")
        print("     1️⃣ أدخل بيانات المنتج:")
        print("        📝 اسم المنتج: 'هاتف سامسونج S24'")
        print("        🏷️ كود المنتج: 'SAM024'")
        print("        📂 الفئة: 'هواتف ذكية'")
        print("")
        print("     2️⃣ اختبر أزرار الباركود:")
        print("        🔵 الأساسي: اضغط 📷 أو 🔍")
        print("        🔸 إضافي 1: اضغط 📷 أو 🔍")
        print("        🔸 إضافي 2: اضغط 📷 أو 🔍")
        print("        🔸 إضافي 3: اضغط 📷 أو 🔍")
        print("")
        print("     3️⃣ أو أدخل باركودات يدوياً:")
        print("        🔵 الأساسي: '8801643171456'")
        print("        🔸 إضافي 1: '8801643171457'")
        print("        🔸 إضافي 2: '8801643171458'")
        print("        🔸 إضافي 3: '8801643171459'")
        print("")
        print("     4️⃣ أكمل البيانات واحفظ:")
        print("        📊 الكمية: 5")
        print("        💰 سعر الشراء: 1200")
        print("        💰 سعر البيع: 1500")
        print("")
        print("   🔍 اختبار البحث:")
        print("     1️⃣ اذهب لفاتورة المبيعات")
        print("     2️⃣ ابحث بأي من الباركودات الأربعة")
        print("     3️⃣ تأكد من العثور على المنتج")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 ملخص الميزات الجديدة:")
    print("")
    print("   ✅ تم إضافة أزرار مسح الباركود:")
    print("     • 4 حقول × 2 أزرار = 8 أزرار جديدة")
    print("     • أزرار الكاميرا 📷 (أزرق)")
    print("     • أزرار الجهاز 🔍 (أخضر)")
    print("")
    print("   🎨 التصميم المحسن:")
    print("     • ألوان مميزة ومتناسقة")
    print("     • أحجام ثابتة ومناسبة")
    print("     • تأثيرات hover جميلة")
    print("     • نصوص توضيحية مفيدة")
    print("")
    print("   🔧 الوظائف المتقدمة:")
    print("     • دعم الكاميرا والجهاز")
    print("     • رسائل واضحة للمستخدم")
    print("     • حماية من الأخطاء")
    print("     • سهولة الاستخدام")
    print("")
    print("   🎯 النتيجة:")
    print("     • إدخال الباركود أصبح أسهل")
    print("     • دعم أجهزة المسح المختلفة")
    print("     • واجهة احترافية وجميلة")
    print("     • تجربة مستخدم ممتازة")
    print("")
    print("🎉 تم إضافة أزرار مسح الباركود بنجاح!")

if __name__ == "__main__":
    main()
