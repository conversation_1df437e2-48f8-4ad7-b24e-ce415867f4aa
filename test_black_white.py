#!/usr/bin/env python3
"""
اختبار الألوان الجديدة (أبيض وأسود)
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار الألوان الجديدة (أبيض وأسود)...")
    print("=" * 60)
    print("🎨 التغييرات المطبقة:")
    print("   • تحويل جميع الألوان إلى أبيض وأسود")
    print("   • الحفاظ على جميع التحسينات الأخرى:")
    print("     ✓ الترويسات المحسنة")
    print("     ✓ المسافات المحسنة")
    print("     ✓ الخطوط المحسنة")
    print("     ✓ التنسيقات المحسنة")
    print("     ✓ اللوجو المخصص")
    print("=" * 60)
    print("🎨 تفاصيل الألوان الجديدة:")
    print("   A4:")
    print("     • الترويسة: خلفية بيضاء + حدود سوداء + نص أسود")
    print("     • عنوان الفاتورة: خلفية بيضاء + حدود سوداء + نص أسود")
    print("     • جدول المنتجات: خلفية بيضاء + حدود سوداء + نص أسود")
    print("     • صفوف متناوبة: أبيض ورمادي فاتح جداً")
    print("     • تفاصيل الفاتورة: خلفية بيضاء + حدود سوداء + نص أسود")
    print("     • الإجمالي: خلفية بيضاء + حدود سوداء سميكة + نص أسود")
    print("   الرول:")
    print("     • الترويسة: خلفية بيضاء + حدود سوداء + نص أسود")
    print("     • اللوجو: خلفية بيضاء + حدود سوداء")
    print("     • النصوص: جميعها سوداء")
    print("     • الإجمالي: خلفية بيضاء + حدود سوداء + نص أسود")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 بالألوان الجديدة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من الألوان الجديدة:")
        print("   • اختبر كلاً من A4 والرول")
        print("   • تأكد من أن جميع الألوان أبيض وأسود")
        print("   • تحقق من وضوح النصوص")
        print("   • لاحظ الحفاظ على جميع التحسينات الأخرى")
        print("   • تأكد من أن اللوجو المخصص يظهر بوضوح")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة الألوان:")
    print("   قبل التحسين:")
    print("     🎨 ألوان متعددة (أخضر، أزرق، إلخ)")
    print("     🎨 تدرجات لونية")
    print("     🎨 خلفيات ملونة")
    print("")
    print("   بعد التحسين:")
    print("     ⚫ أبيض وأسود فقط")
    print("     ⚫ حدود سوداء واضحة")
    print("     ⚫ نصوص سوداء مقروءة")
    print("     ⚫ خلفيات بيضاء نظيفة")
    print("     ⚫ تباين عالي للوضوح")
    print("")
    print("🎯 المزايا:")
    print("   ✅ توفير في تكلفة الطباعة الملونة")
    print("   ✅ وضوح أكبر في الطباعة")
    print("   ✅ مناسب لجميع أنواع الطابعات")
    print("   ✅ مظهر احترافي ونظيف")
    print("   ✅ الحفاظ على جميع التحسينات الأخرى")

if __name__ == "__main__":
    main()
