// qhostaddress.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHostAddress /TypeHintIn="Union[QHostAddress, QHostAddress.SpecialAddress]"/
{
%TypeHeaderCode
#include <qhostaddress.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// QHostAddress::SpecialAddress to be used whenever a QHostAddress is expected.

if (sipIsErr == NULL)
    return (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_QHostAddress_SpecialAddress)) ||
            sipCanConvertToType(sipPy, sipType_QHostAddress, SIP_NO_CONVERTORS));

if (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_QHostAddress_SpecialAddress)))
{
    *sipCppPtr = new QHostAddress((QHostAddress::SpecialAddress)SIPLong_AsLong(sipPy));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QHostAddress *>(sipConvertToType(sipPy, sipType_QHostAddress, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

public:
    enum SpecialAddress
    {
        Null,
        Broadcast,
        LocalHost,
        LocalHostIPv6,
        AnyIPv4,
        AnyIPv6,
        Any,
    };

    QHostAddress();
    QHostAddress(QHostAddress::SpecialAddress address /Constrained/);
    explicit QHostAddress(quint32 ip4Addr);
    explicit QHostAddress(const QString &address);
    explicit QHostAddress(const Q_IPV6ADDR &ip6Addr);
    QHostAddress(const QHostAddress &copy);
    ~QHostAddress();
%If (Qt_5_8_0 -)
    void setAddress(QHostAddress::SpecialAddress address /Constrained/);
%End
    void setAddress(quint32 ip4Addr);
    bool setAddress(const QString &address);
    void setAddress(const Q_IPV6ADDR &ip6Addr);
    QAbstractSocket::NetworkLayerProtocol protocol() const;
    quint32 toIPv4Address() const;
    Q_IPV6ADDR toIPv6Address() const;
    QString toString() const;
    QString scopeId() const;
    void setScopeId(const QString &id);
    bool operator==(const QHostAddress &address) const;
    bool operator==(QHostAddress::SpecialAddress address) const;
    bool operator!=(const QHostAddress &address) const;
    bool operator!=(QHostAddress::SpecialAddress address) const;
    bool isNull() const;
    void clear();
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    bool isInSubnet(const QHostAddress &subnet, int netmask) const;
    bool isInSubnet(const QPair<QHostAddress, int> &subnet) const;
    bool isLoopback() const;
    static QPair<QHostAddress, int> parseSubnet(const QString &subnet);
%If (Qt_5_6_0 -)
    void swap(QHostAddress &other /Constrained/);
%End
%If (Qt_5_6_0 -)
    bool isMulticast() const;
%End
%If (Qt_5_8_0 -)

    enum ConversionModeFlag
    {
        ConvertV4MappedToIPv4,
        ConvertV4CompatToIPv4,
        ConvertUnspecifiedAddress,
        ConvertLocalHost,
        TolerantConversion,
        StrictConversion,
    };

%End
%If (Qt_5_8_0 -)
    typedef QFlags<QHostAddress::ConversionModeFlag> ConversionMode;
%End
%If (Qt_5_8_0 -)
    bool isEqual(const QHostAddress &address, QHostAddress::ConversionMode mode = QHostAddress::TolerantConversion) const;
%End
%If (Qt_5_11_0 -)
    bool isGlobal() const;
%End
%If (Qt_5_11_0 -)
    bool isLinkLocal() const;
%End
%If (Qt_5_11_0 -)
    bool isSiteLocal() const;
%End
%If (Qt_5_11_0 -)
    bool isUniqueLocalUnicast() const;
%End
%If (Qt_5_11_0 -)
    bool isBroadcast() const;
%End
};

bool operator==(QHostAddress::SpecialAddress address1, const QHostAddress &address2);
%If (Qt_5_9_0 -)
bool operator!=(QHostAddress::SpecialAddress lhs, const QHostAddress &rhs);
%End
QDataStream &operator<<(QDataStream &, const QHostAddress &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QHostAddress &) /ReleaseGIL/;
// Q_IPV6ADDR is implemented as a Python 16-tuple of ints.
%MappedType Q_IPV6ADDR /TypeHint="Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]"/
{
%TypeHeaderCode
#include <qhostaddress.h>
%End

%ConvertFromTypeCode
    // Create the tuple.
    PyObject *t;

    if ((t = PyTuple_New(16)) == NULL)
        return NULL;

    // Set the tuple elements.
    for (int i = 0; i < 16; ++i)
    {
        PyObject *pobj;

        if ((pobj = SIPLong_FromLong((*sipCpp)[i])) == NULL)
        {
            Py_DECREF(t);

            return NULL;
        }

        PyTuple_SetItem(t, i, pobj);
    }

    return t;
%End

%ConvertToTypeCode
    // Check the type if that is all that is required.
    if (sipIsErr == NULL)
        return (PySequence_Check(sipPy) && PySequence_Size(sipPy) == 16);

    Q_IPV6ADDR *qa = new Q_IPV6ADDR;
 
    for (Py_ssize_t i = 0; i < 16; ++i)
    {
        PyObject *itm = PySequence_GetItem(sipPy, i);

        if (!itm)
        {
            delete qa;
            *sipIsErr = 1;

            return 0;
        }

        (*qa)[i] = SIPLong_AsLong(itm);

        Py_DECREF(itm);
    }
 
    *sipCppPtr = qa;
 
    return sipGetState(sipTransferObj);
%End
};
%If (Qt_5_8_0 -)
QFlags<QHostAddress::ConversionModeFlag> operator|(QHostAddress::ConversionModeFlag f1, QFlags<QHostAddress::ConversionModeFlag> f2);
%End
