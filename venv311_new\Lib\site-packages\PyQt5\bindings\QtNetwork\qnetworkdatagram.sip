// qnetworkdatagram.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)

class QNetworkDatagram
{
%TypeHeaderCode
#include <qnetworkdatagram.h>
%End

public:
    QNetworkDatagram();
    QNetworkDatagram(const QByteArray &data, const QHostAddress &destinationAddress = QHostAddress(), quint16 port = 0);
    QNetworkDatagram(const QNetworkDatagram &other);
    ~QNetworkDatagram();
    void swap(QNetworkDatagram &other /Constrained/);
    void clear();
    bool isValid() const;
    bool isNull() const;
    uint interfaceIndex() const;
    void setInterfaceIndex(uint index);
    QHostAddress senderAddress() const;
    QHostAddress destinationAddress() const;
    int senderPort() const;
    int destinationPort() const;
    void setSender(const QHostAddress &address, quint16 port = 0);
    void setDestination(const QHostAddress &address, quint16 port);
    int hopLimit() const;
    void setHopLimit(int count);
    QByteArray data() const;
    void setData(const QByteArray &data);
    QNetworkDatagram makeReply(const QByteArray &payload) const;
};

%End
