// qaltimeter.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QAltimeterReading : QSensorReading /NoDefaultCtors/
{
%TypeHeaderCode
#include <qaltimeter.h>
%End

public:
    qreal altitude() const;
    void setAltitude(qreal altitude);
};

%End
%If (Qt_5_1_0 -)

class QAltimeterFilter : QSensorFilter
{
%TypeHeaderCode
#include <qaltimeter.h>
%End

public:
    virtual bool filter(QAltimeterReading *reading) = 0;
};

%End
%If (Qt_5_1_0 -)

class QAltimeter : QSensor
{
%TypeHeaderCode
#include <qaltimeter.h>
%End

public:
    explicit QAltimeter(QObject *parent /TransferThis/ = 0);
    virtual ~QAltimeter();
    QAltimeterReading *reading() const;

private:
    QAltimeter(const QAltimeter &);
};

%End
