#!/usr/bin/env python3
"""
اختبار معلومات البراند في طباعة الرول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🏢 اختبار معلومات البراند في طباعة الرول...")
    print("=" * 70)
    print("🔧 إصلاح مشكلة عرض معلومات البراند:")
    print("")
    print("   ✅ المشكلة التي تم إصلاحها:")
    print("     • كانت معلومات الشركة لا تظهر بسبب حساب خاطئ للمواضع")
    print("     • تم إصلاح حساب مواضع النصوص")
    print("     • تم إزالة الشروط المقيدة للعرض")
    print("")
    print("   ✅ ما يجب أن تراه الآن في طباعة الرول:")
    print("     🏠 اللوجو (مخصص أو افتراضي)")
    print("     🏢 اسم الشركة")
    print("     📍 عنوان الشركة")
    print("     📞 رقم الهاتف")
    print("     📧 البريد الإلكتروني")
    print("     🏢 الرقم الضريبي")
    print("")
    print("   ✅ التحسينات المطبقة:")
    print("     • إزالة الحدود فقط (وليس المحتوى)")
    print("     • إصلاح حساب المواضع")
    print("     • عرض جميع معلومات الشركة")
    print("     • تصميم نظيف بدون حدود")
    print("")
    print("   ✅ يعمل مع جميع أنواع الفواتير:")
    print("     • فواتير المبيعات العادية")
    print("     • فواتير مرتجع المبيعات")
    print("     • فواتير مرتجع المشتريات")
    print("=" * 70)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 تشغيل النظام...")
        
        # اختبار الواجهة الرئيسية
        print("🖥️ تشغيل الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.engine = engine
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار معلومات البراند في طباعة الرول:")
        print("")
        print("   📋 طباعة فاتورة مبيعات:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض الفواتير المحفوظة'")
        print("     2️⃣ اختر أي فاتورة واضغط 'طباعة'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ تأكد من ظهور:")
        print("        🏠 اللوجو في الجانب الأيسر")
        print("        🏢 اسم الشركة في الجانب الأيمن")
        print("        📍 عنوان الشركة")
        print("        📞 رقم الهاتف")
        print("        📧 البريد الإلكتروني")
        print("        🏢 الرقم الضريبي")
        print("")
        print("   🔄 طباعة فاتورة مرتجع:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض فواتير المرتجعات'")
        print("     2️⃣ اختر أي مرتجع واضغط '🖨️'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ تأكد من ظهور:")
        print("        • جميع معلومات البراند كما في الفواتير العادية")
        print("        • عنوان 'فاتورة مرتجع مبيعات' بالأحمر")
        print("        • رقم المرتجع R000001")
        print("        • رقم الفاتورة الأصلية #000001")
        print("")
        print("   🆕 إنشاء مرتجع جديد وطباعته:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'مرتجع المبيعات'")
        print("     2️⃣ ابحث عن فاتورة واختر منتجات")
        print("     3️⃣ اضغط 'حفظ وطباعة'")
        print("     4️⃣ اختر 'رول' واطبع")
        print("     5️⃣ تأكد من ظهور جميع معلومات البراند")
        print("")
        print("   🔍 ما تبحث عنه:")
        print("     ✅ في أعلى الفاتورة:")
        print("        • اللوجو على اليسار")
        print("        • اسم الشركة على اليمين")
        print("        • عنوان الشركة تحت الاسم")
        print("        • رقم الهاتف")
        print("        • البريد الإلكتروني")
        print("        • الرقم الضريبي")
        print("     ✅ تصميم نظيف:")
        print("        • بدون حدود حول المعلومات")
        print("        • نصوص واضحة ومقروءة")
        print("        • ترتيب منطقي للمعلومات")
        print("")
        print("   ❌ إذا لم تظهر معلومات البراند:")
        print("     1️⃣ تأكد من وجود ملف company_settings.json")
        print("     2️⃣ تأكد من إعدادات الشركة في النظام")
        print("     3️⃣ جرب إعادة تشغيل البرنامج")
        print("")
        print("   💡 مقارنة قبل وبعد الإصلاح:")
        print("     🔴 قبل الإصلاح:")
        print("        • معلومات البراند لا تظهر")
        print("        • مواضع النصوص خاطئة")
        print("        • حدود حول المعلومات")
        print("     🟢 بعد الإصلاح:")
        print("        • جميع معلومات البراند تظهر")
        print("        • مواضع صحيحة ومنظمة")
        print("        • تصميم نظيف بدون حدود")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 70)
    print("📊 ملخص الإصلاحات:")
    print("")
    print("   🔧 ما تم إصلاحه:")
    print("     ✅ حساب مواضع النصوص في الرول")
    print("     ✅ إزالة الشروط المقيدة للعرض")
    print("     ✅ إصلاح المسافات بين العناصر")
    print("     ✅ ضمان ظهور جميع معلومات البراند")
    print("")
    print("   🎨 النتيجة النهائية:")
    print("     • معلومات البراند تظهر بوضوح")
    print("     • تصميم نظيف بدون حدود")
    print("     • ترتيب منطقي للمعلومات")
    print("     • يعمل مع جميع أنواع الفواتير")
    print("")
    print("   📋 معلومات البراند المعروضة:")
    print("     🏠 اللوجو (مخصص أو افتراضي)")
    print("     🏢 اسم الشركة")
    print("     📍 عنوان الشركة")
    print("     📞 رقم الهاتف")
    print("     📧 البريد الإلكتروني")
    print("     🏢 الرقم الضريبي")
    print("")
    print("🎉 تم إصلاح مشكلة معلومات البراند بنجاح!")
    print("   📱 جرب الطباعة الآن وستجد جميع المعلومات")
    print("   🖨️ تصميم نظيف ومعلومات كاملة")
    print("   🎯 يعمل مع جميع أنواع الفواتير والمرتجعات")

if __name__ == "__main__":
    main()
