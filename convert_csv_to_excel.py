#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل ملف CSV إلى Excel
"""

import pandas as pd
import os

def convert_csv_to_excel():
    """تحويل ملف CSV إلى Excel"""
    try:
        # قراءة ملف CSV
        csv_file = "sample_data/منتجات_الأدوات_المنزلية_المصرية.csv"
        
        if not os.path.exists(csv_file):
            print(f"❌ الملف غير موجود: {csv_file}")
            return
        
        print("🔄 جاري قراءة ملف CSV...")
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # حفظ كملف Excel
        excel_file = "sample_data/منتجات_الأدوات_المنزلية_المصرية.xlsx"
        print("🔄 جاري إنشاء ملف Excel...")
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='المنتجات', index=False)
            
            # تنسيق الورقة
            worksheet = writer.sheets['المنتجات']
            
            # تعديل عرض الأعمدة
            column_widths = {
                'A': 15,  # Code
                'B': 40,  # Name
                'C': 50,  # Description
                'D': 25,  # Category
                'E': 15,  # Purchase_Price
                'F': 15,  # Sale_Price
                'G': 12,  # Quantity
                'H': 15,  # Min_Quantity
                'I': 10,  # Unit
                'J': 20,  # Barcode
                'K': 20,  # Company
                'L': 12   # Model
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
        
        print(f"✅ تم إنشاء ملف Excel بنجاح: {excel_file}")
        print(f"📊 عدد المنتجات: {len(df)}")
        print(f"🏢 عدد الشركات: {len(df['Company'].unique())}")
        print(f"📂 عدد الفئات: {len(df['Category'].unique())}")
        
        # عرض إحصائيات
        print(f"\n📈 إحصائيات الأسعار:")
        print(f"💰 متوسط سعر الشراء: {df['Purchase_Price'].mean():.0f} جنيه")
        print(f"💰 متوسط سعر البيع: {df['Sale_Price'].mean():.0f} جنيه")
        print(f"📦 إجمالي الكميات: {df['Quantity'].sum()} قطعة")
        
        return excel_file
        
    except Exception as e:
        print(f"❌ خطأ في التحويل: {str(e)}")
        return None

if __name__ == "__main__":
    convert_csv_to_excel()
