# The PEP 484 type hints stub file for the QtWebEngine module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQtWebEngine.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtNetwork
from PyQt5 import QtWebEngineCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt5.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt5.sip.Buffer, int, None]


class QQuickWebEngineProfile(QtCore.QObject):

    class PersistentCookiesPolicy(int):
        NoPersistentCookies = ... # type: QQuickWebEngineProfile.PersistentCookiesPolicy
        AllowPersistentCookies = ... # type: QQuickWebEngineProfile.PersistentCookiesPolicy
        ForcePersistentCookies = ... # type: QQuickWebEngineProfile.PersistentCookiesPolicy

    class HttpCacheType(int):
        MemoryHttpCache = ... # type: QQuickWebEngineProfile.HttpCacheType
        DiskHttpCache = ... # type: QQuickWebEngineProfile.HttpCacheType
        NoCache = ... # type: QQuickWebEngineProfile.HttpCacheType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    presentNotification: typing.ClassVar[QtCore.pyqtSignal]
    downloadPathChanged: typing.ClassVar[QtCore.pyqtSignal]
    useForGlobalCertificateVerificationChanged: typing.ClassVar[QtCore.pyqtSignal]
    def clientCertificateStore(self) -> typing.Optional[QtWebEngineCore.QWebEngineClientCertificateStore]: ...
    def setDownloadPath(self, path: typing.Optional[str]) -> None: ...
    def downloadPath(self) -> str: ...
    def isUsedForGlobalCertificateVerification(self) -> bool: ...
    def setUseForGlobalCertificateVerification(self, b: bool) -> None: ...
    spellCheckEnabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    spellCheckLanguagesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isSpellCheckEnabled(self) -> bool: ...
    def setSpellCheckEnabled(self, enabled: bool) -> None: ...
    def spellCheckLanguages(self) -> typing.List[str]: ...
    def setSpellCheckLanguages(self, languages: typing.Iterable[typing.Optional[str]]) -> None: ...
    httpAcceptLanguageChanged: typing.ClassVar[QtCore.pyqtSignal]
    httpCacheMaximumSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    persistentCookiesPolicyChanged: typing.ClassVar[QtCore.pyqtSignal]
    httpCacheTypeChanged: typing.ClassVar[QtCore.pyqtSignal]
    httpUserAgentChanged: typing.ClassVar[QtCore.pyqtSignal]
    cachePathChanged: typing.ClassVar[QtCore.pyqtSignal]
    persistentStoragePathChanged: typing.ClassVar[QtCore.pyqtSignal]
    offTheRecordChanged: typing.ClassVar[QtCore.pyqtSignal]
    storageNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def defaultProfile() -> typing.Optional['QQuickWebEngineProfile']: ...
    def clearHttpCache(self) -> None: ...
    def removeAllUrlSchemeHandlers(self) -> None: ...
    def removeUrlSchemeHandler(self, a0: typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]) -> None: ...
    def removeUrlScheme(self, scheme: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def installUrlSchemeHandler(self, scheme: typing.Union[QtCore.QByteArray, bytes, bytearray], a1: typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]) -> None: ...
    def urlSchemeHandler(self, a0: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.Optional[QtWebEngineCore.QWebEngineUrlSchemeHandler]: ...
    def setRequestInterceptor(self, interceptor: typing.Optional[QtWebEngineCore.QWebEngineUrlRequestInterceptor]) -> None: ...
    def setUrlRequestInterceptor(self, interceptor: typing.Optional[QtWebEngineCore.QWebEngineUrlRequestInterceptor]) -> None: ...
    def cookieStore(self) -> typing.Optional[QtWebEngineCore.QWebEngineCookieStore]: ...
    def setHttpAcceptLanguage(self, httpAcceptLanguage: typing.Optional[str]) -> None: ...
    def httpAcceptLanguage(self) -> str: ...
    def setHttpCacheMaximumSize(self, maxSize: int) -> None: ...
    def httpCacheMaximumSize(self) -> int: ...
    def setPersistentCookiesPolicy(self, a0: 'QQuickWebEngineProfile.PersistentCookiesPolicy') -> None: ...
    def persistentCookiesPolicy(self) -> 'QQuickWebEngineProfile.PersistentCookiesPolicy': ...
    def setHttpCacheType(self, a0: 'QQuickWebEngineProfile.HttpCacheType') -> None: ...
    def httpCacheType(self) -> 'QQuickWebEngineProfile.HttpCacheType': ...
    def setHttpUserAgent(self, userAgent: typing.Optional[str]) -> None: ...
    def httpUserAgent(self) -> str: ...
    def setCachePath(self, path: typing.Optional[str]) -> None: ...
    def cachePath(self) -> str: ...
    def setPersistentStoragePath(self, path: typing.Optional[str]) -> None: ...
    def persistentStoragePath(self) -> str: ...
    def setOffTheRecord(self, offTheRecord: bool) -> None: ...
    def isOffTheRecord(self) -> bool: ...
    def setStorageName(self, name: typing.Optional[str]) -> None: ...
    def storageName(self) -> str: ...


class QQuickWebEngineScript(QtCore.QObject):

    class ScriptWorldId(int):
        MainWorld = ... # type: QQuickWebEngineScript.ScriptWorldId
        ApplicationWorld = ... # type: QQuickWebEngineScript.ScriptWorldId
        UserWorld = ... # type: QQuickWebEngineScript.ScriptWorldId

    class InjectionPoint(int):
        Deferred = ... # type: QQuickWebEngineScript.InjectionPoint
        DocumentReady = ... # type: QQuickWebEngineScript.InjectionPoint
        DocumentCreation = ... # type: QQuickWebEngineScript.InjectionPoint

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def timerEvent(self, e: typing.Optional[QtCore.QTimerEvent]) -> None: ...
    runOnSubframesChanged: typing.ClassVar[QtCore.pyqtSignal]
    worldIdChanged: typing.ClassVar[QtCore.pyqtSignal]
    injectionPointChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceCodeChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceUrlChanged: typing.ClassVar[QtCore.pyqtSignal]
    nameChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setRunOnSubframes(self, on: bool) -> None: ...
    def setWorldId(self, scriptWorldId: 'QQuickWebEngineScript.ScriptWorldId') -> None: ...
    def setInjectionPoint(self, injectionPoint: 'QQuickWebEngineScript.InjectionPoint') -> None: ...
    def setSourceCode(self, code: typing.Optional[str]) -> None: ...
    def setSourceUrl(self, url: QtCore.QUrl) -> None: ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def runOnSubframes(self) -> bool: ...
    def worldId(self) -> 'QQuickWebEngineScript.ScriptWorldId': ...
    def injectionPoint(self) -> 'QQuickWebEngineScript.InjectionPoint': ...
    def sourceCode(self) -> str: ...
    def sourceUrl(self) -> QtCore.QUrl: ...
    def name(self) -> str: ...
    def toString(self) -> str: ...


class QtWebEngine(PyQt5.sip.simplewrapper):

    def initialize(self) -> None: ...


PYQT_WEBENGINE_VERSION = ... # type: int
PYQT_WEBENGINE_VERSION_STR = ... # type: str
