// qcandlestickseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_5_8_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qcandlestickseries.h>
%End

    class QCandlestickSeries : public QtCharts::QAbstractSeries
    {
%TypeHeaderCode
#include <qcandlestickseries.h>
%End

    public:
        explicit QCandlestickSeries(QObject *parent /TransferThis/ = 0);
        virtual ~QCandlestickSeries();
        bool append(QtCharts::QCandlestickSet *set /Transfer/);
        bool remove(QtCharts::QCandlestickSet *set);
        bool append(const QList<QtCharts::QCandlestickSet *> &sets /Transfer/);
        bool remove(const QList<QtCharts::QCandlestickSet *> &sets);
        bool insert(int index, QtCharts::QCandlestickSet *set /Transfer/);
        bool take(QtCharts::QCandlestickSet *set);
        void clear();
        QList<QtCharts::QCandlestickSet *> sets() const;
        int count() const /__len__/;
        virtual QtCharts::QAbstractSeries::SeriesType type() const;
        void setMaximumColumnWidth(qreal maximumColumnWidth);
        qreal maximumColumnWidth() const;
        void setMinimumColumnWidth(qreal minimumColumnWidth);
        qreal minimumColumnWidth() const;
        void setBodyWidth(qreal bodyWidth);
        qreal bodyWidth() const;
        void setBodyOutlineVisible(bool bodyOutlineVisible);
        bool bodyOutlineVisible() const;
        void setCapsWidth(qreal capsWidth);
        qreal capsWidth() const;
        void setCapsVisible(bool capsVisible);
        bool capsVisible() const;
        void setIncreasingColor(const QColor &increasingColor);
        QColor increasingColor() const;
        void setDecreasingColor(const QColor &decreasingColor);
        QColor decreasingColor() const;
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setPen(const QPen &pen);
        QPen pen() const;

    signals:
        void clicked(QtCharts::QCandlestickSet *set /ScopesStripped=1/);
        void hovered(bool status, QtCharts::QCandlestickSet *set /ScopesStripped=1/);
        void pressed(QtCharts::QCandlestickSet *set /ScopesStripped=1/);
        void released(QtCharts::QCandlestickSet *set /ScopesStripped=1/);
        void doubleClicked(QtCharts::QCandlestickSet *set /ScopesStripped=1/);
        void candlestickSetsAdded(const QList<QtCharts::QCandlestickSet *> &sets /ScopesStripped=1/);
        void candlestickSetsRemoved(const QList<QtCharts::QCandlestickSet *> &sets /ScopesStripped=1/);
        void countChanged();
        void maximumColumnWidthChanged();
        void minimumColumnWidthChanged();
        void bodyWidthChanged();
        void bodyOutlineVisibilityChanged();
        void capsWidthChanged();
        void capsVisibilityChanged();
        void increasingColorChanged();
        void decreasingColorChanged();
        void brushChanged();
        void penChanged();
    };
};

%End
