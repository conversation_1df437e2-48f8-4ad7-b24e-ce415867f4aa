// qtreeview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTreeView : QAbstractItemView
{
%TypeHeaderCode
#include <qtreeview.h>
%End

public:
    explicit QTreeView(QWidget *parent /TransferThis/ = 0);
    virtual ~QTreeView();
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
    virtual void setRootIndex(const QModelIndex &index);
    virtual void setSelectionModel(QItemSelectionModel *selectionModel /KeepReference/);
    QHeaderView *header() const;
    void setHeader(QHeaderView *header /Transfer/);
    int indentation() const;
    void setIndentation(int i);
    bool rootIsDecorated() const;
    void setRootIsDecorated(bool show);
    bool uniformRowHeights() const;
    void setUniformRowHeights(bool uniform);
    bool itemsExpandable() const;
    void setItemsExpandable(bool enable);
    int columnViewportPosition(int column) const;
    int columnWidth(int column) const;
    int columnAt(int x) const;
    bool isColumnHidden(int column) const;
    void setColumnHidden(int column, bool hide);
    bool isRowHidden(int row, const QModelIndex &parent) const;
    void setRowHidden(int row, const QModelIndex &parent, bool hide);
    bool isExpanded(const QModelIndex &index) const;
    void setExpanded(const QModelIndex &index, bool expand);
    virtual void keyboardSearch(const QString &search);
    virtual QRect visualRect(const QModelIndex &index) const;
    virtual void scrollTo(const QModelIndex &index, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);
    virtual QModelIndex indexAt(const QPoint &p) const;
    QModelIndex indexAbove(const QModelIndex &index) const;
    QModelIndex indexBelow(const QModelIndex &index) const;
    virtual void reset();

signals:
    void expanded(const QModelIndex &index);
    void collapsed(const QModelIndex &index);

public:
    virtual void dataChanged(const QModelIndex &topLeft, const QModelIndex &bottomRight, const QVector<int> &roles = QVector<int>());

public slots:
    void hideColumn(int column);
    void showColumn(int column);
    void expand(const QModelIndex &index);
    void expandAll();
    void collapse(const QModelIndex &index);
    void collapseAll();
    void resizeColumnToContents(int column);
    virtual void selectAll();

protected slots:
    void columnResized(int column, int oldSize, int newSize);
    void columnCountChanged(int oldCount, int newCount);
    void columnMoved();
    void reexpand();
    void rowsRemoved(const QModelIndex &parent, int first, int last);

protected:
    virtual void scrollContentsBy(int dx, int dy);
    virtual void rowsInserted(const QModelIndex &parent, int start, int end);
    virtual void rowsAboutToBeRemoved(const QModelIndex &parent, int start, int end);
    virtual QModelIndex moveCursor(QAbstractItemView::CursorAction cursorAction, Qt::KeyboardModifiers modifiers);
    virtual int horizontalOffset() const;
    virtual int verticalOffset() const;
    virtual void setSelection(const QRect &rect, QItemSelectionModel::SelectionFlags command);
    virtual QRegion visualRegionForSelection(const QItemSelection &selection) const;
    virtual QModelIndexList selectedIndexes() const;
    virtual void paintEvent(QPaintEvent *e);
    virtual void timerEvent(QTimerEvent *event);
    virtual void mouseReleaseEvent(QMouseEvent *event);
    virtual void drawRow(QPainter *painter, const QStyleOptionViewItem &options /NoCopy/, const QModelIndex &index) const;
    virtual void drawBranches(QPainter *painter, const QRect &rect, const QModelIndex &index) const;
    void drawTree(QPainter *painter, const QRegion &region) const;
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *event);
    virtual void mouseDoubleClickEvent(QMouseEvent *e);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void updateGeometries();
    virtual int sizeHintForColumn(int column) const;
    int indexRowSizeHint(const QModelIndex &index) const;
    virtual void horizontalScrollbarAction(int action);
    virtual bool isIndexHidden(const QModelIndex &index) const;

public:
    void setColumnWidth(int column, int width);
    void setSortingEnabled(bool enable);
    bool isSortingEnabled() const;
    void setAnimated(bool enable);
    bool isAnimated() const;
    void setAllColumnsShowFocus(bool enable);
    bool allColumnsShowFocus() const;
    void sortByColumn(int column, Qt::SortOrder order);
    int autoExpandDelay() const;
    void setAutoExpandDelay(int delay);
    bool isFirstColumnSpanned(int row, const QModelIndex &parent) const;
    void setFirstColumnSpanned(int row, const QModelIndex &parent, bool span);
    void setWordWrap(bool on);
    bool wordWrap() const;

public slots:
    void expandToDepth(int depth);

protected:
    virtual void dragMoveEvent(QDragMoveEvent *event);
    virtual bool viewportEvent(QEvent *event);
    int rowHeight(const QModelIndex &index) const;
    virtual void selectionChanged(const QItemSelection &selected, const QItemSelection &deselected);
    virtual void currentChanged(const QModelIndex &current, const QModelIndex &previous);

public:
    bool expandsOnDoubleClick() const;
    void setExpandsOnDoubleClick(bool enable);
    bool isHeaderHidden() const;
    void setHeaderHidden(bool hide);
%If (Qt_5_2_0 -)
    void setTreePosition(int logicalIndex);
%End
%If (Qt_5_2_0 -)
    int treePosition() const;
%End

protected:
%If (Qt_5_2_0 -)
    virtual QSize viewportSizeHint() const;
%End

public:
%If (Qt_5_4_0 -)
    void resetIndentation();
%End

public slots:
%If (Qt_5_13_0 -)
    void expandRecursively(const QModelIndex &index, int depth = -1);
%End
};
