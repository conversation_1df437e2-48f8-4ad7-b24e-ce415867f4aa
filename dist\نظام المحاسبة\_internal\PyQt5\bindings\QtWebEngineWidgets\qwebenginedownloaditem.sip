// qwebenginedownloaditem.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_5_0 -)

class QWebEngineDownloadItem : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginedownloaditem.h>
%End

public:
    virtual ~QWebEngineDownloadItem();

    enum DownloadState
    {
        DownloadRequested,
        DownloadInProgress,
        DownloadCompleted,
        DownloadCancelled,
        DownloadInterrupted,
    };

    quint32 id() const;
    QWebEngineDownloadItem::DownloadState state() const;
    qint64 totalBytes() const;
    qint64 receivedBytes() const;
    QUrl url() const;
    QString path() const;
    void setPath(QString path);
    bool isFinished() const;

public slots:
    void accept();
    void cancel();

signals:
    void finished();
    void stateChanged(QWebEngineDownloadItem::DownloadState state);
    void downloadProgress(qint64 bytesReceived, qint64 bytesTotal);

public:
%If (QtWebEngine_5_6_0 -)
    QString mimeType() const;
%End
%If (QtWebEngine_5_7_0 -)

    enum SavePageFormat
    {
        UnknownSaveFormat,
        SingleHtmlSaveFormat,
        CompleteHtmlSaveFormat,
        MimeHtmlSaveFormat,
    };

%End
%If (QtWebEngine_5_7_0 -)
    QWebEngineDownloadItem::SavePageFormat savePageFormat() const;
%End
%If (QtWebEngine_5_7_0 -)
    void setSavePageFormat(QWebEngineDownloadItem::SavePageFormat format);
%End
%If (QtWebEngine_5_8_0 -)

    enum DownloadType
    {
        Attachment,
        DownloadAttribute,
        UserRequested,
        SavePage,
    };

%End
%If (QtWebEngine_5_8_0 -)
    QWebEngineDownloadItem::DownloadType type() const;
%End
%If (QtWebEngine_5_9_0 -)

    enum DownloadInterruptReason
    {
        NoReason,
        FileFailed,
        FileAccessDenied,
        FileNoSpace,
        FileNameTooLong,
        FileTooLarge,
        FileVirusInfected,
        FileTransientError,
        FileBlocked,
        FileSecurityCheckFailed,
        FileTooShort,
        FileHashMismatch,
        NetworkFailed,
        NetworkTimeout,
        NetworkDisconnected,
        NetworkServerDown,
        NetworkInvalidRequest,
        ServerFailed,
        ServerBadContent,
        ServerUnauthorized,
        ServerCertProblem,
        ServerForbidden,
        ServerUnreachable,
        UserCanceled,
    };

%End
%If (QtWebEngine_5_9_0 -)
    QWebEngineDownloadItem::DownloadInterruptReason interruptReason() const;
%End
%If (QtWebEngine_5_9_0 -)
    QString interruptReasonString() const;
%End
%If (QtWebEngine_5_10_0 -)
    bool isPaused() const;
%End

public slots:
%If (QtWebEngine_5_10_0 -)
    void pause();
%End
%If (QtWebEngine_5_10_0 -)
    void resume();
%End

signals:
%If (QtWebEngine_5_10_0 -)
    void isPausedChanged(bool isPaused);
%End

public:
%If (QtWebEngine_5_11_0 -)
    bool isSavePageDownload() const;
%End
%If (QtWebEngine_5_12_0 -)
    QWebEnginePage *page() const;
%End
%If (QtWebEngine_5_14_0 -)
    QString suggestedFileName() const;
%End
%If (QtWebEngine_5_14_0 -)
    QString downloadDirectory() const;
%End
%If (QtWebEngine_5_14_0 -)
    void setDownloadDirectory(const QString &directory);
%End
%If (QtWebEngine_5_14_0 -)
    QString downloadFileName() const;
%End
%If (QtWebEngine_5_14_0 -)
    void setDownloadFileName(const QString &fileName);
%End
};

%End
