#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تجريبي لاختبار تحديث رسالة الترحيب عند تغيير الاسم الكامل للمستخدم
"""

def test_welcome_message_update():
    """اختبار تحديث رسالة الترحيب"""
    
    print("🧪 اختبار تحديث رسالة الترحيب")
    print("=" * 50)
    
    # محاكاة بيانات المستخدم
    class MockUser:
        def __init__(self, username, full_name=None):
            self.username = username
            self.full_name = full_name
    
    # اختبار الحالات المختلفة
    test_cases = [
        ("sicoo", None, "مرحباً sicoo"),
        ("sicoo", "", "مرحباً sicoo"),
        ("sicoo", "أحمد محمد", "مرحباً أحمد محمد"),
        ("admin", "مدير النظام", "مرحباً مدير النظام"),
    ]
    
    for username, full_name, expected in test_cases:
        user = MockUser(username, full_name)
        
        # محاكاة دالة refresh_welcome_label
        if user.full_name and user.full_name.strip():
            welcome_text = f"مرحباً {user.full_name}"
        else:
            welcome_text = f"مرحباً {user.username}"
        
        result = "✅" if welcome_text == expected else "❌"
        print(f"{result} {username} | {full_name or 'None'} -> {welcome_text}")
    
    print("=" * 50)
    print("✅ تم اختبار جميع الحالات بنجاح!")

if __name__ == "__main__":
    test_welcome_message_update() 