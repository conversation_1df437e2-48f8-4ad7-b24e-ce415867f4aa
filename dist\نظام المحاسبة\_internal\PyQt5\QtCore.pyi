# The PEP 484 type hints stub file for the QtCore module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

# Support for QDate, QDateTime and QTime.
import datetime

# Support for Q_ENUM and Q_FLAG.
import enum


# Support for new-style signals and slots.
class pyqtSignal:

    signatures = ...    # type: typing.Tuple[str, ...]

    def __init__(self, *types: typing.Any, name: str = ...) -> None: ...

    @typing.overload
    def __get__(self, instance: None, owner: typing.Type['QObject']) -> 'pyqtSignal': ...

    @typing.overload
    def __get__(self, instance: 'QObject', owner: typing.Type['QObject']) -> 'pyqtBoundSignal': ...



class pyqtBoundSignal:

    signal = ...        # type: str

    def __getitem__(self, key: object) -> 'pyqtBoundSignal': ...

    def connect(self, slot: 'PYQT_SLOT') -> 'QMetaObject.Connection': ...

    @typing.overload
    def disconnect(self) -> None: ...

    @typing.overload
    def disconnect(self, slot: typing.Union['PYQT_SLOT', 'QMetaObject.Connection']) -> None: ...

    def emit(self, *args: typing.Any) -> None: ...


FuncT = typing.TypeVar('FuncT', bound=typing.Callable)
def pyqtSlot(*types, name: typing.Optional[str] = ..., result: typing.Optional[str] = ...) -> typing.Callable[[FuncT], FuncT]: ...


# For QObject.findChild() and QObject.findChildren().
QObjectT = typing.TypeVar('QObjectT', bound=QObject)


# Convenient type aliases.
PYQT_SIGNAL = typing.Union[pyqtSignal, pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., None], pyqtBoundSignal]


class QtMsgType(int):
    QtDebugMsg = ... # type: QtMsgType
    QtWarningMsg = ... # type: QtMsgType
    QtCriticalMsg = ... # type: QtMsgType
    QtFatalMsg = ... # type: QtMsgType
    QtSystemMsg = ... # type: QtMsgType
    QtInfoMsg = ... # type: QtMsgType


class QCborKnownTags(int):
    DateTimeString = ... # type: QCborKnownTags
    UnixTime_t = ... # type: QCborKnownTags
    PositiveBignum = ... # type: QCborKnownTags
    NegativeBignum = ... # type: QCborKnownTags
    Decimal = ... # type: QCborKnownTags
    Bigfloat = ... # type: QCborKnownTags
    COSE_Encrypt0 = ... # type: QCborKnownTags
    COSE_Mac0 = ... # type: QCborKnownTags
    COSE_Sign1 = ... # type: QCborKnownTags
    ExpectedBase64url = ... # type: QCborKnownTags
    ExpectedBase64 = ... # type: QCborKnownTags
    ExpectedBase16 = ... # type: QCborKnownTags
    EncodedCbor = ... # type: QCborKnownTags
    Url = ... # type: QCborKnownTags
    Base64url = ... # type: QCborKnownTags
    Base64 = ... # type: QCborKnownTags
    RegularExpression = ... # type: QCborKnownTags
    MimeMessage = ... # type: QCborKnownTags
    Uuid = ... # type: QCborKnownTags
    COSE_Encrypt = ... # type: QCborKnownTags
    COSE_Mac = ... # type: QCborKnownTags
    COSE_Sign = ... # type: QCborKnownTags
    Signature = ... # type: QCborKnownTags


class QCborSimpleType(int):
    False_ = ... # type: QCborSimpleType
    True_ = ... # type: QCborSimpleType
    Null = ... # type: QCborSimpleType
    Undefined = ... # type: QCborSimpleType


class Qt(PyQt5.sip.simplewrapper):

    class HighDpiScaleFactorRoundingPolicy(int):
        Round = ... # type: Qt.HighDpiScaleFactorRoundingPolicy
        Ceil = ... # type: Qt.HighDpiScaleFactorRoundingPolicy
        Floor = ... # type: Qt.HighDpiScaleFactorRoundingPolicy
        RoundPreferFloor = ... # type: Qt.HighDpiScaleFactorRoundingPolicy
        PassThrough = ... # type: Qt.HighDpiScaleFactorRoundingPolicy

    class ChecksumType(int):
        ChecksumIso3309 = ... # type: Qt.ChecksumType
        ChecksumItuV41 = ... # type: Qt.ChecksumType

    class EnterKeyType(int):
        EnterKeyDefault = ... # type: Qt.EnterKeyType
        EnterKeyReturn = ... # type: Qt.EnterKeyType
        EnterKeyDone = ... # type: Qt.EnterKeyType
        EnterKeyGo = ... # type: Qt.EnterKeyType
        EnterKeySend = ... # type: Qt.EnterKeyType
        EnterKeySearch = ... # type: Qt.EnterKeyType
        EnterKeyNext = ... # type: Qt.EnterKeyType
        EnterKeyPrevious = ... # type: Qt.EnterKeyType

    class ItemSelectionOperation(int):
        ReplaceSelection = ... # type: Qt.ItemSelectionOperation
        AddToSelection = ... # type: Qt.ItemSelectionOperation

    class TabFocusBehavior(int):
        NoTabFocus = ... # type: Qt.TabFocusBehavior
        TabFocusTextControls = ... # type: Qt.TabFocusBehavior
        TabFocusListControls = ... # type: Qt.TabFocusBehavior
        TabFocusAllControls = ... # type: Qt.TabFocusBehavior

    class MouseEventFlag(int):
        MouseEventCreatedDoubleClick = ... # type: Qt.MouseEventFlag

    class MouseEventSource(int):
        MouseEventNotSynthesized = ... # type: Qt.MouseEventSource
        MouseEventSynthesizedBySystem = ... # type: Qt.MouseEventSource
        MouseEventSynthesizedByQt = ... # type: Qt.MouseEventSource
        MouseEventSynthesizedByApplication = ... # type: Qt.MouseEventSource

    class ScrollPhase(int):
        ScrollBegin = ... # type: Qt.ScrollPhase
        ScrollUpdate = ... # type: Qt.ScrollPhase
        ScrollEnd = ... # type: Qt.ScrollPhase
        NoScrollPhase = ... # type: Qt.ScrollPhase
        ScrollMomentum = ... # type: Qt.ScrollPhase

    class NativeGestureType(int):
        BeginNativeGesture = ... # type: Qt.NativeGestureType
        EndNativeGesture = ... # type: Qt.NativeGestureType
        PanNativeGesture = ... # type: Qt.NativeGestureType
        ZoomNativeGesture = ... # type: Qt.NativeGestureType
        SmartZoomNativeGesture = ... # type: Qt.NativeGestureType
        RotateNativeGesture = ... # type: Qt.NativeGestureType
        SwipeNativeGesture = ... # type: Qt.NativeGestureType

    class Edge(int):
        TopEdge = ... # type: Qt.Edge
        LeftEdge = ... # type: Qt.Edge
        RightEdge = ... # type: Qt.Edge
        BottomEdge = ... # type: Qt.Edge

    class ApplicationState(int):
        ApplicationSuspended = ... # type: Qt.ApplicationState
        ApplicationHidden = ... # type: Qt.ApplicationState
        ApplicationInactive = ... # type: Qt.ApplicationState
        ApplicationActive = ... # type: Qt.ApplicationState

    class HitTestAccuracy(int):
        ExactHit = ... # type: Qt.HitTestAccuracy
        FuzzyHit = ... # type: Qt.HitTestAccuracy

    class WhiteSpaceMode(int):
        WhiteSpaceNormal = ... # type: Qt.WhiteSpaceMode
        WhiteSpacePre = ... # type: Qt.WhiteSpaceMode
        WhiteSpaceNoWrap = ... # type: Qt.WhiteSpaceMode
        WhiteSpaceModeUndefined = ... # type: Qt.WhiteSpaceMode

    class FindChildOption(int):
        FindDirectChildrenOnly = ... # type: Qt.FindChildOption
        FindChildrenRecursively = ... # type: Qt.FindChildOption

    class ScreenOrientation(int):
        PrimaryOrientation = ... # type: Qt.ScreenOrientation
        PortraitOrientation = ... # type: Qt.ScreenOrientation
        LandscapeOrientation = ... # type: Qt.ScreenOrientation
        InvertedPortraitOrientation = ... # type: Qt.ScreenOrientation
        InvertedLandscapeOrientation = ... # type: Qt.ScreenOrientation

    class CursorMoveStyle(int):
        LogicalMoveStyle = ... # type: Qt.CursorMoveStyle
        VisualMoveStyle = ... # type: Qt.CursorMoveStyle

    class NavigationMode(int):
        NavigationModeNone = ... # type: Qt.NavigationMode
        NavigationModeKeypadTabOrder = ... # type: Qt.NavigationMode
        NavigationModeKeypadDirectional = ... # type: Qt.NavigationMode
        NavigationModeCursorAuto = ... # type: Qt.NavigationMode
        NavigationModeCursorForceVisible = ... # type: Qt.NavigationMode

    class GestureFlag(int):
        DontStartGestureOnChildren = ... # type: Qt.GestureFlag
        ReceivePartialGestures = ... # type: Qt.GestureFlag
        IgnoredGesturesPropagateToParent = ... # type: Qt.GestureFlag

    class GestureType(int):
        TapGesture = ... # type: Qt.GestureType
        TapAndHoldGesture = ... # type: Qt.GestureType
        PanGesture = ... # type: Qt.GestureType
        PinchGesture = ... # type: Qt.GestureType
        SwipeGesture = ... # type: Qt.GestureType
        CustomGesture = ... # type: Qt.GestureType

    class GestureState(int):
        GestureStarted = ... # type: Qt.GestureState
        GestureUpdated = ... # type: Qt.GestureState
        GestureFinished = ... # type: Qt.GestureState
        GestureCanceled = ... # type: Qt.GestureState

    class TouchPointState(int):
        TouchPointPressed = ... # type: Qt.TouchPointState
        TouchPointMoved = ... # type: Qt.TouchPointState
        TouchPointStationary = ... # type: Qt.TouchPointState
        TouchPointReleased = ... # type: Qt.TouchPointState

    class CoordinateSystem(int):
        DeviceCoordinates = ... # type: Qt.CoordinateSystem
        LogicalCoordinates = ... # type: Qt.CoordinateSystem

    class AnchorPoint(int):
        AnchorLeft = ... # type: Qt.AnchorPoint
        AnchorHorizontalCenter = ... # type: Qt.AnchorPoint
        AnchorRight = ... # type: Qt.AnchorPoint
        AnchorTop = ... # type: Qt.AnchorPoint
        AnchorVerticalCenter = ... # type: Qt.AnchorPoint
        AnchorBottom = ... # type: Qt.AnchorPoint

    class InputMethodHint(int):
        ImhNone = ... # type: Qt.InputMethodHint
        ImhHiddenText = ... # type: Qt.InputMethodHint
        ImhNoAutoUppercase = ... # type: Qt.InputMethodHint
        ImhPreferNumbers = ... # type: Qt.InputMethodHint
        ImhPreferUppercase = ... # type: Qt.InputMethodHint
        ImhPreferLowercase = ... # type: Qt.InputMethodHint
        ImhNoPredictiveText = ... # type: Qt.InputMethodHint
        ImhDigitsOnly = ... # type: Qt.InputMethodHint
        ImhFormattedNumbersOnly = ... # type: Qt.InputMethodHint
        ImhUppercaseOnly = ... # type: Qt.InputMethodHint
        ImhLowercaseOnly = ... # type: Qt.InputMethodHint
        ImhDialableCharactersOnly = ... # type: Qt.InputMethodHint
        ImhEmailCharactersOnly = ... # type: Qt.InputMethodHint
        ImhUrlCharactersOnly = ... # type: Qt.InputMethodHint
        ImhExclusiveInputMask = ... # type: Qt.InputMethodHint
        ImhSensitiveData = ... # type: Qt.InputMethodHint
        ImhDate = ... # type: Qt.InputMethodHint
        ImhTime = ... # type: Qt.InputMethodHint
        ImhPreferLatin = ... # type: Qt.InputMethodHint
        ImhLatinOnly = ... # type: Qt.InputMethodHint
        ImhMultiLine = ... # type: Qt.InputMethodHint
        ImhNoEditMenu = ... # type: Qt.InputMethodHint
        ImhNoTextHandles = ... # type: Qt.InputMethodHint

    class TileRule(int):
        StretchTile = ... # type: Qt.TileRule
        RepeatTile = ... # type: Qt.TileRule
        RoundTile = ... # type: Qt.TileRule

    class WindowFrameSection(int):
        NoSection = ... # type: Qt.WindowFrameSection
        LeftSection = ... # type: Qt.WindowFrameSection
        TopLeftSection = ... # type: Qt.WindowFrameSection
        TopSection = ... # type: Qt.WindowFrameSection
        TopRightSection = ... # type: Qt.WindowFrameSection
        RightSection = ... # type: Qt.WindowFrameSection
        BottomRightSection = ... # type: Qt.WindowFrameSection
        BottomSection = ... # type: Qt.WindowFrameSection
        BottomLeftSection = ... # type: Qt.WindowFrameSection
        TitleBarArea = ... # type: Qt.WindowFrameSection

    class SizeHint(int):
        MinimumSize = ... # type: Qt.SizeHint
        PreferredSize = ... # type: Qt.SizeHint
        MaximumSize = ... # type: Qt.SizeHint
        MinimumDescent = ... # type: Qt.SizeHint

    class SizeMode(int):
        AbsoluteSize = ... # type: Qt.SizeMode
        RelativeSize = ... # type: Qt.SizeMode

    class EventPriority(int):
        HighEventPriority = ... # type: Qt.EventPriority
        NormalEventPriority = ... # type: Qt.EventPriority
        LowEventPriority = ... # type: Qt.EventPriority

    class Axis(int):
        XAxis = ... # type: Qt.Axis
        YAxis = ... # type: Qt.Axis
        ZAxis = ... # type: Qt.Axis

    class MaskMode(int):
        MaskInColor = ... # type: Qt.MaskMode
        MaskOutColor = ... # type: Qt.MaskMode

    class TextInteractionFlag(int):
        NoTextInteraction = ... # type: Qt.TextInteractionFlag
        TextSelectableByMouse = ... # type: Qt.TextInteractionFlag
        TextSelectableByKeyboard = ... # type: Qt.TextInteractionFlag
        LinksAccessibleByMouse = ... # type: Qt.TextInteractionFlag
        LinksAccessibleByKeyboard = ... # type: Qt.TextInteractionFlag
        TextEditable = ... # type: Qt.TextInteractionFlag
        TextEditorInteraction = ... # type: Qt.TextInteractionFlag
        TextBrowserInteraction = ... # type: Qt.TextInteractionFlag

    class ItemSelectionMode(int):
        ContainsItemShape = ... # type: Qt.ItemSelectionMode
        IntersectsItemShape = ... # type: Qt.ItemSelectionMode
        ContainsItemBoundingRect = ... # type: Qt.ItemSelectionMode
        IntersectsItemBoundingRect = ... # type: Qt.ItemSelectionMode

    class ApplicationAttribute(int):
        AA_ImmediateWidgetCreation = ... # type: Qt.ApplicationAttribute
        AA_MSWindowsUseDirect3DByDefault = ... # type: Qt.ApplicationAttribute
        AA_DontShowIconsInMenus = ... # type: Qt.ApplicationAttribute
        AA_NativeWindows = ... # type: Qt.ApplicationAttribute
        AA_DontCreateNativeWidgetSiblings = ... # type: Qt.ApplicationAttribute
        AA_MacPluginApplication = ... # type: Qt.ApplicationAttribute
        AA_DontUseNativeMenuBar = ... # type: Qt.ApplicationAttribute
        AA_MacDontSwapCtrlAndMeta = ... # type: Qt.ApplicationAttribute
        AA_X11InitThreads = ... # type: Qt.ApplicationAttribute
        AA_Use96Dpi = ... # type: Qt.ApplicationAttribute
        AA_SynthesizeTouchForUnhandledMouseEvents = ... # type: Qt.ApplicationAttribute
        AA_SynthesizeMouseForUnhandledTouchEvents = ... # type: Qt.ApplicationAttribute
        AA_UseHighDpiPixmaps = ... # type: Qt.ApplicationAttribute
        AA_ForceRasterWidgets = ... # type: Qt.ApplicationAttribute
        AA_UseDesktopOpenGL = ... # type: Qt.ApplicationAttribute
        AA_UseOpenGLES = ... # type: Qt.ApplicationAttribute
        AA_UseSoftwareOpenGL = ... # type: Qt.ApplicationAttribute
        AA_ShareOpenGLContexts = ... # type: Qt.ApplicationAttribute
        AA_SetPalette = ... # type: Qt.ApplicationAttribute
        AA_EnableHighDpiScaling = ... # type: Qt.ApplicationAttribute
        AA_DisableHighDpiScaling = ... # type: Qt.ApplicationAttribute
        AA_PluginApplication = ... # type: Qt.ApplicationAttribute
        AA_UseStyleSheetPropagationInWidgetStyles = ... # type: Qt.ApplicationAttribute
        AA_DontUseNativeDialogs = ... # type: Qt.ApplicationAttribute
        AA_SynthesizeMouseForUnhandledTabletEvents = ... # type: Qt.ApplicationAttribute
        AA_CompressHighFrequencyEvents = ... # type: Qt.ApplicationAttribute
        AA_DontCheckOpenGLContextThreadAffinity = ... # type: Qt.ApplicationAttribute
        AA_DisableShaderDiskCache = ... # type: Qt.ApplicationAttribute
        AA_DontShowShortcutsInContextMenus = ... # type: Qt.ApplicationAttribute
        AA_CompressTabletEvents = ... # type: Qt.ApplicationAttribute
        AA_DisableWindowContextHelpButton = ... # type: Qt.ApplicationAttribute
        AA_DisableSessionManager = ... # type: Qt.ApplicationAttribute
        AA_DisableNativeVirtualKeyboard = ... # type: Qt.ApplicationAttribute

    class WindowModality(int):
        NonModal = ... # type: Qt.WindowModality
        WindowModal = ... # type: Qt.WindowModality
        ApplicationModal = ... # type: Qt.WindowModality

    class MatchFlag(int):
        MatchExactly = ... # type: Qt.MatchFlag
        MatchFixedString = ... # type: Qt.MatchFlag
        MatchContains = ... # type: Qt.MatchFlag
        MatchStartsWith = ... # type: Qt.MatchFlag
        MatchEndsWith = ... # type: Qt.MatchFlag
        MatchRegExp = ... # type: Qt.MatchFlag
        MatchWildcard = ... # type: Qt.MatchFlag
        MatchCaseSensitive = ... # type: Qt.MatchFlag
        MatchWrap = ... # type: Qt.MatchFlag
        MatchRecursive = ... # type: Qt.MatchFlag
        MatchRegularExpression = ... # type: Qt.MatchFlag

    class ItemFlag(int):
        NoItemFlags = ... # type: Qt.ItemFlag
        ItemIsSelectable = ... # type: Qt.ItemFlag
        ItemIsEditable = ... # type: Qt.ItemFlag
        ItemIsDragEnabled = ... # type: Qt.ItemFlag
        ItemIsDropEnabled = ... # type: Qt.ItemFlag
        ItemIsUserCheckable = ... # type: Qt.ItemFlag
        ItemIsEnabled = ... # type: Qt.ItemFlag
        ItemIsTristate = ... # type: Qt.ItemFlag
        ItemNeverHasChildren = ... # type: Qt.ItemFlag
        ItemIsUserTristate = ... # type: Qt.ItemFlag
        ItemIsAutoTristate = ... # type: Qt.ItemFlag

    class ItemDataRole(int):
        DisplayRole = ... # type: Qt.ItemDataRole
        DecorationRole = ... # type: Qt.ItemDataRole
        EditRole = ... # type: Qt.ItemDataRole
        ToolTipRole = ... # type: Qt.ItemDataRole
        StatusTipRole = ... # type: Qt.ItemDataRole
        WhatsThisRole = ... # type: Qt.ItemDataRole
        FontRole = ... # type: Qt.ItemDataRole
        TextAlignmentRole = ... # type: Qt.ItemDataRole
        BackgroundRole = ... # type: Qt.ItemDataRole
        BackgroundColorRole = ... # type: Qt.ItemDataRole
        ForegroundRole = ... # type: Qt.ItemDataRole
        TextColorRole = ... # type: Qt.ItemDataRole
        CheckStateRole = ... # type: Qt.ItemDataRole
        AccessibleTextRole = ... # type: Qt.ItemDataRole
        AccessibleDescriptionRole = ... # type: Qt.ItemDataRole
        SizeHintRole = ... # type: Qt.ItemDataRole
        InitialSortOrderRole = ... # type: Qt.ItemDataRole
        UserRole = ... # type: Qt.ItemDataRole

    class CheckState(int):
        Unchecked = ... # type: Qt.CheckState
        PartiallyChecked = ... # type: Qt.CheckState
        Checked = ... # type: Qt.CheckState

    class DropAction(int):
        CopyAction = ... # type: Qt.DropAction
        MoveAction = ... # type: Qt.DropAction
        LinkAction = ... # type: Qt.DropAction
        ActionMask = ... # type: Qt.DropAction
        TargetMoveAction = ... # type: Qt.DropAction
        IgnoreAction = ... # type: Qt.DropAction

    class LayoutDirection(int):
        LeftToRight = ... # type: Qt.LayoutDirection
        RightToLeft = ... # type: Qt.LayoutDirection
        LayoutDirectionAuto = ... # type: Qt.LayoutDirection

    class ToolButtonStyle(int):
        ToolButtonIconOnly = ... # type: Qt.ToolButtonStyle
        ToolButtonTextOnly = ... # type: Qt.ToolButtonStyle
        ToolButtonTextBesideIcon = ... # type: Qt.ToolButtonStyle
        ToolButtonTextUnderIcon = ... # type: Qt.ToolButtonStyle
        ToolButtonFollowStyle = ... # type: Qt.ToolButtonStyle

    class InputMethodQuery(int):
        ImMicroFocus = ... # type: Qt.InputMethodQuery
        ImFont = ... # type: Qt.InputMethodQuery
        ImCursorPosition = ... # type: Qt.InputMethodQuery
        ImSurroundingText = ... # type: Qt.InputMethodQuery
        ImCurrentSelection = ... # type: Qt.InputMethodQuery
        ImMaximumTextLength = ... # type: Qt.InputMethodQuery
        ImAnchorPosition = ... # type: Qt.InputMethodQuery
        ImEnabled = ... # type: Qt.InputMethodQuery
        ImCursorRectangle = ... # type: Qt.InputMethodQuery
        ImHints = ... # type: Qt.InputMethodQuery
        ImPreferredLanguage = ... # type: Qt.InputMethodQuery
        ImPlatformData = ... # type: Qt.InputMethodQuery
        ImQueryInput = ... # type: Qt.InputMethodQuery
        ImQueryAll = ... # type: Qt.InputMethodQuery
        ImAbsolutePosition = ... # type: Qt.InputMethodQuery
        ImTextBeforeCursor = ... # type: Qt.InputMethodQuery
        ImTextAfterCursor = ... # type: Qt.InputMethodQuery
        ImEnterKeyType = ... # type: Qt.InputMethodQuery
        ImAnchorRectangle = ... # type: Qt.InputMethodQuery
        ImInputItemClipRectangle = ... # type: Qt.InputMethodQuery

    class ContextMenuPolicy(int):
        NoContextMenu = ... # type: Qt.ContextMenuPolicy
        PreventContextMenu = ... # type: Qt.ContextMenuPolicy
        DefaultContextMenu = ... # type: Qt.ContextMenuPolicy
        ActionsContextMenu = ... # type: Qt.ContextMenuPolicy
        CustomContextMenu = ... # type: Qt.ContextMenuPolicy

    class FocusReason(int):
        MouseFocusReason = ... # type: Qt.FocusReason
        TabFocusReason = ... # type: Qt.FocusReason
        BacktabFocusReason = ... # type: Qt.FocusReason
        ActiveWindowFocusReason = ... # type: Qt.FocusReason
        PopupFocusReason = ... # type: Qt.FocusReason
        ShortcutFocusReason = ... # type: Qt.FocusReason
        MenuBarFocusReason = ... # type: Qt.FocusReason
        OtherFocusReason = ... # type: Qt.FocusReason
        NoFocusReason = ... # type: Qt.FocusReason

    class TransformationMode(int):
        FastTransformation = ... # type: Qt.TransformationMode
        SmoothTransformation = ... # type: Qt.TransformationMode

    class ClipOperation(int):
        NoClip = ... # type: Qt.ClipOperation
        ReplaceClip = ... # type: Qt.ClipOperation
        IntersectClip = ... # type: Qt.ClipOperation

    class FillRule(int):
        OddEvenFill = ... # type: Qt.FillRule
        WindingFill = ... # type: Qt.FillRule

    class ShortcutContext(int):
        WidgetShortcut = ... # type: Qt.ShortcutContext
        WindowShortcut = ... # type: Qt.ShortcutContext
        ApplicationShortcut = ... # type: Qt.ShortcutContext
        WidgetWithChildrenShortcut = ... # type: Qt.ShortcutContext

    class ConnectionType(int):
        AutoConnection = ... # type: Qt.ConnectionType
        DirectConnection = ... # type: Qt.ConnectionType
        QueuedConnection = ... # type: Qt.ConnectionType
        BlockingQueuedConnection = ... # type: Qt.ConnectionType
        UniqueConnection = ... # type: Qt.ConnectionType

    class Corner(int):
        TopLeftCorner = ... # type: Qt.Corner
        TopRightCorner = ... # type: Qt.Corner
        BottomLeftCorner = ... # type: Qt.Corner
        BottomRightCorner = ... # type: Qt.Corner

    class CaseSensitivity(int):
        CaseInsensitive = ... # type: Qt.CaseSensitivity
        CaseSensitive = ... # type: Qt.CaseSensitivity

    class ScrollBarPolicy(int):
        ScrollBarAsNeeded = ... # type: Qt.ScrollBarPolicy
        ScrollBarAlwaysOff = ... # type: Qt.ScrollBarPolicy
        ScrollBarAlwaysOn = ... # type: Qt.ScrollBarPolicy

    class DayOfWeek(int):
        Monday = ... # type: Qt.DayOfWeek
        Tuesday = ... # type: Qt.DayOfWeek
        Wednesday = ... # type: Qt.DayOfWeek
        Thursday = ... # type: Qt.DayOfWeek
        Friday = ... # type: Qt.DayOfWeek
        Saturday = ... # type: Qt.DayOfWeek
        Sunday = ... # type: Qt.DayOfWeek

    class TimeSpec(int):
        LocalTime = ... # type: Qt.TimeSpec
        UTC = ... # type: Qt.TimeSpec
        OffsetFromUTC = ... # type: Qt.TimeSpec
        TimeZone = ... # type: Qt.TimeSpec

    class DateFormat(int):
        TextDate = ... # type: Qt.DateFormat
        ISODate = ... # type: Qt.DateFormat
        ISODateWithMs = ... # type: Qt.DateFormat
        LocalDate = ... # type: Qt.DateFormat
        SystemLocaleDate = ... # type: Qt.DateFormat
        LocaleDate = ... # type: Qt.DateFormat
        SystemLocaleShortDate = ... # type: Qt.DateFormat
        SystemLocaleLongDate = ... # type: Qt.DateFormat
        DefaultLocaleShortDate = ... # type: Qt.DateFormat
        DefaultLocaleLongDate = ... # type: Qt.DateFormat
        RFC2822Date = ... # type: Qt.DateFormat

    class ToolBarArea(int):
        LeftToolBarArea = ... # type: Qt.ToolBarArea
        RightToolBarArea = ... # type: Qt.ToolBarArea
        TopToolBarArea = ... # type: Qt.ToolBarArea
        BottomToolBarArea = ... # type: Qt.ToolBarArea
        ToolBarArea_Mask = ... # type: Qt.ToolBarArea
        AllToolBarAreas = ... # type: Qt.ToolBarArea
        NoToolBarArea = ... # type: Qt.ToolBarArea

    class TimerType(int):
        PreciseTimer = ... # type: Qt.TimerType
        CoarseTimer = ... # type: Qt.TimerType
        VeryCoarseTimer = ... # type: Qt.TimerType

    class DockWidgetArea(int):
        LeftDockWidgetArea = ... # type: Qt.DockWidgetArea
        RightDockWidgetArea = ... # type: Qt.DockWidgetArea
        TopDockWidgetArea = ... # type: Qt.DockWidgetArea
        BottomDockWidgetArea = ... # type: Qt.DockWidgetArea
        DockWidgetArea_Mask = ... # type: Qt.DockWidgetArea
        AllDockWidgetAreas = ... # type: Qt.DockWidgetArea
        NoDockWidgetArea = ... # type: Qt.DockWidgetArea

    class AspectRatioMode(int):
        IgnoreAspectRatio = ... # type: Qt.AspectRatioMode
        KeepAspectRatio = ... # type: Qt.AspectRatioMode
        KeepAspectRatioByExpanding = ... # type: Qt.AspectRatioMode

    class TextFormat(int):
        PlainText = ... # type: Qt.TextFormat
        RichText = ... # type: Qt.TextFormat
        AutoText = ... # type: Qt.TextFormat
        MarkdownText = ... # type: Qt.TextFormat

    class CursorShape(int):
        ArrowCursor = ... # type: Qt.CursorShape
        UpArrowCursor = ... # type: Qt.CursorShape
        CrossCursor = ... # type: Qt.CursorShape
        WaitCursor = ... # type: Qt.CursorShape
        IBeamCursor = ... # type: Qt.CursorShape
        SizeVerCursor = ... # type: Qt.CursorShape
        SizeHorCursor = ... # type: Qt.CursorShape
        SizeBDiagCursor = ... # type: Qt.CursorShape
        SizeFDiagCursor = ... # type: Qt.CursorShape
        SizeAllCursor = ... # type: Qt.CursorShape
        BlankCursor = ... # type: Qt.CursorShape
        SplitVCursor = ... # type: Qt.CursorShape
        SplitHCursor = ... # type: Qt.CursorShape
        PointingHandCursor = ... # type: Qt.CursorShape
        ForbiddenCursor = ... # type: Qt.CursorShape
        OpenHandCursor = ... # type: Qt.CursorShape
        ClosedHandCursor = ... # type: Qt.CursorShape
        WhatsThisCursor = ... # type: Qt.CursorShape
        BusyCursor = ... # type: Qt.CursorShape
        LastCursor = ... # type: Qt.CursorShape
        BitmapCursor = ... # type: Qt.CursorShape
        CustomCursor = ... # type: Qt.CursorShape
        DragCopyCursor = ... # type: Qt.CursorShape
        DragMoveCursor = ... # type: Qt.CursorShape
        DragLinkCursor = ... # type: Qt.CursorShape

    class UIEffect(int):
        UI_General = ... # type: Qt.UIEffect
        UI_AnimateMenu = ... # type: Qt.UIEffect
        UI_FadeMenu = ... # type: Qt.UIEffect
        UI_AnimateCombo = ... # type: Qt.UIEffect
        UI_AnimateTooltip = ... # type: Qt.UIEffect
        UI_FadeTooltip = ... # type: Qt.UIEffect
        UI_AnimateToolBox = ... # type: Qt.UIEffect

    class BrushStyle(int):
        NoBrush = ... # type: Qt.BrushStyle
        SolidPattern = ... # type: Qt.BrushStyle
        Dense1Pattern = ... # type: Qt.BrushStyle
        Dense2Pattern = ... # type: Qt.BrushStyle
        Dense3Pattern = ... # type: Qt.BrushStyle
        Dense4Pattern = ... # type: Qt.BrushStyle
        Dense5Pattern = ... # type: Qt.BrushStyle
        Dense6Pattern = ... # type: Qt.BrushStyle
        Dense7Pattern = ... # type: Qt.BrushStyle
        HorPattern = ... # type: Qt.BrushStyle
        VerPattern = ... # type: Qt.BrushStyle
        CrossPattern = ... # type: Qt.BrushStyle
        BDiagPattern = ... # type: Qt.BrushStyle
        FDiagPattern = ... # type: Qt.BrushStyle
        DiagCrossPattern = ... # type: Qt.BrushStyle
        LinearGradientPattern = ... # type: Qt.BrushStyle
        RadialGradientPattern = ... # type: Qt.BrushStyle
        ConicalGradientPattern = ... # type: Qt.BrushStyle
        TexturePattern = ... # type: Qt.BrushStyle

    class PenJoinStyle(int):
        MiterJoin = ... # type: Qt.PenJoinStyle
        BevelJoin = ... # type: Qt.PenJoinStyle
        RoundJoin = ... # type: Qt.PenJoinStyle
        MPenJoinStyle = ... # type: Qt.PenJoinStyle
        SvgMiterJoin = ... # type: Qt.PenJoinStyle

    class PenCapStyle(int):
        FlatCap = ... # type: Qt.PenCapStyle
        SquareCap = ... # type: Qt.PenCapStyle
        RoundCap = ... # type: Qt.PenCapStyle
        MPenCapStyle = ... # type: Qt.PenCapStyle

    class PenStyle(int):
        NoPen = ... # type: Qt.PenStyle
        SolidLine = ... # type: Qt.PenStyle
        DashLine = ... # type: Qt.PenStyle
        DotLine = ... # type: Qt.PenStyle
        DashDotLine = ... # type: Qt.PenStyle
        DashDotDotLine = ... # type: Qt.PenStyle
        CustomDashLine = ... # type: Qt.PenStyle
        MPenStyle = ... # type: Qt.PenStyle

    class ArrowType(int):
        NoArrow = ... # type: Qt.ArrowType
        UpArrow = ... # type: Qt.ArrowType
        DownArrow = ... # type: Qt.ArrowType
        LeftArrow = ... # type: Qt.ArrowType
        RightArrow = ... # type: Qt.ArrowType

    class Key(int):
        Key_Escape = ... # type: Qt.Key
        Key_Tab = ... # type: Qt.Key
        Key_Backtab = ... # type: Qt.Key
        Key_Backspace = ... # type: Qt.Key
        Key_Return = ... # type: Qt.Key
        Key_Enter = ... # type: Qt.Key
        Key_Insert = ... # type: Qt.Key
        Key_Delete = ... # type: Qt.Key
        Key_Pause = ... # type: Qt.Key
        Key_Print = ... # type: Qt.Key
        Key_SysReq = ... # type: Qt.Key
        Key_Clear = ... # type: Qt.Key
        Key_Home = ... # type: Qt.Key
        Key_End = ... # type: Qt.Key
        Key_Left = ... # type: Qt.Key
        Key_Up = ... # type: Qt.Key
        Key_Right = ... # type: Qt.Key
        Key_Down = ... # type: Qt.Key
        Key_PageUp = ... # type: Qt.Key
        Key_PageDown = ... # type: Qt.Key
        Key_Shift = ... # type: Qt.Key
        Key_Control = ... # type: Qt.Key
        Key_Meta = ... # type: Qt.Key
        Key_Alt = ... # type: Qt.Key
        Key_CapsLock = ... # type: Qt.Key
        Key_NumLock = ... # type: Qt.Key
        Key_ScrollLock = ... # type: Qt.Key
        Key_F1 = ... # type: Qt.Key
        Key_F2 = ... # type: Qt.Key
        Key_F3 = ... # type: Qt.Key
        Key_F4 = ... # type: Qt.Key
        Key_F5 = ... # type: Qt.Key
        Key_F6 = ... # type: Qt.Key
        Key_F7 = ... # type: Qt.Key
        Key_F8 = ... # type: Qt.Key
        Key_F9 = ... # type: Qt.Key
        Key_F10 = ... # type: Qt.Key
        Key_F11 = ... # type: Qt.Key
        Key_F12 = ... # type: Qt.Key
        Key_F13 = ... # type: Qt.Key
        Key_F14 = ... # type: Qt.Key
        Key_F15 = ... # type: Qt.Key
        Key_F16 = ... # type: Qt.Key
        Key_F17 = ... # type: Qt.Key
        Key_F18 = ... # type: Qt.Key
        Key_F19 = ... # type: Qt.Key
        Key_F20 = ... # type: Qt.Key
        Key_F21 = ... # type: Qt.Key
        Key_F22 = ... # type: Qt.Key
        Key_F23 = ... # type: Qt.Key
        Key_F24 = ... # type: Qt.Key
        Key_F25 = ... # type: Qt.Key
        Key_F26 = ... # type: Qt.Key
        Key_F27 = ... # type: Qt.Key
        Key_F28 = ... # type: Qt.Key
        Key_F29 = ... # type: Qt.Key
        Key_F30 = ... # type: Qt.Key
        Key_F31 = ... # type: Qt.Key
        Key_F32 = ... # type: Qt.Key
        Key_F33 = ... # type: Qt.Key
        Key_F34 = ... # type: Qt.Key
        Key_F35 = ... # type: Qt.Key
        Key_Super_L = ... # type: Qt.Key
        Key_Super_R = ... # type: Qt.Key
        Key_Menu = ... # type: Qt.Key
        Key_Hyper_L = ... # type: Qt.Key
        Key_Hyper_R = ... # type: Qt.Key
        Key_Help = ... # type: Qt.Key
        Key_Direction_L = ... # type: Qt.Key
        Key_Direction_R = ... # type: Qt.Key
        Key_Space = ... # type: Qt.Key
        Key_Any = ... # type: Qt.Key
        Key_Exclam = ... # type: Qt.Key
        Key_QuoteDbl = ... # type: Qt.Key
        Key_NumberSign = ... # type: Qt.Key
        Key_Dollar = ... # type: Qt.Key
        Key_Percent = ... # type: Qt.Key
        Key_Ampersand = ... # type: Qt.Key
        Key_Apostrophe = ... # type: Qt.Key
        Key_ParenLeft = ... # type: Qt.Key
        Key_ParenRight = ... # type: Qt.Key
        Key_Asterisk = ... # type: Qt.Key
        Key_Plus = ... # type: Qt.Key
        Key_Comma = ... # type: Qt.Key
        Key_Minus = ... # type: Qt.Key
        Key_Period = ... # type: Qt.Key
        Key_Slash = ... # type: Qt.Key
        Key_0 = ... # type: Qt.Key
        Key_1 = ... # type: Qt.Key
        Key_2 = ... # type: Qt.Key
        Key_3 = ... # type: Qt.Key
        Key_4 = ... # type: Qt.Key
        Key_5 = ... # type: Qt.Key
        Key_6 = ... # type: Qt.Key
        Key_7 = ... # type: Qt.Key
        Key_8 = ... # type: Qt.Key
        Key_9 = ... # type: Qt.Key
        Key_Colon = ... # type: Qt.Key
        Key_Semicolon = ... # type: Qt.Key
        Key_Less = ... # type: Qt.Key
        Key_Equal = ... # type: Qt.Key
        Key_Greater = ... # type: Qt.Key
        Key_Question = ... # type: Qt.Key
        Key_At = ... # type: Qt.Key
        Key_A = ... # type: Qt.Key
        Key_B = ... # type: Qt.Key
        Key_C = ... # type: Qt.Key
        Key_D = ... # type: Qt.Key
        Key_E = ... # type: Qt.Key
        Key_F = ... # type: Qt.Key
        Key_G = ... # type: Qt.Key
        Key_H = ... # type: Qt.Key
        Key_I = ... # type: Qt.Key
        Key_J = ... # type: Qt.Key
        Key_K = ... # type: Qt.Key
        Key_L = ... # type: Qt.Key
        Key_M = ... # type: Qt.Key
        Key_N = ... # type: Qt.Key
        Key_O = ... # type: Qt.Key
        Key_P = ... # type: Qt.Key
        Key_Q = ... # type: Qt.Key
        Key_R = ... # type: Qt.Key
        Key_S = ... # type: Qt.Key
        Key_T = ... # type: Qt.Key
        Key_U = ... # type: Qt.Key
        Key_V = ... # type: Qt.Key
        Key_W = ... # type: Qt.Key
        Key_X = ... # type: Qt.Key
        Key_Y = ... # type: Qt.Key
        Key_Z = ... # type: Qt.Key
        Key_BracketLeft = ... # type: Qt.Key
        Key_Backslash = ... # type: Qt.Key
        Key_BracketRight = ... # type: Qt.Key
        Key_AsciiCircum = ... # type: Qt.Key
        Key_Underscore = ... # type: Qt.Key
        Key_QuoteLeft = ... # type: Qt.Key
        Key_BraceLeft = ... # type: Qt.Key
        Key_Bar = ... # type: Qt.Key
        Key_BraceRight = ... # type: Qt.Key
        Key_AsciiTilde = ... # type: Qt.Key
        Key_nobreakspace = ... # type: Qt.Key
        Key_exclamdown = ... # type: Qt.Key
        Key_cent = ... # type: Qt.Key
        Key_sterling = ... # type: Qt.Key
        Key_currency = ... # type: Qt.Key
        Key_yen = ... # type: Qt.Key
        Key_brokenbar = ... # type: Qt.Key
        Key_section = ... # type: Qt.Key
        Key_diaeresis = ... # type: Qt.Key
        Key_copyright = ... # type: Qt.Key
        Key_ordfeminine = ... # type: Qt.Key
        Key_guillemotleft = ... # type: Qt.Key
        Key_notsign = ... # type: Qt.Key
        Key_hyphen = ... # type: Qt.Key
        Key_registered = ... # type: Qt.Key
        Key_macron = ... # type: Qt.Key
        Key_degree = ... # type: Qt.Key
        Key_plusminus = ... # type: Qt.Key
        Key_twosuperior = ... # type: Qt.Key
        Key_threesuperior = ... # type: Qt.Key
        Key_acute = ... # type: Qt.Key
        Key_mu = ... # type: Qt.Key
        Key_paragraph = ... # type: Qt.Key
        Key_periodcentered = ... # type: Qt.Key
        Key_cedilla = ... # type: Qt.Key
        Key_onesuperior = ... # type: Qt.Key
        Key_masculine = ... # type: Qt.Key
        Key_guillemotright = ... # type: Qt.Key
        Key_onequarter = ... # type: Qt.Key
        Key_onehalf = ... # type: Qt.Key
        Key_threequarters = ... # type: Qt.Key
        Key_questiondown = ... # type: Qt.Key
        Key_Agrave = ... # type: Qt.Key
        Key_Aacute = ... # type: Qt.Key
        Key_Acircumflex = ... # type: Qt.Key
        Key_Atilde = ... # type: Qt.Key
        Key_Adiaeresis = ... # type: Qt.Key
        Key_Aring = ... # type: Qt.Key
        Key_AE = ... # type: Qt.Key
        Key_Ccedilla = ... # type: Qt.Key
        Key_Egrave = ... # type: Qt.Key
        Key_Eacute = ... # type: Qt.Key
        Key_Ecircumflex = ... # type: Qt.Key
        Key_Ediaeresis = ... # type: Qt.Key
        Key_Igrave = ... # type: Qt.Key
        Key_Iacute = ... # type: Qt.Key
        Key_Icircumflex = ... # type: Qt.Key
        Key_Idiaeresis = ... # type: Qt.Key
        Key_ETH = ... # type: Qt.Key
        Key_Ntilde = ... # type: Qt.Key
        Key_Ograve = ... # type: Qt.Key
        Key_Oacute = ... # type: Qt.Key
        Key_Ocircumflex = ... # type: Qt.Key
        Key_Otilde = ... # type: Qt.Key
        Key_Odiaeresis = ... # type: Qt.Key
        Key_multiply = ... # type: Qt.Key
        Key_Ooblique = ... # type: Qt.Key
        Key_Ugrave = ... # type: Qt.Key
        Key_Uacute = ... # type: Qt.Key
        Key_Ucircumflex = ... # type: Qt.Key
        Key_Udiaeresis = ... # type: Qt.Key
        Key_Yacute = ... # type: Qt.Key
        Key_THORN = ... # type: Qt.Key
        Key_ssharp = ... # type: Qt.Key
        Key_division = ... # type: Qt.Key
        Key_ydiaeresis = ... # type: Qt.Key
        Key_AltGr = ... # type: Qt.Key
        Key_Multi_key = ... # type: Qt.Key
        Key_Codeinput = ... # type: Qt.Key
        Key_SingleCandidate = ... # type: Qt.Key
        Key_MultipleCandidate = ... # type: Qt.Key
        Key_PreviousCandidate = ... # type: Qt.Key
        Key_Mode_switch = ... # type: Qt.Key
        Key_Kanji = ... # type: Qt.Key
        Key_Muhenkan = ... # type: Qt.Key
        Key_Henkan = ... # type: Qt.Key
        Key_Romaji = ... # type: Qt.Key
        Key_Hiragana = ... # type: Qt.Key
        Key_Katakana = ... # type: Qt.Key
        Key_Hiragana_Katakana = ... # type: Qt.Key
        Key_Zenkaku = ... # type: Qt.Key
        Key_Hankaku = ... # type: Qt.Key
        Key_Zenkaku_Hankaku = ... # type: Qt.Key
        Key_Touroku = ... # type: Qt.Key
        Key_Massyo = ... # type: Qt.Key
        Key_Kana_Lock = ... # type: Qt.Key
        Key_Kana_Shift = ... # type: Qt.Key
        Key_Eisu_Shift = ... # type: Qt.Key
        Key_Eisu_toggle = ... # type: Qt.Key
        Key_Hangul = ... # type: Qt.Key
        Key_Hangul_Start = ... # type: Qt.Key
        Key_Hangul_End = ... # type: Qt.Key
        Key_Hangul_Hanja = ... # type: Qt.Key
        Key_Hangul_Jamo = ... # type: Qt.Key
        Key_Hangul_Romaja = ... # type: Qt.Key
        Key_Hangul_Jeonja = ... # type: Qt.Key
        Key_Hangul_Banja = ... # type: Qt.Key
        Key_Hangul_PreHanja = ... # type: Qt.Key
        Key_Hangul_PostHanja = ... # type: Qt.Key
        Key_Hangul_Special = ... # type: Qt.Key
        Key_Dead_Grave = ... # type: Qt.Key
        Key_Dead_Acute = ... # type: Qt.Key
        Key_Dead_Circumflex = ... # type: Qt.Key
        Key_Dead_Tilde = ... # type: Qt.Key
        Key_Dead_Macron = ... # type: Qt.Key
        Key_Dead_Breve = ... # type: Qt.Key
        Key_Dead_Abovedot = ... # type: Qt.Key
        Key_Dead_Diaeresis = ... # type: Qt.Key
        Key_Dead_Abovering = ... # type: Qt.Key
        Key_Dead_Doubleacute = ... # type: Qt.Key
        Key_Dead_Caron = ... # type: Qt.Key
        Key_Dead_Cedilla = ... # type: Qt.Key
        Key_Dead_Ogonek = ... # type: Qt.Key
        Key_Dead_Iota = ... # type: Qt.Key
        Key_Dead_Voiced_Sound = ... # type: Qt.Key
        Key_Dead_Semivoiced_Sound = ... # type: Qt.Key
        Key_Dead_Belowdot = ... # type: Qt.Key
        Key_Dead_Hook = ... # type: Qt.Key
        Key_Dead_Horn = ... # type: Qt.Key
        Key_Back = ... # type: Qt.Key
        Key_Forward = ... # type: Qt.Key
        Key_Stop = ... # type: Qt.Key
        Key_Refresh = ... # type: Qt.Key
        Key_VolumeDown = ... # type: Qt.Key
        Key_VolumeMute = ... # type: Qt.Key
        Key_VolumeUp = ... # type: Qt.Key
        Key_BassBoost = ... # type: Qt.Key
        Key_BassUp = ... # type: Qt.Key
        Key_BassDown = ... # type: Qt.Key
        Key_TrebleUp = ... # type: Qt.Key
        Key_TrebleDown = ... # type: Qt.Key
        Key_MediaPlay = ... # type: Qt.Key
        Key_MediaStop = ... # type: Qt.Key
        Key_MediaPrevious = ... # type: Qt.Key
        Key_MediaNext = ... # type: Qt.Key
        Key_MediaRecord = ... # type: Qt.Key
        Key_HomePage = ... # type: Qt.Key
        Key_Favorites = ... # type: Qt.Key
        Key_Search = ... # type: Qt.Key
        Key_Standby = ... # type: Qt.Key
        Key_OpenUrl = ... # type: Qt.Key
        Key_LaunchMail = ... # type: Qt.Key
        Key_LaunchMedia = ... # type: Qt.Key
        Key_Launch0 = ... # type: Qt.Key
        Key_Launch1 = ... # type: Qt.Key
        Key_Launch2 = ... # type: Qt.Key
        Key_Launch3 = ... # type: Qt.Key
        Key_Launch4 = ... # type: Qt.Key
        Key_Launch5 = ... # type: Qt.Key
        Key_Launch6 = ... # type: Qt.Key
        Key_Launch7 = ... # type: Qt.Key
        Key_Launch8 = ... # type: Qt.Key
        Key_Launch9 = ... # type: Qt.Key
        Key_LaunchA = ... # type: Qt.Key
        Key_LaunchB = ... # type: Qt.Key
        Key_LaunchC = ... # type: Qt.Key
        Key_LaunchD = ... # type: Qt.Key
        Key_LaunchE = ... # type: Qt.Key
        Key_LaunchF = ... # type: Qt.Key
        Key_MediaLast = ... # type: Qt.Key
        Key_Select = ... # type: Qt.Key
        Key_Yes = ... # type: Qt.Key
        Key_No = ... # type: Qt.Key
        Key_Context1 = ... # type: Qt.Key
        Key_Context2 = ... # type: Qt.Key
        Key_Context3 = ... # type: Qt.Key
        Key_Context4 = ... # type: Qt.Key
        Key_Call = ... # type: Qt.Key
        Key_Hangup = ... # type: Qt.Key
        Key_Flip = ... # type: Qt.Key
        Key_unknown = ... # type: Qt.Key
        Key_Execute = ... # type: Qt.Key
        Key_Printer = ... # type: Qt.Key
        Key_Play = ... # type: Qt.Key
        Key_Sleep = ... # type: Qt.Key
        Key_Zoom = ... # type: Qt.Key
        Key_Cancel = ... # type: Qt.Key
        Key_MonBrightnessUp = ... # type: Qt.Key
        Key_MonBrightnessDown = ... # type: Qt.Key
        Key_KeyboardLightOnOff = ... # type: Qt.Key
        Key_KeyboardBrightnessUp = ... # type: Qt.Key
        Key_KeyboardBrightnessDown = ... # type: Qt.Key
        Key_PowerOff = ... # type: Qt.Key
        Key_WakeUp = ... # type: Qt.Key
        Key_Eject = ... # type: Qt.Key
        Key_ScreenSaver = ... # type: Qt.Key
        Key_WWW = ... # type: Qt.Key
        Key_Memo = ... # type: Qt.Key
        Key_LightBulb = ... # type: Qt.Key
        Key_Shop = ... # type: Qt.Key
        Key_History = ... # type: Qt.Key
        Key_AddFavorite = ... # type: Qt.Key
        Key_HotLinks = ... # type: Qt.Key
        Key_BrightnessAdjust = ... # type: Qt.Key
        Key_Finance = ... # type: Qt.Key
        Key_Community = ... # type: Qt.Key
        Key_AudioRewind = ... # type: Qt.Key
        Key_BackForward = ... # type: Qt.Key
        Key_ApplicationLeft = ... # type: Qt.Key
        Key_ApplicationRight = ... # type: Qt.Key
        Key_Book = ... # type: Qt.Key
        Key_CD = ... # type: Qt.Key
        Key_Calculator = ... # type: Qt.Key
        Key_ToDoList = ... # type: Qt.Key
        Key_ClearGrab = ... # type: Qt.Key
        Key_Close = ... # type: Qt.Key
        Key_Copy = ... # type: Qt.Key
        Key_Cut = ... # type: Qt.Key
        Key_Display = ... # type: Qt.Key
        Key_DOS = ... # type: Qt.Key
        Key_Documents = ... # type: Qt.Key
        Key_Excel = ... # type: Qt.Key
        Key_Explorer = ... # type: Qt.Key
        Key_Game = ... # type: Qt.Key
        Key_Go = ... # type: Qt.Key
        Key_iTouch = ... # type: Qt.Key
        Key_LogOff = ... # type: Qt.Key
        Key_Market = ... # type: Qt.Key
        Key_Meeting = ... # type: Qt.Key
        Key_MenuKB = ... # type: Qt.Key
        Key_MenuPB = ... # type: Qt.Key
        Key_MySites = ... # type: Qt.Key
        Key_News = ... # type: Qt.Key
        Key_OfficeHome = ... # type: Qt.Key
        Key_Option = ... # type: Qt.Key
        Key_Paste = ... # type: Qt.Key
        Key_Phone = ... # type: Qt.Key
        Key_Calendar = ... # type: Qt.Key
        Key_Reply = ... # type: Qt.Key
        Key_Reload = ... # type: Qt.Key
        Key_RotateWindows = ... # type: Qt.Key
        Key_RotationPB = ... # type: Qt.Key
        Key_RotationKB = ... # type: Qt.Key
        Key_Save = ... # type: Qt.Key
        Key_Send = ... # type: Qt.Key
        Key_Spell = ... # type: Qt.Key
        Key_SplitScreen = ... # type: Qt.Key
        Key_Support = ... # type: Qt.Key
        Key_TaskPane = ... # type: Qt.Key
        Key_Terminal = ... # type: Qt.Key
        Key_Tools = ... # type: Qt.Key
        Key_Travel = ... # type: Qt.Key
        Key_Video = ... # type: Qt.Key
        Key_Word = ... # type: Qt.Key
        Key_Xfer = ... # type: Qt.Key
        Key_ZoomIn = ... # type: Qt.Key
        Key_ZoomOut = ... # type: Qt.Key
        Key_Away = ... # type: Qt.Key
        Key_Messenger = ... # type: Qt.Key
        Key_WebCam = ... # type: Qt.Key
        Key_MailForward = ... # type: Qt.Key
        Key_Pictures = ... # type: Qt.Key
        Key_Music = ... # type: Qt.Key
        Key_Battery = ... # type: Qt.Key
        Key_Bluetooth = ... # type: Qt.Key
        Key_WLAN = ... # type: Qt.Key
        Key_UWB = ... # type: Qt.Key
        Key_AudioForward = ... # type: Qt.Key
        Key_AudioRepeat = ... # type: Qt.Key
        Key_AudioRandomPlay = ... # type: Qt.Key
        Key_Subtitle = ... # type: Qt.Key
        Key_AudioCycleTrack = ... # type: Qt.Key
        Key_Time = ... # type: Qt.Key
        Key_Hibernate = ... # type: Qt.Key
        Key_View = ... # type: Qt.Key
        Key_TopMenu = ... # type: Qt.Key
        Key_PowerDown = ... # type: Qt.Key
        Key_Suspend = ... # type: Qt.Key
        Key_ContrastAdjust = ... # type: Qt.Key
        Key_MediaPause = ... # type: Qt.Key
        Key_MediaTogglePlayPause = ... # type: Qt.Key
        Key_LaunchG = ... # type: Qt.Key
        Key_LaunchH = ... # type: Qt.Key
        Key_ToggleCallHangup = ... # type: Qt.Key
        Key_VoiceDial = ... # type: Qt.Key
        Key_LastNumberRedial = ... # type: Qt.Key
        Key_Camera = ... # type: Qt.Key
        Key_CameraFocus = ... # type: Qt.Key
        Key_TouchpadToggle = ... # type: Qt.Key
        Key_TouchpadOn = ... # type: Qt.Key
        Key_TouchpadOff = ... # type: Qt.Key
        Key_MicMute = ... # type: Qt.Key
        Key_Red = ... # type: Qt.Key
        Key_Green = ... # type: Qt.Key
        Key_Yellow = ... # type: Qt.Key
        Key_Blue = ... # type: Qt.Key
        Key_ChannelUp = ... # type: Qt.Key
        Key_ChannelDown = ... # type: Qt.Key
        Key_Guide = ... # type: Qt.Key
        Key_Info = ... # type: Qt.Key
        Key_Settings = ... # type: Qt.Key
        Key_Exit = ... # type: Qt.Key
        Key_MicVolumeUp = ... # type: Qt.Key
        Key_MicVolumeDown = ... # type: Qt.Key
        Key_New = ... # type: Qt.Key
        Key_Open = ... # type: Qt.Key
        Key_Find = ... # type: Qt.Key
        Key_Undo = ... # type: Qt.Key
        Key_Redo = ... # type: Qt.Key
        Key_Dead_Stroke = ... # type: Qt.Key
        Key_Dead_Abovecomma = ... # type: Qt.Key
        Key_Dead_Abovereversedcomma = ... # type: Qt.Key
        Key_Dead_Doublegrave = ... # type: Qt.Key
        Key_Dead_Belowring = ... # type: Qt.Key
        Key_Dead_Belowmacron = ... # type: Qt.Key
        Key_Dead_Belowcircumflex = ... # type: Qt.Key
        Key_Dead_Belowtilde = ... # type: Qt.Key
        Key_Dead_Belowbreve = ... # type: Qt.Key
        Key_Dead_Belowdiaeresis = ... # type: Qt.Key
        Key_Dead_Invertedbreve = ... # type: Qt.Key
        Key_Dead_Belowcomma = ... # type: Qt.Key
        Key_Dead_Currency = ... # type: Qt.Key
        Key_Dead_a = ... # type: Qt.Key
        Key_Dead_A = ... # type: Qt.Key
        Key_Dead_e = ... # type: Qt.Key
        Key_Dead_E = ... # type: Qt.Key
        Key_Dead_i = ... # type: Qt.Key
        Key_Dead_I = ... # type: Qt.Key
        Key_Dead_o = ... # type: Qt.Key
        Key_Dead_O = ... # type: Qt.Key
        Key_Dead_u = ... # type: Qt.Key
        Key_Dead_U = ... # type: Qt.Key
        Key_Dead_Small_Schwa = ... # type: Qt.Key
        Key_Dead_Capital_Schwa = ... # type: Qt.Key
        Key_Dead_Greek = ... # type: Qt.Key
        Key_Dead_Lowline = ... # type: Qt.Key
        Key_Dead_Aboveverticalline = ... # type: Qt.Key
        Key_Dead_Belowverticalline = ... # type: Qt.Key
        Key_Dead_Longsolidusoverlay = ... # type: Qt.Key

    class BGMode(int):
        TransparentMode = ... # type: Qt.BGMode
        OpaqueMode = ... # type: Qt.BGMode

    class ImageConversionFlag(int):
        AutoColor = ... # type: Qt.ImageConversionFlag
        ColorOnly = ... # type: Qt.ImageConversionFlag
        MonoOnly = ... # type: Qt.ImageConversionFlag
        ThresholdAlphaDither = ... # type: Qt.ImageConversionFlag
        OrderedAlphaDither = ... # type: Qt.ImageConversionFlag
        DiffuseAlphaDither = ... # type: Qt.ImageConversionFlag
        DiffuseDither = ... # type: Qt.ImageConversionFlag
        OrderedDither = ... # type: Qt.ImageConversionFlag
        ThresholdDither = ... # type: Qt.ImageConversionFlag
        AutoDither = ... # type: Qt.ImageConversionFlag
        PreferDither = ... # type: Qt.ImageConversionFlag
        AvoidDither = ... # type: Qt.ImageConversionFlag
        NoOpaqueDetection = ... # type: Qt.ImageConversionFlag
        NoFormatConversion = ... # type: Qt.ImageConversionFlag

    class WidgetAttribute(int):
        WA_Disabled = ... # type: Qt.WidgetAttribute
        WA_UnderMouse = ... # type: Qt.WidgetAttribute
        WA_MouseTracking = ... # type: Qt.WidgetAttribute
        WA_OpaquePaintEvent = ... # type: Qt.WidgetAttribute
        WA_StaticContents = ... # type: Qt.WidgetAttribute
        WA_LaidOut = ... # type: Qt.WidgetAttribute
        WA_PaintOnScreen = ... # type: Qt.WidgetAttribute
        WA_NoSystemBackground = ... # type: Qt.WidgetAttribute
        WA_UpdatesDisabled = ... # type: Qt.WidgetAttribute
        WA_Mapped = ... # type: Qt.WidgetAttribute
        WA_MacNoClickThrough = ... # type: Qt.WidgetAttribute
        WA_InputMethodEnabled = ... # type: Qt.WidgetAttribute
        WA_WState_Visible = ... # type: Qt.WidgetAttribute
        WA_WState_Hidden = ... # type: Qt.WidgetAttribute
        WA_ForceDisabled = ... # type: Qt.WidgetAttribute
        WA_KeyCompression = ... # type: Qt.WidgetAttribute
        WA_PendingMoveEvent = ... # type: Qt.WidgetAttribute
        WA_PendingResizeEvent = ... # type: Qt.WidgetAttribute
        WA_SetPalette = ... # type: Qt.WidgetAttribute
        WA_SetFont = ... # type: Qt.WidgetAttribute
        WA_SetCursor = ... # type: Qt.WidgetAttribute
        WA_NoChildEventsFromChildren = ... # type: Qt.WidgetAttribute
        WA_WindowModified = ... # type: Qt.WidgetAttribute
        WA_Resized = ... # type: Qt.WidgetAttribute
        WA_Moved = ... # type: Qt.WidgetAttribute
        WA_PendingUpdate = ... # type: Qt.WidgetAttribute
        WA_InvalidSize = ... # type: Qt.WidgetAttribute
        WA_MacMetalStyle = ... # type: Qt.WidgetAttribute
        WA_CustomWhatsThis = ... # type: Qt.WidgetAttribute
        WA_LayoutOnEntireRect = ... # type: Qt.WidgetAttribute
        WA_OutsideWSRange = ... # type: Qt.WidgetAttribute
        WA_GrabbedShortcut = ... # type: Qt.WidgetAttribute
        WA_TransparentForMouseEvents = ... # type: Qt.WidgetAttribute
        WA_PaintUnclipped = ... # type: Qt.WidgetAttribute
        WA_SetWindowIcon = ... # type: Qt.WidgetAttribute
        WA_NoMouseReplay = ... # type: Qt.WidgetAttribute
        WA_DeleteOnClose = ... # type: Qt.WidgetAttribute
        WA_RightToLeft = ... # type: Qt.WidgetAttribute
        WA_SetLayoutDirection = ... # type: Qt.WidgetAttribute
        WA_NoChildEventsForParent = ... # type: Qt.WidgetAttribute
        WA_ForceUpdatesDisabled = ... # type: Qt.WidgetAttribute
        WA_WState_Created = ... # type: Qt.WidgetAttribute
        WA_WState_CompressKeys = ... # type: Qt.WidgetAttribute
        WA_WState_InPaintEvent = ... # type: Qt.WidgetAttribute
        WA_WState_Reparented = ... # type: Qt.WidgetAttribute
        WA_WState_ConfigPending = ... # type: Qt.WidgetAttribute
        WA_WState_Polished = ... # type: Qt.WidgetAttribute
        WA_WState_OwnSizePolicy = ... # type: Qt.WidgetAttribute
        WA_WState_ExplicitShowHide = ... # type: Qt.WidgetAttribute
        WA_MouseNoMask = ... # type: Qt.WidgetAttribute
        WA_GroupLeader = ... # type: Qt.WidgetAttribute
        WA_NoMousePropagation = ... # type: Qt.WidgetAttribute
        WA_Hover = ... # type: Qt.WidgetAttribute
        WA_InputMethodTransparent = ... # type: Qt.WidgetAttribute
        WA_QuitOnClose = ... # type: Qt.WidgetAttribute
        WA_KeyboardFocusChange = ... # type: Qt.WidgetAttribute
        WA_AcceptDrops = ... # type: Qt.WidgetAttribute
        WA_WindowPropagation = ... # type: Qt.WidgetAttribute
        WA_NoX11EventCompression = ... # type: Qt.WidgetAttribute
        WA_TintedBackground = ... # type: Qt.WidgetAttribute
        WA_X11OpenGLOverlay = ... # type: Qt.WidgetAttribute
        WA_AttributeCount = ... # type: Qt.WidgetAttribute
        WA_AlwaysShowToolTips = ... # type: Qt.WidgetAttribute
        WA_MacOpaqueSizeGrip = ... # type: Qt.WidgetAttribute
        WA_SetStyle = ... # type: Qt.WidgetAttribute
        WA_MacBrushedMetal = ... # type: Qt.WidgetAttribute
        WA_SetLocale = ... # type: Qt.WidgetAttribute
        WA_MacShowFocusRect = ... # type: Qt.WidgetAttribute
        WA_MacNormalSize = ... # type: Qt.WidgetAttribute
        WA_MacSmallSize = ... # type: Qt.WidgetAttribute
        WA_MacMiniSize = ... # type: Qt.WidgetAttribute
        WA_LayoutUsesWidgetRect = ... # type: Qt.WidgetAttribute
        WA_StyledBackground = ... # type: Qt.WidgetAttribute
        WA_MSWindowsUseDirect3D = ... # type: Qt.WidgetAttribute
        WA_MacAlwaysShowToolWindow = ... # type: Qt.WidgetAttribute
        WA_StyleSheet = ... # type: Qt.WidgetAttribute
        WA_ShowWithoutActivating = ... # type: Qt.WidgetAttribute
        WA_NativeWindow = ... # type: Qt.WidgetAttribute
        WA_DontCreateNativeAncestors = ... # type: Qt.WidgetAttribute
        WA_MacVariableSize = ... # type: Qt.WidgetAttribute
        WA_DontShowOnScreen = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeDesktop = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeDock = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeToolBar = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeMenu = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeUtility = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeSplash = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeDialog = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeDropDownMenu = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypePopupMenu = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeToolTip = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeNotification = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeCombo = ... # type: Qt.WidgetAttribute
        WA_X11NetWmWindowTypeDND = ... # type: Qt.WidgetAttribute
        WA_MacFrameworkScaled = ... # type: Qt.WidgetAttribute
        WA_TranslucentBackground = ... # type: Qt.WidgetAttribute
        WA_AcceptTouchEvents = ... # type: Qt.WidgetAttribute
        WA_TouchPadAcceptSingleTouchEvents = ... # type: Qt.WidgetAttribute
        WA_X11DoNotAcceptFocus = ... # type: Qt.WidgetAttribute
        WA_MacNoShadow = ... # type: Qt.WidgetAttribute
        WA_AlwaysStackOnTop = ... # type: Qt.WidgetAttribute
        WA_TabletTracking = ... # type: Qt.WidgetAttribute
        WA_ContentsMarginsRespectsSafeArea = ... # type: Qt.WidgetAttribute
        WA_StyleSheetTarget = ... # type: Qt.WidgetAttribute

    class WindowState(int):
        WindowNoState = ... # type: Qt.WindowState
        WindowMinimized = ... # type: Qt.WindowState
        WindowMaximized = ... # type: Qt.WindowState
        WindowFullScreen = ... # type: Qt.WindowState
        WindowActive = ... # type: Qt.WindowState

    class WindowType(int):
        Widget = ... # type: Qt.WindowType
        Window = ... # type: Qt.WindowType
        Dialog = ... # type: Qt.WindowType
        Sheet = ... # type: Qt.WindowType
        Drawer = ... # type: Qt.WindowType
        Popup = ... # type: Qt.WindowType
        Tool = ... # type: Qt.WindowType
        ToolTip = ... # type: Qt.WindowType
        SplashScreen = ... # type: Qt.WindowType
        Desktop = ... # type: Qt.WindowType
        SubWindow = ... # type: Qt.WindowType
        WindowType_Mask = ... # type: Qt.WindowType
        MSWindowsFixedSizeDialogHint = ... # type: Qt.WindowType
        MSWindowsOwnDC = ... # type: Qt.WindowType
        X11BypassWindowManagerHint = ... # type: Qt.WindowType
        FramelessWindowHint = ... # type: Qt.WindowType
        CustomizeWindowHint = ... # type: Qt.WindowType
        WindowTitleHint = ... # type: Qt.WindowType
        WindowSystemMenuHint = ... # type: Qt.WindowType
        WindowMinimizeButtonHint = ... # type: Qt.WindowType
        WindowMaximizeButtonHint = ... # type: Qt.WindowType
        WindowMinMaxButtonsHint = ... # type: Qt.WindowType
        WindowContextHelpButtonHint = ... # type: Qt.WindowType
        WindowShadeButtonHint = ... # type: Qt.WindowType
        WindowStaysOnTopHint = ... # type: Qt.WindowType
        WindowStaysOnBottomHint = ... # type: Qt.WindowType
        WindowCloseButtonHint = ... # type: Qt.WindowType
        MacWindowToolBarButtonHint = ... # type: Qt.WindowType
        BypassGraphicsProxyWidget = ... # type: Qt.WindowType
        WindowTransparentForInput = ... # type: Qt.WindowType
        WindowOverridesSystemGestures = ... # type: Qt.WindowType
        WindowDoesNotAcceptFocus = ... # type: Qt.WindowType
        NoDropShadowWindowHint = ... # type: Qt.WindowType
        WindowFullscreenButtonHint = ... # type: Qt.WindowType
        ForeignWindow = ... # type: Qt.WindowType
        BypassWindowManagerHint = ... # type: Qt.WindowType
        CoverWindow = ... # type: Qt.WindowType
        MaximizeUsingFullscreenGeometryHint = ... # type: Qt.WindowType

    class TextElideMode(int):
        ElideLeft = ... # type: Qt.TextElideMode
        ElideRight = ... # type: Qt.TextElideMode
        ElideMiddle = ... # type: Qt.TextElideMode
        ElideNone = ... # type: Qt.TextElideMode

    class TextFlag(int):
        TextSingleLine = ... # type: Qt.TextFlag
        TextDontClip = ... # type: Qt.TextFlag
        TextExpandTabs = ... # type: Qt.TextFlag
        TextShowMnemonic = ... # type: Qt.TextFlag
        TextWordWrap = ... # type: Qt.TextFlag
        TextWrapAnywhere = ... # type: Qt.TextFlag
        TextDontPrint = ... # type: Qt.TextFlag
        TextIncludeTrailingSpaces = ... # type: Qt.TextFlag
        TextHideMnemonic = ... # type: Qt.TextFlag
        TextJustificationForced = ... # type: Qt.TextFlag

    class AlignmentFlag(int):
        AlignLeft = ... # type: Qt.AlignmentFlag
        AlignLeading = ... # type: Qt.AlignmentFlag
        AlignRight = ... # type: Qt.AlignmentFlag
        AlignTrailing = ... # type: Qt.AlignmentFlag
        AlignHCenter = ... # type: Qt.AlignmentFlag
        AlignJustify = ... # type: Qt.AlignmentFlag
        AlignAbsolute = ... # type: Qt.AlignmentFlag
        AlignHorizontal_Mask = ... # type: Qt.AlignmentFlag
        AlignTop = ... # type: Qt.AlignmentFlag
        AlignBottom = ... # type: Qt.AlignmentFlag
        AlignVCenter = ... # type: Qt.AlignmentFlag
        AlignVertical_Mask = ... # type: Qt.AlignmentFlag
        AlignCenter = ... # type: Qt.AlignmentFlag
        AlignBaseline = ... # type: Qt.AlignmentFlag

    class SortOrder(int):
        AscendingOrder = ... # type: Qt.SortOrder
        DescendingOrder = ... # type: Qt.SortOrder

    class FocusPolicy(int):
        NoFocus = ... # type: Qt.FocusPolicy
        TabFocus = ... # type: Qt.FocusPolicy
        ClickFocus = ... # type: Qt.FocusPolicy
        StrongFocus = ... # type: Qt.FocusPolicy
        WheelFocus = ... # type: Qt.FocusPolicy

    class Orientation(int):
        Horizontal = ... # type: Qt.Orientation
        Vertical = ... # type: Qt.Orientation

    class MouseButton(int):
        NoButton = ... # type: Qt.MouseButton
        AllButtons = ... # type: Qt.MouseButton
        LeftButton = ... # type: Qt.MouseButton
        RightButton = ... # type: Qt.MouseButton
        MidButton = ... # type: Qt.MouseButton
        MiddleButton = ... # type: Qt.MouseButton
        XButton1 = ... # type: Qt.MouseButton
        XButton2 = ... # type: Qt.MouseButton
        BackButton = ... # type: Qt.MouseButton
        ExtraButton1 = ... # type: Qt.MouseButton
        ForwardButton = ... # type: Qt.MouseButton
        ExtraButton2 = ... # type: Qt.MouseButton
        TaskButton = ... # type: Qt.MouseButton
        ExtraButton3 = ... # type: Qt.MouseButton
        ExtraButton4 = ... # type: Qt.MouseButton
        ExtraButton5 = ... # type: Qt.MouseButton
        ExtraButton6 = ... # type: Qt.MouseButton
        ExtraButton7 = ... # type: Qt.MouseButton
        ExtraButton8 = ... # type: Qt.MouseButton
        ExtraButton9 = ... # type: Qt.MouseButton
        ExtraButton10 = ... # type: Qt.MouseButton
        ExtraButton11 = ... # type: Qt.MouseButton
        ExtraButton12 = ... # type: Qt.MouseButton
        ExtraButton13 = ... # type: Qt.MouseButton
        ExtraButton14 = ... # type: Qt.MouseButton
        ExtraButton15 = ... # type: Qt.MouseButton
        ExtraButton16 = ... # type: Qt.MouseButton
        ExtraButton17 = ... # type: Qt.MouseButton
        ExtraButton18 = ... # type: Qt.MouseButton
        ExtraButton19 = ... # type: Qt.MouseButton
        ExtraButton20 = ... # type: Qt.MouseButton
        ExtraButton21 = ... # type: Qt.MouseButton
        ExtraButton22 = ... # type: Qt.MouseButton
        ExtraButton23 = ... # type: Qt.MouseButton
        ExtraButton24 = ... # type: Qt.MouseButton

    class Modifier(int):
        META = ... # type: Qt.Modifier
        SHIFT = ... # type: Qt.Modifier
        CTRL = ... # type: Qt.Modifier
        ALT = ... # type: Qt.Modifier
        MODIFIER_MASK = ... # type: Qt.Modifier
        UNICODE_ACCEL = ... # type: Qt.Modifier

    class KeyboardModifier(int):
        NoModifier = ... # type: Qt.KeyboardModifier
        ShiftModifier = ... # type: Qt.KeyboardModifier
        ControlModifier = ... # type: Qt.KeyboardModifier
        AltModifier = ... # type: Qt.KeyboardModifier
        MetaModifier = ... # type: Qt.KeyboardModifier
        KeypadModifier = ... # type: Qt.KeyboardModifier
        GroupSwitchModifier = ... # type: Qt.KeyboardModifier
        KeyboardModifierMask = ... # type: Qt.KeyboardModifier

    class GlobalColor(int):
        color0 = ... # type: Qt.GlobalColor
        color1 = ... # type: Qt.GlobalColor
        black = ... # type: Qt.GlobalColor
        white = ... # type: Qt.GlobalColor
        darkGray = ... # type: Qt.GlobalColor
        gray = ... # type: Qt.GlobalColor
        lightGray = ... # type: Qt.GlobalColor
        red = ... # type: Qt.GlobalColor
        green = ... # type: Qt.GlobalColor
        blue = ... # type: Qt.GlobalColor
        cyan = ... # type: Qt.GlobalColor
        magenta = ... # type: Qt.GlobalColor
        yellow = ... # type: Qt.GlobalColor
        darkRed = ... # type: Qt.GlobalColor
        darkGreen = ... # type: Qt.GlobalColor
        darkBlue = ... # type: Qt.GlobalColor
        darkCyan = ... # type: Qt.GlobalColor
        darkMagenta = ... # type: Qt.GlobalColor
        darkYellow = ... # type: Qt.GlobalColor
        transparent = ... # type: Qt.GlobalColor

    class KeyboardModifiers(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __xor__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __ior__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __or__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __iand__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __and__(self, f: typing.Union['Qt.KeyboardModifiers', 'Qt.KeyboardModifier']) -> 'Qt.KeyboardModifiers': ...
        def __invert__(self) -> 'Qt.KeyboardModifiers': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MouseButtons(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __xor__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __ior__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __or__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __iand__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __and__(self, f: typing.Union['Qt.MouseButtons', 'Qt.MouseButton']) -> 'Qt.MouseButtons': ...
        def __invert__(self) -> 'Qt.MouseButtons': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Orientations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __xor__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __ior__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __or__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __iand__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __and__(self, f: typing.Union['Qt.Orientations', 'Qt.Orientation']) -> 'Qt.Orientations': ...
        def __invert__(self) -> 'Qt.Orientations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Alignment(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __xor__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __ior__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __or__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __iand__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __and__(self, f: typing.Union['Qt.Alignment', 'Qt.AlignmentFlag']) -> 'Qt.Alignment': ...
        def __invert__(self) -> 'Qt.Alignment': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class WindowFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __xor__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __ior__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __or__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __iand__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __and__(self, f: typing.Union['Qt.WindowFlags', 'Qt.WindowType']) -> 'Qt.WindowFlags': ...
        def __invert__(self) -> 'Qt.WindowFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class WindowStates(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __xor__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __ior__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __or__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __iand__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __and__(self, f: typing.Union['Qt.WindowStates', 'Qt.WindowState']) -> 'Qt.WindowStates': ...
        def __invert__(self) -> 'Qt.WindowStates': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ImageConversionFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __xor__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __ior__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __or__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __iand__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __and__(self, f: typing.Union['Qt.ImageConversionFlags', 'Qt.ImageConversionFlag']) -> 'Qt.ImageConversionFlags': ...
        def __invert__(self) -> 'Qt.ImageConversionFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class DockWidgetAreas(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __xor__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __ior__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __or__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __iand__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __and__(self, f: typing.Union['Qt.DockWidgetAreas', 'Qt.DockWidgetArea']) -> 'Qt.DockWidgetAreas': ...
        def __invert__(self) -> 'Qt.DockWidgetAreas': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ToolBarAreas(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __xor__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __ior__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __or__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __iand__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __and__(self, f: typing.Union['Qt.ToolBarAreas', 'Qt.ToolBarArea']) -> 'Qt.ToolBarAreas': ...
        def __invert__(self) -> 'Qt.ToolBarAreas': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class InputMethodQueries(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __xor__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __ior__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __or__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __iand__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __and__(self, f: typing.Union['Qt.InputMethodQueries', 'Qt.InputMethodQuery']) -> 'Qt.InputMethodQueries': ...
        def __invert__(self) -> 'Qt.InputMethodQueries': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class DropActions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __xor__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __ior__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __or__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __iand__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __and__(self, f: typing.Union['Qt.DropActions', 'Qt.DropAction']) -> 'Qt.DropActions': ...
        def __invert__(self) -> 'Qt.DropActions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ItemFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __xor__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __ior__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __or__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __iand__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __and__(self, f: typing.Union['Qt.ItemFlags', 'Qt.ItemFlag']) -> 'Qt.ItemFlags': ...
        def __invert__(self) -> 'Qt.ItemFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MatchFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __xor__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __ior__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __or__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __iand__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __and__(self, f: typing.Union['Qt.MatchFlags', 'Qt.MatchFlag']) -> 'Qt.MatchFlags': ...
        def __invert__(self) -> 'Qt.MatchFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class TextInteractionFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __xor__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __ior__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __or__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __iand__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __and__(self, f: typing.Union['Qt.TextInteractionFlags', 'Qt.TextInteractionFlag']) -> 'Qt.TextInteractionFlags': ...
        def __invert__(self) -> 'Qt.TextInteractionFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class InputMethodHints(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __xor__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __ior__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __or__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __iand__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __and__(self, f: typing.Union['Qt.InputMethodHints', 'Qt.InputMethodHint']) -> 'Qt.InputMethodHints': ...
        def __invert__(self) -> 'Qt.InputMethodHints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class TouchPointStates(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __xor__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __ior__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __or__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __iand__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __and__(self, f: typing.Union['Qt.TouchPointStates', 'Qt.TouchPointState']) -> 'Qt.TouchPointStates': ...
        def __invert__(self) -> 'Qt.TouchPointStates': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class GestureFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __xor__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __ior__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __or__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __iand__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __and__(self, f: typing.Union['Qt.GestureFlags', 'Qt.GestureFlag']) -> 'Qt.GestureFlags': ...
        def __invert__(self) -> 'Qt.GestureFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ScreenOrientations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __xor__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __ior__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __or__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __iand__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __and__(self, f: typing.Union['Qt.ScreenOrientations', 'Qt.ScreenOrientation']) -> 'Qt.ScreenOrientations': ...
        def __invert__(self) -> 'Qt.ScreenOrientations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FindChildOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __xor__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __ior__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __or__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __iand__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __and__(self, f: typing.Union['Qt.FindChildOptions', 'Qt.FindChildOption']) -> 'Qt.FindChildOptions': ...
        def __invert__(self) -> 'Qt.FindChildOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ApplicationStates(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __xor__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __ior__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __or__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __iand__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __and__(self, f: typing.Union['Qt.ApplicationStates', 'Qt.ApplicationState']) -> 'Qt.ApplicationStates': ...
        def __invert__(self) -> 'Qt.ApplicationStates': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Edges(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __xor__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __ior__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __or__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __iand__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __and__(self, f: typing.Union['Qt.Edges', 'Qt.Edge']) -> 'Qt.Edges': ...
        def __invert__(self) -> 'Qt.Edges': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MouseEventFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __xor__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __ior__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __or__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __iand__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __and__(self, f: typing.Union['Qt.MouseEventFlags', 'Qt.MouseEventFlag']) -> 'Qt.MouseEventFlags': ...
        def __invert__(self) -> 'Qt.MouseEventFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...


class QObject(PyQt5.sip.wrapper):

    staticMetaObject = ... # type: 'QMetaObject'

    def __init__(self, parent: typing.Optional['QObject'] = ...) -> None: ...

    @typing.overload
    @staticmethod
    def disconnect(a0: 'QMetaObject.Connection') -> bool: ...
    @typing.overload
    def disconnect(self) -> None: ...
    def isSignalConnected(self, signal: 'QMetaMethod') -> bool: ...
    def senderSignalIndex(self) -> int: ...
    def disconnectNotify(self, signal: 'QMetaMethod') -> None: ...
    def connectNotify(self, signal: 'QMetaMethod') -> None: ...
    def customEvent(self, a0: typing.Optional['QEvent']) -> None: ...
    def childEvent(self, a0: typing.Optional['QChildEvent']) -> None: ...
    def timerEvent(self, a0: typing.Optional['QTimerEvent']) -> None: ...
    def receivers(self, signal: PYQT_SIGNAL) -> int: ...
    def sender(self) -> typing.Optional['QObject']: ...
    def deleteLater(self) -> None: ...
    def inherits(self, classname: typing.Optional[str]) -> bool: ...
    def parent(self) -> typing.Optional['QObject']: ...
    objectNameChanged: typing.ClassVar[pyqtSignal]
    destroyed: typing.ClassVar[pyqtSignal]
    def property(self, name: typing.Optional[str]) -> typing.Any: ...
    def setProperty(self, name: typing.Optional[str], value: typing.Any) -> bool: ...
    def dynamicPropertyNames(self) -> typing.List['QByteArray']: ...
    def dumpObjectTree(self) -> None: ...
    def dumpObjectInfo(self) -> None: ...
    def removeEventFilter(self, a0: typing.Optional['QObject']) -> None: ...
    def installEventFilter(self, a0: typing.Optional['QObject']) -> None: ...
    def setParent(self, a0: typing.Optional['QObject']) -> None: ...
    def children(self) -> typing.List['QObject']: ...
    def killTimer(self, id: int) -> None: ...
    def startTimer(self, interval: int, timerType: Qt.TimerType = ...) -> int: ...
    def moveToThread(self, thread: typing.Optional['QThread']) -> None: ...
    def thread(self) -> typing.Optional['QThread']: ...
    def blockSignals(self, b: bool) -> bool: ...
    def signalsBlocked(self) -> bool: ...
    def isWindowType(self) -> bool: ...
    def isWidgetType(self) -> bool: ...
    def setObjectName(self, name: typing.Optional[str]) -> None: ...
    def objectName(self) -> str: ...
    @typing.overload
    def findChildren(self, type: typing.Type[QObjectT], name: typing.Optional[str] = ..., options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChildren(self, types: typing.Tuple[typing.Type[QObjectT], ...], name: typing.Optional[str] = ..., options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChildren(self, type: typing.Type[QObjectT], regExp: 'QRegExp', options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChildren(self, types: typing.Tuple[typing.Type[QObjectT], ...], regExp: 'QRegExp', options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChildren(self, type: typing.Type[QObjectT], re: 'QRegularExpression', options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChildren(self, types: typing.Tuple[typing.Type[QObjectT], ...], re: 'QRegularExpression', options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> typing.List[QObjectT]: ...
    @typing.overload
    def findChild(self, type: typing.Type[QObjectT], name: typing.Optional[str] = ..., options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> QObjectT: ...
    @typing.overload
    def findChild(self, types: typing.Tuple[typing.Type[QObjectT], ...], name: typing.Optional[str] = ..., options: typing.Union[Qt.FindChildOptions, Qt.FindChildOption] = ...) -> QObjectT: ...
    def tr(self, sourceText: typing.Optional[str], disambiguation: typing.Optional[str] = ..., n: int = ...) -> str: ...
    def eventFilter(self, a0: typing.Optional['QObject'], a1: typing.Optional['QEvent']) -> bool: ...
    def event(self, a0: typing.Optional['QEvent']) -> bool: ...
    def pyqtConfigure(self, a0: typing.Any) -> None: ...
    def metaObject(self) -> typing.Optional['QMetaObject']: ...


class QAbstractAnimation(QObject):

    class DeletionPolicy(int):
        KeepWhenStopped = ... # type: QAbstractAnimation.DeletionPolicy
        DeleteWhenStopped = ... # type: QAbstractAnimation.DeletionPolicy

    class State(int):
        Stopped = ... # type: QAbstractAnimation.State
        Paused = ... # type: QAbstractAnimation.State
        Running = ... # type: QAbstractAnimation.State

    class Direction(int):
        Forward = ... # type: QAbstractAnimation.Direction
        Backward = ... # type: QAbstractAnimation.Direction

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def updateDirection(self, direction: 'QAbstractAnimation.Direction') -> None: ...
    def updateState(self, newState: 'QAbstractAnimation.State', oldState: 'QAbstractAnimation.State') -> None: ...
    def updateCurrentTime(self, currentTime: int) -> None: ...
    def event(self, event: typing.Optional['QEvent']) -> bool: ...
    def setCurrentTime(self, msecs: int) -> None: ...
    def stop(self) -> None: ...
    def setPaused(self, a0: bool) -> None: ...
    def resume(self) -> None: ...
    def pause(self) -> None: ...
    def start(self, policy: 'QAbstractAnimation.DeletionPolicy' = ...) -> None: ...
    directionChanged: typing.ClassVar[pyqtSignal]
    currentLoopChanged: typing.ClassVar[pyqtSignal]
    stateChanged: typing.ClassVar[pyqtSignal]
    finished: typing.ClassVar[pyqtSignal]
    def totalDuration(self) -> int: ...
    def duration(self) -> int: ...
    def currentLoop(self) -> int: ...
    def setLoopCount(self, loopCount: int) -> None: ...
    def loopCount(self) -> int: ...
    def currentLoopTime(self) -> int: ...
    def currentTime(self) -> int: ...
    def setDirection(self, direction: 'QAbstractAnimation.Direction') -> None: ...
    def direction(self) -> 'QAbstractAnimation.Direction': ...
    def group(self) -> typing.Optional['QAnimationGroup']: ...
    def state(self) -> 'QAbstractAnimation.State': ...


class QAbstractEventDispatcher(QObject):

    class TimerInfo(PyQt5.sipsimplewrapper):

        interval = ... # type: int
        timerId = ... # type: int
        timerType = ... # type: Qt.TimerType

        @typing.overload
        def __init__(self, id: int, i: int, t: Qt.TimerType) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QAbstractEventDispatcher.TimerInfo') -> None: ...

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    awake: typing.ClassVar[pyqtSignal]
    aboutToBlock: typing.ClassVar[pyqtSignal]
    def filterNativeEvent(self, eventType: typing.Union['QByteArray', bytes, bytearray], message: typing.Optional[PyQt5.sip.voidptr]) -> typing.Tuple[bool, typing.Optional[int]]: ...
    def unregisterEventNotifier(self, notifier: typing.Optional['QWinEventNotifier']) -> None: ...
    def registerEventNotifier(self, notifier: typing.Optional['QWinEventNotifier']) -> bool: ...
    def removeNativeEventFilter(self, filterObj: typing.Optional['QAbstractNativeEventFilter']) -> None: ...
    def installNativeEventFilter(self, filterObj: typing.Optional['QAbstractNativeEventFilter']) -> None: ...
    def remainingTime(self, timerId: int) -> int: ...
    def closingDown(self) -> None: ...
    def startingUp(self) -> None: ...
    def flush(self) -> None: ...
    def interrupt(self) -> None: ...
    def wakeUp(self) -> None: ...
    def registeredTimers(self, object: typing.Optional[QObject]) -> typing.List['QAbstractEventDispatcher.TimerInfo']: ...
    def unregisterTimers(self, object: typing.Optional[QObject]) -> bool: ...
    def unregisterTimer(self, timerId: int) -> bool: ...
    @typing.overload
    def registerTimer(self, interval: int, timerType: Qt.TimerType, object: typing.Optional[QObject]) -> int: ...
    @typing.overload
    def registerTimer(self, timerId: int, interval: int, timerType: Qt.TimerType, object: typing.Optional[QObject]) -> None: ...
    def unregisterSocketNotifier(self, notifier: typing.Optional['QSocketNotifier']) -> None: ...
    def registerSocketNotifier(self, notifier: typing.Optional['QSocketNotifier']) -> None: ...
    def hasPendingEvents(self) -> bool: ...
    def processEvents(self, flags: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> bool: ...
    @staticmethod
    def instance(thread: typing.Optional['QThread'] = ...) -> typing.Optional['QAbstractEventDispatcher']: ...


class QModelIndex(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QModelIndex') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPersistentModelIndex') -> None: ...

    def __ge__(self, other: 'QModelIndex') -> bool: ...
    def __hash__(self) -> int: ...
    def __ne__(self, other: object): ...
    def __lt__(self, other: 'QModelIndex') -> bool: ...
    def __eq__(self, other: object): ...
    def siblingAtRow(self, row: int) -> 'QModelIndex': ...
    def siblingAtColumn(self, column: int) -> 'QModelIndex': ...
    def sibling(self, arow: int, acolumn: int) -> 'QModelIndex': ...
    def parent(self) -> 'QModelIndex': ...
    def isValid(self) -> bool: ...
    def model(self) -> typing.Optional['QAbstractItemModel']: ...
    def internalId(self) -> int: ...
    def internalPointer(self) -> typing.Any: ...
    def flags(self) -> Qt.ItemFlags: ...
    def data(self, role: int = ...) -> typing.Any: ...
    def column(self) -> int: ...
    def row(self) -> int: ...
    def child(self, arow: int, acolumn: int) -> 'QModelIndex': ...


class QPersistentModelIndex(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, index: QModelIndex) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPersistentModelIndex') -> None: ...

    def __ge__(self, other: 'QPersistentModelIndex') -> bool: ...
    def __hash__(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __lt__(self, other: 'QPersistentModelIndex') -> bool: ...
    def swap(self, other: 'QPersistentModelIndex') -> None: ...
    def isValid(self) -> bool: ...
    def model(self) -> typing.Optional['QAbstractItemModel']: ...
    def child(self, row: int, column: int) -> QModelIndex: ...
    def sibling(self, row: int, column: int) -> QModelIndex: ...
    def parent(self) -> QModelIndex: ...
    def flags(self) -> Qt.ItemFlags: ...
    def data(self, role: int = ...) -> typing.Any: ...
    def column(self) -> int: ...
    def row(self) -> int: ...


class QAbstractItemModel(QObject):

    class CheckIndexOption(int):
        NoOption = ... # type: QAbstractItemModel.CheckIndexOption
        IndexIsValid = ... # type: QAbstractItemModel.CheckIndexOption
        DoNotUseParent = ... # type: QAbstractItemModel.CheckIndexOption
        ParentIsInvalid = ... # type: QAbstractItemModel.CheckIndexOption

    class LayoutChangeHint(int):
        NoLayoutChangeHint = ... # type: QAbstractItemModel.LayoutChangeHint
        VerticalSortHint = ... # type: QAbstractItemModel.LayoutChangeHint
        HorizontalSortHint = ... # type: QAbstractItemModel.LayoutChangeHint

    class CheckIndexOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __xor__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __ior__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __or__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __iand__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __and__(self, f: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption']) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __invert__(self) -> 'QAbstractItemModel.CheckIndexOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def checkIndex(self, index: QModelIndex, options: typing.Union['QAbstractItemModel.CheckIndexOptions', 'QAbstractItemModel.CheckIndexOption'] = ...) -> bool: ...
    def moveColumn(self, sourceParent: QModelIndex, sourceColumn: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def moveRow(self, sourceParent: QModelIndex, sourceRow: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def moveColumns(self, sourceParent: QModelIndex, sourceColumn: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def moveRows(self, sourceParent: QModelIndex, sourceRow: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def canDropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def resetInternalData(self) -> None: ...
    def endResetModel(self) -> None: ...
    def beginResetModel(self) -> None: ...
    def endMoveColumns(self) -> None: ...
    def beginMoveColumns(self, sourceParent: QModelIndex, sourceFirst: int, sourceLast: int, destinationParent: QModelIndex, destinationColumn: int) -> bool: ...
    def endMoveRows(self) -> None: ...
    def beginMoveRows(self, sourceParent: QModelIndex, sourceFirst: int, sourceLast: int, destinationParent: QModelIndex, destinationRow: int) -> bool: ...
    columnsMoved: typing.ClassVar[pyqtSignal]
    columnsAboutToBeMoved: typing.ClassVar[pyqtSignal]
    rowsMoved: typing.ClassVar[pyqtSignal]
    rowsAboutToBeMoved: typing.ClassVar[pyqtSignal]
    def createIndex(self, row: int, column: int, object: typing.Any = ...) -> QModelIndex: ...
    def roleNames(self) -> typing.Dict[int, 'QByteArray']: ...
    def supportedDragActions(self) -> Qt.DropActions: ...
    def removeColumn(self, column: int, parent: QModelIndex = ...) -> bool: ...
    def removeRow(self, row: int, parent: QModelIndex = ...) -> bool: ...
    def insertColumn(self, column: int, parent: QModelIndex = ...) -> bool: ...
    def insertRow(self, row: int, parent: QModelIndex = ...) -> bool: ...
    def changePersistentIndexList(self, from_: typing.Iterable[QModelIndex], to: typing.Iterable[QModelIndex]) -> None: ...
    def changePersistentIndex(self, from_: QModelIndex, to: QModelIndex) -> None: ...
    def persistentIndexList(self) -> typing.List[QModelIndex]: ...
    def endRemoveColumns(self) -> None: ...
    def beginRemoveColumns(self, parent: QModelIndex, first: int, last: int) -> None: ...
    def endInsertColumns(self) -> None: ...
    def beginInsertColumns(self, parent: QModelIndex, first: int, last: int) -> None: ...
    def endRemoveRows(self) -> None: ...
    def beginRemoveRows(self, parent: QModelIndex, first: int, last: int) -> None: ...
    def endInsertRows(self) -> None: ...
    def beginInsertRows(self, parent: QModelIndex, first: int, last: int) -> None: ...
    def decodeData(self, row: int, column: int, parent: QModelIndex, stream: 'QDataStream') -> bool: ...
    def encodeData(self, indexes: typing.Iterable[QModelIndex], stream: 'QDataStream') -> None: ...
    def revert(self) -> None: ...
    def submit(self) -> bool: ...
    modelReset: typing.ClassVar[pyqtSignal]
    modelAboutToBeReset: typing.ClassVar[pyqtSignal]
    columnsRemoved: typing.ClassVar[pyqtSignal]
    columnsAboutToBeRemoved: typing.ClassVar[pyqtSignal]
    columnsInserted: typing.ClassVar[pyqtSignal]
    columnsAboutToBeInserted: typing.ClassVar[pyqtSignal]
    rowsRemoved: typing.ClassVar[pyqtSignal]
    rowsAboutToBeRemoved: typing.ClassVar[pyqtSignal]
    rowsInserted: typing.ClassVar[pyqtSignal]
    rowsAboutToBeInserted: typing.ClassVar[pyqtSignal]
    layoutChanged: typing.ClassVar[pyqtSignal]
    layoutAboutToBeChanged: typing.ClassVar[pyqtSignal]
    headerDataChanged: typing.ClassVar[pyqtSignal]
    dataChanged: typing.ClassVar[pyqtSignal]
    def span(self, index: QModelIndex) -> 'QSize': ...
    def match(self, start: QModelIndex, role: int, value: typing.Any, hits: int = ..., flags: typing.Union[Qt.MatchFlags, Qt.MatchFlag] = ...) -> typing.List[QModelIndex]: ...
    def buddy(self, index: QModelIndex) -> QModelIndex: ...
    def sort(self, column: int, order: Qt.SortOrder = ...) -> None: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def canFetchMore(self, parent: QModelIndex) -> bool: ...
    def fetchMore(self, parent: QModelIndex) -> None: ...
    def removeColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def removeRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def supportedDropActions(self) -> Qt.DropActions: ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def mimeData(self, indexes: typing.Iterable[QModelIndex]) -> typing.Optional['QMimeData']: ...
    def mimeTypes(self) -> typing.List[str]: ...
    def setItemData(self, index: QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def itemData(self, index: QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def setHeaderData(self, section: int, orientation: Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QModelIndex, role: int = ...) -> typing.Any: ...
    def hasChildren(self, parent: QModelIndex = ...) -> bool: ...
    def columnCount(self, parent: QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...
    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    @typing.overload
    def parent(self, child: QModelIndex) -> QModelIndex: ...
    @typing.overload
    def parent(self) -> typing.Optional[QObject]: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...
    def hasIndex(self, row: int, column: int, parent: QModelIndex = ...) -> bool: ...


class QAbstractTableModel(QAbstractItemModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def parent(self) -> typing.Optional[QObject]: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...


class QAbstractListModel(QAbstractItemModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def parent(self) -> typing.Optional[QObject]: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def index(self, row: int, column: int = ..., parent: QModelIndex = ...) -> QModelIndex: ...


class QAbstractNativeEventFilter(PyQt5.sipsimplewrapper):

    def __init__(self) -> None: ...

    def nativeEventFilter(self, eventType: typing.Union['QByteArray', bytes, bytearray], message: typing.Optional[PyQt5.sip.voidptr]) -> typing.Tuple[bool, typing.Optional[int]]: ...


class QAbstractProxyModel(QAbstractItemModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def supportedDragActions(self) -> Qt.DropActions: ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def canDropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    sourceModelChanged: typing.ClassVar[pyqtSignal]
    def resetInternalData(self) -> None: ...
    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def supportedDropActions(self) -> Qt.DropActions: ...
    def mimeTypes(self) -> typing.List[str]: ...
    def mimeData(self, indexes: typing.Iterable[QModelIndex]) -> typing.Optional['QMimeData']: ...
    def hasChildren(self, parent: QModelIndex = ...) -> bool: ...
    def span(self, index: QModelIndex) -> 'QSize': ...
    def sort(self, column: int, order: Qt.SortOrder = ...) -> None: ...
    def fetchMore(self, parent: QModelIndex) -> None: ...
    def canFetchMore(self, parent: QModelIndex) -> bool: ...
    def buddy(self, index: QModelIndex) -> QModelIndex: ...
    def setItemData(self, index: QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def itemData(self, index: QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def setHeaderData(self, section: int, orientation: Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, proxyIndex: QModelIndex, role: int = ...) -> typing.Any: ...
    def revert(self) -> None: ...
    def submit(self) -> bool: ...
    def mapSelectionFromSource(self, selection: 'QItemSelection') -> 'QItemSelection': ...
    def mapSelectionToSource(self, selection: 'QItemSelection') -> 'QItemSelection': ...
    def mapFromSource(self, sourceIndex: QModelIndex) -> QModelIndex: ...
    def mapToSource(self, proxyIndex: QModelIndex) -> QModelIndex: ...
    def sourceModel(self) -> typing.Optional[QAbstractItemModel]: ...
    def setSourceModel(self, sourceModel: typing.Optional[QAbstractItemModel]) -> None: ...


class QAbstractState(QObject):

    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional['QEvent']) -> bool: ...
    def onExit(self, event: typing.Optional['QEvent']) -> None: ...
    def onEntry(self, event: typing.Optional['QEvent']) -> None: ...
    exited: typing.ClassVar[pyqtSignal]
    entered: typing.ClassVar[pyqtSignal]
    activeChanged: typing.ClassVar[pyqtSignal]
    def active(self) -> bool: ...
    def machine(self) -> typing.Optional['QStateMachine']: ...
    def parentState(self) -> typing.Optional['QState']: ...


class QAbstractTransition(QObject):

    class TransitionType(int):
        ExternalTransition = ... # type: QAbstractTransition.TransitionType
        InternalTransition = ... # type: QAbstractTransition.TransitionType

    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def setTransitionType(self, type: 'QAbstractTransition.TransitionType') -> None: ...
    def transitionType(self) -> 'QAbstractTransition.TransitionType': ...
    def event(self, e: typing.Optional['QEvent']) -> bool: ...
    def onTransition(self, event: typing.Optional['QEvent']) -> None: ...
    def eventTest(self, event: typing.Optional['QEvent']) -> bool: ...
    targetStatesChanged: typing.ClassVar[pyqtSignal]
    targetStateChanged: typing.ClassVar[pyqtSignal]
    triggered: typing.ClassVar[pyqtSignal]
    def animations(self) -> typing.List[QAbstractAnimation]: ...
    def removeAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def addAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def machine(self) -> typing.Optional['QStateMachine']: ...
    def setTargetStates(self, targets: typing.Iterable[QAbstractState]) -> None: ...
    def targetStates(self) -> typing.List[QAbstractState]: ...
    def setTargetState(self, target: typing.Optional[QAbstractState]) -> None: ...
    def targetState(self) -> typing.Optional[QAbstractState]: ...
    def sourceState(self) -> typing.Optional['QState']: ...


class QAnimationGroup(QAbstractAnimation):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, event: typing.Optional['QEvent']) -> bool: ...
    def clear(self) -> None: ...
    def takeAnimation(self, index: int) -> typing.Optional[QAbstractAnimation]: ...
    def removeAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def insertAnimation(self, index: int, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def addAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def indexOfAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> int: ...
    def animationCount(self) -> int: ...
    def animationAt(self, index: int) -> typing.Optional[QAbstractAnimation]: ...


class QBasicTimer(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QBasicTimer') -> None: ...

    def swap(self, other: 'QBasicTimer') -> None: ...
    def stop(self) -> None: ...
    @typing.overload
    def start(self, msec: int, timerType: Qt.TimerType, obj: typing.Optional[QObject]) -> None: ...
    @typing.overload
    def start(self, msec: int, obj: typing.Optional[QObject]) -> None: ...
    def timerId(self) -> int: ...
    def isActive(self) -> bool: ...


class QBitArray(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: int, value: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBitArray') -> None: ...

    def __or__(self, a0: 'QBitArray') -> 'QBitArray': ...
    def __and__(self, a0: 'QBitArray') -> 'QBitArray': ...
    def __xor__(self, a0: 'QBitArray') -> 'QBitArray': ...
    @staticmethod
    def fromBits(data: typing.Optional[bytes], len: int) -> 'QBitArray': ...
    def bits(self) -> bytes: ...
    def swap(self, other: 'QBitArray') -> None: ...
    def __hash__(self) -> int: ...
    def at(self, i: int) -> bool: ...
    def __getitem__(self, i: int) -> bool: ...
    def toggleBit(self, i: int) -> bool: ...
    def clearBit(self, i: int) -> None: ...
    @typing.overload
    def setBit(self, i: int) -> None: ...
    @typing.overload
    def setBit(self, i: int, val: bool) -> None: ...
    def testBit(self, i: int) -> bool: ...
    def truncate(self, pos: int) -> None: ...
    @typing.overload
    def fill(self, val: bool, first: int, last: int) -> None: ...
    @typing.overload
    def fill(self, value: bool, size: int = ...) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __invert__(self) -> 'QBitArray': ...
    def __ixor__(self, a0: 'QBitArray') -> 'QBitArray': ...
    def __ior__(self, a0: 'QBitArray') -> 'QBitArray': ...
    def __iand__(self, a0: 'QBitArray') -> 'QBitArray': ...
    def clear(self) -> None: ...
    def isDetached(self) -> bool: ...
    def detach(self) -> None: ...
    def resize(self, size: int) -> None: ...
    def isNull(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    @typing.overload
    def count(self, on: bool) -> int: ...
    def size(self) -> int: ...


class QIODevice(QObject):

    class OpenModeFlag(int):
        NotOpen = ... # type: QIODevice.OpenModeFlag
        ReadOnly = ... # type: QIODevice.OpenModeFlag
        WriteOnly = ... # type: QIODevice.OpenModeFlag
        ReadWrite = ... # type: QIODevice.OpenModeFlag
        Append = ... # type: QIODevice.OpenModeFlag
        Truncate = ... # type: QIODevice.OpenModeFlag
        Text = ... # type: QIODevice.OpenModeFlag
        Unbuffered = ... # type: QIODevice.OpenModeFlag
        NewOnly = ... # type: QIODevice.OpenModeFlag
        ExistingOnly = ... # type: QIODevice.OpenModeFlag

    class OpenMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __xor__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __ior__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __or__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __iand__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __and__(self, f: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> 'QIODevice.OpenMode': ...
        def __invert__(self) -> 'QIODevice.OpenMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QObject]) -> None: ...

    def skip(self, maxSize: int) -> int: ...
    channelBytesWritten: typing.ClassVar[pyqtSignal]
    channelReadyRead: typing.ClassVar[pyqtSignal]
    def isTransactionStarted(self) -> bool: ...
    def rollbackTransaction(self) -> None: ...
    def commitTransaction(self) -> None: ...
    def startTransaction(self) -> None: ...
    def setCurrentWriteChannel(self, channel: int) -> None: ...
    def currentWriteChannel(self) -> int: ...
    def setCurrentReadChannel(self, channel: int) -> None: ...
    def currentReadChannel(self) -> int: ...
    def writeChannelCount(self) -> int: ...
    def readChannelCount(self) -> int: ...
    def setErrorString(self, errorString: typing.Optional[str]) -> None: ...
    def setOpenMode(self, openMode: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def readData(self, maxlen: int) -> bytes: ...
    readChannelFinished: typing.ClassVar[pyqtSignal]
    aboutToClose: typing.ClassVar[pyqtSignal]
    bytesWritten: typing.ClassVar[pyqtSignal]
    readyRead: typing.ClassVar[pyqtSignal]
    def errorString(self) -> str: ...
    def getChar(self) -> typing.Tuple[bool, typing.Optional[bytes]]: ...
    def putChar(self, c: str) -> bool: ...
    def ungetChar(self, c: str) -> None: ...
    def waitForBytesWritten(self, msecs: int) -> bool: ...
    def waitForReadyRead(self, msecs: int) -> bool: ...
    def write(self, data: typing.Union['QByteArray', bytes, bytearray]) -> int: ...
    def peek(self, maxlen: int) -> 'QByteArray': ...
    def canReadLine(self) -> bool: ...
    def readLine(self, maxlen: int = ...) -> bytes: ...
    def readAll(self) -> 'QByteArray': ...
    def read(self, maxlen: int) -> bytes: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def reset(self) -> bool: ...
    def atEnd(self) -> bool: ...
    def seek(self, pos: int) -> bool: ...
    def size(self) -> int: ...
    def pos(self) -> int: ...
    def close(self) -> None: ...
    def open(self, mode: typing.Union['QIODevice.OpenMode', 'QIODevice.OpenModeFlag']) -> bool: ...
    def isSequential(self) -> bool: ...
    def isWritable(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def isOpen(self) -> bool: ...
    def isTextModeEnabled(self) -> bool: ...
    def setTextModeEnabled(self, enabled: bool) -> None: ...
    def openMode(self) -> 'QIODevice.OpenMode': ...


class QBuffer(QIODevice):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, byteArray: typing.Optional['QByteArray'], parent: typing.Optional[QObject] = ...) -> None: ...

    def disconnectNotify(self, a0: 'QMetaMethod') -> None: ...
    def connectNotify(self, a0: 'QMetaMethod') -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    def canReadLine(self) -> bool: ...
    def atEnd(self) -> bool: ...
    def seek(self, off: int) -> bool: ...
    def pos(self) -> int: ...
    def size(self) -> int: ...
    def close(self) -> None: ...
    def open(self, openMode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag]) -> bool: ...
    @typing.overload
    def setData(self, data: typing.Union['QByteArray', bytes, bytearray]) -> None: ...
    @typing.overload
    def setData(self, adata: typing.Optional[PyQt5.sip.array[bytes]]) -> None: ...
    def setBuffer(self, a: typing.Optional['QByteArray']) -> None: ...
    def data(self) -> 'QByteArray': ...
    def buffer(self) -> 'QByteArray': ...


class QByteArray(PyQt5.sipsimplewrapper):

    class Base64DecodingStatus(int):
        Ok = ... # type: QByteArray.Base64DecodingStatus
        IllegalInputLength = ... # type: QByteArray.Base64DecodingStatus
        IllegalCharacter = ... # type: QByteArray.Base64DecodingStatus
        IllegalPadding = ... # type: QByteArray.Base64DecodingStatus

    class Base64Option(int):
        Base64Encoding = ... # type: QByteArray.Base64Option
        Base64UrlEncoding = ... # type: QByteArray.Base64Option
        KeepTrailingEquals = ... # type: QByteArray.Base64Option
        OmitTrailingEquals = ... # type: QByteArray.Base64Option
        IgnoreBase64DecodingErrors = ... # type: QByteArray.Base64Option
        AbortOnBase64DecodingErrors = ... # type: QByteArray.Base64Option

    class Base64Options(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __xor__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __ior__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __or__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __iand__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __and__(self, f: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray.Base64Options': ...
        def __invert__(self) -> 'QByteArray.Base64Options': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FromBase64Result(PyQt5.sipsimplewrapper):

        decoded = ... # type: typing.Union['QByteArray', bytes, bytearray]
        decodingStatus = ... # type: 'QByteArray.Base64DecodingStatus'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QByteArray.FromBase64Result') -> None: ...

        def __eq__(self, other: object): ...
        def __ne__(self, other: object): ...
        def __hash__(self) -> int: ...
        def __int__(self) -> bool: ...
        def swap(self, other: 'QByteArray.FromBase64Result') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: int, c: str) -> None: ...
    @typing.overload
    def __init__(self, a: typing.Union['QByteArray', bytes, bytearray]) -> None: ...

    def __add__(self, a2: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @staticmethod
    def fromBase64Encoding(base64: typing.Union['QByteArray', bytes, bytearray], options: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option'] = ...) -> 'QByteArray.FromBase64Result': ...
    def isLower(self) -> bool: ...
    def isUpper(self) -> bool: ...
    def compare(self, a: typing.Union['QByteArray', bytes, bytearray], cs: Qt.CaseSensitivity = ...) -> int: ...
    def chopped(self, len: int) -> 'QByteArray': ...
    def swap(self, other: 'QByteArray') -> None: ...
    def repeated(self, times: int) -> 'QByteArray': ...
    @staticmethod
    def fromPercentEncoding(input: typing.Union['QByteArray', bytes, bytearray], percent: str = ...) -> 'QByteArray': ...
    def toPercentEncoding(self, exclude: typing.Union['QByteArray', bytes, bytearray] = ..., include: typing.Union['QByteArray', bytes, bytearray] = ..., percent: str = ...) -> 'QByteArray': ...
    @typing.overload
    def toHex(self) -> 'QByteArray': ...
    @typing.overload
    def toHex(self, separator: str) -> 'QByteArray': ...
    def contains(self, a: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    def push_front(self, a: typing.Union['QByteArray', bytes, bytearray]) -> None: ...
    def push_back(self, a: typing.Union['QByteArray', bytes, bytearray]) -> None: ...
    def squeeze(self) -> None: ...
    def reserve(self, size: int) -> None: ...
    def capacity(self) -> int: ...
    def data(self) -> bytes: ...
    def isEmpty(self) -> bool: ...
    def __imul__(self, m: int) -> 'QByteArray': ...
    def __mul__(self, m: int) -> 'QByteArray': ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def __hash__(self) -> int: ...
    def __contains__(self, a: typing.Union['QByteArray', bytes, bytearray]) -> int: ...
    @typing.overload
    def __getitem__(self, i: int) -> bytes: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QByteArray': ...
    def at(self, i: int) -> bytes: ...
    def size(self) -> int: ...
    def isNull(self) -> bool: ...
    def length(self) -> int: ...
    def __len__(self) -> int: ...
    @staticmethod
    def fromHex(hexEncoded: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @staticmethod
    def fromRawData(a0: typing.Optional[PyQt5.sip.array[bytes]]) -> 'QByteArray': ...
    @typing.overload
    @staticmethod
    def fromBase64(base64: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    @staticmethod
    def fromBase64(base64: typing.Union['QByteArray', bytes, bytearray], options: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray': ...
    @typing.overload
    @staticmethod
    def number(n: float, format: str = ..., precision: int = ...) -> 'QByteArray': ...
    @typing.overload
    @staticmethod
    def number(n: int, base: int = ...) -> 'QByteArray': ...
    @typing.overload
    def setNum(self, n: float, format: str = ..., precision: int = ...) -> 'QByteArray': ...
    @typing.overload
    def setNum(self, n: int, base: int = ...) -> 'QByteArray': ...
    @typing.overload
    def toBase64(self) -> 'QByteArray': ...
    @typing.overload
    def toBase64(self, options: typing.Union['QByteArray.Base64Options', 'QByteArray.Base64Option']) -> 'QByteArray': ...
    def toDouble(self) -> typing.Tuple[float, typing.Optional[bool]]: ...
    def toFloat(self) -> typing.Tuple[float, typing.Optional[bool]]: ...
    def toULongLong(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toLongLong(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toULong(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toLong(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toUInt(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toInt(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toUShort(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toShort(self, base: int = ...) -> typing.Tuple[int, typing.Optional[bool]]: ...
    @typing.overload
    def __ge__(self, s2: typing.Optional[str]) -> bool: ...
    @typing.overload
    def __ge__(self, a2: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    @typing.overload
    def __le__(self, s2: typing.Optional[str]) -> bool: ...
    @typing.overload
    def __le__(self, a2: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    @typing.overload
    def __gt__(self, s2: typing.Optional[str]) -> bool: ...
    @typing.overload
    def __gt__(self, a2: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    @typing.overload
    def __lt__(self, s2: typing.Optional[str]) -> bool: ...
    @typing.overload
    def __lt__(self, a2: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, a: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def __iadd__(self, s: typing.Optional[str]) -> 'QByteArray': ...
    def split(self, sep: str) -> typing.List['QByteArray']: ...
    @typing.overload
    def replace(self, index: int, len: int, s: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def replace(self, before: typing.Union['QByteArray', bytes, bytearray], after: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def replace(self, before: typing.Optional[str], after: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    def remove(self, index: int, len: int) -> 'QByteArray': ...
    @typing.overload
    def insert(self, i: int, a: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def insert(self, i: int, s: typing.Optional[str]) -> 'QByteArray': ...
    @typing.overload
    def insert(self, i: int, count: int, c: bytes) -> 'QByteArray': ...
    @typing.overload
    def append(self, a: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def append(self, s: typing.Optional[str]) -> 'QByteArray': ...
    @typing.overload
    def append(self, count: int, c: bytes) -> 'QByteArray': ...
    @typing.overload
    def prepend(self, a: typing.Union['QByteArray', bytes, bytearray]) -> 'QByteArray': ...
    @typing.overload
    def prepend(self, count: int, c: bytes) -> 'QByteArray': ...
    def rightJustified(self, width: int, fill: str = ..., truncate: bool = ...) -> 'QByteArray': ...
    def leftJustified(self, width: int, fill: str = ..., truncate: bool = ...) -> 'QByteArray': ...
    def simplified(self) -> 'QByteArray': ...
    def trimmed(self) -> 'QByteArray': ...
    def toUpper(self) -> 'QByteArray': ...
    def toLower(self) -> 'QByteArray': ...
    def chop(self, n: int) -> None: ...
    def truncate(self, pos: int) -> None: ...
    def endsWith(self, a: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    def startsWith(self, a: typing.Union['QByteArray', bytes, bytearray]) -> bool: ...
    def mid(self, pos: int, length: int = ...) -> 'QByteArray': ...
    def right(self, len: int) -> 'QByteArray': ...
    def left(self, len: int) -> 'QByteArray': ...
    @typing.overload
    def count(self, a: typing.Union['QByteArray', bytes, bytearray]) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    @typing.overload
    def lastIndexOf(self, ba: typing.Union['QByteArray', bytes, bytearray], from_: int = ...) -> int: ...
    @typing.overload
    def lastIndexOf(self, str: typing.Optional[str], from_: int = ...) -> int: ...
    @typing.overload
    def indexOf(self, ba: typing.Union['QByteArray', bytes, bytearray], from_: int = ...) -> int: ...
    @typing.overload
    def indexOf(self, str: typing.Optional[str], from_: int = ...) -> int: ...
    def clear(self) -> None: ...
    def fill(self, ch: str, size: int = ...) -> 'QByteArray': ...
    def resize(self, size: int) -> None: ...


class QByteArrayMatcher(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pattern: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QByteArrayMatcher') -> None: ...

    def pattern(self) -> QByteArray: ...
    def indexIn(self, ba: typing.Union[QByteArray, bytes, bytearray], from_: int = ...) -> int: ...
    def setPattern(self, pattern: typing.Union[QByteArray, bytes, bytearray]) -> None: ...


class QCalendar(PyQt5.sipsimplewrapper):

    class System(int):
        Gregorian = ... # type: QCalendar.System
        Julian = ... # type: QCalendar.System
        Milankovic = ... # type: QCalendar.System
        Jalali = ... # type: QCalendar.System
        IslamicCivil = ... # type: QCalendar.System

    Unspecified = ... # type: int

    class YearMonthDay(PyQt5.sipsimplewrapper):

        day = ... # type: int
        month = ... # type: int
        year = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, year: int, month: int = ..., day: int = ...) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QCalendar.YearMonthDay') -> None: ...

        def isValid(self) -> bool: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, system: 'QCalendar.System') -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QCalendar') -> None: ...

    @staticmethod
    def availableCalendars() -> typing.List[str]: ...
    def dateTimeToString(self, format: typing.Optional[str], datetime: typing.Union['QDateTime', datetime.datetime], dateOnly: typing.Union['QDate', datetime.date], timeOnly: typing.Union['QTime', datetime.time], locale: 'QLocale') -> str: ...
    def standaloneWeekDayName(self, locale: 'QLocale', day: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def weekDayName(self, locale: 'QLocale', day: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def standaloneMonthName(self, locale: 'QLocale', month: int, year: int = ..., format: 'QLocale.FormatType' = ...) -> str: ...
    def monthName(self, locale: 'QLocale', month: int, year: int = ..., format: 'QLocale.FormatType' = ...) -> str: ...
    def dayOfWeek(self, date: typing.Union['QDate', datetime.date]) -> int: ...
    def partsFromDate(self, date: typing.Union['QDate', datetime.date]) -> 'QCalendar.YearMonthDay': ...
    @typing.overload
    def dateFromParts(self, year: int, month: int, day: int) -> 'QDate': ...
    @typing.overload
    def dateFromParts(self, parts: 'QCalendar.YearMonthDay') -> 'QDate': ...
    def name(self) -> str: ...
    def maximumMonthsInYear(self) -> int: ...
    def minimumDaysInMonth(self) -> int: ...
    def maximumDaysInMonth(self) -> int: ...
    def hasYearZero(self) -> bool: ...
    def isProleptic(self) -> bool: ...
    def isSolar(self) -> bool: ...
    def isLuniSolar(self) -> bool: ...
    def isLunar(self) -> bool: ...
    def isGregorian(self) -> bool: ...
    def isLeapYear(self, year: int) -> bool: ...
    def isDateValid(self, year: int, month: int, day: int) -> bool: ...
    def monthsInYear(self, year: int) -> int: ...
    def daysInYear(self, year: int) -> int: ...
    def daysInMonth(self, month: int, year: int = ...) -> int: ...


class QCborError(PyQt5.sipsimplewrapper):

    class Code(int):
        UnknownError = ... # type: QCborError.Code
        AdvancePastEnd = ... # type: QCborError.Code
        InputOutputError = ... # type: QCborError.Code
        GarbageAtEnd = ... # type: QCborError.Code
        EndOfFile = ... # type: QCborError.Code
        UnexpectedBreak = ... # type: QCborError.Code
        UnknownType = ... # type: QCborError.Code
        IllegalType = ... # type: QCborError.Code
        IllegalNumber = ... # type: QCborError.Code
        IllegalSimpleType = ... # type: QCborError.Code
        InvalidUtf8String = ... # type: QCborError.Code
        DataTooLarge = ... # type: QCborError.Code
        NestingTooDeep = ... # type: QCborError.Code
        UnsupportedType = ... # type: QCborError.Code
        NoError = ... # type: QCborError.Code

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QCborError') -> None: ...

    def toString(self) -> str: ...
    def code(self) -> 'QCborError.Code': ...


class QCborStreamWriter(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, device: typing.Optional[QIODevice]) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Optional[typing.Union[QByteArray, bytes, bytearray]]) -> None: ...

    def endMap(self) -> bool: ...
    @typing.overload
    def startMap(self) -> None: ...
    @typing.overload
    def startMap(self, count: int) -> None: ...
    def endArray(self) -> bool: ...
    @typing.overload
    def startArray(self) -> None: ...
    @typing.overload
    def startArray(self, count: int) -> None: ...
    def appendUndefined(self) -> None: ...
    def appendNull(self) -> None: ...
    @typing.overload
    def append(self, st: QCborSimpleType) -> None: ...
    @typing.overload
    def append(self, tag: QCborKnownTags) -> None: ...
    @typing.overload
    def append(self, str: typing.Optional[str]) -> None: ...
    @typing.overload
    def append(self, ba: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def append(self, b: bool) -> None: ...
    @typing.overload
    def append(self, d: float) -> None: ...
    @typing.overload
    def append(self, a0: int) -> None: ...
    def device(self) -> typing.Optional[QIODevice]: ...
    def setDevice(self, device: typing.Optional[QIODevice]) -> None: ...


class QCborStreamReader(PyQt5.sipsimplewrapper):

    class StringResultCode(int):
        EndOfString = ... # type: QCborStreamReader.StringResultCode
        Ok = ... # type: QCborStreamReader.StringResultCode
        Error = ... # type: QCborStreamReader.StringResultCode

    class Type(int):
        UnsignedInteger = ... # type: QCborStreamReader.Type
        NegativeInteger = ... # type: QCborStreamReader.Type
        ByteString = ... # type: QCborStreamReader.Type
        ByteArray = ... # type: QCborStreamReader.Type
        TextString = ... # type: QCborStreamReader.Type
        String = ... # type: QCborStreamReader.Type
        Array = ... # type: QCborStreamReader.Type
        Map = ... # type: QCborStreamReader.Type
        Tag = ... # type: QCborStreamReader.Type
        SimpleType = ... # type: QCborStreamReader.Type
        HalfFloat = ... # type: QCborStreamReader.Type
        Float16 = ... # type: QCborStreamReader.Type
        Float = ... # type: QCborStreamReader.Type
        Double = ... # type: QCborStreamReader.Type
        Invalid = ... # type: QCborStreamReader.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QIODevice]) -> None: ...

    def toInteger(self) -> int: ...
    def toDouble(self) -> float: ...
    def toSimpleType(self) -> QCborSimpleType: ...
    def toUnsignedInteger(self) -> int: ...
    def toBool(self) -> bool: ...
    def readByteArray(self) -> typing.Tuple[QByteArray, 'QCborStreamReader.StringResultCode']: ...
    def readString(self) -> typing.Tuple[str, 'QCborStreamReader.StringResultCode']: ...
    def leaveContainer(self) -> bool: ...
    def enterContainer(self) -> bool: ...
    def isContainer(self) -> bool: ...
    def __len__(self) -> int: ...
    def length(self) -> int: ...
    def isLengthKnown(self) -> bool: ...
    def isUndefined(self) -> bool: ...
    def isNull(self) -> bool: ...
    def isBool(self) -> bool: ...
    def isTrue(self) -> bool: ...
    def isFalse(self) -> bool: ...
    def isInvalid(self) -> bool: ...
    def isDouble(self) -> bool: ...
    def isFloat(self) -> bool: ...
    def isFloat16(self) -> bool: ...
    @typing.overload
    def isSimpleType(self) -> bool: ...
    @typing.overload
    def isSimpleType(self, st: QCborSimpleType) -> bool: ...
    def isTag(self) -> bool: ...
    def isMap(self) -> bool: ...
    def isArray(self) -> bool: ...
    def isString(self) -> bool: ...
    def isByteArray(self) -> bool: ...
    def isInteger(self) -> bool: ...
    def isNegativeInteger(self) -> bool: ...
    def isUnsignedInteger(self) -> bool: ...
    def type(self) -> 'QCborStreamReader.Type': ...
    def next(self, maxRecursion: int = ...) -> bool: ...
    def hasNext(self) -> bool: ...
    def parentContainerType(self) -> 'QCborStreamReader.Type': ...
    def containerDepth(self) -> int: ...
    def isValid(self) -> bool: ...
    def currentOffset(self) -> int: ...
    def lastError(self) -> QCborError: ...
    def reset(self) -> None: ...
    def clear(self) -> None: ...
    def reparse(self) -> None: ...
    def addData(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    def device(self) -> typing.Optional[QIODevice]: ...
    def setDevice(self, device: typing.Optional[QIODevice]) -> None: ...


class QCollatorSortKey(PyQt5.sipsimplewrapper):

    def __init__(self, other: 'QCollatorSortKey') -> None: ...

    def __ge__(self, rhs: 'QCollatorSortKey') -> bool: ...
    def __lt__(self, rhs: 'QCollatorSortKey') -> bool: ...
    def compare(self, key: 'QCollatorSortKey') -> int: ...
    def swap(self, other: 'QCollatorSortKey') -> None: ...


class QCollator(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, locale: 'QLocale' = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QCollator') -> None: ...

    def sortKey(self, string: typing.Optional[str]) -> QCollatorSortKey: ...
    def compare(self, s1: typing.Optional[str], s2: typing.Optional[str]) -> int: ...
    def ignorePunctuation(self) -> bool: ...
    def setIgnorePunctuation(self, on: bool) -> None: ...
    def numericMode(self) -> bool: ...
    def setNumericMode(self, on: bool) -> None: ...
    def setCaseSensitivity(self, cs: Qt.CaseSensitivity) -> None: ...
    def caseSensitivity(self) -> Qt.CaseSensitivity: ...
    def locale(self) -> 'QLocale': ...
    def setLocale(self, locale: 'QLocale') -> None: ...
    def swap(self, other: 'QCollator') -> None: ...


class QCommandLineOption(PyQt5.sipsimplewrapper):

    class Flag(int):
        HiddenFromHelp = ... # type: QCommandLineOption.Flag
        ShortOptionStyle = ... # type: QCommandLineOption.Flag

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __xor__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __ior__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __or__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __iand__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __and__(self, f: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> 'QCommandLineOption.Flags': ...
        def __invert__(self) -> 'QCommandLineOption.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, names: typing.Iterable[typing.Optional[str]]) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], description: typing.Optional[str], valueName: typing.Optional[str] = ..., defaultValue: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, names: typing.Iterable[typing.Optional[str]], description: typing.Optional[str], valueName: typing.Optional[str] = ..., defaultValue: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCommandLineOption') -> None: ...

    def setFlags(self, aflags: typing.Union['QCommandLineOption.Flags', 'QCommandLineOption.Flag']) -> None: ...
    def flags(self) -> 'QCommandLineOption.Flags': ...
    def isHidden(self) -> bool: ...
    def setHidden(self, hidden: bool) -> None: ...
    def defaultValues(self) -> typing.List[str]: ...
    def setDefaultValues(self, defaultValues: typing.Iterable[typing.Optional[str]]) -> None: ...
    def setDefaultValue(self, defaultValue: typing.Optional[str]) -> None: ...
    def description(self) -> str: ...
    def setDescription(self, description: typing.Optional[str]) -> None: ...
    def valueName(self) -> str: ...
    def setValueName(self, name: typing.Optional[str]) -> None: ...
    def names(self) -> typing.List[str]: ...
    def swap(self, other: 'QCommandLineOption') -> None: ...


class QCommandLineParser(PyQt5.sipsimplewrapper):

    class OptionsAfterPositionalArgumentsMode(int):
        ParseAsOptions = ... # type: QCommandLineParser.OptionsAfterPositionalArgumentsMode
        ParseAsPositionalArguments = ... # type: QCommandLineParser.OptionsAfterPositionalArgumentsMode

    class SingleDashWordOptionMode(int):
        ParseAsCompactedShortOptions = ... # type: QCommandLineParser.SingleDashWordOptionMode
        ParseAsLongOptions = ... # type: QCommandLineParser.SingleDashWordOptionMode

    def __init__(self) -> None: ...

    def setOptionsAfterPositionalArgumentsMode(self, mode: 'QCommandLineParser.OptionsAfterPositionalArgumentsMode') -> None: ...
    def showVersion(self) -> None: ...
    def addOptions(self, options: typing.Iterable[QCommandLineOption]) -> bool: ...
    def helpText(self) -> str: ...
    def showHelp(self, exitCode: int = ...) -> None: ...
    def unknownOptionNames(self) -> typing.List[str]: ...
    def optionNames(self) -> typing.List[str]: ...
    def positionalArguments(self) -> typing.List[str]: ...
    @typing.overload
    def values(self, name: typing.Optional[str]) -> typing.List[str]: ...
    @typing.overload
    def values(self, option: QCommandLineOption) -> typing.List[str]: ...
    @typing.overload
    def value(self, name: typing.Optional[str]) -> str: ...
    @typing.overload
    def value(self, option: QCommandLineOption) -> str: ...
    @typing.overload
    def isSet(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def isSet(self, option: QCommandLineOption) -> bool: ...
    def errorText(self) -> str: ...
    def parse(self, arguments: typing.Iterable[typing.Optional[str]]) -> bool: ...
    @typing.overload
    def process(self, arguments: typing.Iterable[typing.Optional[str]]) -> None: ...
    @typing.overload
    def process(self, app: 'QCoreApplication') -> None: ...
    def clearPositionalArguments(self) -> None: ...
    def addPositionalArgument(self, name: typing.Optional[str], description: typing.Optional[str], syntax: typing.Optional[str] = ...) -> None: ...
    def applicationDescription(self) -> str: ...
    def setApplicationDescription(self, description: typing.Optional[str]) -> None: ...
    def addHelpOption(self) -> QCommandLineOption: ...
    def addVersionOption(self) -> QCommandLineOption: ...
    def addOption(self, commandLineOption: QCommandLineOption) -> bool: ...
    def setSingleDashWordOptionMode(self, parsingMode: 'QCommandLineParser.SingleDashWordOptionMode') -> None: ...


class QConcatenateTablesProxyModel(QAbstractItemModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def sourceModels(self) -> typing.List[QAbstractItemModel]: ...
    def span(self, index: QModelIndex) -> 'QSize': ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def canDropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def mimeData(self, indexes: typing.Iterable[QModelIndex]) -> typing.Optional['QMimeData']: ...
    def mimeTypes(self) -> typing.List[str]: ...
    def columnCount(self, parent: QModelIndex = ...) -> int: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...
    def parent(self, index: QModelIndex) -> QModelIndex: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def setItemData(self, index: QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def itemData(self, proxyIndex: QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def setData(self, index: QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QModelIndex, role: int = ...) -> typing.Any: ...
    def mapToSource(self, proxyIndex: QModelIndex) -> QModelIndex: ...
    def mapFromSource(self, sourceIndex: QModelIndex) -> QModelIndex: ...
    def removeSourceModel(self, sourceModel: typing.Optional[QAbstractItemModel]) -> None: ...
    def addSourceModel(self, sourceModel: typing.Optional[QAbstractItemModel]) -> None: ...


class QCoreApplication(QObject):

    def __init__(self, argv: typing.List[str]) -> None: ...

    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...
    @staticmethod
    def isSetuidAllowed() -> bool: ...
    @staticmethod
    def setSetuidAllowed(allow: bool) -> None: ...
    def removeNativeEventFilter(self, filterObj: typing.Optional[QAbstractNativeEventFilter]) -> None: ...
    def installNativeEventFilter(self, filterObj: typing.Optional[QAbstractNativeEventFilter]) -> None: ...
    @staticmethod
    def setQuitLockEnabled(enabled: bool) -> None: ...
    @staticmethod
    def isQuitLockEnabled() -> bool: ...
    @staticmethod
    def setEventDispatcher(eventDispatcher: typing.Optional[QAbstractEventDispatcher]) -> None: ...
    @staticmethod
    def eventDispatcher() -> typing.Optional[QAbstractEventDispatcher]: ...
    @staticmethod
    def applicationPid() -> int: ...
    @staticmethod
    def applicationVersion() -> str: ...
    @staticmethod
    def setApplicationVersion(version: typing.Optional[str]) -> None: ...
    def event(self, a0: typing.Optional['QEvent']) -> bool: ...
    aboutToQuit: typing.ClassVar[pyqtSignal]
    @staticmethod
    def quit() -> None: ...
    @staticmethod
    def testAttribute(attribute: Qt.ApplicationAttribute) -> bool: ...
    @staticmethod
    def setAttribute(attribute: Qt.ApplicationAttribute, on: bool = ...) -> None: ...
    @staticmethod
    def flush() -> None: ...
    @staticmethod
    def translate(context: typing.Optional[str], sourceText: typing.Optional[str], disambiguation: typing.Optional[str] = ..., n: int = ...) -> str: ...
    @staticmethod
    def removeTranslator(messageFile: typing.Optional['QTranslator']) -> bool: ...
    @staticmethod
    def installTranslator(messageFile: typing.Optional['QTranslator']) -> bool: ...
    @staticmethod
    def removeLibraryPath(a0: typing.Optional[str]) -> None: ...
    @staticmethod
    def addLibraryPath(a0: typing.Optional[str]) -> None: ...
    @staticmethod
    def libraryPaths() -> typing.List[str]: ...
    @staticmethod
    def setLibraryPaths(a0: typing.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def applicationFilePath() -> str: ...
    @staticmethod
    def applicationDirPath() -> str: ...
    @staticmethod
    def closingDown() -> bool: ...
    @staticmethod
    def startingUp() -> bool: ...
    def notify(self, a0: typing.Optional[QObject], a1: typing.Optional['QEvent']) -> bool: ...
    @staticmethod
    def hasPendingEvents() -> bool: ...
    @staticmethod
    def removePostedEvents(receiver: typing.Optional[QObject], eventType: int = ...) -> None: ...
    @staticmethod
    def sendPostedEvents(receiver: typing.Optional[QObject] = ..., eventType: int = ...) -> None: ...
    @staticmethod
    def postEvent(receiver: typing.Optional[QObject], event: typing.Optional['QEvent'], priority: int = ...) -> None: ...
    @staticmethod
    def sendEvent(receiver: typing.Optional[QObject], event: typing.Optional['QEvent']) -> bool: ...
    @staticmethod
    def exit(returnCode: int = ...) -> None: ...
    @typing.overload
    @staticmethod
    def processEvents(flags: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag'] = ...) -> None: ...
    @typing.overload
    @staticmethod
    def processEvents(flags: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag'], maxtime: int) -> None: ...
    @staticmethod
    def exec() -> int: ...
    @staticmethod
    def exec_() -> int: ...
    @staticmethod
    def instance() -> typing.Optional['QCoreApplication']: ...
    @staticmethod
    def arguments() -> typing.List[str]: ...
    @staticmethod
    def applicationName() -> str: ...
    @staticmethod
    def setApplicationName(application: typing.Optional[str]) -> None: ...
    @staticmethod
    def organizationName() -> str: ...
    @staticmethod
    def setOrganizationName(orgName: typing.Optional[str]) -> None: ...
    @staticmethod
    def organizationDomain() -> str: ...
    @staticmethod
    def setOrganizationDomain(orgDomain: typing.Optional[str]) -> None: ...


class QEvent(PyQt5.sip.wrapper):

    class Type(int):
        None_ = ... # type: QEvent.Type
        Timer = ... # type: QEvent.Type
        MouseButtonPress = ... # type: QEvent.Type
        MouseButtonRelease = ... # type: QEvent.Type
        MouseButtonDblClick = ... # type: QEvent.Type
        MouseMove = ... # type: QEvent.Type
        KeyPress = ... # type: QEvent.Type
        KeyRelease = ... # type: QEvent.Type
        FocusIn = ... # type: QEvent.Type
        FocusOut = ... # type: QEvent.Type
        Enter = ... # type: QEvent.Type
        Leave = ... # type: QEvent.Type
        Paint = ... # type: QEvent.Type
        Move = ... # type: QEvent.Type
        Resize = ... # type: QEvent.Type
        Show = ... # type: QEvent.Type
        Hide = ... # type: QEvent.Type
        Close = ... # type: QEvent.Type
        ParentChange = ... # type: QEvent.Type
        ParentAboutToChange = ... # type: QEvent.Type
        ThreadChange = ... # type: QEvent.Type
        WindowActivate = ... # type: QEvent.Type
        WindowDeactivate = ... # type: QEvent.Type
        ShowToParent = ... # type: QEvent.Type
        HideToParent = ... # type: QEvent.Type
        Wheel = ... # type: QEvent.Type
        WindowTitleChange = ... # type: QEvent.Type
        WindowIconChange = ... # type: QEvent.Type
        ApplicationWindowIconChange = ... # type: QEvent.Type
        ApplicationFontChange = ... # type: QEvent.Type
        ApplicationLayoutDirectionChange = ... # type: QEvent.Type
        ApplicationPaletteChange = ... # type: QEvent.Type
        PaletteChange = ... # type: QEvent.Type
        Clipboard = ... # type: QEvent.Type
        MetaCall = ... # type: QEvent.Type
        SockAct = ... # type: QEvent.Type
        WinEventAct = ... # type: QEvent.Type
        DeferredDelete = ... # type: QEvent.Type
        DragEnter = ... # type: QEvent.Type
        DragMove = ... # type: QEvent.Type
        DragLeave = ... # type: QEvent.Type
        Drop = ... # type: QEvent.Type
        ChildAdded = ... # type: QEvent.Type
        ChildPolished = ... # type: QEvent.Type
        ChildRemoved = ... # type: QEvent.Type
        PolishRequest = ... # type: QEvent.Type
        Polish = ... # type: QEvent.Type
        LayoutRequest = ... # type: QEvent.Type
        UpdateRequest = ... # type: QEvent.Type
        UpdateLater = ... # type: QEvent.Type
        ContextMenu = ... # type: QEvent.Type
        InputMethod = ... # type: QEvent.Type
        TabletMove = ... # type: QEvent.Type
        LocaleChange = ... # type: QEvent.Type
        LanguageChange = ... # type: QEvent.Type
        LayoutDirectionChange = ... # type: QEvent.Type
        TabletPress = ... # type: QEvent.Type
        TabletRelease = ... # type: QEvent.Type
        OkRequest = ... # type: QEvent.Type
        IconDrag = ... # type: QEvent.Type
        FontChange = ... # type: QEvent.Type
        EnabledChange = ... # type: QEvent.Type
        ActivationChange = ... # type: QEvent.Type
        StyleChange = ... # type: QEvent.Type
        IconTextChange = ... # type: QEvent.Type
        ModifiedChange = ... # type: QEvent.Type
        MouseTrackingChange = ... # type: QEvent.Type
        WindowBlocked = ... # type: QEvent.Type
        WindowUnblocked = ... # type: QEvent.Type
        WindowStateChange = ... # type: QEvent.Type
        ToolTip = ... # type: QEvent.Type
        WhatsThis = ... # type: QEvent.Type
        StatusTip = ... # type: QEvent.Type
        ActionChanged = ... # type: QEvent.Type
        ActionAdded = ... # type: QEvent.Type
        ActionRemoved = ... # type: QEvent.Type
        FileOpen = ... # type: QEvent.Type
        Shortcut = ... # type: QEvent.Type
        ShortcutOverride = ... # type: QEvent.Type
        WhatsThisClicked = ... # type: QEvent.Type
        ToolBarChange = ... # type: QEvent.Type
        ApplicationActivate = ... # type: QEvent.Type
        ApplicationActivated = ... # type: QEvent.Type
        ApplicationDeactivate = ... # type: QEvent.Type
        ApplicationDeactivated = ... # type: QEvent.Type
        QueryWhatsThis = ... # type: QEvent.Type
        EnterWhatsThisMode = ... # type: QEvent.Type
        LeaveWhatsThisMode = ... # type: QEvent.Type
        ZOrderChange = ... # type: QEvent.Type
        HoverEnter = ... # type: QEvent.Type
        HoverLeave = ... # type: QEvent.Type
        HoverMove = ... # type: QEvent.Type
        GraphicsSceneMouseMove = ... # type: QEvent.Type
        GraphicsSceneMousePress = ... # type: QEvent.Type
        GraphicsSceneMouseRelease = ... # type: QEvent.Type
        GraphicsSceneMouseDoubleClick = ... # type: QEvent.Type
        GraphicsSceneContextMenu = ... # type: QEvent.Type
        GraphicsSceneHoverEnter = ... # type: QEvent.Type
        GraphicsSceneHoverMove = ... # type: QEvent.Type
        GraphicsSceneHoverLeave = ... # type: QEvent.Type
        GraphicsSceneHelp = ... # type: QEvent.Type
        GraphicsSceneDragEnter = ... # type: QEvent.Type
        GraphicsSceneDragMove = ... # type: QEvent.Type
        GraphicsSceneDragLeave = ... # type: QEvent.Type
        GraphicsSceneDrop = ... # type: QEvent.Type
        GraphicsSceneWheel = ... # type: QEvent.Type
        GraphicsSceneResize = ... # type: QEvent.Type
        GraphicsSceneMove = ... # type: QEvent.Type
        KeyboardLayoutChange = ... # type: QEvent.Type
        DynamicPropertyChange = ... # type: QEvent.Type
        TabletEnterProximity = ... # type: QEvent.Type
        TabletLeaveProximity = ... # type: QEvent.Type
        NonClientAreaMouseMove = ... # type: QEvent.Type
        NonClientAreaMouseButtonPress = ... # type: QEvent.Type
        NonClientAreaMouseButtonRelease = ... # type: QEvent.Type
        NonClientAreaMouseButtonDblClick = ... # type: QEvent.Type
        MacSizeChange = ... # type: QEvent.Type
        ContentsRectChange = ... # type: QEvent.Type
        CursorChange = ... # type: QEvent.Type
        ToolTipChange = ... # type: QEvent.Type
        GrabMouse = ... # type: QEvent.Type
        UngrabMouse = ... # type: QEvent.Type
        GrabKeyboard = ... # type: QEvent.Type
        UngrabKeyboard = ... # type: QEvent.Type
        StateMachineSignal = ... # type: QEvent.Type
        StateMachineWrapped = ... # type: QEvent.Type
        TouchBegin = ... # type: QEvent.Type
        TouchUpdate = ... # type: QEvent.Type
        TouchEnd = ... # type: QEvent.Type
        NativeGesture = ... # type: QEvent.Type
        RequestSoftwareInputPanel = ... # type: QEvent.Type
        CloseSoftwareInputPanel = ... # type: QEvent.Type
        WinIdChange = ... # type: QEvent.Type
        Gesture = ... # type: QEvent.Type
        GestureOverride = ... # type: QEvent.Type
        FocusAboutToChange = ... # type: QEvent.Type
        ScrollPrepare = ... # type: QEvent.Type
        Scroll = ... # type: QEvent.Type
        Expose = ... # type: QEvent.Type
        InputMethodQuery = ... # type: QEvent.Type
        OrientationChange = ... # type: QEvent.Type
        TouchCancel = ... # type: QEvent.Type
        PlatformPanel = ... # type: QEvent.Type
        ApplicationStateChange = ... # type: QEvent.Type
        ReadOnlyChange = ... # type: QEvent.Type
        PlatformSurface = ... # type: QEvent.Type
        TabletTrackingChange = ... # type: QEvent.Type
        EnterEditFocus = ... # type: QEvent.Type
        LeaveEditFocus = ... # type: QEvent.Type
        User = ... # type: QEvent.Type
        MaxUser = ... # type: QEvent.Type

    @typing.overload
    def __init__(self, type: 'QEvent.Type') -> None: ...
    @typing.overload
    def __init__(self, other: 'QEvent') -> None: ...

    @staticmethod
    def registerEventType(hint: int = ...) -> int: ...
    def ignore(self) -> None: ...
    def accept(self) -> None: ...
    def isAccepted(self) -> bool: ...
    def setAccepted(self, accepted: bool) -> None: ...
    def spontaneous(self) -> bool: ...
    def type(self) -> 'QEvent.Type': ...


class QTimerEvent(QEvent):

    @typing.overload
    def __init__(self, timerId: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTimerEvent') -> None: ...

    def timerId(self) -> int: ...


class QChildEvent(QEvent):

    @typing.overload
    def __init__(self, type: QEvent.Type, child: typing.Optional[QObject]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QChildEvent') -> None: ...

    def removed(self) -> bool: ...
    def polished(self) -> bool: ...
    def added(self) -> bool: ...
    def child(self) -> typing.Optional[QObject]: ...


class QDynamicPropertyChangeEvent(QEvent):

    @typing.overload
    def __init__(self, name: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDynamicPropertyChangeEvent') -> None: ...

    def propertyName(self) -> QByteArray: ...


class QCryptographicHash(PyQt5.sipsimplewrapper):

    class Algorithm(int):
        Md4 = ... # type: QCryptographicHash.Algorithm
        Md5 = ... # type: QCryptographicHash.Algorithm
        Sha1 = ... # type: QCryptographicHash.Algorithm
        Sha224 = ... # type: QCryptographicHash.Algorithm
        Sha256 = ... # type: QCryptographicHash.Algorithm
        Sha384 = ... # type: QCryptographicHash.Algorithm
        Sha512 = ... # type: QCryptographicHash.Algorithm
        Sha3_224 = ... # type: QCryptographicHash.Algorithm
        Sha3_256 = ... # type: QCryptographicHash.Algorithm
        Sha3_384 = ... # type: QCryptographicHash.Algorithm
        Sha3_512 = ... # type: QCryptographicHash.Algorithm
        Keccak_224 = ... # type: QCryptographicHash.Algorithm
        Keccak_256 = ... # type: QCryptographicHash.Algorithm
        Keccak_384 = ... # type: QCryptographicHash.Algorithm
        Keccak_512 = ... # type: QCryptographicHash.Algorithm

    def __init__(self, method: 'QCryptographicHash.Algorithm') -> None: ...

    @staticmethod
    def hashLength(method: 'QCryptographicHash.Algorithm') -> int: ...
    @staticmethod
    def hash(data: typing.Union[QByteArray, bytes, bytearray], method: 'QCryptographicHash.Algorithm') -> QByteArray: ...
    def result(self) -> QByteArray: ...
    @typing.overload
    def addData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> None: ...
    @typing.overload
    def addData(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def addData(self, device: typing.Optional[QIODevice]) -> bool: ...
    def reset(self) -> None: ...


class QDataStream(PyQt5.sipsimplewrapper):

    class FloatingPointPrecision(int):
        SinglePrecision = ... # type: QDataStream.FloatingPointPrecision
        DoublePrecision = ... # type: QDataStream.FloatingPointPrecision

    class Status(int):
        Ok = ... # type: QDataStream.Status
        ReadPastEnd = ... # type: QDataStream.Status
        ReadCorruptData = ... # type: QDataStream.Status
        WriteFailed = ... # type: QDataStream.Status

    class ByteOrder(int):
        BigEndian = ... # type: QDataStream.ByteOrder
        LittleEndian = ... # type: QDataStream.ByteOrder

    class Version(int):
        Qt_1_0 = ... # type: QDataStream.Version
        Qt_2_0 = ... # type: QDataStream.Version
        Qt_2_1 = ... # type: QDataStream.Version
        Qt_3_0 = ... # type: QDataStream.Version
        Qt_3_1 = ... # type: QDataStream.Version
        Qt_3_3 = ... # type: QDataStream.Version
        Qt_4_0 = ... # type: QDataStream.Version
        Qt_4_1 = ... # type: QDataStream.Version
        Qt_4_2 = ... # type: QDataStream.Version
        Qt_4_3 = ... # type: QDataStream.Version
        Qt_4_4 = ... # type: QDataStream.Version
        Qt_4_5 = ... # type: QDataStream.Version
        Qt_4_6 = ... # type: QDataStream.Version
        Qt_4_7 = ... # type: QDataStream.Version
        Qt_4_8 = ... # type: QDataStream.Version
        Qt_4_9 = ... # type: QDataStream.Version
        Qt_5_0 = ... # type: QDataStream.Version
        Qt_5_1 = ... # type: QDataStream.Version
        Qt_5_2 = ... # type: QDataStream.Version
        Qt_5_3 = ... # type: QDataStream.Version
        Qt_5_4 = ... # type: QDataStream.Version
        Qt_5_5 = ... # type: QDataStream.Version
        Qt_5_6 = ... # type: QDataStream.Version
        Qt_5_7 = ... # type: QDataStream.Version
        Qt_5_8 = ... # type: QDataStream.Version
        Qt_5_9 = ... # type: QDataStream.Version
        Qt_5_10 = ... # type: QDataStream.Version
        Qt_5_11 = ... # type: QDataStream.Version
        Qt_5_12 = ... # type: QDataStream.Version
        Qt_5_13 = ... # type: QDataStream.Version
        Qt_5_14 = ... # type: QDataStream.Version
        Qt_5_15 = ... # type: QDataStream.Version

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QIODevice]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QByteArray], flags: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag]) -> None: ...
    @typing.overload
    def __init__(self, a0: QByteArray) -> None: ...

    @typing.overload
    def __lshift__(self, a0: QBitArray) -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: QByteArray) -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QDate') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QTime') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QDateTime') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QEasingCurve') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QJsonDocument') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: typing.Optional['QJsonValue']) -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QLine') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QLineF') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QLocale') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QMargins') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QMarginsF') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QPoint') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QPointF') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QRect') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QRectF') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, regExp: 'QRegExp') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, re: 'QRegularExpression') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QSize') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QSizeF') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, tz: 'QTimeZone') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QUrl') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, a0: 'QUuid') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, p: typing.Optional['QVariant']) -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, p: 'QVariant.Type') -> 'QDataStream': ...
    @typing.overload
    def __lshift__(self, version: 'QVersionNumber') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: QBitArray) -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: QByteArray) -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QDate') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QTime') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QDateTime') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QEasingCurve') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QJsonDocument') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: typing.Optional['QJsonValue']) -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QLine') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QLineF') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QLocale') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QMargins') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QMarginsF') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QPoint') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QPointF') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QRect') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QRectF') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, regExp: 'QRegExp') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, re: 'QRegularExpression') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QSize') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QSizeF') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, tz: 'QTimeZone') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QUrl') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, a0: 'QUuid') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, p: typing.Optional['QVariant']) -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, p: 'QVariant.Type') -> 'QDataStream': ...
    @typing.overload
    def __rshift__(self, version: 'QVersionNumber') -> 'QDataStream': ...
    def abortTransaction(self) -> None: ...
    def rollbackTransaction(self) -> None: ...
    def commitTransaction(self) -> bool: ...
    def startTransaction(self) -> None: ...
    def setFloatingPointPrecision(self, precision: 'QDataStream.FloatingPointPrecision') -> None: ...
    def floatingPointPrecision(self) -> 'QDataStream.FloatingPointPrecision': ...
    def writeRawData(self, a0: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def writeBytes(self, a0: typing.Optional[PyQt5.sip.array[bytes]]) -> 'QDataStream': ...
    def readRawData(self, len: int) -> bytes: ...
    def readBytes(self) -> bytes: ...
    def writeQVariantHash(self, qvarhash: typing.Dict[typing.Optional[str], typing.Any]) -> None: ...
    def readQVariantHash(self) -> typing.Dict[str, typing.Any]: ...
    def writeQVariantMap(self, qvarmap: typing.Dict[str, typing.Any]) -> None: ...
    def readQVariantMap(self) -> typing.Dict[str, typing.Any]: ...
    def writeQVariantList(self, qvarlst: typing.Iterable[typing.Any]) -> None: ...
    def readQVariantList(self) -> typing.List[typing.Any]: ...
    def writeQVariant(self, qvar: typing.Any) -> None: ...
    def readQVariant(self) -> typing.Any: ...
    def writeQStringList(self, qstrlst: typing.Iterable[typing.Optional[str]]) -> None: ...
    def readQStringList(self) -> typing.List[str]: ...
    def writeQString(self, qstr: typing.Optional[str]) -> None: ...
    def readQString(self) -> str: ...
    def writeString(self, str: typing.Optional[bytes]) -> None: ...
    def writeDouble(self, f: float) -> None: ...
    def writeFloat(self, f: float) -> None: ...
    def writeBool(self, i: bool) -> None: ...
    def writeUInt64(self, i: int) -> None: ...
    def writeInt64(self, i: int) -> None: ...
    def writeUInt32(self, i: int) -> None: ...
    def writeInt32(self, i: int) -> None: ...
    def writeUInt16(self, i: int) -> None: ...
    def writeInt16(self, i: int) -> None: ...
    def writeUInt8(self, i: int) -> None: ...
    def writeInt8(self, i: int) -> None: ...
    def writeInt(self, i: int) -> None: ...
    def readString(self) -> bytes: ...
    def readDouble(self) -> float: ...
    def readFloat(self) -> float: ...
    def readBool(self) -> bool: ...
    def readUInt64(self) -> int: ...
    def readInt64(self) -> int: ...
    def readUInt32(self) -> int: ...
    def readInt32(self) -> int: ...
    def readUInt16(self) -> int: ...
    def readInt16(self) -> int: ...
    def readUInt8(self) -> int: ...
    def readInt8(self) -> int: ...
    def readInt(self) -> int: ...
    def skipRawData(self, len: int) -> int: ...
    def setVersion(self, v: int) -> None: ...
    def version(self) -> int: ...
    def setByteOrder(self, a0: 'QDataStream.ByteOrder') -> None: ...
    def byteOrder(self) -> 'QDataStream.ByteOrder': ...
    def resetStatus(self) -> None: ...
    def setStatus(self, status: 'QDataStream.Status') -> None: ...
    def status(self) -> 'QDataStream.Status': ...
    def atEnd(self) -> bool: ...
    def setDevice(self, a0: typing.Optional[QIODevice]) -> None: ...
    def device(self) -> typing.Optional[QIODevice]: ...


class QDate(PyQt5.sipsimplewrapper):

    class MonthNameType(int):
        DateFormat = ... # type: QDate.MonthNameType
        StandaloneFormat = ... # type: QDate.MonthNameType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, y: int, m: int, d: int) -> None: ...
    @typing.overload
    def __init__(self, y: int, m: int, d: int, cal: QCalendar) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDate') -> None: ...

    @typing.overload
    def endOfDay(self, spec: Qt.TimeSpec = ..., offsetSeconds: int = ...) -> 'QDateTime': ...
    @typing.overload
    def endOfDay(self, zone: 'QTimeZone') -> 'QDateTime': ...
    @typing.overload
    def startOfDay(self, spec: Qt.TimeSpec = ..., offsetSeconds: int = ...) -> 'QDateTime': ...
    @typing.overload
    def startOfDay(self, zone: 'QTimeZone') -> 'QDateTime': ...
    def getDate(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    @typing.overload
    def setDate(self, year: int, month: int, date: int) -> bool: ...
    @typing.overload
    def setDate(self, year: int, month: int, day: int, cal: QCalendar) -> bool: ...
    def toJulianDay(self) -> int: ...
    @staticmethod
    def fromJulianDay(jd: int) -> 'QDate': ...
    @staticmethod
    def isLeapYear(year: int) -> bool: ...
    @typing.overload
    @staticmethod
    def fromString(string: typing.Optional[str], format: Qt.DateFormat = ...) -> 'QDate': ...
    @typing.overload
    @staticmethod
    def fromString(s: typing.Optional[str], format: typing.Optional[str]) -> 'QDate': ...
    @typing.overload
    @staticmethod
    def fromString(s: typing.Optional[str], format: typing.Optional[str], cal: QCalendar) -> 'QDate': ...
    @staticmethod
    def currentDate() -> 'QDate': ...
    def __ge__(self, other: typing.Union['QDate', datetime.date]) -> bool: ...
    def __gt__(self, other: typing.Union['QDate', datetime.date]) -> bool: ...
    def __le__(self, other: typing.Union['QDate', datetime.date]) -> bool: ...
    def __lt__(self, other: typing.Union['QDate', datetime.date]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def daysTo(self, a0: typing.Union['QDate', datetime.date]) -> int: ...
    @typing.overload
    def addYears(self, years: int) -> 'QDate': ...
    @typing.overload
    def addYears(self, years: int, cal: QCalendar) -> 'QDate': ...
    @typing.overload
    def addMonths(self, months: int) -> 'QDate': ...
    @typing.overload
    def addMonths(self, months: int, cal: QCalendar) -> 'QDate': ...
    def addDays(self, days: int) -> 'QDate': ...
    @typing.overload
    def toString(self, format: Qt.DateFormat = ...) -> str: ...
    @typing.overload
    def toString(self, f: Qt.DateFormat, cal: QCalendar) -> str: ...
    @typing.overload
    def toString(self, format: typing.Optional[str]) -> str: ...
    @typing.overload
    def toString(self, format: typing.Optional[str], cal: QCalendar) -> str: ...
    @staticmethod
    def longDayName(weekday: int, type: 'QDate.MonthNameType' = ...) -> str: ...
    @staticmethod
    def longMonthName(month: int, type: 'QDate.MonthNameType' = ...) -> str: ...
    @staticmethod
    def shortDayName(weekday: int, type: 'QDate.MonthNameType' = ...) -> str: ...
    @staticmethod
    def shortMonthName(month: int, type: 'QDate.MonthNameType' = ...) -> str: ...
    def weekNumber(self) -> typing.Tuple[int, typing.Optional[int]]: ...
    @typing.overload
    def daysInYear(self) -> int: ...
    @typing.overload
    def daysInYear(self, cal: QCalendar) -> int: ...
    @typing.overload
    def daysInMonth(self) -> int: ...
    @typing.overload
    def daysInMonth(self, cal: QCalendar) -> int: ...
    @typing.overload
    def dayOfYear(self) -> int: ...
    @typing.overload
    def dayOfYear(self, cal: QCalendar) -> int: ...
    @typing.overload
    def dayOfWeek(self) -> int: ...
    @typing.overload
    def dayOfWeek(self, cal: QCalendar) -> int: ...
    @typing.overload
    def day(self) -> int: ...
    @typing.overload
    def day(self, cal: QCalendar) -> int: ...
    @typing.overload
    def month(self) -> int: ...
    @typing.overload
    def month(self, cal: QCalendar) -> int: ...
    @typing.overload
    def year(self) -> int: ...
    @typing.overload
    def year(self, cal: QCalendar) -> int: ...
    @typing.overload
    def isValid(self) -> bool: ...
    @typing.overload
    @staticmethod
    def isValid(y: int, m: int, d: int) -> bool: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def toPyDate(self) -> datetime.date: ...
    def __hash__(self) -> int: ...
    def __repr__(self) -> str: ...


class QTime(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, h: int, m: int, second: int = ..., msec: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTime') -> None: ...

    def msecsSinceStartOfDay(self) -> int: ...
    @staticmethod
    def fromMSecsSinceStartOfDay(msecs: int) -> 'QTime': ...
    def elapsed(self) -> int: ...
    def restart(self) -> int: ...
    def start(self) -> None: ...
    @typing.overload
    @staticmethod
    def fromString(string: typing.Optional[str], format: Qt.DateFormat = ...) -> 'QTime': ...
    @typing.overload
    @staticmethod
    def fromString(s: typing.Optional[str], format: typing.Optional[str]) -> 'QTime': ...
    @staticmethod
    def currentTime() -> 'QTime': ...
    def __ge__(self, other: typing.Union['QTime', datetime.time]) -> bool: ...
    def __gt__(self, other: typing.Union['QTime', datetime.time]) -> bool: ...
    def __le__(self, other: typing.Union['QTime', datetime.time]) -> bool: ...
    def __lt__(self, other: typing.Union['QTime', datetime.time]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def msecsTo(self, a0: typing.Union['QTime', datetime.time]) -> int: ...
    def addMSecs(self, ms: int) -> 'QTime': ...
    def secsTo(self, a0: typing.Union['QTime', datetime.time]) -> int: ...
    def addSecs(self, secs: int) -> 'QTime': ...
    def setHMS(self, h: int, m: int, s: int, msec: int = ...) -> bool: ...
    @typing.overload
    def toString(self, format: Qt.DateFormat = ...) -> str: ...
    @typing.overload
    def toString(self, format: typing.Optional[str]) -> str: ...
    def msec(self) -> int: ...
    def second(self) -> int: ...
    def minute(self) -> int: ...
    def hour(self) -> int: ...
    @typing.overload
    def isValid(self) -> bool: ...
    @typing.overload
    @staticmethod
    def isValid(h: int, m: int, s: int, msec: int = ...) -> bool: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def toPyTime(self) -> datetime.time: ...
    def __hash__(self) -> int: ...
    def __repr__(self) -> str: ...


class QDateTime(PyQt5.sipsimplewrapper):

    class YearRange(int):
        First = ... # type: QDateTime.YearRange
        Last = ... # type: QDateTime.YearRange

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: typing.Union['QDateTime', datetime.datetime]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Union[QDate, datetime.date]) -> None: ...
    @typing.overload
    def __init__(self, date: typing.Union[QDate, datetime.date], time: typing.Union[QTime, datetime.time], timeSpec: Qt.TimeSpec = ...) -> None: ...
    @typing.overload
    def __init__(self, year: int, month: int, day: int, hour: int, minute: int, second: int = ..., msec: int = ..., timeSpec: int = ...) -> None: ...
    @typing.overload
    def __init__(self, date: typing.Union[QDate, datetime.date], time: typing.Union[QTime, datetime.time], spec: Qt.TimeSpec, offsetSeconds: int) -> None: ...
    @typing.overload
    def __init__(self, date: typing.Union[QDate, datetime.date], time: typing.Union[QTime, datetime.time], timeZone: 'QTimeZone') -> None: ...

    @staticmethod
    def currentSecsSinceEpoch() -> int: ...
    @typing.overload
    @staticmethod
    def fromSecsSinceEpoch(secs: int, spec: Qt.TimeSpec = ..., offsetSeconds: int = ...) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromSecsSinceEpoch(secs: int, timeZone: 'QTimeZone') -> 'QDateTime': ...
    def setSecsSinceEpoch(self, secs: int) -> None: ...
    def toSecsSinceEpoch(self) -> int: ...
    def toTimeZone(self, toZone: 'QTimeZone') -> 'QDateTime': ...
    def toOffsetFromUtc(self, offsetSeconds: int) -> 'QDateTime': ...
    def setTimeZone(self, toZone: 'QTimeZone') -> None: ...
    def setOffsetFromUtc(self, offsetSeconds: int) -> None: ...
    def isDaylightTime(self) -> bool: ...
    def timeZoneAbbreviation(self) -> str: ...
    def timeZone(self) -> 'QTimeZone': ...
    def offsetFromUtc(self) -> int: ...
    def swap(self, other: 'QDateTime') -> None: ...
    @staticmethod
    def currentMSecsSinceEpoch() -> int: ...
    @typing.overload
    @staticmethod
    def fromMSecsSinceEpoch(msecs: int) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromMSecsSinceEpoch(msecs: int, spec: Qt.TimeSpec, offsetSeconds: int = ...) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromMSecsSinceEpoch(msecs: int, timeZone: 'QTimeZone') -> 'QDateTime': ...
    @staticmethod
    def currentDateTimeUtc() -> 'QDateTime': ...
    def msecsTo(self, a0: typing.Union['QDateTime', datetime.datetime]) -> int: ...
    def setMSecsSinceEpoch(self, msecs: int) -> None: ...
    def toMSecsSinceEpoch(self) -> int: ...
    @typing.overload
    @staticmethod
    def fromTime_t(secsSince1Jan1970UTC: int) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromTime_t(secsSince1Jan1970UTC: int, spec: Qt.TimeSpec, offsetSeconds: int = ...) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromTime_t(secsSince1Jan1970UTC: int, timeZone: 'QTimeZone') -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromString(string: typing.Optional[str], format: Qt.DateFormat = ...) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromString(s: typing.Optional[str], format: typing.Optional[str]) -> 'QDateTime': ...
    @typing.overload
    @staticmethod
    def fromString(s: typing.Optional[str], format: typing.Optional[str], cal: QCalendar) -> 'QDateTime': ...
    @staticmethod
    def currentDateTime() -> 'QDateTime': ...
    def __ge__(self, other: typing.Union['QDateTime', datetime.datetime]) -> bool: ...
    def __gt__(self, other: typing.Union['QDateTime', datetime.datetime]) -> bool: ...
    def __le__(self, other: typing.Union['QDateTime', datetime.datetime]) -> bool: ...
    def __lt__(self, other: typing.Union['QDateTime', datetime.datetime]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def secsTo(self, a0: typing.Union['QDateTime', datetime.datetime]) -> int: ...
    def daysTo(self, a0: typing.Union['QDateTime', datetime.datetime]) -> int: ...
    def toUTC(self) -> 'QDateTime': ...
    def toLocalTime(self) -> 'QDateTime': ...
    def toTimeSpec(self, spec: Qt.TimeSpec) -> 'QDateTime': ...
    def addMSecs(self, msecs: int) -> 'QDateTime': ...
    def addSecs(self, secs: int) -> 'QDateTime': ...
    def addYears(self, years: int) -> 'QDateTime': ...
    def addMonths(self, months: int) -> 'QDateTime': ...
    def addDays(self, days: int) -> 'QDateTime': ...
    @typing.overload
    def toString(self, format: Qt.DateFormat = ...) -> str: ...
    @typing.overload
    def toString(self, format: typing.Optional[str]) -> str: ...
    @typing.overload
    def toString(self, format: typing.Optional[str], cal: QCalendar) -> str: ...
    def setTime_t(self, secsSince1Jan1970UTC: int) -> None: ...
    def setTimeSpec(self, spec: Qt.TimeSpec) -> None: ...
    def setTime(self, time: typing.Union[QTime, datetime.time]) -> None: ...
    def setDate(self, date: typing.Union[QDate, datetime.date]) -> None: ...
    def toTime_t(self) -> int: ...
    def timeSpec(self) -> Qt.TimeSpec: ...
    def time(self) -> QTime: ...
    def date(self) -> QDate: ...
    def isValid(self) -> bool: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def toPyDateTime(self) -> datetime.datetime: ...
    def __hash__(self) -> int: ...
    def __repr__(self) -> str: ...


class QDeadlineTimer(PyQt5.sipsimplewrapper):

    class ForeverConstant(int):
        Forever = ... # type: QDeadlineTimer.ForeverConstant

    @typing.overload
    def __init__(self, type: Qt.TimerType = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDeadlineTimer.ForeverConstant', type: Qt.TimerType = ...) -> None: ...
    @typing.overload
    def __init__(self, msecs: int, type: Qt.TimerType = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDeadlineTimer') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __lt__(self, d2: 'QDeadlineTimer') -> bool: ...
    def __le__(self, d2: 'QDeadlineTimer') -> bool: ...
    def __gt__(self, d2: 'QDeadlineTimer') -> bool: ...
    def __ge__(self, d2: 'QDeadlineTimer') -> bool: ...
    def __add__(self, msecs: int) -> 'QDeadlineTimer': ...
    def __radd__(self, msecs: int) -> 'QDeadlineTimer': ...
    @typing.overload
    def __sub__(self, msecs: int) -> 'QDeadlineTimer': ...
    @typing.overload
    def __sub__(self, dt2: 'QDeadlineTimer') -> int: ...
    def __isub__(self, msecs: int) -> 'QDeadlineTimer': ...
    def __iadd__(self, msecs: int) -> 'QDeadlineTimer': ...
    @staticmethod
    def current(type: Qt.TimerType = ...) -> 'QDeadlineTimer': ...
    @staticmethod
    def addNSecs(dt: 'QDeadlineTimer', nsecs: int) -> 'QDeadlineTimer': ...
    def setPreciseDeadline(self, secs: int, nsecs: int = ..., type: Qt.TimerType = ...) -> None: ...
    def setDeadline(self, msecs: int, type: Qt.TimerType = ...) -> None: ...
    def deadlineNSecs(self) -> int: ...
    def deadline(self) -> int: ...
    def setPreciseRemainingTime(self, secs: int, nsecs: int = ..., type: Qt.TimerType = ...) -> None: ...
    def setRemainingTime(self, msecs: int, type: Qt.TimerType = ...) -> None: ...
    def remainingTimeNSecs(self) -> int: ...
    def remainingTime(self) -> int: ...
    def setTimerType(self, type: Qt.TimerType) -> None: ...
    def timerType(self) -> Qt.TimerType: ...
    def hasExpired(self) -> bool: ...
    def isForever(self) -> bool: ...
    def swap(self, other: 'QDeadlineTimer') -> None: ...


class QDir(PyQt5.sipsimplewrapper):

    class SortFlag(int):
        Name = ... # type: QDir.SortFlag
        Time = ... # type: QDir.SortFlag
        Size = ... # type: QDir.SortFlag
        Unsorted = ... # type: QDir.SortFlag
        SortByMask = ... # type: QDir.SortFlag
        DirsFirst = ... # type: QDir.SortFlag
        Reversed = ... # type: QDir.SortFlag
        IgnoreCase = ... # type: QDir.SortFlag
        DirsLast = ... # type: QDir.SortFlag
        LocaleAware = ... # type: QDir.SortFlag
        Type = ... # type: QDir.SortFlag
        NoSort = ... # type: QDir.SortFlag

    class Filter(int):
        Dirs = ... # type: QDir.Filter
        Files = ... # type: QDir.Filter
        Drives = ... # type: QDir.Filter
        NoSymLinks = ... # type: QDir.Filter
        AllEntries = ... # type: QDir.Filter
        TypeMask = ... # type: QDir.Filter
        Readable = ... # type: QDir.Filter
        Writable = ... # type: QDir.Filter
        Executable = ... # type: QDir.Filter
        PermissionMask = ... # type: QDir.Filter
        Modified = ... # type: QDir.Filter
        Hidden = ... # type: QDir.Filter
        System = ... # type: QDir.Filter
        AccessMask = ... # type: QDir.Filter
        AllDirs = ... # type: QDir.Filter
        CaseSensitive = ... # type: QDir.Filter
        NoDotAndDotDot = ... # type: QDir.Filter
        NoFilter = ... # type: QDir.Filter
        NoDot = ... # type: QDir.Filter
        NoDotDot = ... # type: QDir.Filter

    class Filters(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __xor__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __ior__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __or__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __iand__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __and__(self, f: typing.Union['QDir.Filters', 'QDir.Filter']) -> 'QDir.Filters': ...
        def __invert__(self) -> 'QDir.Filters': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class SortFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __xor__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __ior__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __or__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __iand__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __and__(self, f: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> 'QDir.SortFlags': ...
        def __invert__(self) -> 'QDir.SortFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, a0: 'QDir') -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str], nameFilter: typing.Optional[str], sort: 'QDir.SortFlags' = ..., filters: 'QDir.Filters' = ...) -> None: ...

    def isEmpty(self, filters: typing.Union['QDir.Filters', 'QDir.Filter'] = ...) -> bool: ...
    @staticmethod
    def listSeparator() -> str: ...
    def swap(self, other: 'QDir') -> None: ...
    def removeRecursively(self) -> bool: ...
    @staticmethod
    def searchPaths(prefix: typing.Optional[str]) -> typing.List[str]: ...
    @staticmethod
    def addSearchPath(prefix: typing.Optional[str], path: typing.Optional[str]) -> None: ...
    @staticmethod
    def setSearchPaths(prefix: typing.Optional[str], searchPaths: typing.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def fromNativeSeparators(pathName: typing.Optional[str]) -> str: ...
    @staticmethod
    def toNativeSeparators(pathName: typing.Optional[str]) -> str: ...
    @staticmethod
    def cleanPath(path: typing.Optional[str]) -> str: ...
    @typing.overload
    @staticmethod
    def match(filters: typing.Iterable[typing.Optional[str]], fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def match(filter: typing.Optional[str], fileName: typing.Optional[str]) -> bool: ...
    @staticmethod
    def tempPath() -> str: ...
    @staticmethod
    def temp() -> 'QDir': ...
    @staticmethod
    def rootPath() -> str: ...
    @staticmethod
    def root() -> 'QDir': ...
    @staticmethod
    def homePath() -> str: ...
    @staticmethod
    def home() -> 'QDir': ...
    @staticmethod
    def currentPath() -> str: ...
    @staticmethod
    def current() -> 'QDir': ...
    @staticmethod
    def setCurrent(path: typing.Optional[str]) -> bool: ...
    @staticmethod
    def separator() -> str: ...
    @staticmethod
    def drives() -> typing.List['QFileInfo']: ...
    def refresh(self) -> None: ...
    def rename(self, oldName: typing.Optional[str], newName: typing.Optional[str]) -> bool: ...
    def remove(self, fileName: typing.Optional[str]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def makeAbsolute(self) -> bool: ...
    def isAbsolute(self) -> bool: ...
    def isRelative(self) -> bool: ...
    @staticmethod
    def isAbsolutePath(path: typing.Optional[str]) -> bool: ...
    @staticmethod
    def isRelativePath(path: typing.Optional[str]) -> bool: ...
    def isRoot(self) -> bool: ...
    @typing.overload
    def exists(self) -> bool: ...
    @typing.overload
    def exists(self, name: typing.Optional[str]) -> bool: ...
    def isReadable(self) -> bool: ...
    def rmpath(self, dirPath: typing.Optional[str]) -> bool: ...
    def mkpath(self, dirPath: typing.Optional[str]) -> bool: ...
    def rmdir(self, dirName: typing.Optional[str]) -> bool: ...
    def mkdir(self, dirName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def entryInfoList(self, filters: typing.Union['QDir.Filters', 'QDir.Filter'] = ..., sort: typing.Union['QDir.SortFlags', 'QDir.SortFlag'] = ...) -> typing.List['QFileInfo']: ...
    @typing.overload
    def entryInfoList(self, nameFilters: typing.Iterable[typing.Optional[str]], filters: typing.Union['QDir.Filters', 'QDir.Filter'] = ..., sort: typing.Union['QDir.SortFlags', 'QDir.SortFlag'] = ...) -> typing.List['QFileInfo']: ...
    @typing.overload
    def entryList(self, filters: typing.Union['QDir.Filters', 'QDir.Filter'] = ..., sort: typing.Union['QDir.SortFlags', 'QDir.SortFlag'] = ...) -> typing.List[str]: ...
    @typing.overload
    def entryList(self, nameFilters: typing.Iterable[typing.Optional[str]], filters: typing.Union['QDir.Filters', 'QDir.Filter'] = ..., sort: typing.Union['QDir.SortFlags', 'QDir.SortFlag'] = ...) -> typing.List[str]: ...
    @staticmethod
    def nameFiltersFromString(nameFilter: typing.Optional[str]) -> typing.List[str]: ...
    def __contains__(self, a0: typing.Optional[str]) -> int: ...
    @typing.overload
    def __getitem__(self, a0: int) -> str: ...
    @typing.overload
    def __getitem__(self, a0: slice) -> typing.List[str]: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def setSorting(self, sort: typing.Union['QDir.SortFlags', 'QDir.SortFlag']) -> None: ...
    def sorting(self) -> 'QDir.SortFlags': ...
    def setFilter(self, filter: typing.Union['QDir.Filters', 'QDir.Filter']) -> None: ...
    def filter(self) -> 'QDir.Filters': ...
    def setNameFilters(self, nameFilters: typing.Iterable[typing.Optional[str]]) -> None: ...
    def nameFilters(self) -> typing.List[str]: ...
    def cdUp(self) -> bool: ...
    def cd(self, dirName: typing.Optional[str]) -> bool: ...
    def relativeFilePath(self, fileName: typing.Optional[str]) -> str: ...
    def absoluteFilePath(self, fileName: typing.Optional[str]) -> str: ...
    def filePath(self, fileName: typing.Optional[str]) -> str: ...
    def dirName(self) -> str: ...
    def canonicalPath(self) -> str: ...
    def absolutePath(self) -> str: ...
    def path(self) -> str: ...
    def setPath(self, path: typing.Optional[str]) -> None: ...


class QDirIterator(PyQt5.sipsimplewrapper):

    class IteratorFlag(int):
        NoIteratorFlags = ... # type: QDirIterator.IteratorFlag
        FollowSymlinks = ... # type: QDirIterator.IteratorFlag
        Subdirectories = ... # type: QDirIterator.IteratorFlag

    class IteratorFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __xor__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __ior__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __or__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __iand__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __and__(self, f: typing.Union['QDirIterator.IteratorFlags', 'QDirIterator.IteratorFlag']) -> 'QDirIterator.IteratorFlags': ...
        def __invert__(self) -> 'QDirIterator.IteratorFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, dir: QDir, flags: 'QDirIterator.IteratorFlags' = ...) -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str], flags: 'QDirIterator.IteratorFlags' = ...) -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str], filters: QDir.Filters, flags: 'QDirIterator.IteratorFlags' = ...) -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str], nameFilters: typing.Iterable[typing.Optional[str]], filters: QDir.Filters = ..., flags: 'QDirIterator.IteratorFlags' = ...) -> None: ...

    def path(self) -> str: ...
    def fileInfo(self) -> 'QFileInfo': ...
    def filePath(self) -> str: ...
    def fileName(self) -> str: ...
    def hasNext(self) -> bool: ...
    def next(self) -> str: ...


class QEasingCurve(PyQt5.sipsimplewrapper):

    class Type(int):
        Linear = ... # type: QEasingCurve.Type
        InQuad = ... # type: QEasingCurve.Type
        OutQuad = ... # type: QEasingCurve.Type
        InOutQuad = ... # type: QEasingCurve.Type
        OutInQuad = ... # type: QEasingCurve.Type
        InCubic = ... # type: QEasingCurve.Type
        OutCubic = ... # type: QEasingCurve.Type
        InOutCubic = ... # type: QEasingCurve.Type
        OutInCubic = ... # type: QEasingCurve.Type
        InQuart = ... # type: QEasingCurve.Type
        OutQuart = ... # type: QEasingCurve.Type
        InOutQuart = ... # type: QEasingCurve.Type
        OutInQuart = ... # type: QEasingCurve.Type
        InQuint = ... # type: QEasingCurve.Type
        OutQuint = ... # type: QEasingCurve.Type
        InOutQuint = ... # type: QEasingCurve.Type
        OutInQuint = ... # type: QEasingCurve.Type
        InSine = ... # type: QEasingCurve.Type
        OutSine = ... # type: QEasingCurve.Type
        InOutSine = ... # type: QEasingCurve.Type
        OutInSine = ... # type: QEasingCurve.Type
        InExpo = ... # type: QEasingCurve.Type
        OutExpo = ... # type: QEasingCurve.Type
        InOutExpo = ... # type: QEasingCurve.Type
        OutInExpo = ... # type: QEasingCurve.Type
        InCirc = ... # type: QEasingCurve.Type
        OutCirc = ... # type: QEasingCurve.Type
        InOutCirc = ... # type: QEasingCurve.Type
        OutInCirc = ... # type: QEasingCurve.Type
        InElastic = ... # type: QEasingCurve.Type
        OutElastic = ... # type: QEasingCurve.Type
        InOutElastic = ... # type: QEasingCurve.Type
        OutInElastic = ... # type: QEasingCurve.Type
        InBack = ... # type: QEasingCurve.Type
        OutBack = ... # type: QEasingCurve.Type
        InOutBack = ... # type: QEasingCurve.Type
        OutInBack = ... # type: QEasingCurve.Type
        InBounce = ... # type: QEasingCurve.Type
        OutBounce = ... # type: QEasingCurve.Type
        InOutBounce = ... # type: QEasingCurve.Type
        OutInBounce = ... # type: QEasingCurve.Type
        InCurve = ... # type: QEasingCurve.Type
        OutCurve = ... # type: QEasingCurve.Type
        SineCurve = ... # type: QEasingCurve.Type
        CosineCurve = ... # type: QEasingCurve.Type
        BezierSpline = ... # type: QEasingCurve.Type
        TCBSpline = ... # type: QEasingCurve.Type
        Custom = ... # type: QEasingCurve.Type

    @typing.overload
    def __init__(self, type: 'QEasingCurve.Type' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: typing.Union['QEasingCurve', 'QEasingCurve.Type']) -> None: ...

    def toCubicSpline(self) -> typing.List['QPointF']: ...
    def addTCBSegment(self, nextPoint: typing.Union['QPointF', 'QPoint'], t: float, c: float, b: float) -> None: ...
    def addCubicBezierSegment(self, c1: typing.Union['QPointF', 'QPoint'], c2: typing.Union['QPointF', 'QPoint'], endPoint: typing.Union['QPointF', 'QPoint']) -> None: ...
    def swap(self, other: 'QEasingCurve') -> None: ...
    def valueForProgress(self, progress: float) -> float: ...
    def customType(self) -> typing.Callable[[float], float]: ...
    def setCustomType(self, func: typing.Callable[[float], float]) -> None: ...
    def setType(self, type: 'QEasingCurve.Type') -> None: ...
    def type(self) -> 'QEasingCurve.Type': ...
    def setOvershoot(self, overshoot: float) -> None: ...
    def overshoot(self) -> float: ...
    def setPeriod(self, period: float) -> None: ...
    def period(self) -> float: ...
    def setAmplitude(self, amplitude: float) -> None: ...
    def amplitude(self) -> float: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QElapsedTimer(PyQt5.sipsimplewrapper):

    class ClockType(int):
        SystemTime = ... # type: QElapsedTimer.ClockType
        MonotonicClock = ... # type: QElapsedTimer.ClockType
        TickCounter = ... # type: QElapsedTimer.ClockType
        MachAbsoluteTime = ... # type: QElapsedTimer.ClockType
        PerformanceCounter = ... # type: QElapsedTimer.ClockType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QElapsedTimer') -> None: ...

    def __ge__(self, v2: 'QElapsedTimer') -> bool: ...
    def __lt__(self, v2: 'QElapsedTimer') -> bool: ...
    def nsecsElapsed(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def secsTo(self, other: 'QElapsedTimer') -> int: ...
    def msecsTo(self, other: 'QElapsedTimer') -> int: ...
    def msecsSinceReference(self) -> int: ...
    def hasExpired(self, timeout: int) -> bool: ...
    def elapsed(self) -> int: ...
    def isValid(self) -> bool: ...
    def invalidate(self) -> None: ...
    def restart(self) -> int: ...
    def start(self) -> None: ...
    @staticmethod
    def isMonotonic() -> bool: ...
    @staticmethod
    def clockType() -> 'QElapsedTimer.ClockType': ...


class QEventLoop(QObject):

    class ProcessEventsFlag(int):
        AllEvents = ... # type: QEventLoop.ProcessEventsFlag
        ExcludeUserInputEvents = ... # type: QEventLoop.ProcessEventsFlag
        ExcludeSocketNotifiers = ... # type: QEventLoop.ProcessEventsFlag
        WaitForMoreEvents = ... # type: QEventLoop.ProcessEventsFlag
        X11ExcludeTimers = ... # type: QEventLoop.ProcessEventsFlag

    class ProcessEventsFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __xor__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __ior__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __or__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __iand__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __and__(self, f: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag']) -> 'QEventLoop.ProcessEventsFlags': ...
        def __invert__(self) -> 'QEventLoop.ProcessEventsFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    def quit(self) -> None: ...
    def wakeUp(self) -> None: ...
    def isRunning(self) -> bool: ...
    def exit(self, returnCode: int = ...) -> None: ...
    def exec(self, flags: 'QEventLoop.ProcessEventsFlags' = ...) -> int: ...
    def exec_(self, flags: 'QEventLoop.ProcessEventsFlags' = ...) -> int: ...
    @typing.overload
    def processEvents(self, flags: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag'] = ...) -> bool: ...
    @typing.overload
    def processEvents(self, flags: typing.Union['QEventLoop.ProcessEventsFlags', 'QEventLoop.ProcessEventsFlag'], maximumTime: int) -> None: ...


class QEventLoopLocker(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, loop: typing.Optional[QEventLoop]) -> None: ...
    @typing.overload
    def __init__(self, thread: typing.Optional['QThread']) -> None: ...


class QEventTransition(QAbstractTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, object: typing.Optional[QObject], type: QEvent.Type, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QEvent]) -> None: ...
    def eventTest(self, event: typing.Optional[QEvent]) -> bool: ...
    def setEventType(self, type: QEvent.Type) -> None: ...
    def eventType(self) -> QEvent.Type: ...
    def setEventSource(self, object: typing.Optional[QObject]) -> None: ...
    def eventSource(self) -> typing.Optional[QObject]: ...


class QFileDevice(QIODevice):

    class FileTime(int):
        FileAccessTime = ... # type: QFileDevice.FileTime
        FileBirthTime = ... # type: QFileDevice.FileTime
        FileMetadataChangeTime = ... # type: QFileDevice.FileTime
        FileModificationTime = ... # type: QFileDevice.FileTime

    class MemoryMapFlags(int):
        NoOptions = ... # type: QFileDevice.MemoryMapFlags
        MapPrivateOption = ... # type: QFileDevice.MemoryMapFlags

    class FileHandleFlag(int):
        AutoCloseHandle = ... # type: QFileDevice.FileHandleFlag
        DontCloseHandle = ... # type: QFileDevice.FileHandleFlag

    class Permission(int):
        ReadOwner = ... # type: QFileDevice.Permission
        WriteOwner = ... # type: QFileDevice.Permission
        ExeOwner = ... # type: QFileDevice.Permission
        ReadUser = ... # type: QFileDevice.Permission
        WriteUser = ... # type: QFileDevice.Permission
        ExeUser = ... # type: QFileDevice.Permission
        ReadGroup = ... # type: QFileDevice.Permission
        WriteGroup = ... # type: QFileDevice.Permission
        ExeGroup = ... # type: QFileDevice.Permission
        ReadOther = ... # type: QFileDevice.Permission
        WriteOther = ... # type: QFileDevice.Permission
        ExeOther = ... # type: QFileDevice.Permission

    class FileError(int):
        NoError = ... # type: QFileDevice.FileError
        ReadError = ... # type: QFileDevice.FileError
        WriteError = ... # type: QFileDevice.FileError
        FatalError = ... # type: QFileDevice.FileError
        ResourceError = ... # type: QFileDevice.FileError
        OpenError = ... # type: QFileDevice.FileError
        AbortError = ... # type: QFileDevice.FileError
        TimeOutError = ... # type: QFileDevice.FileError
        UnspecifiedError = ... # type: QFileDevice.FileError
        RemoveError = ... # type: QFileDevice.FileError
        RenameError = ... # type: QFileDevice.FileError
        PositionError = ... # type: QFileDevice.FileError
        ResizeError = ... # type: QFileDevice.FileError
        PermissionsError = ... # type: QFileDevice.FileError
        CopyError = ... # type: QFileDevice.FileError

    class Permissions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __xor__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __ior__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __or__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __iand__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __and__(self, f: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> 'QFileDevice.Permissions': ...
        def __invert__(self) -> 'QFileDevice.Permissions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FileHandleFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __xor__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __ior__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __or__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __iand__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __and__(self, f: typing.Union['QFileDevice.FileHandleFlags', 'QFileDevice.FileHandleFlag']) -> 'QFileDevice.FileHandleFlags': ...
        def __invert__(self) -> 'QFileDevice.FileHandleFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def setFileTime(self, newDate: typing.Union[QDateTime, datetime.datetime], fileTime: 'QFileDevice.FileTime') -> bool: ...
    def fileTime(self, time: 'QFileDevice.FileTime') -> QDateTime: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    def unmap(self, address: typing.Optional[PyQt5.sip.voidptr]) -> bool: ...
    def map(self, offset: int, size: int, flags: 'QFileDevice.MemoryMapFlags' = ...) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def setPermissions(self, permissionSpec: typing.Union['QFileDevice.Permissions', 'QFileDevice.Permission']) -> bool: ...
    def permissions(self) -> 'QFileDevice.Permissions': ...
    def resize(self, sz: int) -> bool: ...
    def size(self) -> int: ...
    def flush(self) -> bool: ...
    def atEnd(self) -> bool: ...
    def seek(self, offset: int) -> bool: ...
    def pos(self) -> int: ...
    def fileName(self) -> str: ...
    def handle(self) -> int: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    def unsetError(self) -> None: ...
    def error(self) -> 'QFileDevice.FileError': ...


class QFile(QFileDevice):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QObject]) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QObject]) -> None: ...

    @typing.overload
    def moveToTrash(self) -> bool: ...
    @typing.overload
    @staticmethod
    def moveToTrash(fileName: typing.Optional[str]) -> typing.Tuple[bool, typing.Optional[str]]: ...
    @typing.overload
    def setPermissions(self, permissionSpec: typing.Union[QFileDevice.Permissions, QFileDevice.Permission]) -> bool: ...
    @typing.overload
    @staticmethod
    def setPermissions(filename: typing.Optional[str], permissionSpec: typing.Union[QFileDevice.Permissions, QFileDevice.Permission]) -> bool: ...
    @typing.overload
    def permissions(self) -> QFileDevice.Permissions: ...
    @typing.overload
    @staticmethod
    def permissions(filename: typing.Optional[str]) -> QFileDevice.Permissions: ...
    @typing.overload
    def resize(self, sz: int) -> bool: ...
    @typing.overload
    @staticmethod
    def resize(filename: typing.Optional[str], sz: int) -> bool: ...
    def size(self) -> int: ...
    @typing.overload
    def open(self, flags: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag]) -> bool: ...
    @typing.overload
    def open(self, fd: int, ioFlags: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag], handleFlags: typing.Union[QFileDevice.FileHandleFlags, QFileDevice.FileHandleFlag] = ...) -> bool: ...
    @typing.overload
    def copy(self, newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def copy(fileName: typing.Optional[str], newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def link(self, newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def link(oldname: typing.Optional[str], newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def rename(self, newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def rename(oldName: typing.Optional[str], newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def remove(self) -> bool: ...
    @typing.overload
    @staticmethod
    def remove(fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def symLinkTarget(self) -> str: ...
    @typing.overload
    @staticmethod
    def symLinkTarget(fileName: typing.Optional[str]) -> str: ...
    @typing.overload
    def exists(self) -> bool: ...
    @typing.overload
    @staticmethod
    def exists(fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def decodeName(localFileName: typing.Union[QByteArray, bytes, bytearray]) -> str: ...
    @typing.overload
    @staticmethod
    def decodeName(localFileName: typing.Optional[str]) -> str: ...
    @staticmethod
    def encodeName(fileName: typing.Optional[str]) -> QByteArray: ...
    def setFileName(self, name: typing.Optional[str]) -> None: ...
    def fileName(self) -> str: ...


class QFileInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, file: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, file: QFile) -> None: ...
    @typing.overload
    def __init__(self, dir: QDir, file: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, fileinfo: 'QFileInfo') -> None: ...

    def isJunction(self) -> bool: ...
    def isShortcut(self) -> bool: ...
    def isSymbolicLink(self) -> bool: ...
    def fileTime(self, time: QFileDevice.FileTime) -> QDateTime: ...
    def metadataChangeTime(self) -> QDateTime: ...
    def birthTime(self) -> QDateTime: ...
    def swap(self, other: 'QFileInfo') -> None: ...
    def isNativePath(self) -> bool: ...
    def isBundle(self) -> bool: ...
    def bundleName(self) -> str: ...
    def symLinkTarget(self) -> str: ...
    def setCaching(self, on: bool) -> None: ...
    def caching(self) -> bool: ...
    def lastRead(self) -> QDateTime: ...
    def lastModified(self) -> QDateTime: ...
    def created(self) -> QDateTime: ...
    def size(self) -> int: ...
    def permissions(self) -> QFileDevice.Permissions: ...
    def permission(self, permissions: typing.Union[QFileDevice.Permissions, QFileDevice.Permission]) -> bool: ...
    def groupId(self) -> int: ...
    def group(self) -> str: ...
    def ownerId(self) -> int: ...
    def owner(self) -> str: ...
    def isRoot(self) -> bool: ...
    def isSymLink(self) -> bool: ...
    def isDir(self) -> bool: ...
    def isFile(self) -> bool: ...
    def makeAbsolute(self) -> bool: ...
    def isAbsolute(self) -> bool: ...
    def isRelative(self) -> bool: ...
    def isHidden(self) -> bool: ...
    def isExecutable(self) -> bool: ...
    def isWritable(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def absoluteDir(self) -> QDir: ...
    def dir(self) -> QDir: ...
    def canonicalPath(self) -> str: ...
    def absolutePath(self) -> str: ...
    def path(self) -> str: ...
    def completeSuffix(self) -> str: ...
    def suffix(self) -> str: ...
    def completeBaseName(self) -> str: ...
    def baseName(self) -> str: ...
    def fileName(self) -> str: ...
    def canonicalFilePath(self) -> str: ...
    def absoluteFilePath(self) -> str: ...
    def __fspath__(self) -> typing.Any: ...
    def filePath(self) -> str: ...
    def refresh(self) -> None: ...
    @typing.overload
    def exists(self) -> bool: ...
    @typing.overload
    @staticmethod
    def exists(file: typing.Optional[str]) -> bool: ...
    @typing.overload
    def setFile(self, file: typing.Optional[str]) -> None: ...
    @typing.overload
    def setFile(self, file: QFile) -> None: ...
    @typing.overload
    def setFile(self, dir: QDir, file: typing.Optional[str]) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QFileSelector(QObject):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def allSelectors(self) -> typing.List[str]: ...
    def setExtraSelectors(self, list: typing.Iterable[typing.Optional[str]]) -> None: ...
    def extraSelectors(self) -> typing.List[str]: ...
    @typing.overload
    def select(self, filePath: typing.Optional[str]) -> str: ...
    @typing.overload
    def select(self, filePath: 'QUrl') -> 'QUrl': ...


class QFileSystemWatcher(QObject):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, paths: typing.Iterable[typing.Optional[str]], parent: typing.Optional[QObject] = ...) -> None: ...

    fileChanged: typing.ClassVar[pyqtSignal]
    directoryChanged: typing.ClassVar[pyqtSignal]
    def removePaths(self, files: typing.Iterable[typing.Optional[str]]) -> typing.List[str]: ...
    def removePath(self, file: typing.Optional[str]) -> bool: ...
    def files(self) -> typing.List[str]: ...
    def directories(self) -> typing.List[str]: ...
    def addPaths(self, files: typing.Iterable[typing.Optional[str]]) -> typing.List[str]: ...
    def addPath(self, file: typing.Optional[str]) -> bool: ...


class QFinalState(QAbstractState):

    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QEvent]) -> None: ...


class QHistoryState(QAbstractState):

    class HistoryType(int):
        ShallowHistory = ... # type: QHistoryState.HistoryType
        DeepHistory = ... # type: QHistoryState.HistoryType

    @typing.overload
    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QHistoryState.HistoryType', parent: typing.Optional['QState'] = ...) -> None: ...

    defaultTransitionChanged: typing.ClassVar[pyqtSignal]
    def setDefaultTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    def defaultTransition(self) -> typing.Optional[QAbstractTransition]: ...
    historyTypeChanged: typing.ClassVar[pyqtSignal]
    defaultStateChanged: typing.ClassVar[pyqtSignal]
    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QEvent]) -> None: ...
    def setHistoryType(self, type: 'QHistoryState.HistoryType') -> None: ...
    def historyType(self) -> 'QHistoryState.HistoryType': ...
    def setDefaultState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def defaultState(self) -> typing.Optional[QAbstractState]: ...


class QIdentityProxyModel(QAbstractProxyModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def moveColumns(self, sourceParent: QModelIndex, sourceColumn: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def moveRows(self, sourceParent: QModelIndex, sourceRow: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def removeRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def removeColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def setSourceModel(self, sourceModel: typing.Optional[QAbstractItemModel]) -> None: ...
    def match(self, start: QModelIndex, role: int, value: typing.Any, hits: int = ..., flags: typing.Union[Qt.MatchFlags, Qt.MatchFlag] = ...) -> typing.List[QModelIndex]: ...
    def mapSelectionToSource(self, selection: 'QItemSelection') -> 'QItemSelection': ...
    def mapSelectionFromSource(self, selection: 'QItemSelection') -> 'QItemSelection': ...
    def dropMimeData(self, data: typing.Optional['QMimeData'], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...
    def parent(self, child: QModelIndex) -> QModelIndex: ...
    def mapToSource(self, proxyIndex: QModelIndex) -> QModelIndex: ...
    def mapFromSource(self, sourceIndex: QModelIndex) -> QModelIndex: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...
    def columnCount(self, parent: QModelIndex = ...) -> int: ...


class QItemSelectionRange(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QItemSelectionRange') -> None: ...
    @typing.overload
    def __init__(self, atopLeft: QModelIndex, abottomRight: QModelIndex) -> None: ...
    @typing.overload
    def __init__(self, index: QModelIndex) -> None: ...

    def __ge__(self, other: 'QItemSelectionRange') -> bool: ...
    def swap(self, other: 'QItemSelectionRange') -> None: ...
    def __lt__(self, other: 'QItemSelectionRange') -> bool: ...
    def isEmpty(self) -> bool: ...
    def __hash__(self) -> int: ...
    def intersected(self, other: 'QItemSelectionRange') -> 'QItemSelectionRange': ...
    def indexes(self) -> typing.List[QModelIndex]: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def intersects(self, other: 'QItemSelectionRange') -> bool: ...
    @typing.overload
    def contains(self, index: QModelIndex) -> bool: ...
    @typing.overload
    def contains(self, row: int, column: int, parentIndex: QModelIndex) -> bool: ...
    def model(self) -> typing.Optional[QAbstractItemModel]: ...
    def parent(self) -> QModelIndex: ...
    def bottomRight(self) -> QPersistentModelIndex: ...
    def topLeft(self) -> QPersistentModelIndex: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def right(self) -> int: ...
    def bottom(self) -> int: ...
    def left(self) -> int: ...
    def top(self) -> int: ...


class QItemSelectionModel(QObject):

    class SelectionFlag(int):
        NoUpdate = ... # type: QItemSelectionModel.SelectionFlag
        Clear = ... # type: QItemSelectionModel.SelectionFlag
        Select = ... # type: QItemSelectionModel.SelectionFlag
        Deselect = ... # type: QItemSelectionModel.SelectionFlag
        Toggle = ... # type: QItemSelectionModel.SelectionFlag
        Current = ... # type: QItemSelectionModel.SelectionFlag
        Rows = ... # type: QItemSelectionModel.SelectionFlag
        Columns = ... # type: QItemSelectionModel.SelectionFlag
        SelectCurrent = ... # type: QItemSelectionModel.SelectionFlag
        ToggleCurrent = ... # type: QItemSelectionModel.SelectionFlag
        ClearAndSelect = ... # type: QItemSelectionModel.SelectionFlag

    class SelectionFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __xor__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __ior__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __or__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __iand__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __and__(self, f: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> 'QItemSelectionModel.SelectionFlags': ...
        def __invert__(self) -> 'QItemSelectionModel.SelectionFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, model: typing.Optional[QAbstractItemModel] = ...) -> None: ...
    @typing.overload
    def __init__(self, model: typing.Optional[QAbstractItemModel], parent: typing.Optional[QObject]) -> None: ...

    modelChanged: typing.ClassVar[pyqtSignal]
    def setModel(self, model: typing.Optional[QAbstractItemModel]) -> None: ...
    def selectedColumns(self, row: int = ...) -> typing.List[QModelIndex]: ...
    def selectedRows(self, column: int = ...) -> typing.List[QModelIndex]: ...
    def hasSelection(self) -> bool: ...
    def emitSelectionChanged(self, newSelection: 'QItemSelection', oldSelection: 'QItemSelection') -> None: ...
    currentColumnChanged: typing.ClassVar[pyqtSignal]
    currentRowChanged: typing.ClassVar[pyqtSignal]
    currentChanged: typing.ClassVar[pyqtSignal]
    selectionChanged: typing.ClassVar[pyqtSignal]
    def clearCurrentIndex(self) -> None: ...
    def setCurrentIndex(self, index: QModelIndex, command: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> None: ...
    @typing.overload
    def select(self, index: QModelIndex, command: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> None: ...
    @typing.overload
    def select(self, selection: 'QItemSelection', command: typing.Union['QItemSelectionModel.SelectionFlags', 'QItemSelectionModel.SelectionFlag']) -> None: ...
    def reset(self) -> None: ...
    def clearSelection(self) -> None: ...
    def clear(self) -> None: ...
    def model(self) -> typing.Optional[QAbstractItemModel]: ...
    def selection(self) -> 'QItemSelection': ...
    def selectedIndexes(self) -> typing.List[QModelIndex]: ...
    def columnIntersectsSelection(self, column: int, parent: QModelIndex = ...) -> bool: ...
    def rowIntersectsSelection(self, row: int, parent: QModelIndex = ...) -> bool: ...
    def isColumnSelected(self, column: int, parent: QModelIndex = ...) -> bool: ...
    def isRowSelected(self, row: int, parent: QModelIndex = ...) -> bool: ...
    def isSelected(self, index: QModelIndex) -> bool: ...
    def currentIndex(self) -> QModelIndex: ...


class QItemSelection(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, topLeft: QModelIndex, bottomRight: QModelIndex) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QItemSelection') -> None: ...

    @typing.overload
    def __iadd__(self, other: 'QItemSelection') -> 'QItemSelection': ...
    @typing.overload
    def __iadd__(self, value: QItemSelectionRange) -> 'QItemSelection': ...
    def lastIndexOf(self, value: QItemSelectionRange, from_: int = ...) -> int: ...
    def indexOf(self, value: QItemSelectionRange, from_: int = ...) -> int: ...
    def last(self) -> QItemSelectionRange: ...
    def first(self) -> QItemSelectionRange: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, range: QItemSelectionRange) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def swap(self, i: int, j: int) -> None: ...
    def move(self, from_: int, to: int) -> None: ...
    def takeLast(self) -> QItemSelectionRange: ...
    def takeFirst(self) -> QItemSelectionRange: ...
    def takeAt(self, i: int) -> QItemSelectionRange: ...
    def removeAll(self, range: QItemSelectionRange) -> int: ...
    def removeAt(self, i: int) -> None: ...
    def replace(self, i: int, range: QItemSelectionRange) -> None: ...
    def insert(self, i: int, range: QItemSelectionRange) -> None: ...
    def prepend(self, range: QItemSelectionRange) -> None: ...
    def append(self, range: QItemSelectionRange) -> None: ...
    def isEmpty(self) -> bool: ...
    def clear(self) -> None: ...
    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __getitem__(self, i: int) -> QItemSelectionRange: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QItemSelection': ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, range: QItemSelectionRange) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QItemSelection') -> None: ...
    @staticmethod
    def split(range: QItemSelectionRange, other: QItemSelectionRange, result: typing.Optional['QItemSelection']) -> None: ...
    def merge(self, other: 'QItemSelection', command: typing.Union[QItemSelectionModel.SelectionFlags, QItemSelectionModel.SelectionFlag]) -> None: ...
    def indexes(self) -> typing.List[QModelIndex]: ...
    def __contains__(self, index: QModelIndex) -> int: ...
    def contains(self, index: QModelIndex) -> bool: ...
    def select(self, topLeft: QModelIndex, bottomRight: QModelIndex) -> None: ...


class QJsonParseError(PyQt5.sipsimplewrapper):

    class ParseError(int):
        NoError = ... # type: QJsonParseError.ParseError
        UnterminatedObject = ... # type: QJsonParseError.ParseError
        MissingNameSeparator = ... # type: QJsonParseError.ParseError
        UnterminatedArray = ... # type: QJsonParseError.ParseError
        MissingValueSeparator = ... # type: QJsonParseError.ParseError
        IllegalValue = ... # type: QJsonParseError.ParseError
        TerminationByNumber = ... # type: QJsonParseError.ParseError
        IllegalNumber = ... # type: QJsonParseError.ParseError
        IllegalEscapeSequence = ... # type: QJsonParseError.ParseError
        IllegalUTF8String = ... # type: QJsonParseError.ParseError
        UnterminatedString = ... # type: QJsonParseError.ParseError
        MissingObject = ... # type: QJsonParseError.ParseError
        DeepNesting = ... # type: QJsonParseError.ParseError
        DocumentTooLarge = ... # type: QJsonParseError.ParseError
        GarbageAtEnd = ... # type: QJsonParseError.ParseError

    error = ... # type: 'QJsonParseError.ParseError'
    offset = ... # type: int

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QJsonParseError') -> None: ...

    def errorString(self) -> str: ...


class QJsonDocument(PyQt5.sipsimplewrapper):

    class JsonFormat(int):
        Indented = ... # type: QJsonDocument.JsonFormat
        Compact = ... # type: QJsonDocument.JsonFormat

    class DataValidation(int):
        Validate = ... # type: QJsonDocument.DataValidation
        BypassValidation = ... # type: QJsonDocument.DataValidation

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, object: typing.Dict[typing.Optional[str], typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> None: ...
    @typing.overload
    def __init__(self, array: typing.Iterable[typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QJsonDocument') -> None: ...

    @typing.overload
    def __getitem__(self, key: typing.Optional[str]) -> typing.Optional['QJsonValue']: ...
    @typing.overload
    def __getitem__(self, i: int) -> typing.Optional['QJsonValue']: ...
    def swap(self, other: 'QJsonDocument') -> None: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setArray(self, array: typing.Iterable[typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> None: ...
    def setObject(self, object: typing.Dict[typing.Optional[str], typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> None: ...
    def array(self) -> typing.List['QJsonValue']: ...
    def object(self) -> typing.Dict[str, 'QJsonValue']: ...
    def isObject(self) -> bool: ...
    def isArray(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    @typing.overload
    def toJson(self) -> QByteArray: ...
    @typing.overload
    def toJson(self, format: 'QJsonDocument.JsonFormat') -> QByteArray: ...
    @staticmethod
    def fromJson(json: typing.Union[QByteArray, bytes, bytearray], error: typing.Optional[QJsonParseError] = ...) -> 'QJsonDocument': ...
    def toVariant(self) -> typing.Any: ...
    @staticmethod
    def fromVariant(variant: typing.Any) -> 'QJsonDocument': ...
    def toBinaryData(self) -> QByteArray: ...
    @staticmethod
    def fromBinaryData(data: typing.Union[QByteArray, bytes, bytearray], validation: 'QJsonDocument.DataValidation' = ...) -> 'QJsonDocument': ...
    def rawData(self) -> typing.Tuple[typing.Optional[bytes], typing.Optional[int]]: ...
    @staticmethod
    def fromRawData(data: typing.Optional[bytes], size: int, validation: 'QJsonDocument.DataValidation' = ...) -> 'QJsonDocument': ...


class QJsonValue(PyQt5.sipsimplewrapper):

    class Type(int):
        Null = ... # type: QJsonValue.Type
        Bool = ... # type: QJsonValue.Type
        Double = ... # type: QJsonValue.Type
        String = ... # type: QJsonValue.Type
        Array = ... # type: QJsonValue.Type
        Object = ... # type: QJsonValue.Type
        Undefined = ... # type: QJsonValue.Type

    @typing.overload
    def __init__(self, type: 'QJsonValue.Type' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]) -> None: ...

    def __hash__(self) -> int: ...
    @typing.overload
    def __getitem__(self, key: typing.Optional[str]) -> typing.Optional['QJsonValue']: ...
    @typing.overload
    def __getitem__(self, i: int) -> typing.Optional['QJsonValue']: ...
    def swap(self, other: typing.Optional['QJsonValue']) -> None: ...
    @typing.overload
    def toString(self) -> str: ...
    @typing.overload
    def toString(self, defaultValue: typing.Optional[str]) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def toObject(self) -> typing.Dict[str, 'QJsonValue']: ...
    @typing.overload
    def toObject(self, defaultValue: typing.Dict[typing.Optional[str], typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> typing.Dict[str, 'QJsonValue']: ...
    @typing.overload
    def toArray(self) -> typing.List['QJsonValue']: ...
    @typing.overload
    def toArray(self, defaultValue: typing.Iterable[typing.Union['QJsonValue', 'QJsonValue.Type', typing.Iterable['QJsonValue'], typing.Dict[typing.Optional[str], 'QJsonValue'], bool, int, float, None, typing.Optional[str]]]) -> typing.List['QJsonValue']: ...
    def toDouble(self, defaultValue: float = ...) -> float: ...
    def toInt(self, defaultValue: int = ...) -> int: ...
    def toBool(self, defaultValue: bool = ...) -> bool: ...
    def isUndefined(self) -> bool: ...
    def isObject(self) -> bool: ...
    def isArray(self) -> bool: ...
    def isString(self) -> bool: ...
    def isDouble(self) -> bool: ...
    def isBool(self) -> bool: ...
    def isNull(self) -> bool: ...
    def type(self) -> 'QJsonValue.Type': ...
    def toVariant(self) -> typing.Any: ...
    @staticmethod
    def fromVariant(variant: typing.Any) -> typing.Optional['QJsonValue']: ...


class QLibrary(QObject):

    class LoadHint(int):
        ResolveAllSymbolsHint = ... # type: QLibrary.LoadHint
        ExportExternalSymbolsHint = ... # type: QLibrary.LoadHint
        LoadArchiveMemberHint = ... # type: QLibrary.LoadHint
        PreventUnloadHint = ... # type: QLibrary.LoadHint
        DeepBindHint = ... # type: QLibrary.LoadHint

    class LoadHints(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __xor__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __ior__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __or__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __iand__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __and__(self, f: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> 'QLibrary.LoadHints': ...
        def __invert__(self) -> 'QLibrary.LoadHints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], verNum: int, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], version: typing.Optional[str], parent: typing.Optional[QObject] = ...) -> None: ...

    def setLoadHints(self, hints: typing.Union['QLibrary.LoadHints', 'QLibrary.LoadHint']) -> None: ...
    @typing.overload
    def setFileNameAndVersion(self, fileName: typing.Optional[str], verNum: int) -> None: ...
    @typing.overload
    def setFileNameAndVersion(self, fileName: typing.Optional[str], version: typing.Optional[str]) -> None: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    @staticmethod
    def isLibrary(fileName: typing.Optional[str]) -> bool: ...
    def unload(self) -> bool: ...
    @typing.overload
    def resolve(self, symbol: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
    @typing.overload
    @staticmethod
    def resolve(fileName: typing.Optional[str], symbol: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
    @typing.overload
    @staticmethod
    def resolve(fileName: typing.Optional[str], verNum: int, symbol: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
    @typing.overload
    @staticmethod
    def resolve(fileName: typing.Optional[str], version: typing.Optional[str], symbol: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def loadHints(self) -> 'QLibrary.LoadHints': ...
    def load(self) -> bool: ...
    def isLoaded(self) -> bool: ...
    def fileName(self) -> str: ...
    def errorString(self) -> str: ...


class QLibraryInfo(PyQt5.sipsimplewrapper):

    class LibraryLocation(int):
        PrefixPath = ... # type: QLibraryInfo.LibraryLocation
        DocumentationPath = ... # type: QLibraryInfo.LibraryLocation
        HeadersPath = ... # type: QLibraryInfo.LibraryLocation
        LibrariesPath = ... # type: QLibraryInfo.LibraryLocation
        BinariesPath = ... # type: QLibraryInfo.LibraryLocation
        PluginsPath = ... # type: QLibraryInfo.LibraryLocation
        DataPath = ... # type: QLibraryInfo.LibraryLocation
        TranslationsPath = ... # type: QLibraryInfo.LibraryLocation
        SettingsPath = ... # type: QLibraryInfo.LibraryLocation
        ExamplesPath = ... # type: QLibraryInfo.LibraryLocation
        ImportsPath = ... # type: QLibraryInfo.LibraryLocation
        TestsPath = ... # type: QLibraryInfo.LibraryLocation
        LibraryExecutablesPath = ... # type: QLibraryInfo.LibraryLocation
        Qml2ImportsPath = ... # type: QLibraryInfo.LibraryLocation
        ArchDataPath = ... # type: QLibraryInfo.LibraryLocation

    def __init__(self, a0: 'QLibraryInfo') -> None: ...

    @staticmethod
    def version() -> 'QVersionNumber': ...
    @staticmethod
    def isDebugBuild() -> bool: ...
    @staticmethod
    def buildDate() -> QDate: ...
    @staticmethod
    def location(a0: 'QLibraryInfo.LibraryLocation') -> str: ...
    @staticmethod
    def licensedProducts() -> str: ...
    @staticmethod
    def licensee() -> str: ...


class QLine(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pt1_: 'QPoint', pt2_: 'QPoint') -> None: ...
    @typing.overload
    def __init__(self, x1pos: int, y1pos: int, x2pos: int, y2pos: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QLine') -> None: ...

    def center(self) -> 'QPoint': ...
    def setLine(self, aX1: int, aY1: int, aX2: int, aY2: int) -> None: ...
    def setPoints(self, aP1: 'QPoint', aP2: 'QPoint') -> None: ...
    def setP2(self, aP2: 'QPoint') -> None: ...
    def setP1(self, aP1: 'QPoint') -> None: ...
    @typing.overload
    def translated(self, p: 'QPoint') -> 'QLine': ...
    @typing.overload
    def translated(self, adx: int, ady: int) -> 'QLine': ...
    def __eq__(self, other: object): ...
    @typing.overload
    def translate(self, point: 'QPoint') -> None: ...
    @typing.overload
    def translate(self, adx: int, ady: int) -> None: ...
    def dy(self) -> int: ...
    def dx(self) -> int: ...
    def p2(self) -> 'QPoint': ...
    def p1(self) -> 'QPoint': ...
    def y2(self) -> int: ...
    def x2(self) -> int: ...
    def y1(self) -> int: ...
    def x1(self) -> int: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...
    def __ne__(self, other: object): ...


class QLineF(PyQt5.sipsimplewrapper):

    class IntersectType(int):
        NoIntersection = ... # type: QLineF.IntersectType
        BoundedIntersection = ... # type: QLineF.IntersectType
        UnboundedIntersection = ... # type: QLineF.IntersectType

    @typing.overload
    def __init__(self, line: QLine) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, apt1: typing.Union['QPointF', 'QPoint'], apt2: typing.Union['QPointF', 'QPoint']) -> None: ...
    @typing.overload
    def __init__(self, x1pos: float, y1pos: float, x2pos: float, y2pos: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QLineF') -> None: ...

    def center(self) -> 'QPointF': ...
    def setLine(self, aX1: float, aY1: float, aX2: float, aY2: float) -> None: ...
    def setPoints(self, aP1: typing.Union['QPointF', 'QPoint'], aP2: typing.Union['QPointF', 'QPoint']) -> None: ...
    def setP2(self, aP2: typing.Union['QPointF', 'QPoint']) -> None: ...
    def setP1(self, aP1: typing.Union['QPointF', 'QPoint']) -> None: ...
    @typing.overload
    def translated(self, p: typing.Union['QPointF', 'QPoint']) -> 'QLineF': ...
    @typing.overload
    def translated(self, adx: float, ady: float) -> 'QLineF': ...
    def angleTo(self, l: 'QLineF') -> float: ...
    def setAngle(self, angle: float) -> None: ...
    def angle(self) -> float: ...
    @staticmethod
    def fromPolar(length: float, angle: float) -> 'QLineF': ...
    def __eq__(self, other: object): ...
    def toLine(self) -> QLine: ...
    def pointAt(self, t: float) -> 'QPointF': ...
    def setLength(self, len: float) -> None: ...
    @typing.overload
    def translate(self, point: typing.Union['QPointF', 'QPoint']) -> None: ...
    @typing.overload
    def translate(self, adx: float, ady: float) -> None: ...
    def normalVector(self) -> 'QLineF': ...
    def dy(self) -> float: ...
    def dx(self) -> float: ...
    def p2(self) -> 'QPointF': ...
    def p1(self) -> 'QPointF': ...
    def y2(self) -> float: ...
    def x2(self) -> float: ...
    def y1(self) -> float: ...
    def x1(self) -> float: ...
    def __repr__(self) -> str: ...
    def __ne__(self, other: object): ...
    def intersects(self, l: 'QLineF') -> typing.Tuple['QLineF.IntersectType', typing.Optional['QPointF']]: ...
    def intersect(self, l: 'QLineF', intersectionPoint: typing.Optional[typing.Union['QPointF', 'QPoint']]) -> 'QLineF.IntersectType': ...
    def unitVector(self) -> 'QLineF': ...
    def length(self) -> float: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...


class QLocale(PyQt5.sipsimplewrapper):

    class DataSizeFormat(int):
        DataSizeIecFormat = ... # type: QLocale.DataSizeFormat
        DataSizeTraditionalFormat = ... # type: QLocale.DataSizeFormat
        DataSizeSIFormat = ... # type: QLocale.DataSizeFormat

    class FloatingPointPrecisionOption(int):
        FloatingPointShortest = ... # type: QLocale.FloatingPointPrecisionOption

    class QuotationStyle(int):
        StandardQuotation = ... # type: QLocale.QuotationStyle
        AlternateQuotation = ... # type: QLocale.QuotationStyle

    class CurrencySymbolFormat(int):
        CurrencyIsoCode = ... # type: QLocale.CurrencySymbolFormat
        CurrencySymbol = ... # type: QLocale.CurrencySymbolFormat
        CurrencyDisplayName = ... # type: QLocale.CurrencySymbolFormat

    class Script(int):
        AnyScript = ... # type: QLocale.Script
        ArabicScript = ... # type: QLocale.Script
        CyrillicScript = ... # type: QLocale.Script
        DeseretScript = ... # type: QLocale.Script
        GurmukhiScript = ... # type: QLocale.Script
        SimplifiedHanScript = ... # type: QLocale.Script
        TraditionalHanScript = ... # type: QLocale.Script
        LatinScript = ... # type: QLocale.Script
        MongolianScript = ... # type: QLocale.Script
        TifinaghScript = ... # type: QLocale.Script
        SimplifiedChineseScript = ... # type: QLocale.Script
        TraditionalChineseScript = ... # type: QLocale.Script
        ArmenianScript = ... # type: QLocale.Script
        BengaliScript = ... # type: QLocale.Script
        CherokeeScript = ... # type: QLocale.Script
        DevanagariScript = ... # type: QLocale.Script
        EthiopicScript = ... # type: QLocale.Script
        GeorgianScript = ... # type: QLocale.Script
        GreekScript = ... # type: QLocale.Script
        GujaratiScript = ... # type: QLocale.Script
        HebrewScript = ... # type: QLocale.Script
        JapaneseScript = ... # type: QLocale.Script
        KhmerScript = ... # type: QLocale.Script
        KannadaScript = ... # type: QLocale.Script
        KoreanScript = ... # type: QLocale.Script
        LaoScript = ... # type: QLocale.Script
        MalayalamScript = ... # type: QLocale.Script
        MyanmarScript = ... # type: QLocale.Script
        OriyaScript = ... # type: QLocale.Script
        TamilScript = ... # type: QLocale.Script
        TeluguScript = ... # type: QLocale.Script
        ThaanaScript = ... # type: QLocale.Script
        ThaiScript = ... # type: QLocale.Script
        TibetanScript = ... # type: QLocale.Script
        SinhalaScript = ... # type: QLocale.Script
        SyriacScript = ... # type: QLocale.Script
        YiScript = ... # type: QLocale.Script
        VaiScript = ... # type: QLocale.Script
        AvestanScript = ... # type: QLocale.Script
        BalineseScript = ... # type: QLocale.Script
        BamumScript = ... # type: QLocale.Script
        BatakScript = ... # type: QLocale.Script
        BopomofoScript = ... # type: QLocale.Script
        BrahmiScript = ... # type: QLocale.Script
        BugineseScript = ... # type: QLocale.Script
        BuhidScript = ... # type: QLocale.Script
        CanadianAboriginalScript = ... # type: QLocale.Script
        CarianScript = ... # type: QLocale.Script
        ChakmaScript = ... # type: QLocale.Script
        ChamScript = ... # type: QLocale.Script
        CopticScript = ... # type: QLocale.Script
        CypriotScript = ... # type: QLocale.Script
        EgyptianHieroglyphsScript = ... # type: QLocale.Script
        FraserScript = ... # type: QLocale.Script
        GlagoliticScript = ... # type: QLocale.Script
        GothicScript = ... # type: QLocale.Script
        HanScript = ... # type: QLocale.Script
        HangulScript = ... # type: QLocale.Script
        HanunooScript = ... # type: QLocale.Script
        ImperialAramaicScript = ... # type: QLocale.Script
        InscriptionalPahlaviScript = ... # type: QLocale.Script
        InscriptionalParthianScript = ... # type: QLocale.Script
        JavaneseScript = ... # type: QLocale.Script
        KaithiScript = ... # type: QLocale.Script
        KatakanaScript = ... # type: QLocale.Script
        KayahLiScript = ... # type: QLocale.Script
        KharoshthiScript = ... # type: QLocale.Script
        LannaScript = ... # type: QLocale.Script
        LepchaScript = ... # type: QLocale.Script
        LimbuScript = ... # type: QLocale.Script
        LinearBScript = ... # type: QLocale.Script
        LycianScript = ... # type: QLocale.Script
        LydianScript = ... # type: QLocale.Script
        MandaeanScript = ... # type: QLocale.Script
        MeiteiMayekScript = ... # type: QLocale.Script
        MeroiticScript = ... # type: QLocale.Script
        MeroiticCursiveScript = ... # type: QLocale.Script
        NkoScript = ... # type: QLocale.Script
        NewTaiLueScript = ... # type: QLocale.Script
        OghamScript = ... # type: QLocale.Script
        OlChikiScript = ... # type: QLocale.Script
        OldItalicScript = ... # type: QLocale.Script
        OldPersianScript = ... # type: QLocale.Script
        OldSouthArabianScript = ... # type: QLocale.Script
        OrkhonScript = ... # type: QLocale.Script
        OsmanyaScript = ... # type: QLocale.Script
        PhagsPaScript = ... # type: QLocale.Script
        PhoenicianScript = ... # type: QLocale.Script
        PollardPhoneticScript = ... # type: QLocale.Script
        RejangScript = ... # type: QLocale.Script
        RunicScript = ... # type: QLocale.Script
        SamaritanScript = ... # type: QLocale.Script
        SaurashtraScript = ... # type: QLocale.Script
        SharadaScript = ... # type: QLocale.Script
        ShavianScript = ... # type: QLocale.Script
        SoraSompengScript = ... # type: QLocale.Script
        CuneiformScript = ... # type: QLocale.Script
        SundaneseScript = ... # type: QLocale.Script
        SylotiNagriScript = ... # type: QLocale.Script
        TagalogScript = ... # type: QLocale.Script
        TagbanwaScript = ... # type: QLocale.Script
        TaiLeScript = ... # type: QLocale.Script
        TaiVietScript = ... # type: QLocale.Script
        TakriScript = ... # type: QLocale.Script
        UgariticScript = ... # type: QLocale.Script
        BrailleScript = ... # type: QLocale.Script
        HiraganaScript = ... # type: QLocale.Script
        CaucasianAlbanianScript = ... # type: QLocale.Script
        BassaVahScript = ... # type: QLocale.Script
        DuployanScript = ... # type: QLocale.Script
        ElbasanScript = ... # type: QLocale.Script
        GranthaScript = ... # type: QLocale.Script
        PahawhHmongScript = ... # type: QLocale.Script
        KhojkiScript = ... # type: QLocale.Script
        LinearAScript = ... # type: QLocale.Script
        MahajaniScript = ... # type: QLocale.Script
        ManichaeanScript = ... # type: QLocale.Script
        MendeKikakuiScript = ... # type: QLocale.Script
        ModiScript = ... # type: QLocale.Script
        MroScript = ... # type: QLocale.Script
        OldNorthArabianScript = ... # type: QLocale.Script
        NabataeanScript = ... # type: QLocale.Script
        PalmyreneScript = ... # type: QLocale.Script
        PauCinHauScript = ... # type: QLocale.Script
        OldPermicScript = ... # type: QLocale.Script
        PsalterPahlaviScript = ... # type: QLocale.Script
        SiddhamScript = ... # type: QLocale.Script
        KhudawadiScript = ... # type: QLocale.Script
        TirhutaScript = ... # type: QLocale.Script
        VarangKshitiScript = ... # type: QLocale.Script
        AhomScript = ... # type: QLocale.Script
        AnatolianHieroglyphsScript = ... # type: QLocale.Script
        HatranScript = ... # type: QLocale.Script
        MultaniScript = ... # type: QLocale.Script
        OldHungarianScript = ... # type: QLocale.Script
        SignWritingScript = ... # type: QLocale.Script
        AdlamScript = ... # type: QLocale.Script
        BhaiksukiScript = ... # type: QLocale.Script
        MarchenScript = ... # type: QLocale.Script
        NewaScript = ... # type: QLocale.Script
        OsageScript = ... # type: QLocale.Script
        TangutScript = ... # type: QLocale.Script
        HanWithBopomofoScript = ... # type: QLocale.Script
        JamoScript = ... # type: QLocale.Script

    class MeasurementSystem(int):
        MetricSystem = ... # type: QLocale.MeasurementSystem
        ImperialSystem = ... # type: QLocale.MeasurementSystem
        ImperialUSSystem = ... # type: QLocale.MeasurementSystem
        ImperialUKSystem = ... # type: QLocale.MeasurementSystem

    class FormatType(int):
        LongFormat = ... # type: QLocale.FormatType
        ShortFormat = ... # type: QLocale.FormatType
        NarrowFormat = ... # type: QLocale.FormatType

    class NumberOption(int):
        OmitGroupSeparator = ... # type: QLocale.NumberOption
        RejectGroupSeparator = ... # type: QLocale.NumberOption
        DefaultNumberOptions = ... # type: QLocale.NumberOption
        OmitLeadingZeroInExponent = ... # type: QLocale.NumberOption
        RejectLeadingZeroInExponent = ... # type: QLocale.NumberOption
        IncludeTrailingZeroesAfterDot = ... # type: QLocale.NumberOption
        RejectTrailingZeroesAfterDot = ... # type: QLocale.NumberOption

    class Country(int):
        AnyCountry = ... # type: QLocale.Country
        Afghanistan = ... # type: QLocale.Country
        Albania = ... # type: QLocale.Country
        Algeria = ... # type: QLocale.Country
        AmericanSamoa = ... # type: QLocale.Country
        Andorra = ... # type: QLocale.Country
        Angola = ... # type: QLocale.Country
        Anguilla = ... # type: QLocale.Country
        Antarctica = ... # type: QLocale.Country
        AntiguaAndBarbuda = ... # type: QLocale.Country
        Argentina = ... # type: QLocale.Country
        Armenia = ... # type: QLocale.Country
        Aruba = ... # type: QLocale.Country
        Australia = ... # type: QLocale.Country
        Austria = ... # type: QLocale.Country
        Azerbaijan = ... # type: QLocale.Country
        Bahamas = ... # type: QLocale.Country
        Bahrain = ... # type: QLocale.Country
        Bangladesh = ... # type: QLocale.Country
        Barbados = ... # type: QLocale.Country
        Belarus = ... # type: QLocale.Country
        Belgium = ... # type: QLocale.Country
        Belize = ... # type: QLocale.Country
        Benin = ... # type: QLocale.Country
        Bermuda = ... # type: QLocale.Country
        Bhutan = ... # type: QLocale.Country
        Bolivia = ... # type: QLocale.Country
        BosniaAndHerzegowina = ... # type: QLocale.Country
        Botswana = ... # type: QLocale.Country
        BouvetIsland = ... # type: QLocale.Country
        Brazil = ... # type: QLocale.Country
        BritishIndianOceanTerritory = ... # type: QLocale.Country
        Bulgaria = ... # type: QLocale.Country
        BurkinaFaso = ... # type: QLocale.Country
        Burundi = ... # type: QLocale.Country
        Cambodia = ... # type: QLocale.Country
        Cameroon = ... # type: QLocale.Country
        Canada = ... # type: QLocale.Country
        CapeVerde = ... # type: QLocale.Country
        CaymanIslands = ... # type: QLocale.Country
        CentralAfricanRepublic = ... # type: QLocale.Country
        Chad = ... # type: QLocale.Country
        Chile = ... # type: QLocale.Country
        China = ... # type: QLocale.Country
        ChristmasIsland = ... # type: QLocale.Country
        CocosIslands = ... # type: QLocale.Country
        Colombia = ... # type: QLocale.Country
        Comoros = ... # type: QLocale.Country
        DemocraticRepublicOfCongo = ... # type: QLocale.Country
        PeoplesRepublicOfCongo = ... # type: QLocale.Country
        CookIslands = ... # type: QLocale.Country
        CostaRica = ... # type: QLocale.Country
        IvoryCoast = ... # type: QLocale.Country
        Croatia = ... # type: QLocale.Country
        Cuba = ... # type: QLocale.Country
        Cyprus = ... # type: QLocale.Country
        CzechRepublic = ... # type: QLocale.Country
        Denmark = ... # type: QLocale.Country
        Djibouti = ... # type: QLocale.Country
        Dominica = ... # type: QLocale.Country
        DominicanRepublic = ... # type: QLocale.Country
        EastTimor = ... # type: QLocale.Country
        Ecuador = ... # type: QLocale.Country
        Egypt = ... # type: QLocale.Country
        ElSalvador = ... # type: QLocale.Country
        EquatorialGuinea = ... # type: QLocale.Country
        Eritrea = ... # type: QLocale.Country
        Estonia = ... # type: QLocale.Country
        Ethiopia = ... # type: QLocale.Country
        FalklandIslands = ... # type: QLocale.Country
        FaroeIslands = ... # type: QLocale.Country
        Finland = ... # type: QLocale.Country
        France = ... # type: QLocale.Country
        FrenchGuiana = ... # type: QLocale.Country
        FrenchPolynesia = ... # type: QLocale.Country
        FrenchSouthernTerritories = ... # type: QLocale.Country
        Gabon = ... # type: QLocale.Country
        Gambia = ... # type: QLocale.Country
        Georgia = ... # type: QLocale.Country
        Germany = ... # type: QLocale.Country
        Ghana = ... # type: QLocale.Country
        Gibraltar = ... # type: QLocale.Country
        Greece = ... # type: QLocale.Country
        Greenland = ... # type: QLocale.Country
        Grenada = ... # type: QLocale.Country
        Guadeloupe = ... # type: QLocale.Country
        Guam = ... # type: QLocale.Country
        Guatemala = ... # type: QLocale.Country
        Guinea = ... # type: QLocale.Country
        GuineaBissau = ... # type: QLocale.Country
        Guyana = ... # type: QLocale.Country
        Haiti = ... # type: QLocale.Country
        HeardAndMcDonaldIslands = ... # type: QLocale.Country
        Honduras = ... # type: QLocale.Country
        HongKong = ... # type: QLocale.Country
        Hungary = ... # type: QLocale.Country
        Iceland = ... # type: QLocale.Country
        India = ... # type: QLocale.Country
        Indonesia = ... # type: QLocale.Country
        Iran = ... # type: QLocale.Country
        Iraq = ... # type: QLocale.Country
        Ireland = ... # type: QLocale.Country
        Israel = ... # type: QLocale.Country
        Italy = ... # type: QLocale.Country
        Jamaica = ... # type: QLocale.Country
        Japan = ... # type: QLocale.Country
        Jordan = ... # type: QLocale.Country
        Kazakhstan = ... # type: QLocale.Country
        Kenya = ... # type: QLocale.Country
        Kiribati = ... # type: QLocale.Country
        DemocraticRepublicOfKorea = ... # type: QLocale.Country
        RepublicOfKorea = ... # type: QLocale.Country
        Kuwait = ... # type: QLocale.Country
        Kyrgyzstan = ... # type: QLocale.Country
        Latvia = ... # type: QLocale.Country
        Lebanon = ... # type: QLocale.Country
        Lesotho = ... # type: QLocale.Country
        Liberia = ... # type: QLocale.Country
        Liechtenstein = ... # type: QLocale.Country
        Lithuania = ... # type: QLocale.Country
        Luxembourg = ... # type: QLocale.Country
        Macau = ... # type: QLocale.Country
        Macedonia = ... # type: QLocale.Country
        Madagascar = ... # type: QLocale.Country
        Malawi = ... # type: QLocale.Country
        Malaysia = ... # type: QLocale.Country
        Maldives = ... # type: QLocale.Country
        Mali = ... # type: QLocale.Country
        Malta = ... # type: QLocale.Country
        MarshallIslands = ... # type: QLocale.Country
        Martinique = ... # type: QLocale.Country
        Mauritania = ... # type: QLocale.Country
        Mauritius = ... # type: QLocale.Country
        Mayotte = ... # type: QLocale.Country
        Mexico = ... # type: QLocale.Country
        Micronesia = ... # type: QLocale.Country
        Moldova = ... # type: QLocale.Country
        Monaco = ... # type: QLocale.Country
        Mongolia = ... # type: QLocale.Country
        Montserrat = ... # type: QLocale.Country
        Morocco = ... # type: QLocale.Country
        Mozambique = ... # type: QLocale.Country
        Myanmar = ... # type: QLocale.Country
        Namibia = ... # type: QLocale.Country
        NauruCountry = ... # type: QLocale.Country
        Nepal = ... # type: QLocale.Country
        Netherlands = ... # type: QLocale.Country
        NewCaledonia = ... # type: QLocale.Country
        NewZealand = ... # type: QLocale.Country
        Nicaragua = ... # type: QLocale.Country
        Niger = ... # type: QLocale.Country
        Nigeria = ... # type: QLocale.Country
        Niue = ... # type: QLocale.Country
        NorfolkIsland = ... # type: QLocale.Country
        NorthernMarianaIslands = ... # type: QLocale.Country
        Norway = ... # type: QLocale.Country
        Oman = ... # type: QLocale.Country
        Pakistan = ... # type: QLocale.Country
        Palau = ... # type: QLocale.Country
        Panama = ... # type: QLocale.Country
        PapuaNewGuinea = ... # type: QLocale.Country
        Paraguay = ... # type: QLocale.Country
        Peru = ... # type: QLocale.Country
        Philippines = ... # type: QLocale.Country
        Pitcairn = ... # type: QLocale.Country
        Poland = ... # type: QLocale.Country
        Portugal = ... # type: QLocale.Country
        PuertoRico = ... # type: QLocale.Country
        Qatar = ... # type: QLocale.Country
        Reunion = ... # type: QLocale.Country
        Romania = ... # type: QLocale.Country
        RussianFederation = ... # type: QLocale.Country
        Rwanda = ... # type: QLocale.Country
        SaintKittsAndNevis = ... # type: QLocale.Country
        Samoa = ... # type: QLocale.Country
        SanMarino = ... # type: QLocale.Country
        SaoTomeAndPrincipe = ... # type: QLocale.Country
        SaudiArabia = ... # type: QLocale.Country
        Senegal = ... # type: QLocale.Country
        Seychelles = ... # type: QLocale.Country
        SierraLeone = ... # type: QLocale.Country
        Singapore = ... # type: QLocale.Country
        Slovakia = ... # type: QLocale.Country
        Slovenia = ... # type: QLocale.Country
        SolomonIslands = ... # type: QLocale.Country
        Somalia = ... # type: QLocale.Country
        SouthAfrica = ... # type: QLocale.Country
        SouthGeorgiaAndTheSouthSandwichIslands = ... # type: QLocale.Country
        Spain = ... # type: QLocale.Country
        SriLanka = ... # type: QLocale.Country
        Sudan = ... # type: QLocale.Country
        Suriname = ... # type: QLocale.Country
        SvalbardAndJanMayenIslands = ... # type: QLocale.Country
        Swaziland = ... # type: QLocale.Country
        Sweden = ... # type: QLocale.Country
        Switzerland = ... # type: QLocale.Country
        SyrianArabRepublic = ... # type: QLocale.Country
        Taiwan = ... # type: QLocale.Country
        Tajikistan = ... # type: QLocale.Country
        Tanzania = ... # type: QLocale.Country
        Thailand = ... # type: QLocale.Country
        Togo = ... # type: QLocale.Country
        Tokelau = ... # type: QLocale.Country
        TrinidadAndTobago = ... # type: QLocale.Country
        Tunisia = ... # type: QLocale.Country
        Turkey = ... # type: QLocale.Country
        Turkmenistan = ... # type: QLocale.Country
        TurksAndCaicosIslands = ... # type: QLocale.Country
        Tuvalu = ... # type: QLocale.Country
        Uganda = ... # type: QLocale.Country
        Ukraine = ... # type: QLocale.Country
        UnitedArabEmirates = ... # type: QLocale.Country
        UnitedKingdom = ... # type: QLocale.Country
        UnitedStates = ... # type: QLocale.Country
        UnitedStatesMinorOutlyingIslands = ... # type: QLocale.Country
        Uruguay = ... # type: QLocale.Country
        Uzbekistan = ... # type: QLocale.Country
        Vanuatu = ... # type: QLocale.Country
        VaticanCityState = ... # type: QLocale.Country
        Venezuela = ... # type: QLocale.Country
        BritishVirginIslands = ... # type: QLocale.Country
        WallisAndFutunaIslands = ... # type: QLocale.Country
        WesternSahara = ... # type: QLocale.Country
        Yemen = ... # type: QLocale.Country
        Zambia = ... # type: QLocale.Country
        Zimbabwe = ... # type: QLocale.Country
        Montenegro = ... # type: QLocale.Country
        Serbia = ... # type: QLocale.Country
        SaintBarthelemy = ... # type: QLocale.Country
        SaintMartin = ... # type: QLocale.Country
        LatinAmericaAndTheCaribbean = ... # type: QLocale.Country
        LastCountry = ... # type: QLocale.Country
        Brunei = ... # type: QLocale.Country
        CongoKinshasa = ... # type: QLocale.Country
        CongoBrazzaville = ... # type: QLocale.Country
        Fiji = ... # type: QLocale.Country
        Guernsey = ... # type: QLocale.Country
        NorthKorea = ... # type: QLocale.Country
        SouthKorea = ... # type: QLocale.Country
        Laos = ... # type: QLocale.Country
        Libya = ... # type: QLocale.Country
        CuraSao = ... # type: QLocale.Country
        PalestinianTerritories = ... # type: QLocale.Country
        Russia = ... # type: QLocale.Country
        SaintLucia = ... # type: QLocale.Country
        SaintVincentAndTheGrenadines = ... # type: QLocale.Country
        SaintHelena = ... # type: QLocale.Country
        SaintPierreAndMiquelon = ... # type: QLocale.Country
        Syria = ... # type: QLocale.Country
        Tonga = ... # type: QLocale.Country
        Vietnam = ... # type: QLocale.Country
        UnitedStatesVirginIslands = ... # type: QLocale.Country
        CanaryIslands = ... # type: QLocale.Country
        ClippertonIsland = ... # type: QLocale.Country
        AscensionIsland = ... # type: QLocale.Country
        AlandIslands = ... # type: QLocale.Country
        DiegoGarcia = ... # type: QLocale.Country
        CeutaAndMelilla = ... # type: QLocale.Country
        IsleOfMan = ... # type: QLocale.Country
        Jersey = ... # type: QLocale.Country
        TristanDaCunha = ... # type: QLocale.Country
        SouthSudan = ... # type: QLocale.Country
        Bonaire = ... # type: QLocale.Country
        SintMaarten = ... # type: QLocale.Country
        Kosovo = ... # type: QLocale.Country
        TokelauCountry = ... # type: QLocale.Country
        TuvaluCountry = ... # type: QLocale.Country
        EuropeanUnion = ... # type: QLocale.Country
        OutlyingOceania = ... # type: QLocale.Country
        LatinAmerica = ... # type: QLocale.Country
        World = ... # type: QLocale.Country
        Europe = ... # type: QLocale.Country

    class Language(int):
        C = ... # type: QLocale.Language
        Abkhazian = ... # type: QLocale.Language
        Afan = ... # type: QLocale.Language
        Afar = ... # type: QLocale.Language
        Afrikaans = ... # type: QLocale.Language
        Albanian = ... # type: QLocale.Language
        Amharic = ... # type: QLocale.Language
        Arabic = ... # type: QLocale.Language
        Armenian = ... # type: QLocale.Language
        Assamese = ... # type: QLocale.Language
        Aymara = ... # type: QLocale.Language
        Azerbaijani = ... # type: QLocale.Language
        Bashkir = ... # type: QLocale.Language
        Basque = ... # type: QLocale.Language
        Bengali = ... # type: QLocale.Language
        Bhutani = ... # type: QLocale.Language
        Bihari = ... # type: QLocale.Language
        Bislama = ... # type: QLocale.Language
        Breton = ... # type: QLocale.Language
        Bulgarian = ... # type: QLocale.Language
        Burmese = ... # type: QLocale.Language
        Byelorussian = ... # type: QLocale.Language
        Cambodian = ... # type: QLocale.Language
        Catalan = ... # type: QLocale.Language
        Chinese = ... # type: QLocale.Language
        Corsican = ... # type: QLocale.Language
        Croatian = ... # type: QLocale.Language
        Czech = ... # type: QLocale.Language
        Danish = ... # type: QLocale.Language
        Dutch = ... # type: QLocale.Language
        English = ... # type: QLocale.Language
        Esperanto = ... # type: QLocale.Language
        Estonian = ... # type: QLocale.Language
        Faroese = ... # type: QLocale.Language
        Finnish = ... # type: QLocale.Language
        French = ... # type: QLocale.Language
        Frisian = ... # type: QLocale.Language
        Gaelic = ... # type: QLocale.Language
        Galician = ... # type: QLocale.Language
        Georgian = ... # type: QLocale.Language
        German = ... # type: QLocale.Language
        Greek = ... # type: QLocale.Language
        Greenlandic = ... # type: QLocale.Language
        Guarani = ... # type: QLocale.Language
        Gujarati = ... # type: QLocale.Language
        Hausa = ... # type: QLocale.Language
        Hebrew = ... # type: QLocale.Language
        Hindi = ... # type: QLocale.Language
        Hungarian = ... # type: QLocale.Language
        Icelandic = ... # type: QLocale.Language
        Indonesian = ... # type: QLocale.Language
        Interlingua = ... # type: QLocale.Language
        Interlingue = ... # type: QLocale.Language
        Inuktitut = ... # type: QLocale.Language
        Inupiak = ... # type: QLocale.Language
        Irish = ... # type: QLocale.Language
        Italian = ... # type: QLocale.Language
        Japanese = ... # type: QLocale.Language
        Javanese = ... # type: QLocale.Language
        Kannada = ... # type: QLocale.Language
        Kashmiri = ... # type: QLocale.Language
        Kazakh = ... # type: QLocale.Language
        Kinyarwanda = ... # type: QLocale.Language
        Kirghiz = ... # type: QLocale.Language
        Korean = ... # type: QLocale.Language
        Kurdish = ... # type: QLocale.Language
        Kurundi = ... # type: QLocale.Language
        Latin = ... # type: QLocale.Language
        Latvian = ... # type: QLocale.Language
        Lingala = ... # type: QLocale.Language
        Lithuanian = ... # type: QLocale.Language
        Macedonian = ... # type: QLocale.Language
        Malagasy = ... # type: QLocale.Language
        Malay = ... # type: QLocale.Language
        Malayalam = ... # type: QLocale.Language
        Maltese = ... # type: QLocale.Language
        Maori = ... # type: QLocale.Language
        Marathi = ... # type: QLocale.Language
        Moldavian = ... # type: QLocale.Language
        Mongolian = ... # type: QLocale.Language
        NauruLanguage = ... # type: QLocale.Language
        Nepali = ... # type: QLocale.Language
        Norwegian = ... # type: QLocale.Language
        Occitan = ... # type: QLocale.Language
        Oriya = ... # type: QLocale.Language
        Pashto = ... # type: QLocale.Language
        Persian = ... # type: QLocale.Language
        Polish = ... # type: QLocale.Language
        Portuguese = ... # type: QLocale.Language
        Punjabi = ... # type: QLocale.Language
        Quechua = ... # type: QLocale.Language
        RhaetoRomance = ... # type: QLocale.Language
        Romanian = ... # type: QLocale.Language
        Russian = ... # type: QLocale.Language
        Samoan = ... # type: QLocale.Language
        Sanskrit = ... # type: QLocale.Language
        Serbian = ... # type: QLocale.Language
        SerboCroatian = ... # type: QLocale.Language
        Shona = ... # type: QLocale.Language
        Sindhi = ... # type: QLocale.Language
        Slovak = ... # type: QLocale.Language
        Slovenian = ... # type: QLocale.Language
        Somali = ... # type: QLocale.Language
        Spanish = ... # type: QLocale.Language
        Sundanese = ... # type: QLocale.Language
        Swahili = ... # type: QLocale.Language
        Swedish = ... # type: QLocale.Language
        Tagalog = ... # type: QLocale.Language
        Tajik = ... # type: QLocale.Language
        Tamil = ... # type: QLocale.Language
        Tatar = ... # type: QLocale.Language
        Telugu = ... # type: QLocale.Language
        Thai = ... # type: QLocale.Language
        Tibetan = ... # type: QLocale.Language
        Tigrinya = ... # type: QLocale.Language
        Tsonga = ... # type: QLocale.Language
        Turkish = ... # type: QLocale.Language
        Turkmen = ... # type: QLocale.Language
        Twi = ... # type: QLocale.Language
        Uigur = ... # type: QLocale.Language
        Ukrainian = ... # type: QLocale.Language
        Urdu = ... # type: QLocale.Language
        Uzbek = ... # type: QLocale.Language
        Vietnamese = ... # type: QLocale.Language
        Volapuk = ... # type: QLocale.Language
        Welsh = ... # type: QLocale.Language
        Wolof = ... # type: QLocale.Language
        Xhosa = ... # type: QLocale.Language
        Yiddish = ... # type: QLocale.Language
        Yoruba = ... # type: QLocale.Language
        Zhuang = ... # type: QLocale.Language
        Zulu = ... # type: QLocale.Language
        Bosnian = ... # type: QLocale.Language
        Divehi = ... # type: QLocale.Language
        Manx = ... # type: QLocale.Language
        Cornish = ... # type: QLocale.Language
        LastLanguage = ... # type: QLocale.Language
        NorwegianBokmal = ... # type: QLocale.Language
        NorwegianNynorsk = ... # type: QLocale.Language
        Akan = ... # type: QLocale.Language
        Konkani = ... # type: QLocale.Language
        Ga = ... # type: QLocale.Language
        Igbo = ... # type: QLocale.Language
        Kamba = ... # type: QLocale.Language
        Syriac = ... # type: QLocale.Language
        Blin = ... # type: QLocale.Language
        Geez = ... # type: QLocale.Language
        Koro = ... # type: QLocale.Language
        Sidamo = ... # type: QLocale.Language
        Atsam = ... # type: QLocale.Language
        Tigre = ... # type: QLocale.Language
        Jju = ... # type: QLocale.Language
        Friulian = ... # type: QLocale.Language
        Venda = ... # type: QLocale.Language
        Ewe = ... # type: QLocale.Language
        Walamo = ... # type: QLocale.Language
        Hawaiian = ... # type: QLocale.Language
        Tyap = ... # type: QLocale.Language
        Chewa = ... # type: QLocale.Language
        Filipino = ... # type: QLocale.Language
        SwissGerman = ... # type: QLocale.Language
        SichuanYi = ... # type: QLocale.Language
        Kpelle = ... # type: QLocale.Language
        LowGerman = ... # type: QLocale.Language
        SouthNdebele = ... # type: QLocale.Language
        NorthernSotho = ... # type: QLocale.Language
        NorthernSami = ... # type: QLocale.Language
        Taroko = ... # type: QLocale.Language
        Gusii = ... # type: QLocale.Language
        Taita = ... # type: QLocale.Language
        Fulah = ... # type: QLocale.Language
        Kikuyu = ... # type: QLocale.Language
        Samburu = ... # type: QLocale.Language
        Sena = ... # type: QLocale.Language
        NorthNdebele = ... # type: QLocale.Language
        Rombo = ... # type: QLocale.Language
        Tachelhit = ... # type: QLocale.Language
        Kabyle = ... # type: QLocale.Language
        Nyankole = ... # type: QLocale.Language
        Bena = ... # type: QLocale.Language
        Vunjo = ... # type: QLocale.Language
        Bambara = ... # type: QLocale.Language
        Embu = ... # type: QLocale.Language
        Cherokee = ... # type: QLocale.Language
        Morisyen = ... # type: QLocale.Language
        Makonde = ... # type: QLocale.Language
        Langi = ... # type: QLocale.Language
        Ganda = ... # type: QLocale.Language
        Bemba = ... # type: QLocale.Language
        Kabuverdianu = ... # type: QLocale.Language
        Meru = ... # type: QLocale.Language
        Kalenjin = ... # type: QLocale.Language
        Nama = ... # type: QLocale.Language
        Machame = ... # type: QLocale.Language
        Colognian = ... # type: QLocale.Language
        Masai = ... # type: QLocale.Language
        Soga = ... # type: QLocale.Language
        Luyia = ... # type: QLocale.Language
        Asu = ... # type: QLocale.Language
        Teso = ... # type: QLocale.Language
        Saho = ... # type: QLocale.Language
        KoyraChiini = ... # type: QLocale.Language
        Rwa = ... # type: QLocale.Language
        Luo = ... # type: QLocale.Language
        Chiga = ... # type: QLocale.Language
        CentralMoroccoTamazight = ... # type: QLocale.Language
        KoyraboroSenni = ... # type: QLocale.Language
        Shambala = ... # type: QLocale.Language
        AnyLanguage = ... # type: QLocale.Language
        Rundi = ... # type: QLocale.Language
        Bodo = ... # type: QLocale.Language
        Aghem = ... # type: QLocale.Language
        Basaa = ... # type: QLocale.Language
        Zarma = ... # type: QLocale.Language
        Duala = ... # type: QLocale.Language
        JolaFonyi = ... # type: QLocale.Language
        Ewondo = ... # type: QLocale.Language
        Bafia = ... # type: QLocale.Language
        LubaKatanga = ... # type: QLocale.Language
        MakhuwaMeetto = ... # type: QLocale.Language
        Mundang = ... # type: QLocale.Language
        Kwasio = ... # type: QLocale.Language
        Nuer = ... # type: QLocale.Language
        Sakha = ... # type: QLocale.Language
        Sangu = ... # type: QLocale.Language
        CongoSwahili = ... # type: QLocale.Language
        Tasawaq = ... # type: QLocale.Language
        Vai = ... # type: QLocale.Language
        Walser = ... # type: QLocale.Language
        Yangben = ... # type: QLocale.Language
        Oromo = ... # type: QLocale.Language
        Dzongkha = ... # type: QLocale.Language
        Belarusian = ... # type: QLocale.Language
        Khmer = ... # type: QLocale.Language
        Fijian = ... # type: QLocale.Language
        WesternFrisian = ... # type: QLocale.Language
        Lao = ... # type: QLocale.Language
        Marshallese = ... # type: QLocale.Language
        Romansh = ... # type: QLocale.Language
        Sango = ... # type: QLocale.Language
        Ossetic = ... # type: QLocale.Language
        SouthernSotho = ... # type: QLocale.Language
        Tswana = ... # type: QLocale.Language
        Sinhala = ... # type: QLocale.Language
        Swati = ... # type: QLocale.Language
        Sardinian = ... # type: QLocale.Language
        Tongan = ... # type: QLocale.Language
        Tahitian = ... # type: QLocale.Language
        Nyanja = ... # type: QLocale.Language
        Avaric = ... # type: QLocale.Language
        Chamorro = ... # type: QLocale.Language
        Chechen = ... # type: QLocale.Language
        Church = ... # type: QLocale.Language
        Chuvash = ... # type: QLocale.Language
        Cree = ... # type: QLocale.Language
        Haitian = ... # type: QLocale.Language
        Herero = ... # type: QLocale.Language
        HiriMotu = ... # type: QLocale.Language
        Kanuri = ... # type: QLocale.Language
        Komi = ... # type: QLocale.Language
        Kongo = ... # type: QLocale.Language
        Kwanyama = ... # type: QLocale.Language
        Limburgish = ... # type: QLocale.Language
        Luxembourgish = ... # type: QLocale.Language
        Navaho = ... # type: QLocale.Language
        Ndonga = ... # type: QLocale.Language
        Ojibwa = ... # type: QLocale.Language
        Pali = ... # type: QLocale.Language
        Walloon = ... # type: QLocale.Language
        Avestan = ... # type: QLocale.Language
        Asturian = ... # type: QLocale.Language
        Ngomba = ... # type: QLocale.Language
        Kako = ... # type: QLocale.Language
        Meta = ... # type: QLocale.Language
        Ngiemboon = ... # type: QLocale.Language
        Uighur = ... # type: QLocale.Language
        Aragonese = ... # type: QLocale.Language
        Akkadian = ... # type: QLocale.Language
        AncientEgyptian = ... # type: QLocale.Language
        AncientGreek = ... # type: QLocale.Language
        Aramaic = ... # type: QLocale.Language
        Balinese = ... # type: QLocale.Language
        Bamun = ... # type: QLocale.Language
        BatakToba = ... # type: QLocale.Language
        Buginese = ... # type: QLocale.Language
        Buhid = ... # type: QLocale.Language
        Carian = ... # type: QLocale.Language
        Chakma = ... # type: QLocale.Language
        ClassicalMandaic = ... # type: QLocale.Language
        Coptic = ... # type: QLocale.Language
        Dogri = ... # type: QLocale.Language
        EasternCham = ... # type: QLocale.Language
        EasternKayah = ... # type: QLocale.Language
        Etruscan = ... # type: QLocale.Language
        Gothic = ... # type: QLocale.Language
        Hanunoo = ... # type: QLocale.Language
        Ingush = ... # type: QLocale.Language
        LargeFloweryMiao = ... # type: QLocale.Language
        Lepcha = ... # type: QLocale.Language
        Limbu = ... # type: QLocale.Language
        Lisu = ... # type: QLocale.Language
        Lu = ... # type: QLocale.Language
        Lycian = ... # type: QLocale.Language
        Lydian = ... # type: QLocale.Language
        Mandingo = ... # type: QLocale.Language
        Manipuri = ... # type: QLocale.Language
        Meroitic = ... # type: QLocale.Language
        NorthernThai = ... # type: QLocale.Language
        OldIrish = ... # type: QLocale.Language
        OldNorse = ... # type: QLocale.Language
        OldPersian = ... # type: QLocale.Language
        OldTurkish = ... # type: QLocale.Language
        Pahlavi = ... # type: QLocale.Language
        Parthian = ... # type: QLocale.Language
        Phoenician = ... # type: QLocale.Language
        PrakritLanguage = ... # type: QLocale.Language
        Rejang = ... # type: QLocale.Language
        Sabaean = ... # type: QLocale.Language
        Samaritan = ... # type: QLocale.Language
        Santali = ... # type: QLocale.Language
        Saurashtra = ... # type: QLocale.Language
        Sora = ... # type: QLocale.Language
        Sylheti = ... # type: QLocale.Language
        Tagbanwa = ... # type: QLocale.Language
        TaiDam = ... # type: QLocale.Language
        TaiNua = ... # type: QLocale.Language
        Ugaritic = ... # type: QLocale.Language
        Akoose = ... # type: QLocale.Language
        Lakota = ... # type: QLocale.Language
        StandardMoroccanTamazight = ... # type: QLocale.Language
        Mapuche = ... # type: QLocale.Language
        CentralKurdish = ... # type: QLocale.Language
        LowerSorbian = ... # type: QLocale.Language
        UpperSorbian = ... # type: QLocale.Language
        Kenyang = ... # type: QLocale.Language
        Mohawk = ... # type: QLocale.Language
        Nko = ... # type: QLocale.Language
        Prussian = ... # type: QLocale.Language
        Kiche = ... # type: QLocale.Language
        SouthernSami = ... # type: QLocale.Language
        LuleSami = ... # type: QLocale.Language
        InariSami = ... # type: QLocale.Language
        SkoltSami = ... # type: QLocale.Language
        Warlpiri = ... # type: QLocale.Language
        ManichaeanMiddlePersian = ... # type: QLocale.Language
        Mende = ... # type: QLocale.Language
        AncientNorthArabian = ... # type: QLocale.Language
        LinearA = ... # type: QLocale.Language
        HmongNjua = ... # type: QLocale.Language
        Ho = ... # type: QLocale.Language
        Lezghian = ... # type: QLocale.Language
        Bassa = ... # type: QLocale.Language
        Mono = ... # type: QLocale.Language
        TedimChin = ... # type: QLocale.Language
        Maithili = ... # type: QLocale.Language
        Ahom = ... # type: QLocale.Language
        AmericanSignLanguage = ... # type: QLocale.Language
        ArdhamagadhiPrakrit = ... # type: QLocale.Language
        Bhojpuri = ... # type: QLocale.Language
        HieroglyphicLuwian = ... # type: QLocale.Language
        LiteraryChinese = ... # type: QLocale.Language
        Mazanderani = ... # type: QLocale.Language
        Mru = ... # type: QLocale.Language
        Newari = ... # type: QLocale.Language
        NorthernLuri = ... # type: QLocale.Language
        Palauan = ... # type: QLocale.Language
        Papiamento = ... # type: QLocale.Language
        Saraiki = ... # type: QLocale.Language
        TokelauLanguage = ... # type: QLocale.Language
        TokPisin = ... # type: QLocale.Language
        TuvaluLanguage = ... # type: QLocale.Language
        UncodedLanguages = ... # type: QLocale.Language
        Cantonese = ... # type: QLocale.Language
        Osage = ... # type: QLocale.Language
        Tangut = ... # type: QLocale.Language
        Ido = ... # type: QLocale.Language
        Lojban = ... # type: QLocale.Language
        Sicilian = ... # type: QLocale.Language
        SouthernKurdish = ... # type: QLocale.Language
        WesternBalochi = ... # type: QLocale.Language
        Cebuano = ... # type: QLocale.Language
        Erzya = ... # type: QLocale.Language
        Chickasaw = ... # type: QLocale.Language
        Muscogee = ... # type: QLocale.Language
        Silesian = ... # type: QLocale.Language
        NigerianPidgin = ... # type: QLocale.Language

    class NumberOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __xor__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __ior__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __or__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __iand__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __and__(self, f: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> 'QLocale.NumberOptions': ...
        def __invert__(self) -> 'QLocale.NumberOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class DataSizeFormats(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __xor__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __ior__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __or__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __iand__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __and__(self, f: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat']) -> 'QLocale.DataSizeFormats': ...
        def __invert__(self) -> 'QLocale.DataSizeFormats': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, language: 'QLocale.Language', country: 'QLocale.Country' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QLocale') -> None: ...
    @typing.overload
    def __init__(self, language: 'QLocale.Language', script: 'QLocale.Script', country: 'QLocale.Country') -> None: ...

    def collation(self) -> 'QLocale': ...
    def toULong(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toLong(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def formattedDataSize(self, bytes: int, precision: int = ..., format: typing.Union['QLocale.DataSizeFormats', 'QLocale.DataSizeFormat'] = ...) -> str: ...
    def swap(self, other: 'QLocale') -> None: ...
    def __hash__(self) -> int: ...
    def createSeparatedList(self, list: typing.Iterable[typing.Optional[str]]) -> str: ...
    def quoteString(self, str: typing.Optional[str], style: 'QLocale.QuotationStyle' = ...) -> str: ...
    @staticmethod
    def matchingLocales(language: 'QLocale.Language', script: 'QLocale.Script', country: 'QLocale.Country') -> typing.List['QLocale']: ...
    @staticmethod
    def scriptToString(script: 'QLocale.Script') -> str: ...
    def uiLanguages(self) -> typing.List[str]: ...
    @typing.overload
    def toCurrencyString(self, value: float, symbol: typing.Optional[str] = ...) -> str: ...
    @typing.overload
    def toCurrencyString(self, value: float, symbol: typing.Optional[str], precision: int) -> str: ...
    @typing.overload
    def toCurrencyString(self, value: int, symbol: typing.Optional[str] = ...) -> str: ...
    def currencySymbol(self, format: 'QLocale.CurrencySymbolFormat' = ...) -> str: ...
    def toLower(self, str: typing.Optional[str]) -> str: ...
    def toUpper(self, str: typing.Optional[str]) -> str: ...
    def weekdays(self) -> typing.List[Qt.DayOfWeek]: ...
    def firstDayOfWeek(self) -> Qt.DayOfWeek: ...
    def nativeCountryName(self) -> str: ...
    def nativeLanguageName(self) -> str: ...
    def bcp47Name(self) -> str: ...
    def script(self) -> 'QLocale.Script': ...
    def textDirection(self) -> Qt.LayoutDirection: ...
    def pmText(self) -> str: ...
    def amText(self) -> str: ...
    def standaloneDayName(self, a0: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def standaloneMonthName(self, a0: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def positiveSign(self) -> str: ...
    def measurementSystem(self) -> 'QLocale.MeasurementSystem': ...
    def numberOptions(self) -> 'QLocale.NumberOptions': ...
    def setNumberOptions(self, options: typing.Union['QLocale.NumberOptions', 'QLocale.NumberOption']) -> None: ...
    def dayName(self, a0: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def monthName(self, a0: int, format: 'QLocale.FormatType' = ...) -> str: ...
    def exponential(self) -> str: ...
    def negativeSign(self) -> str: ...
    def zeroDigit(self) -> str: ...
    def percent(self) -> str: ...
    def groupSeparator(self) -> str: ...
    def decimalPoint(self) -> str: ...
    @typing.overload
    def toDateTime(self, string: typing.Optional[str], format: 'QLocale.FormatType' = ...) -> QDateTime: ...
    @typing.overload
    def toDateTime(self, string: typing.Optional[str], format: typing.Optional[str]) -> QDateTime: ...
    @typing.overload
    def toDateTime(self, string: typing.Optional[str], format: 'QLocale.FormatType', cal: QCalendar) -> QDateTime: ...
    @typing.overload
    def toDateTime(self, string: typing.Optional[str], format: typing.Optional[str], cal: QCalendar) -> QDateTime: ...
    @typing.overload
    def toTime(self, string: typing.Optional[str], format: 'QLocale.FormatType' = ...) -> QTime: ...
    @typing.overload
    def toTime(self, string: typing.Optional[str], format: typing.Optional[str]) -> QTime: ...
    @typing.overload
    def toTime(self, string: typing.Optional[str], format: 'QLocale.FormatType', cal: QCalendar) -> QTime: ...
    @typing.overload
    def toTime(self, string: typing.Optional[str], format: typing.Optional[str], cal: QCalendar) -> QTime: ...
    @typing.overload
    def toDate(self, string: typing.Optional[str], format: 'QLocale.FormatType' = ...) -> QDate: ...
    @typing.overload
    def toDate(self, string: typing.Optional[str], format: typing.Optional[str]) -> QDate: ...
    @typing.overload
    def toDate(self, string: typing.Optional[str], format: 'QLocale.FormatType', cal: QCalendar) -> QDate: ...
    @typing.overload
    def toDate(self, string: typing.Optional[str], format: typing.Optional[str], cal: QCalendar) -> QDate: ...
    def dateTimeFormat(self, format: 'QLocale.FormatType' = ...) -> str: ...
    def timeFormat(self, format: 'QLocale.FormatType' = ...) -> str: ...
    def dateFormat(self, format: 'QLocale.FormatType' = ...) -> str: ...
    @staticmethod
    def system() -> 'QLocale': ...
    @staticmethod
    def c() -> 'QLocale': ...
    @staticmethod
    def setDefault(locale: 'QLocale') -> None: ...
    @staticmethod
    def countryToString(country: 'QLocale.Country') -> str: ...
    @staticmethod
    def languageToString(language: 'QLocale.Language') -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def toString(self, i: float, format: str = ..., precision: int = ...) -> str: ...
    @typing.overload
    def toString(self, dateTime: typing.Union[QDateTime, datetime.datetime], format: typing.Optional[str]) -> str: ...
    @typing.overload
    def toString(self, dateTime: typing.Union[QDateTime, datetime.datetime], formatStr: typing.Optional[str], cal: QCalendar) -> str: ...
    @typing.overload
    def toString(self, dateTime: typing.Union[QDateTime, datetime.datetime], format: 'QLocale.FormatType' = ...) -> str: ...
    @typing.overload
    def toString(self, dateTime: typing.Union[QDateTime, datetime.datetime], format: 'QLocale.FormatType', cal: QCalendar) -> str: ...
    @typing.overload
    def toString(self, date: typing.Union[QDate, datetime.date], formatStr: typing.Optional[str]) -> str: ...
    @typing.overload
    def toString(self, date: typing.Union[QDate, datetime.date], formatStr: typing.Optional[str], cal: QCalendar) -> str: ...
    @typing.overload
    def toString(self, date: typing.Union[QDate, datetime.date], format: 'QLocale.FormatType' = ...) -> str: ...
    @typing.overload
    def toString(self, date: typing.Union[QDate, datetime.date], format: 'QLocale.FormatType', cal: QCalendar) -> str: ...
    @typing.overload
    def toString(self, time: typing.Union[QTime, datetime.time], formatStr: typing.Optional[str]) -> str: ...
    @typing.overload
    def toString(self, time: typing.Union[QTime, datetime.time], format: 'QLocale.FormatType' = ...) -> str: ...
    @typing.overload
    def toString(self, i: int) -> str: ...
    def toDouble(self, s: typing.Optional[str]) -> typing.Tuple[float, typing.Optional[bool]]: ...
    def toFloat(self, s: typing.Optional[str]) -> typing.Tuple[float, typing.Optional[bool]]: ...
    def toULongLong(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toLongLong(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toUInt(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toInt(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toUShort(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def toShort(self, s: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def name(self) -> str: ...
    def country(self) -> 'QLocale.Country': ...
    def language(self) -> 'QLocale.Language': ...


class QLockFile(PyQt5.sipsimplewrapper):

    class LockError(int):
        NoError = ... # type: QLockFile.LockError
        LockFailedError = ... # type: QLockFile.LockError
        PermissionError = ... # type: QLockFile.LockError
        UnknownError = ... # type: QLockFile.LockError

    def __init__(self, fileName: typing.Optional[str]) -> None: ...

    def error(self) -> 'QLockFile.LockError': ...
    def removeStaleLockFile(self) -> bool: ...
    def getLockInfo(self) -> typing.Tuple[bool, typing.Optional[int], typing.Optional[str], typing.Optional[str]]: ...
    def isLocked(self) -> bool: ...
    def staleLockTime(self) -> int: ...
    def setStaleLockTime(self, a0: int) -> None: ...
    def unlock(self) -> None: ...
    def tryLock(self, timeout: int = ...) -> bool: ...
    def lock(self) -> bool: ...


class QMessageLogContext(PyQt5.sipsimplewrapper):

    category = ... # type: str
    file = ... # type: str
    function = ... # type: str
    line = ... # type: int


class QMessageLogger(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, file: typing.Optional[str], line: int, function: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, file: typing.Optional[str], line: int, function: typing.Optional[str], category: typing.Optional[str]) -> None: ...

    def info(self, msg: typing.Optional[str]) -> None: ...
    def fatal(self, msg: typing.Optional[str]) -> None: ...
    def critical(self, msg: typing.Optional[str]) -> None: ...
    def warning(self, msg: typing.Optional[str]) -> None: ...
    def debug(self, msg: typing.Optional[str]) -> None: ...


class QLoggingCategory(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, category: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, category: typing.Optional[str], severityLevel: QtMsgType) -> None: ...

    @staticmethod
    def setFilterRules(rules: typing.Optional[str]) -> None: ...
    @staticmethod
    def defaultCategory() -> typing.Optional['QLoggingCategory']: ...
    def __call__(self) -> 'QLoggingCategory': ...
    def categoryName(self) -> typing.Optional[str]: ...
    def isCriticalEnabled(self) -> bool: ...
    def isWarningEnabled(self) -> bool: ...
    def isInfoEnabled(self) -> bool: ...
    def isDebugEnabled(self) -> bool: ...
    def setEnabled(self, type: QtMsgType, enable: bool) -> None: ...
    def isEnabled(self, type: QtMsgType) -> bool: ...


class QMargins(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, aleft: int, atop: int, aright: int, abottom: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMargins') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __add__(self, m2: 'QMargins') -> 'QMargins': ...
    @typing.overload
    def __add__(self, rhs: int) -> 'QMargins': ...
    @typing.overload
    def __add__(self, rectangle: 'QRect') -> 'QRect': ...
    def __radd__(self, lhs: int) -> 'QMargins': ...
    @typing.overload
    def __sub__(self, m2: 'QMargins') -> 'QMargins': ...
    @typing.overload
    def __sub__(self, rhs: int) -> 'QMargins': ...
    @typing.overload
    def __mul__(self, factor: int) -> 'QMargins': ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QMargins': ...
    @typing.overload
    def __truediv__(self, divisor: int) -> 'QMargins': ...
    @typing.overload
    def __truediv__(self, divisor: float) -> 'QMargins': ...
    def __neg__(self) -> 'QMargins': ...
    def __pos__(self) -> 'QMargins': ...
    @typing.overload
    def __itruediv__(self, divisor: int) -> 'QMargins': ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QMargins': ...
    @typing.overload
    def __imul__(self, factor: int) -> 'QMargins': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QMargins': ...
    @typing.overload
    def __isub__(self, margins: 'QMargins') -> 'QMargins': ...
    @typing.overload
    def __isub__(self, margin: int) -> 'QMargins': ...
    @typing.overload
    def __iadd__(self, margins: 'QMargins') -> 'QMargins': ...
    @typing.overload
    def __iadd__(self, margin: int) -> 'QMargins': ...
    def setBottom(self, abottom: int) -> None: ...
    def setRight(self, aright: int) -> None: ...
    def setTop(self, atop: int) -> None: ...
    def setLeft(self, aleft: int) -> None: ...
    def bottom(self) -> int: ...
    def right(self) -> int: ...
    def top(self) -> int: ...
    def left(self) -> int: ...
    def isNull(self) -> bool: ...


class QMarginsF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, aleft: float, atop: float, aright: float, abottom: float) -> None: ...
    @typing.overload
    def __init__(self, margins: QMargins) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMarginsF') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __add__(self, rhs: 'QMarginsF') -> 'QMarginsF': ...
    @typing.overload
    def __add__(self, rhs: float) -> 'QMarginsF': ...
    @typing.overload
    def __add__(self, rhs: 'QRectF') -> 'QRectF': ...
    def __radd__(self, lhs: float) -> 'QMarginsF': ...
    @typing.overload
    def __sub__(self, rhs: 'QMarginsF') -> 'QMarginsF': ...
    @typing.overload
    def __sub__(self, rhs: float) -> 'QMarginsF': ...
    def __mul__(self, rhs: float) -> 'QMarginsF': ...
    def __rmul__(self, lhs: float) -> 'QMarginsF': ...
    def __truediv__(self, divisor: float) -> 'QMarginsF': ...
    def __neg__(self) -> 'QMarginsF': ...
    def __pos__(self) -> 'QMarginsF': ...
    def toMargins(self) -> QMargins: ...
    def __itruediv__(self, divisor: float) -> 'QMarginsF': ...
    def __imul__(self, factor: float) -> 'QMarginsF': ...
    @typing.overload
    def __isub__(self, margins: 'QMarginsF') -> 'QMarginsF': ...
    @typing.overload
    def __isub__(self, subtrahend: float) -> 'QMarginsF': ...
    @typing.overload
    def __iadd__(self, margins: 'QMarginsF') -> 'QMarginsF': ...
    @typing.overload
    def __iadd__(self, addend: float) -> 'QMarginsF': ...
    def setBottom(self, abottom: float) -> None: ...
    def setRight(self, aright: float) -> None: ...
    def setTop(self, atop: float) -> None: ...
    def setLeft(self, aleft: float) -> None: ...
    def bottom(self) -> float: ...
    def right(self) -> float: ...
    def top(self) -> float: ...
    def left(self) -> float: ...
    def isNull(self) -> bool: ...


class QMessageAuthenticationCode(PyQt5.sipsimplewrapper):

    def __init__(self, method: QCryptographicHash.Algorithm, key: typing.Union[QByteArray, bytes, bytearray] = ...) -> None: ...

    @staticmethod
    def hash(message: typing.Union[QByteArray, bytes, bytearray], key: typing.Union[QByteArray, bytes, bytearray], method: QCryptographicHash.Algorithm) -> QByteArray: ...
    def result(self) -> QByteArray: ...
    @typing.overload
    def addData(self, data: typing.Optional[str], length: int) -> None: ...
    @typing.overload
    def addData(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def addData(self, device: typing.Optional[QIODevice]) -> bool: ...
    def setKey(self, key: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    def reset(self) -> None: ...


class QMetaMethod(PyQt5.sipsimplewrapper):

    class MethodType(int):
        Method = ... # type: QMetaMethod.MethodType
        Signal = ... # type: QMetaMethod.MethodType
        Slot = ... # type: QMetaMethod.MethodType
        Constructor = ... # type: QMetaMethod.MethodType

    class Access(int):
        Private = ... # type: QMetaMethod.Access
        Protected = ... # type: QMetaMethod.Access
        Public = ... # type: QMetaMethod.Access

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMetaMethod') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def parameterType(self, index: int) -> int: ...
    def parameterCount(self) -> int: ...
    def returnType(self) -> int: ...
    def name(self) -> QByteArray: ...
    def methodSignature(self) -> QByteArray: ...
    def isValid(self) -> bool: ...
    def methodIndex(self) -> int: ...
    @typing.overload
    def invoke(self, object: typing.Optional[QObject], connectionType: Qt.ConnectionType, returnValue: 'QGenericReturnArgument', value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    def invoke(self, object: typing.Optional[QObject], returnValue: 'QGenericReturnArgument', value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    def invoke(self, object: typing.Optional[QObject], connectionType: Qt.ConnectionType, value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    def invoke(self, object: typing.Optional[QObject], value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    def methodType(self) -> 'QMetaMethod.MethodType': ...
    def access(self) -> 'QMetaMethod.Access': ...
    def tag(self) -> typing.Optional[str]: ...
    def parameterNames(self) -> typing.List[QByteArray]: ...
    def parameterTypes(self) -> typing.List[QByteArray]: ...
    def typeName(self) -> typing.Optional[str]: ...


class QMetaEnum(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMetaEnum') -> None: ...

    def enumName(self) -> typing.Optional[str]: ...
    def isScoped(self) -> bool: ...
    def isValid(self) -> bool: ...
    def valueToKeys(self, value: int) -> QByteArray: ...
    def keysToValue(self, keys: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def valueToKey(self, value: int) -> typing.Optional[str]: ...
    def keyToValue(self, key: typing.Optional[str]) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def scope(self) -> typing.Optional[str]: ...
    def value(self, index: int) -> int: ...
    def key(self, index: int) -> typing.Optional[str]: ...
    def keyCount(self) -> int: ...
    def isFlag(self) -> bool: ...
    def name(self) -> typing.Optional[str]: ...


class QMetaProperty(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMetaProperty') -> None: ...

    def isRequired(self) -> bool: ...
    def relativePropertyIndex(self) -> int: ...
    def isFinal(self) -> bool: ...
    def isConstant(self) -> bool: ...
    def propertyIndex(self) -> int: ...
    def notifySignalIndex(self) -> int: ...
    def notifySignal(self) -> QMetaMethod: ...
    def hasNotifySignal(self) -> bool: ...
    def userType(self) -> int: ...
    def isUser(self, object: typing.Optional[QObject] = ...) -> bool: ...
    def isResettable(self) -> bool: ...
    def isValid(self) -> bool: ...
    def hasStdCppSet(self) -> bool: ...
    def reset(self, obj: typing.Optional[QObject]) -> bool: ...
    def write(self, obj: typing.Optional[QObject], value: typing.Any) -> bool: ...
    def read(self, obj: typing.Optional[QObject]) -> typing.Any: ...
    def enumerator(self) -> QMetaEnum: ...
    def isEnumType(self) -> bool: ...
    def isFlagType(self) -> bool: ...
    def isStored(self, object: typing.Optional[QObject] = ...) -> bool: ...
    def isScriptable(self, object: typing.Optional[QObject] = ...) -> bool: ...
    def isDesignable(self, object: typing.Optional[QObject] = ...) -> bool: ...
    def isWritable(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def type(self) -> 'QVariant.Type': ...
    def typeName(self) -> typing.Optional[str]: ...
    def name(self) -> typing.Optional[str]: ...


class QMetaClassInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMetaClassInfo') -> None: ...

    def value(self) -> typing.Optional[str]: ...
    def name(self) -> typing.Optional[str]: ...


class QMetaType(PyQt5.sipsimplewrapper):

    class TypeFlag(int):
        NeedsConstruction = ... # type: QMetaType.TypeFlag
        NeedsDestruction = ... # type: QMetaType.TypeFlag
        MovableType = ... # type: QMetaType.TypeFlag
        PointerToQObject = ... # type: QMetaType.TypeFlag
        IsEnumeration = ... # type: QMetaType.TypeFlag

    class Type(int):
        UnknownType = ... # type: QMetaType.Type
        Void = ... # type: QMetaType.Type
        Bool = ... # type: QMetaType.Type
        Int = ... # type: QMetaType.Type
        UInt = ... # type: QMetaType.Type
        LongLong = ... # type: QMetaType.Type
        ULongLong = ... # type: QMetaType.Type
        Double = ... # type: QMetaType.Type
        QChar = ... # type: QMetaType.Type
        QVariantMap = ... # type: QMetaType.Type
        QVariantList = ... # type: QMetaType.Type
        QVariantHash = ... # type: QMetaType.Type
        QString = ... # type: QMetaType.Type
        QStringList = ... # type: QMetaType.Type
        QByteArray = ... # type: QMetaType.Type
        QBitArray = ... # type: QMetaType.Type
        QDate = ... # type: QMetaType.Type
        QTime = ... # type: QMetaType.Type
        QDateTime = ... # type: QMetaType.Type
        QUrl = ... # type: QMetaType.Type
        QLocale = ... # type: QMetaType.Type
        QRect = ... # type: QMetaType.Type
        QRectF = ... # type: QMetaType.Type
        QSize = ... # type: QMetaType.Type
        QSizeF = ... # type: QMetaType.Type
        QLine = ... # type: QMetaType.Type
        QLineF = ... # type: QMetaType.Type
        QPoint = ... # type: QMetaType.Type
        QPointF = ... # type: QMetaType.Type
        QRegExp = ... # type: QMetaType.Type
        LastCoreType = ... # type: QMetaType.Type
        FirstGuiType = ... # type: QMetaType.Type
        QFont = ... # type: QMetaType.Type
        QPixmap = ... # type: QMetaType.Type
        QBrush = ... # type: QMetaType.Type
        QColor = ... # type: QMetaType.Type
        QPalette = ... # type: QMetaType.Type
        QIcon = ... # type: QMetaType.Type
        QImage = ... # type: QMetaType.Type
        QPolygon = ... # type: QMetaType.Type
        QRegion = ... # type: QMetaType.Type
        QBitmap = ... # type: QMetaType.Type
        QCursor = ... # type: QMetaType.Type
        QSizePolicy = ... # type: QMetaType.Type
        QKeySequence = ... # type: QMetaType.Type
        QPen = ... # type: QMetaType.Type
        QTextLength = ... # type: QMetaType.Type
        QTextFormat = ... # type: QMetaType.Type
        QMatrix = ... # type: QMetaType.Type
        QTransform = ... # type: QMetaType.Type
        VoidStar = ... # type: QMetaType.Type
        Long = ... # type: QMetaType.Type
        Short = ... # type: QMetaType.Type
        Char = ... # type: QMetaType.Type
        ULong = ... # type: QMetaType.Type
        UShort = ... # type: QMetaType.Type
        UChar = ... # type: QMetaType.Type
        Float = ... # type: QMetaType.Type
        QObjectStar = ... # type: QMetaType.Type
        QMatrix4x4 = ... # type: QMetaType.Type
        QVector2D = ... # type: QMetaType.Type
        QVector3D = ... # type: QMetaType.Type
        QVector4D = ... # type: QMetaType.Type
        QQuaternion = ... # type: QMetaType.Type
        QEasingCurve = ... # type: QMetaType.Type
        QVariant = ... # type: QMetaType.Type
        QUuid = ... # type: QMetaType.Type
        QModelIndex = ... # type: QMetaType.Type
        QPolygonF = ... # type: QMetaType.Type
        SChar = ... # type: QMetaType.Type
        QRegularExpression = ... # type: QMetaType.Type
        QJsonValue = ... # type: QMetaType.Type
        QJsonObject = ... # type: QMetaType.Type
        QJsonArray = ... # type: QMetaType.Type
        QJsonDocument = ... # type: QMetaType.Type
        QByteArrayList = ... # type: QMetaType.Type
        QPersistentModelIndex = ... # type: QMetaType.Type
        QCborSimpleType = ... # type: QMetaType.Type
        QCborValue = ... # type: QMetaType.Type
        QCborArray = ... # type: QMetaType.Type
        QCborMap = ... # type: QMetaType.Type
        QColorSpace = ... # type: QMetaType.Type
        User = ... # type: QMetaType.Type

    class TypeFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __xor__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __ior__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __or__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __iand__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __and__(self, f: typing.Union['QMetaType.TypeFlags', 'QMetaType.TypeFlag']) -> 'QMetaType.TypeFlags': ...
        def __invert__(self) -> 'QMetaType.TypeFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, type: int = ...) -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def name(self) -> QByteArray: ...
    def id(self) -> int: ...
    @staticmethod
    def metaObjectForType(type: int) -> typing.Optional['QMetaObject']: ...
    def isValid(self) -> bool: ...
    def flags(self) -> 'QMetaType.TypeFlags': ...
    @staticmethod
    def typeFlags(type: int) -> 'QMetaType.TypeFlags': ...
    @typing.overload
    @staticmethod
    def isRegistered(type: int) -> bool: ...
    @typing.overload
    def isRegistered(self) -> bool: ...
    @staticmethod
    def typeName(type: int) -> typing.Optional[str]: ...
    @staticmethod
    def type(typeName: typing.Optional[str]) -> int: ...


class QMimeData(QObject):

    def __init__(self) -> None: ...

    def retrieveData(self, mimetype: typing.Optional[str], preferredType: 'QVariant.Type') -> typing.Any: ...
    def removeFormat(self, mimetype: typing.Optional[str]) -> None: ...
    def clear(self) -> None: ...
    def formats(self) -> typing.List[str]: ...
    def hasFormat(self, mimetype: typing.Optional[str]) -> bool: ...
    def setData(self, mimetype: typing.Optional[str], data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    def data(self, mimetype: typing.Optional[str]) -> QByteArray: ...
    def hasColor(self) -> bool: ...
    def setColorData(self, color: typing.Any) -> None: ...
    def colorData(self) -> typing.Any: ...
    def hasImage(self) -> bool: ...
    def setImageData(self, image: typing.Any) -> None: ...
    def imageData(self) -> typing.Any: ...
    def hasHtml(self) -> bool: ...
    def setHtml(self, html: typing.Optional[str]) -> None: ...
    def html(self) -> str: ...
    def hasText(self) -> bool: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def text(self) -> str: ...
    def hasUrls(self) -> bool: ...
    def setUrls(self, urls: typing.Iterable['QUrl']) -> None: ...
    def urls(self) -> typing.List['QUrl']: ...


class QMimeDatabase(PyQt5.sipsimplewrapper):

    class MatchMode(int):
        MatchDefault = ... # type: QMimeDatabase.MatchMode
        MatchExtension = ... # type: QMimeDatabase.MatchMode
        MatchContent = ... # type: QMimeDatabase.MatchMode

    def __init__(self) -> None: ...

    def allMimeTypes(self) -> typing.List['QMimeType']: ...
    def suffixForFileName(self, fileName: typing.Optional[str]) -> str: ...
    @typing.overload
    def mimeTypeForFileNameAndData(self, fileName: typing.Optional[str], device: typing.Optional[QIODevice]) -> 'QMimeType': ...
    @typing.overload
    def mimeTypeForFileNameAndData(self, fileName: typing.Optional[str], data: typing.Union[QByteArray, bytes, bytearray]) -> 'QMimeType': ...
    def mimeTypeForUrl(self, url: 'QUrl') -> 'QMimeType': ...
    @typing.overload
    def mimeTypeForData(self, data: typing.Union[QByteArray, bytes, bytearray]) -> 'QMimeType': ...
    @typing.overload
    def mimeTypeForData(self, device: typing.Optional[QIODevice]) -> 'QMimeType': ...
    def mimeTypesForFileName(self, fileName: typing.Optional[str]) -> typing.List['QMimeType']: ...
    @typing.overload
    def mimeTypeForFile(self, fileName: typing.Optional[str], mode: 'QMimeDatabase.MatchMode' = ...) -> 'QMimeType': ...
    @typing.overload
    def mimeTypeForFile(self, fileInfo: QFileInfo, mode: 'QMimeDatabase.MatchMode' = ...) -> 'QMimeType': ...
    def mimeTypeForName(self, nameOrAlias: typing.Optional[str]) -> 'QMimeType': ...


class QMimeType(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMimeType') -> None: ...

    def __hash__(self) -> int: ...
    def filterString(self) -> str: ...
    def inherits(self, mimeTypeName: typing.Optional[str]) -> bool: ...
    def preferredSuffix(self) -> str: ...
    def suffixes(self) -> typing.List[str]: ...
    def aliases(self) -> typing.List[str]: ...
    def allAncestors(self) -> typing.List[str]: ...
    def parentMimeTypes(self) -> typing.List[str]: ...
    def globPatterns(self) -> typing.List[str]: ...
    def iconName(self) -> str: ...
    def genericIconName(self) -> str: ...
    def comment(self) -> str: ...
    def name(self) -> str: ...
    def isDefault(self) -> bool: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def swap(self, other: 'QMimeType') -> None: ...


class QMutexLocker(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, m: typing.Optional['QMutex']) -> None: ...
    @typing.overload
    def __init__(self, m: typing.Optional['QRecursiveMutex']) -> None: ...

    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...
    def mutex(self) -> typing.Optional['QMutex']: ...
    def relock(self) -> None: ...
    def unlock(self) -> None: ...


class QMutex(PyQt5.sipsimplewrapper):

    class RecursionMode(int):
        NonRecursive = ... # type: QMutex.RecursionMode
        Recursive = ... # type: QMutex.RecursionMode

    def __init__(self, mode: 'QMutex.RecursionMode' = ...) -> None: ...

    def isRecursive(self) -> bool: ...
    def unlock(self) -> None: ...
    def tryLock(self, timeout: int = ...) -> bool: ...
    def lock(self) -> None: ...


class QRecursiveMutex(PyQt5.sipsimplewrapper):

    def __init__(self) -> None: ...


class QSignalBlocker(PyQt5.sipsimplewrapper):

    def __init__(self, o: typing.Optional[QObject]) -> None: ...

    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...
    def unblock(self) -> None: ...
    def reblock(self) -> None: ...


class QObjectCleanupHandler(QObject):

    def __init__(self) -> None: ...

    def clear(self) -> None: ...
    def isEmpty(self) -> bool: ...
    def remove(self, object: typing.Optional[QObject]) -> None: ...
    def add(self, object: typing.Optional[QObject]) -> typing.Optional[QObject]: ...


class QMetaObject(PyQt5.sipsimplewrapper):

    class Connection(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, other: 'QMetaObject.Connection') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMetaObject') -> None: ...

    def inherits(self, metaObject: typing.Optional['QMetaObject']) -> bool: ...
    def constructor(self, index: int) -> QMetaMethod: ...
    def indexOfConstructor(self, constructor: typing.Optional[str]) -> int: ...
    def constructorCount(self) -> int: ...
    @typing.overload
    @staticmethod
    def invokeMethod(obj: typing.Optional[QObject], member: typing.Optional[str], type: Qt.ConnectionType, ret: 'QGenericReturnArgument', value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def invokeMethod(obj: typing.Optional[QObject], member: typing.Optional[str], ret: 'QGenericReturnArgument', value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def invokeMethod(obj: typing.Optional[QObject], member: typing.Optional[str], type: Qt.ConnectionType, value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def invokeMethod(obj: typing.Optional[QObject], member: typing.Optional[str], value0: 'QGenericArgument' = ..., value1: 'QGenericArgument' = ..., value2: 'QGenericArgument' = ..., value3: 'QGenericArgument' = ..., value4: 'QGenericArgument' = ..., value5: 'QGenericArgument' = ..., value6: 'QGenericArgument' = ..., value7: 'QGenericArgument' = ..., value8: 'QGenericArgument' = ..., value9: 'QGenericArgument' = ...) -> typing.Any: ...
    @staticmethod
    def normalizedType(type: typing.Optional[str]) -> QByteArray: ...
    @staticmethod
    def normalizedSignature(method: typing.Optional[str]) -> QByteArray: ...
    @staticmethod
    def connectSlotsByName(o: typing.Optional[QObject]) -> None: ...
    @typing.overload
    @staticmethod
    def checkConnectArgs(signal: typing.Optional[str], method: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def checkConnectArgs(signal: QMetaMethod, method: QMetaMethod) -> bool: ...
    def classInfo(self, index: int) -> QMetaClassInfo: ...
    def property(self, index: int) -> QMetaProperty: ...
    def enumerator(self, index: int) -> QMetaEnum: ...
    def method(self, index: int) -> QMetaMethod: ...
    def indexOfClassInfo(self, name: typing.Optional[str]) -> int: ...
    def indexOfProperty(self, name: typing.Optional[str]) -> int: ...
    def indexOfEnumerator(self, name: typing.Optional[str]) -> int: ...
    def indexOfSlot(self, slot: typing.Optional[str]) -> int: ...
    def indexOfSignal(self, signal: typing.Optional[str]) -> int: ...
    def indexOfMethod(self, method: typing.Optional[str]) -> int: ...
    def classInfoCount(self) -> int: ...
    def propertyCount(self) -> int: ...
    def enumeratorCount(self) -> int: ...
    def methodCount(self) -> int: ...
    def classInfoOffset(self) -> int: ...
    def propertyOffset(self) -> int: ...
    def enumeratorOffset(self) -> int: ...
    def methodOffset(self) -> int: ...
    def userProperty(self) -> QMetaProperty: ...
    def superClass(self) -> typing.Optional['QMetaObject']: ...
    def className(self) -> typing.Optional[str]: ...


class QGenericArgument(PyQt5.sipsimplewrapper): ...


class QGenericReturnArgument(PyQt5.sipsimplewrapper): ...


class QOperatingSystemVersion(PyQt5.sipsimplewrapper):

    class OSType(int):
        Unknown = ... # type: QOperatingSystemVersion.OSType
        Windows = ... # type: QOperatingSystemVersion.OSType
        MacOS = ... # type: QOperatingSystemVersion.OSType
        IOS = ... # type: QOperatingSystemVersion.OSType
        TvOS = ... # type: QOperatingSystemVersion.OSType
        WatchOS = ... # type: QOperatingSystemVersion.OSType
        Android = ... # type: QOperatingSystemVersion.OSType

    AndroidJellyBean = ... # type: 'QOperatingSystemVersion'
    AndroidJellyBean_MR1 = ... # type: 'QOperatingSystemVersion'
    AndroidJellyBean_MR2 = ... # type: 'QOperatingSystemVersion'
    AndroidKitKat = ... # type: 'QOperatingSystemVersion'
    AndroidLollipop = ... # type: 'QOperatingSystemVersion'
    AndroidLollipop_MR1 = ... # type: 'QOperatingSystemVersion'
    AndroidMarshmallow = ... # type: 'QOperatingSystemVersion'
    AndroidNougat = ... # type: 'QOperatingSystemVersion'
    AndroidNougat_MR1 = ... # type: 'QOperatingSystemVersion'
    AndroidOreo = ... # type: 'QOperatingSystemVersion'
    MacOSBigSur = ... # type: 'QOperatingSystemVersion'
    MacOSCatalina = ... # type: 'QOperatingSystemVersion'
    MacOSHighSierra = ... # type: 'QOperatingSystemVersion'
    MacOSMojave = ... # type: 'QOperatingSystemVersion'
    MacOSSierra = ... # type: 'QOperatingSystemVersion'
    OSXElCapitan = ... # type: 'QOperatingSystemVersion'
    OSXMavericks = ... # type: 'QOperatingSystemVersion'
    OSXYosemite = ... # type: 'QOperatingSystemVersion'
    Windows10 = ... # type: 'QOperatingSystemVersion'
    Windows7 = ... # type: 'QOperatingSystemVersion'
    Windows8 = ... # type: 'QOperatingSystemVersion'
    Windows8_1 = ... # type: 'QOperatingSystemVersion'

    @typing.overload
    def __init__(self, osType: 'QOperatingSystemVersion.OSType', vmajor: int, vminor: int = ..., vmicro: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QOperatingSystemVersion') -> None: ...

    def __lt__(self, rhs: 'QOperatingSystemVersion') -> bool: ...
    def __le__(self, rhs: 'QOperatingSystemVersion') -> bool: ...
    def __gt__(self, rhs: 'QOperatingSystemVersion') -> bool: ...
    def __ge__(self, rhs: 'QOperatingSystemVersion') -> bool: ...
    def name(self) -> str: ...
    def type(self) -> 'QOperatingSystemVersion.OSType': ...
    def segmentCount(self) -> int: ...
    def microVersion(self) -> int: ...
    def minorVersion(self) -> int: ...
    def majorVersion(self) -> int: ...
    @staticmethod
    def currentType() -> 'QOperatingSystemVersion.OSType': ...
    @staticmethod
    def current() -> 'QOperatingSystemVersion': ...


class QParallelAnimationGroup(QAnimationGroup):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def updateDirection(self, direction: QAbstractAnimation.Direction) -> None: ...
    def updateState(self, newState: QAbstractAnimation.State, oldState: QAbstractAnimation.State) -> None: ...
    def updateCurrentTime(self, currentTime: int) -> None: ...
    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    def duration(self) -> int: ...


class QPauseAnimation(QAbstractAnimation):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, msecs: int, parent: typing.Optional[QObject] = ...) -> None: ...

    def updateCurrentTime(self, a0: int) -> None: ...
    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def setDuration(self, msecs: int) -> None: ...
    def duration(self) -> int: ...


class QVariantAnimation(QAbstractAnimation):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def interpolated(self, from_: typing.Any, to: typing.Any, progress: float) -> typing.Any: ...
    def updateCurrentValue(self, value: typing.Any) -> None: ...
    def updateState(self, newState: QAbstractAnimation.State, oldState: QAbstractAnimation.State) -> None: ...
    def updateCurrentTime(self, a0: int) -> None: ...
    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    valueChanged: typing.ClassVar[pyqtSignal]
    def setEasingCurve(self, easing: typing.Union[QEasingCurve, QEasingCurve.Type]) -> None: ...
    def easingCurve(self) -> QEasingCurve: ...
    def setDuration(self, msecs: int) -> None: ...
    def duration(self) -> int: ...
    def currentValue(self) -> typing.Any: ...
    def setKeyValues(self, values: typing.Iterable[typing.Tuple[float, typing.Any]]) -> None: ...
    def keyValues(self) -> typing.List[typing.Tuple[float, typing.Any]]: ...
    def setKeyValueAt(self, step: float, value: typing.Any) -> None: ...
    def keyValueAt(self, step: float) -> typing.Any: ...
    def setEndValue(self, value: typing.Any) -> None: ...
    def endValue(self) -> typing.Any: ...
    def setStartValue(self, value: typing.Any) -> None: ...
    def startValue(self) -> typing.Any: ...


class QPropertyAnimation(QVariantAnimation):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, target: typing.Optional[QObject], propertyName: typing.Union[QByteArray, bytes, bytearray], parent: typing.Optional[QObject] = ...) -> None: ...

    def updateState(self, newState: QAbstractAnimation.State, oldState: QAbstractAnimation.State) -> None: ...
    def updateCurrentValue(self, value: typing.Any) -> None: ...
    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    def setPropertyName(self, propertyName: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    def propertyName(self) -> QByteArray: ...
    def setTargetObject(self, target: typing.Optional[QObject]) -> None: ...
    def targetObject(self) -> typing.Optional[QObject]: ...


class QPluginLoader(QObject):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], parent: typing.Optional[QObject] = ...) -> None: ...

    def loadHints(self) -> QLibrary.LoadHints: ...
    def setLoadHints(self, loadHints: typing.Union[QLibrary.LoadHints, QLibrary.LoadHint]) -> None: ...
    def errorString(self) -> str: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def isLoaded(self) -> bool: ...
    def unload(self) -> bool: ...
    def load(self) -> bool: ...
    @staticmethod
    def staticInstances() -> typing.List[QObject]: ...
    def instance(self) -> typing.Optional[QObject]: ...


class QPoint(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: int, ypos: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPoint') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, p2: 'QPoint') -> 'QPoint': ...
    def __sub__(self, p2: 'QPoint') -> 'QPoint': ...
    @typing.overload
    def __mul__(self, c: int) -> 'QPoint': ...
    @typing.overload
    def __mul__(self, c: float) -> 'QPoint': ...
    @typing.overload
    def __rmul__(self, c: int) -> 'QPoint': ...
    @typing.overload
    def __rmul__(self, c: float) -> 'QPoint': ...
    def __truediv__(self, c: float) -> 'QPoint': ...
    def __neg__(self) -> 'QPoint': ...
    def __pos__(self) -> 'QPoint': ...
    def transposed(self) -> 'QPoint': ...
    @staticmethod
    def dotProduct(p1: 'QPoint', p2: 'QPoint') -> int: ...
    def __itruediv__(self, c: float) -> 'QPoint': ...
    @typing.overload
    def __imul__(self, c: int) -> 'QPoint': ...
    @typing.overload
    def __imul__(self, c: float) -> 'QPoint': ...
    def __isub__(self, p: 'QPoint') -> 'QPoint': ...
    def __iadd__(self, p: 'QPoint') -> 'QPoint': ...
    def setY(self, ypos: int) -> None: ...
    def setX(self, xpos: int) -> None: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...
    def manhattanLength(self) -> int: ...


class QPointF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float) -> None: ...
    @typing.overload
    def __init__(self, p: QPoint) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPointF') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, p2: typing.Union['QPointF', QPoint]) -> 'QPointF': ...
    def __sub__(self, p2: typing.Union['QPointF', QPoint]) -> 'QPointF': ...
    def __mul__(self, c: float) -> 'QPointF': ...
    def __rmul__(self, c: float) -> 'QPointF': ...
    def __truediv__(self, c: float) -> 'QPointF': ...
    def __neg__(self) -> 'QPointF': ...
    def __pos__(self) -> 'QPointF': ...
    def transposed(self) -> 'QPointF': ...
    @staticmethod
    def dotProduct(p1: typing.Union['QPointF', QPoint], p2: typing.Union['QPointF', QPoint]) -> float: ...
    def manhattanLength(self) -> float: ...
    def toPoint(self) -> QPoint: ...
    def __itruediv__(self, c: float) -> 'QPointF': ...
    def __imul__(self, c: float) -> 'QPointF': ...
    def __isub__(self, p: typing.Union['QPointF', QPoint]) -> 'QPointF': ...
    def __iadd__(self, p: typing.Union['QPointF', QPoint]) -> 'QPointF': ...
    def setY(self, ypos: float) -> None: ...
    def setX(self, xpos: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def __bool__(self) -> int: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...


class QProcess(QIODevice):

    class InputChannelMode(int):
        ManagedInputChannel = ... # type: QProcess.InputChannelMode
        ForwardedInputChannel = ... # type: QProcess.InputChannelMode

    class ProcessChannelMode(int):
        SeparateChannels = ... # type: QProcess.ProcessChannelMode
        MergedChannels = ... # type: QProcess.ProcessChannelMode
        ForwardedChannels = ... # type: QProcess.ProcessChannelMode
        ForwardedOutputChannel = ... # type: QProcess.ProcessChannelMode
        ForwardedErrorChannel = ... # type: QProcess.ProcessChannelMode

    class ProcessChannel(int):
        StandardOutput = ... # type: QProcess.ProcessChannel
        StandardError = ... # type: QProcess.ProcessChannel

    class ProcessState(int):
        NotRunning = ... # type: QProcess.ProcessState
        Starting = ... # type: QProcess.ProcessState
        Running = ... # type: QProcess.ProcessState

    class ProcessError(int):
        FailedToStart = ... # type: QProcess.ProcessError
        Crashed = ... # type: QProcess.ProcessError
        Timedout = ... # type: QProcess.ProcessError
        ReadError = ... # type: QProcess.ProcessError
        WriteError = ... # type: QProcess.ProcessError
        UnknownError = ... # type: QProcess.ProcessError

    class ExitStatus(int):
        NormalExit = ... # type: QProcess.ExitStatus
        CrashExit = ... # type: QProcess.ExitStatus

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def processId(self) -> int: ...
    @staticmethod
    def nullDevice() -> str: ...
    def setInputChannelMode(self, mode: 'QProcess.InputChannelMode') -> None: ...
    def inputChannelMode(self) -> 'QProcess.InputChannelMode': ...
    def open(self, mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> bool: ...
    def setArguments(self, arguments: typing.Iterable[typing.Optional[str]]) -> None: ...
    def arguments(self) -> typing.List[str]: ...
    def setProgram(self, program: typing.Optional[str]) -> None: ...
    def program(self) -> str: ...
    def processEnvironment(self) -> 'QProcessEnvironment': ...
    def setProcessEnvironment(self, environment: 'QProcessEnvironment') -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    def setupChildProcess(self) -> None: ...
    def setProcessState(self, state: 'QProcess.ProcessState') -> None: ...
    errorOccurred: typing.ClassVar[pyqtSignal]
    readyReadStandardError: typing.ClassVar[pyqtSignal]
    readyReadStandardOutput: typing.ClassVar[pyqtSignal]
    stateChanged: typing.ClassVar[pyqtSignal]
    finished: typing.ClassVar[pyqtSignal]
    started: typing.ClassVar[pyqtSignal]
    def kill(self) -> None: ...
    def terminate(self) -> None: ...
    def setStandardOutputProcess(self, destination: typing.Optional['QProcess']) -> None: ...
    def setStandardErrorFile(self, fileName: typing.Optional[str], mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...
    def setStandardOutputFile(self, fileName: typing.Optional[str], mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...
    def setStandardInputFile(self, fileName: typing.Optional[str]) -> None: ...
    def setProcessChannelMode(self, mode: 'QProcess.ProcessChannelMode') -> None: ...
    def processChannelMode(self) -> 'QProcess.ProcessChannelMode': ...
    @staticmethod
    def systemEnvironment() -> typing.List[str]: ...
    @typing.overload
    @staticmethod
    def startDetached(program: typing.Optional[str], arguments: typing.Iterable[typing.Optional[str]], workingDirectory: typing.Optional[str]) -> typing.Tuple[bool, typing.Optional[int]]: ...
    @typing.overload
    @staticmethod
    def startDetached(program: typing.Optional[str], arguments: typing.Iterable[typing.Optional[str]]) -> bool: ...
    @typing.overload
    @staticmethod
    def startDetached(program: typing.Optional[str]) -> bool: ...
    @typing.overload
    def startDetached(self) -> typing.Tuple[bool, typing.Optional[int]]: ...
    @typing.overload
    @staticmethod
    def execute(program: typing.Optional[str], arguments: typing.Iterable[typing.Optional[str]]) -> int: ...
    @typing.overload
    @staticmethod
    def execute(program: typing.Optional[str]) -> int: ...
    def atEnd(self) -> bool: ...
    def close(self) -> None: ...
    def canReadLine(self) -> bool: ...
    def isSequential(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def exitStatus(self) -> 'QProcess.ExitStatus': ...
    def exitCode(self) -> int: ...
    def readAllStandardError(self) -> QByteArray: ...
    def readAllStandardOutput(self) -> QByteArray: ...
    def waitForFinished(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForStarted(self, msecs: int = ...) -> bool: ...
    def pid(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def state(self) -> 'QProcess.ProcessState': ...
    error: typing.ClassVar[pyqtSignal]
    def setWorkingDirectory(self, dir: typing.Optional[str]) -> None: ...
    def workingDirectory(self) -> str: ...
    def closeWriteChannel(self) -> None: ...
    def closeReadChannel(self, channel: 'QProcess.ProcessChannel') -> None: ...
    def setReadChannel(self, channel: 'QProcess.ProcessChannel') -> None: ...
    def readChannel(self) -> 'QProcess.ProcessChannel': ...
    @typing.overload
    def start(self, program: typing.Optional[str], arguments: typing.Iterable[typing.Optional[str]], mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...
    @typing.overload
    def start(self, command: typing.Optional[str], mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...
    @typing.overload
    def start(self, mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...


class QProcessEnvironment(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QProcessEnvironment') -> None: ...

    def swap(self, other: 'QProcessEnvironment') -> None: ...
    def keys(self) -> typing.List[str]: ...
    @staticmethod
    def systemEnvironment() -> 'QProcessEnvironment': ...
    def toStringList(self) -> typing.List[str]: ...
    def value(self, name: typing.Optional[str], defaultValue: typing.Optional[str] = ...) -> str: ...
    def remove(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def insert(self, name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def insert(self, e: 'QProcessEnvironment') -> None: ...
    def contains(self, name: typing.Optional[str]) -> bool: ...
    def clear(self) -> None: ...
    def isEmpty(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QRandomGenerator(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, seed: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QRandomGenerator') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @staticmethod
    def securelySeeded() -> 'QRandomGenerator': ...
    @staticmethod
    def global_() -> typing.Optional['QRandomGenerator']: ...
    @staticmethod
    def system() -> typing.Optional['QRandomGenerator']: ...
    @staticmethod
    def max() -> int: ...
    @staticmethod
    def min() -> int: ...
    def discard(self, z: int) -> None: ...
    def seed(self, seed: int = ...) -> None: ...
    def __call__(self) -> int: ...
    @typing.overload
    def bounded(self, highest: float) -> float: ...
    @typing.overload
    def bounded(self, highest: int) -> int: ...
    @typing.overload
    def bounded(self, lowest: int, highest: int) -> int: ...
    def generateDouble(self) -> float: ...
    def generate64(self) -> int: ...
    def generate(self) -> int: ...


class QReadWriteLock(PyQt5.sipsimplewrapper):

    class RecursionMode(int):
        NonRecursive = ... # type: QReadWriteLock.RecursionMode
        Recursive = ... # type: QReadWriteLock.RecursionMode

    def __init__(self, recursionMode: 'QReadWriteLock.RecursionMode' = ...) -> None: ...

    def unlock(self) -> None: ...
    @typing.overload
    def tryLockForWrite(self) -> bool: ...
    @typing.overload
    def tryLockForWrite(self, timeout: int) -> bool: ...
    def lockForWrite(self) -> None: ...
    @typing.overload
    def tryLockForRead(self) -> bool: ...
    @typing.overload
    def tryLockForRead(self, timeout: int) -> bool: ...
    def lockForRead(self) -> None: ...


class QReadLocker(PyQt5.sipsimplewrapper):

    def __init__(self, areadWriteLock: typing.Optional[QReadWriteLock]) -> None: ...

    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...
    def readWriteLock(self) -> typing.Optional[QReadWriteLock]: ...
    def relock(self) -> None: ...
    def unlock(self) -> None: ...


class QWriteLocker(PyQt5.sipsimplewrapper):

    def __init__(self, areadWriteLock: typing.Optional[QReadWriteLock]) -> None: ...

    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...
    def readWriteLock(self) -> typing.Optional[QReadWriteLock]: ...
    def relock(self) -> None: ...
    def unlock(self) -> None: ...


class QRect(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, aleft: int, atop: int, awidth: int, aheight: int) -> None: ...
    @typing.overload
    def __init__(self, atopLeft: QPoint, abottomRight: QPoint) -> None: ...
    @typing.overload
    def __init__(self, atopLeft: QPoint, asize: 'QSize') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRect') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, margins: QMargins) -> 'QRect': ...
    def __sub__(self, rhs: QMargins) -> 'QRect': ...
    def transposed(self) -> 'QRect': ...
    def __isub__(self, margins: QMargins) -> 'QRect': ...
    def __iadd__(self, margins: QMargins) -> 'QRect': ...
    def marginsRemoved(self, margins: QMargins) -> 'QRect': ...
    def marginsAdded(self, margins: QMargins) -> 'QRect': ...
    def united(self, r: 'QRect') -> 'QRect': ...
    def intersected(self, other: 'QRect') -> 'QRect': ...
    def __iand__(self, r: 'QRect') -> 'QRect': ...
    def __ior__(self, r: 'QRect') -> 'QRect': ...
    def setSize(self, s: 'QSize') -> None: ...
    def setHeight(self, h: int) -> None: ...
    def setWidth(self, w: int) -> None: ...
    def adjust(self, dx1: int, dy1: int, dx2: int, dy2: int) -> None: ...
    def adjusted(self, xp1: int, yp1: int, xp2: int, yp2: int) -> 'QRect': ...
    def setCoords(self, xp1: int, yp1: int, xp2: int, yp2: int) -> None: ...
    def getCoords(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def setRect(self, ax: int, ay: int, aw: int, ah: int) -> None: ...
    def getRect(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def moveBottomLeft(self, p: QPoint) -> None: ...
    def moveTopRight(self, p: QPoint) -> None: ...
    def moveBottomRight(self, p: QPoint) -> None: ...
    def moveTopLeft(self, p: QPoint) -> None: ...
    def moveBottom(self, pos: int) -> None: ...
    def moveRight(self, pos: int) -> None: ...
    def moveTop(self, pos: int) -> None: ...
    def moveLeft(self, pos: int) -> None: ...
    @typing.overload
    def moveTo(self, ax: int, ay: int) -> None: ...
    @typing.overload
    def moveTo(self, p: QPoint) -> None: ...
    @typing.overload
    def translated(self, dx: int, dy: int) -> 'QRect': ...
    @typing.overload
    def translated(self, p: QPoint) -> 'QRect': ...
    @typing.overload
    def translate(self, dx: int, dy: int) -> None: ...
    @typing.overload
    def translate(self, p: QPoint) -> None: ...
    def size(self) -> 'QSize': ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def center(self) -> QPoint: ...
    def bottomLeft(self) -> QPoint: ...
    def topRight(self) -> QPoint: ...
    def bottomRight(self) -> QPoint: ...
    def topLeft(self) -> QPoint: ...
    def setY(self, ay: int) -> None: ...
    def setX(self, ax: int) -> None: ...
    def setBottomLeft(self, p: QPoint) -> None: ...
    def setTopRight(self, p: QPoint) -> None: ...
    def setBottomRight(self, p: QPoint) -> None: ...
    def setTopLeft(self, p: QPoint) -> None: ...
    def setBottom(self, pos: int) -> None: ...
    def setRight(self, pos: int) -> None: ...
    def setTop(self, pos: int) -> None: ...
    def setLeft(self, pos: int) -> None: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def bottom(self) -> int: ...
    def right(self) -> int: ...
    def top(self) -> int: ...
    def left(self) -> int: ...
    def __bool__(self) -> int: ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...
    def intersects(self, r: 'QRect') -> bool: ...
    @typing.overload
    def __contains__(self, p: QPoint) -> int: ...
    @typing.overload
    def __contains__(self, r: 'QRect') -> int: ...
    @typing.overload
    def contains(self, point: QPoint, proper: bool = ...) -> bool: ...
    @typing.overload
    def contains(self, rectangle: 'QRect', proper: bool = ...) -> bool: ...
    @typing.overload
    def contains(self, ax: int, ay: int, aproper: bool) -> bool: ...
    @typing.overload
    def contains(self, ax: int, ay: int) -> bool: ...
    def __and__(self, r: 'QRect') -> 'QRect': ...
    def __or__(self, r: 'QRect') -> 'QRect': ...
    def moveCenter(self, p: QPoint) -> None: ...
    def normalized(self) -> 'QRect': ...


class QRectF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, atopLeft: typing.Union[QPointF, QPoint], asize: 'QSizeF') -> None: ...
    @typing.overload
    def __init__(self, atopLeft: typing.Union[QPointF, QPoint], abottomRight: typing.Union[QPointF, QPoint]) -> None: ...
    @typing.overload
    def __init__(self, aleft: float, atop: float, awidth: float, aheight: float) -> None: ...
    @typing.overload
    def __init__(self, r: QRect) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRectF') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, rhs: QMarginsF) -> 'QRectF': ...
    def __sub__(self, rhs: QMarginsF) -> 'QRectF': ...
    def transposed(self) -> 'QRectF': ...
    def __isub__(self, margins: QMarginsF) -> 'QRectF': ...
    def __iadd__(self, margins: QMarginsF) -> 'QRectF': ...
    def marginsRemoved(self, margins: QMarginsF) -> 'QRectF': ...
    def marginsAdded(self, margins: QMarginsF) -> 'QRectF': ...
    def toRect(self) -> QRect: ...
    def toAlignedRect(self) -> QRect: ...
    def united(self, r: 'QRectF') -> 'QRectF': ...
    def intersected(self, r: 'QRectF') -> 'QRectF': ...
    def __iand__(self, r: 'QRectF') -> 'QRectF': ...
    def __ior__(self, r: 'QRectF') -> 'QRectF': ...
    def setSize(self, s: 'QSizeF') -> None: ...
    def setHeight(self, ah: float) -> None: ...
    def setWidth(self, aw: float) -> None: ...
    def adjusted(self, xp1: float, yp1: float, xp2: float, yp2: float) -> 'QRectF': ...
    def adjust(self, xp1: float, yp1: float, xp2: float, yp2: float) -> None: ...
    def setCoords(self, xp1: float, yp1: float, xp2: float, yp2: float) -> None: ...
    def getCoords(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    def setRect(self, ax: float, ay: float, aaw: float, aah: float) -> None: ...
    def getRect(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    @typing.overload
    def translated(self, dx: float, dy: float) -> 'QRectF': ...
    @typing.overload
    def translated(self, p: typing.Union[QPointF, QPoint]) -> 'QRectF': ...
    @typing.overload
    def moveTo(self, ax: float, ay: float) -> None: ...
    @typing.overload
    def moveTo(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def size(self) -> 'QSizeF': ...
    def height(self) -> float: ...
    def width(self) -> float: ...
    def moveCenter(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def moveBottomRight(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def moveBottomLeft(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def moveTopRight(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def moveTopLeft(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def moveBottom(self, pos: float) -> None: ...
    def moveRight(self, pos: float) -> None: ...
    def moveTop(self, pos: float) -> None: ...
    def moveLeft(self, pos: float) -> None: ...
    def center(self) -> QPointF: ...
    def setBottomRight(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def setBottomLeft(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def setTopRight(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def setTopLeft(self, p: typing.Union[QPointF, QPoint]) -> None: ...
    def setBottom(self, pos: float) -> None: ...
    def setTop(self, pos: float) -> None: ...
    def setRight(self, pos: float) -> None: ...
    def setLeft(self, pos: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def __bool__(self) -> int: ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def isNull(self) -> bool: ...
    def intersects(self, r: 'QRectF') -> bool: ...
    @typing.overload
    def __contains__(self, p: typing.Union[QPointF, QPoint]) -> int: ...
    @typing.overload
    def __contains__(self, r: 'QRectF') -> int: ...
    @typing.overload
    def contains(self, p: typing.Union[QPointF, QPoint]) -> bool: ...
    @typing.overload
    def contains(self, r: 'QRectF') -> bool: ...
    @typing.overload
    def contains(self, ax: float, ay: float) -> bool: ...
    def __and__(self, r: 'QRectF') -> 'QRectF': ...
    def __or__(self, r: 'QRectF') -> 'QRectF': ...
    def bottomLeft(self) -> QPointF: ...
    def topRight(self) -> QPointF: ...
    def bottomRight(self) -> QPointF: ...
    def topLeft(self) -> QPointF: ...
    def setY(self, pos: float) -> None: ...
    def setX(self, pos: float) -> None: ...
    def bottom(self) -> float: ...
    def right(self) -> float: ...
    def top(self) -> float: ...
    def left(self) -> float: ...
    def normalized(self) -> 'QRectF': ...
    def __repr__(self) -> str: ...


class QRegExp(PyQt5.sipsimplewrapper):

    class CaretMode(int):
        CaretAtZero = ... # type: QRegExp.CaretMode
        CaretAtOffset = ... # type: QRegExp.CaretMode
        CaretWontMatch = ... # type: QRegExp.CaretMode

    class PatternSyntax(int):
        RegExp = ... # type: QRegExp.PatternSyntax
        RegExp2 = ... # type: QRegExp.PatternSyntax
        Wildcard = ... # type: QRegExp.PatternSyntax
        FixedString = ... # type: QRegExp.PatternSyntax
        WildcardUnix = ... # type: QRegExp.PatternSyntax
        W3CXmlSchema11 = ... # type: QRegExp.PatternSyntax

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pattern: typing.Optional[str], cs: Qt.CaseSensitivity = ..., syntax: 'QRegExp.PatternSyntax' = ...) -> None: ...
    @typing.overload
    def __init__(self, rx: 'QRegExp') -> None: ...

    def __hash__(self) -> int: ...
    def swap(self, other: 'QRegExp') -> None: ...
    def captureCount(self) -> int: ...
    @staticmethod
    def escape(str: typing.Optional[str]) -> str: ...
    def errorString(self) -> str: ...
    def pos(self, nth: int = ...) -> int: ...
    def cap(self, nth: int = ...) -> str: ...
    def capturedTexts(self) -> typing.List[str]: ...
    def matchedLength(self) -> int: ...
    def lastIndexIn(self, str: typing.Optional[str], offset: int = ..., caretMode: 'QRegExp.CaretMode' = ...) -> int: ...
    def indexIn(self, str: typing.Optional[str], offset: int = ..., caretMode: 'QRegExp.CaretMode' = ...) -> int: ...
    def exactMatch(self, str: typing.Optional[str]) -> bool: ...
    def setMinimal(self, minimal: bool) -> None: ...
    def isMinimal(self) -> bool: ...
    def setPatternSyntax(self, syntax: 'QRegExp.PatternSyntax') -> None: ...
    def patternSyntax(self) -> 'QRegExp.PatternSyntax': ...
    def setCaseSensitivity(self, cs: Qt.CaseSensitivity) -> None: ...
    def caseSensitivity(self) -> Qt.CaseSensitivity: ...
    def setPattern(self, pattern: typing.Optional[str]) -> None: ...
    def pattern(self) -> str: ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __repr__(self) -> str: ...


class QRegularExpression(PyQt5.sipsimplewrapper):

    class MatchOption(int):
        NoMatchOption = ... # type: QRegularExpression.MatchOption
        AnchoredMatchOption = ... # type: QRegularExpression.MatchOption
        DontCheckSubjectStringMatchOption = ... # type: QRegularExpression.MatchOption

    class MatchType(int):
        NormalMatch = ... # type: QRegularExpression.MatchType
        PartialPreferCompleteMatch = ... # type: QRegularExpression.MatchType
        PartialPreferFirstMatch = ... # type: QRegularExpression.MatchType
        NoMatch = ... # type: QRegularExpression.MatchType

    class PatternOption(int):
        NoPatternOption = ... # type: QRegularExpression.PatternOption
        CaseInsensitiveOption = ... # type: QRegularExpression.PatternOption
        DotMatchesEverythingOption = ... # type: QRegularExpression.PatternOption
        MultilineOption = ... # type: QRegularExpression.PatternOption
        ExtendedPatternSyntaxOption = ... # type: QRegularExpression.PatternOption
        InvertedGreedinessOption = ... # type: QRegularExpression.PatternOption
        DontCaptureOption = ... # type: QRegularExpression.PatternOption
        UseUnicodePropertiesOption = ... # type: QRegularExpression.PatternOption
        OptimizeOnFirstUsageOption = ... # type: QRegularExpression.PatternOption
        DontAutomaticallyOptimizeOption = ... # type: QRegularExpression.PatternOption

    class PatternOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __xor__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __ior__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __or__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __iand__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __and__(self, f: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> 'QRegularExpression.PatternOptions': ...
        def __invert__(self) -> 'QRegularExpression.PatternOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MatchOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __xor__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __ior__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __or__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __iand__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __and__(self, f: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption']) -> 'QRegularExpression.MatchOptions': ...
        def __invert__(self) -> 'QRegularExpression.MatchOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pattern: typing.Optional[str], options: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption'] = ...) -> None: ...
    @typing.overload
    def __init__(self, re: 'QRegularExpression') -> None: ...

    @staticmethod
    def anchoredPattern(expression: typing.Optional[str]) -> str: ...
    @staticmethod
    def wildcardToRegularExpression(str: typing.Optional[str]) -> str: ...
    def __hash__(self) -> int: ...
    def optimize(self) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def namedCaptureGroups(self) -> typing.List[str]: ...
    @staticmethod
    def escape(str: typing.Optional[str]) -> str: ...
    def globalMatch(self, subject: typing.Optional[str], offset: int = ..., matchType: 'QRegularExpression.MatchType' = ..., matchOptions: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption'] = ...) -> 'QRegularExpressionMatchIterator': ...
    def match(self, subject: typing.Optional[str], offset: int = ..., matchType: 'QRegularExpression.MatchType' = ..., matchOptions: typing.Union['QRegularExpression.MatchOptions', 'QRegularExpression.MatchOption'] = ...) -> 'QRegularExpressionMatch': ...
    def captureCount(self) -> int: ...
    def errorString(self) -> str: ...
    def patternErrorOffset(self) -> int: ...
    def isValid(self) -> bool: ...
    def setPattern(self, pattern: typing.Optional[str]) -> None: ...
    def pattern(self) -> str: ...
    def swap(self, re: 'QRegularExpression') -> None: ...
    def __repr__(self) -> str: ...
    def setPatternOptions(self, options: typing.Union['QRegularExpression.PatternOptions', 'QRegularExpression.PatternOption']) -> None: ...
    def patternOptions(self) -> 'QRegularExpression.PatternOptions': ...


class QRegularExpressionMatch(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, match: 'QRegularExpressionMatch') -> None: ...

    @typing.overload
    def capturedEnd(self, nth: int = ...) -> int: ...
    @typing.overload
    def capturedEnd(self, name: typing.Optional[str]) -> int: ...
    @typing.overload
    def capturedLength(self, nth: int = ...) -> int: ...
    @typing.overload
    def capturedLength(self, name: typing.Optional[str]) -> int: ...
    @typing.overload
    def capturedStart(self, nth: int = ...) -> int: ...
    @typing.overload
    def capturedStart(self, name: typing.Optional[str]) -> int: ...
    def capturedTexts(self) -> typing.List[str]: ...
    @typing.overload
    def captured(self, nth: int = ...) -> str: ...
    @typing.overload
    def captured(self, name: typing.Optional[str]) -> str: ...
    def lastCapturedIndex(self) -> int: ...
    def isValid(self) -> bool: ...
    def hasPartialMatch(self) -> bool: ...
    def hasMatch(self) -> bool: ...
    def matchOptions(self) -> QRegularExpression.MatchOptions: ...
    def matchType(self) -> QRegularExpression.MatchType: ...
    def regularExpression(self) -> QRegularExpression: ...
    def swap(self, match: 'QRegularExpressionMatch') -> None: ...


class QRegularExpressionMatchIterator(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, iterator: 'QRegularExpressionMatchIterator') -> None: ...

    def matchOptions(self) -> QRegularExpression.MatchOptions: ...
    def matchType(self) -> QRegularExpression.MatchType: ...
    def regularExpression(self) -> QRegularExpression: ...
    def peekNext(self) -> QRegularExpressionMatch: ...
    def next(self) -> QRegularExpressionMatch: ...
    def hasNext(self) -> bool: ...
    def isValid(self) -> bool: ...
    def swap(self, iterator: 'QRegularExpressionMatchIterator') -> None: ...


class QResource(PyQt5.sipsimplewrapper):

    class Compression(int):
        NoCompression = ... # type: QResource.Compression
        ZlibCompression = ... # type: QResource.Compression
        ZstdCompression = ... # type: QResource.Compression

    def __init__(self, fileName: typing.Optional[str] = ..., locale: QLocale = ...) -> None: ...

    def uncompressedData(self) -> QByteArray: ...
    def uncompressedSize(self) -> int: ...
    def compressionAlgorithm(self) -> 'QResource.Compression': ...
    def lastModified(self) -> QDateTime: ...
    def isFile(self) -> bool: ...
    def isDir(self) -> bool: ...
    def children(self) -> typing.List[str]: ...
    @staticmethod
    def unregisterResourceData(rccData: typing.Optional[bytes], mapRoot: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def unregisterResource(rccFileName: typing.Optional[str], mapRoot: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def registerResourceData(rccData: typing.Optional[bytes], mapRoot: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def registerResource(rccFileName: typing.Optional[str], mapRoot: typing.Optional[str] = ...) -> bool: ...
    def size(self) -> int: ...
    def setLocale(self, locale: QLocale) -> None: ...
    def setFileName(self, file: typing.Optional[str]) -> None: ...
    def locale(self) -> QLocale: ...
    def isValid(self) -> bool: ...
    def isCompressed(self) -> bool: ...
    def fileName(self) -> str: ...
    def data(self) -> bytes: ...
    def absoluteFilePath(self) -> str: ...


class QRunnable(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRunnable') -> None: ...

    @staticmethod
    def create(functionToRun: typing.Callable[[], None]) -> typing.Optional['QRunnable']: ...
    def setAutoDelete(self, _autoDelete: bool) -> None: ...
    def autoDelete(self) -> bool: ...
    def run(self) -> None: ...


class QSaveFile(QFileDevice):

    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QObject]) -> None: ...

    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def directWriteFallback(self) -> bool: ...
    def setDirectWriteFallback(self, enabled: bool) -> None: ...
    def cancelWriting(self) -> None: ...
    def commit(self) -> bool: ...
    def open(self, flags: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag]) -> bool: ...
    def setFileName(self, name: typing.Optional[str]) -> None: ...
    def fileName(self) -> str: ...


class QSemaphore(PyQt5.sipsimplewrapper):

    def __init__(self, n: int = ...) -> None: ...

    def available(self) -> int: ...
    def release(self, n: int = ...) -> None: ...
    @typing.overload
    def tryAcquire(self, n: int = ...) -> bool: ...
    @typing.overload
    def tryAcquire(self, n: int, timeout: int) -> bool: ...
    def acquire(self, n: int = ...) -> None: ...


class QSemaphoreReleaser(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, sem: typing.Optional[QSemaphore], n: int = ...) -> None: ...

    def cancel(self) -> typing.Optional[QSemaphore]: ...
    def semaphore(self) -> typing.Optional[QSemaphore]: ...
    def swap(self, other: 'QSemaphoreReleaser') -> None: ...


class QSequentialAnimationGroup(QAnimationGroup):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def updateDirection(self, direction: QAbstractAnimation.Direction) -> None: ...
    def updateState(self, newState: QAbstractAnimation.State, oldState: QAbstractAnimation.State) -> None: ...
    def updateCurrentTime(self, a0: int) -> None: ...
    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    currentAnimationChanged: typing.ClassVar[pyqtSignal]
    def duration(self) -> int: ...
    def currentAnimation(self) -> typing.Optional[QAbstractAnimation]: ...
    def insertPause(self, index: int, msecs: int) -> typing.Optional[QPauseAnimation]: ...
    def addPause(self, msecs: int) -> typing.Optional[QPauseAnimation]: ...


class QSettings(QObject):

    class Scope(int):
        UserScope = ... # type: QSettings.Scope
        SystemScope = ... # type: QSettings.Scope

    class Format(int):
        NativeFormat = ... # type: QSettings.Format
        IniFormat = ... # type: QSettings.Format
        InvalidFormat = ... # type: QSettings.Format

    class Status(int):
        NoError = ... # type: QSettings.Status
        AccessError = ... # type: QSettings.Status
        FormatError = ... # type: QSettings.Status

    @typing.overload
    def __init__(self, organization: typing.Optional[str], application: typing.Optional[str] = ..., parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, scope: 'QSettings.Scope', organization: typing.Optional[str], application: typing.Optional[str] = ..., parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, format: 'QSettings.Format', scope: 'QSettings.Scope', organization: typing.Optional[str], application: typing.Optional[str] = ..., parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: 'QSettings.Format', parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, scope: 'QSettings.Scope', parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    def setAtomicSyncRequired(self, enable: bool) -> None: ...
    def isAtomicSyncRequired(self) -> bool: ...
    def iniCodec(self) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    def setIniCodec(self, codec: typing.Optional['QTextCodec']) -> None: ...
    @typing.overload
    def setIniCodec(self, codecName: typing.Optional[str]) -> None: ...
    @staticmethod
    def defaultFormat() -> 'QSettings.Format': ...
    @staticmethod
    def setDefaultFormat(format: 'QSettings.Format') -> None: ...
    def applicationName(self) -> str: ...
    def organizationName(self) -> str: ...
    def scope(self) -> 'QSettings.Scope': ...
    def format(self) -> 'QSettings.Format': ...
    @staticmethod
    def setPath(format: 'QSettings.Format', scope: 'QSettings.Scope', path: typing.Optional[str]) -> None: ...
    def fileName(self) -> str: ...
    def fallbacksEnabled(self) -> bool: ...
    def setFallbacksEnabled(self, b: bool) -> None: ...
    def contains(self, key: typing.Optional[str]) -> bool: ...
    def remove(self, key: typing.Optional[str]) -> None: ...
    def value(self, key: typing.Optional[str], defaultValue: typing.Any = ..., type: type = ...) -> typing.Any: ...
    def setValue(self, key: typing.Optional[str], value: typing.Any) -> None: ...
    def isWritable(self) -> bool: ...
    def childGroups(self) -> typing.List[str]: ...
    def childKeys(self) -> typing.List[str]: ...
    def allKeys(self) -> typing.List[str]: ...
    def setArrayIndex(self, i: int) -> None: ...
    def endArray(self) -> None: ...
    def beginWriteArray(self, prefix: typing.Optional[str], size: int = ...) -> None: ...
    def beginReadArray(self, prefix: typing.Optional[str]) -> int: ...
    def group(self) -> str: ...
    def endGroup(self) -> None: ...
    def beginGroup(self, prefix: typing.Optional[str]) -> None: ...
    def status(self) -> 'QSettings.Status': ...
    def sync(self) -> None: ...
    def clear(self) -> None: ...


class QSharedMemory(QObject):

    class SharedMemoryError(int):
        NoError = ... # type: QSharedMemory.SharedMemoryError
        PermissionDenied = ... # type: QSharedMemory.SharedMemoryError
        InvalidSize = ... # type: QSharedMemory.SharedMemoryError
        KeyError = ... # type: QSharedMemory.SharedMemoryError
        AlreadyExists = ... # type: QSharedMemory.SharedMemoryError
        NotFound = ... # type: QSharedMemory.SharedMemoryError
        LockError = ... # type: QSharedMemory.SharedMemoryError
        OutOfResources = ... # type: QSharedMemory.SharedMemoryError
        UnknownError = ... # type: QSharedMemory.SharedMemoryError

    class AccessMode(int):
        ReadOnly = ... # type: QSharedMemory.AccessMode
        ReadWrite = ... # type: QSharedMemory.AccessMode

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, key: typing.Optional[str], parent: typing.Optional[QObject] = ...) -> None: ...

    def nativeKey(self) -> str: ...
    def setNativeKey(self, key: typing.Optional[str]) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QSharedMemory.SharedMemoryError': ...
    def unlock(self) -> bool: ...
    def lock(self) -> bool: ...
    def constData(self) -> PyQt5.sip.voidptr: ...
    def data(self) -> PyQt5.sip.voidptr: ...
    def detach(self) -> bool: ...
    def isAttached(self) -> bool: ...
    def attach(self, mode: 'QSharedMemory.AccessMode' = ...) -> bool: ...
    def size(self) -> int: ...
    def create(self, size: int, mode: 'QSharedMemory.AccessMode' = ...) -> bool: ...
    def key(self) -> str: ...
    def setKey(self, key: typing.Optional[str]) -> None: ...


class QSignalMapper(QObject):

    from PyQt5.QtWidgets import QWidget

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    @typing.overload
    def map(self) -> None: ...
    @typing.overload
    def map(self, sender: typing.Optional[QObject]) -> None: ...
    mappedObject: typing.ClassVar[pyqtSignal]
    mappedWidget: typing.ClassVar[pyqtSignal]
    mappedString: typing.ClassVar[pyqtSignal]
    mappedInt: typing.ClassVar[pyqtSignal]
    mapped: typing.ClassVar[pyqtSignal]
    @typing.overload
    def mapping(self, id: int) -> typing.Optional[QObject]: ...
    @typing.overload
    def mapping(self, text: typing.Optional[str]) -> typing.Optional[QObject]: ...
    @typing.overload
    def mapping(self, widget: typing.Optional[QWidget]) -> typing.Optional[QObject]: ...
    @typing.overload
    def mapping(self, object: typing.Optional[QObject]) -> typing.Optional[QObject]: ...
    def removeMappings(self, sender: typing.Optional[QObject]) -> None: ...
    @typing.overload
    def setMapping(self, sender: typing.Optional[QObject], id: int) -> None: ...
    @typing.overload
    def setMapping(self, sender: typing.Optional[QObject], text: typing.Optional[str]) -> None: ...
    @typing.overload
    def setMapping(self, sender: typing.Optional[QObject], widget: typing.Optional[QWidget]) -> None: ...
    @typing.overload
    def setMapping(self, sender: typing.Optional[QObject], object: typing.Optional[QObject]) -> None: ...


class QSignalTransition(QAbstractTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, signal: pyqtBoundSignal, sourceState: typing.Optional['QState'] = ...) -> None: ...

    signalChanged: typing.ClassVar[pyqtSignal]
    senderObjectChanged: typing.ClassVar[pyqtSignal]
    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QEvent]) -> None: ...
    def eventTest(self, event: typing.Optional[QEvent]) -> bool: ...
    def setSignal(self, signal: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    def signal(self) -> QByteArray: ...
    def setSenderObject(self, sender: typing.Optional[QObject]) -> None: ...
    def senderObject(self) -> typing.Optional[QObject]: ...


class QSize(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, w: int, h: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSize') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, s2: 'QSize') -> 'QSize': ...
    def __sub__(self, s2: 'QSize') -> 'QSize': ...
    def __mul__(self, c: float) -> 'QSize': ...
    def __rmul__(self, c: float) -> 'QSize': ...
    def __truediv__(self, c: float) -> 'QSize': ...
    def shrunkBy(self, m: QMargins) -> 'QSize': ...
    def grownBy(self, m: QMargins) -> 'QSize': ...
    def transposed(self) -> 'QSize': ...
    @typing.overload
    def scaled(self, s: 'QSize', mode: Qt.AspectRatioMode) -> 'QSize': ...
    @typing.overload
    def scaled(self, w: int, h: int, mode: Qt.AspectRatioMode) -> 'QSize': ...
    def boundedTo(self, otherSize: 'QSize') -> 'QSize': ...
    def expandedTo(self, otherSize: 'QSize') -> 'QSize': ...
    def __itruediv__(self, c: float) -> 'QSize': ...
    def __imul__(self, c: float) -> 'QSize': ...
    def __isub__(self, s: 'QSize') -> 'QSize': ...
    def __iadd__(self, s: 'QSize') -> 'QSize': ...
    def setHeight(self, h: int) -> None: ...
    def setWidth(self, w: int) -> None: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def __bool__(self) -> int: ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...
    @typing.overload
    def scale(self, s: 'QSize', mode: Qt.AspectRatioMode) -> None: ...
    @typing.overload
    def scale(self, w: int, h: int, mode: Qt.AspectRatioMode) -> None: ...
    def transpose(self) -> None: ...


class QSizeF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, sz: QSize) -> None: ...
    @typing.overload
    def __init__(self, w: float, h: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSizeF') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, s2: 'QSizeF') -> 'QSizeF': ...
    def __sub__(self, s2: 'QSizeF') -> 'QSizeF': ...
    def __mul__(self, c: float) -> 'QSizeF': ...
    def __rmul__(self, c: float) -> 'QSizeF': ...
    def __truediv__(self, c: float) -> 'QSizeF': ...
    def shrunkBy(self, m: QMarginsF) -> 'QSizeF': ...
    def grownBy(self, m: QMarginsF) -> 'QSizeF': ...
    def transposed(self) -> 'QSizeF': ...
    @typing.overload
    def scaled(self, s: 'QSizeF', mode: Qt.AspectRatioMode) -> 'QSizeF': ...
    @typing.overload
    def scaled(self, w: float, h: float, mode: Qt.AspectRatioMode) -> 'QSizeF': ...
    def toSize(self) -> QSize: ...
    def boundedTo(self, otherSize: 'QSizeF') -> 'QSizeF': ...
    def expandedTo(self, otherSize: 'QSizeF') -> 'QSizeF': ...
    def __itruediv__(self, c: float) -> 'QSizeF': ...
    def __imul__(self, c: float) -> 'QSizeF': ...
    def __isub__(self, s: 'QSizeF') -> 'QSizeF': ...
    def __iadd__(self, s: 'QSizeF') -> 'QSizeF': ...
    def setHeight(self, h: float) -> None: ...
    def setWidth(self, w: float) -> None: ...
    def height(self) -> float: ...
    def width(self) -> float: ...
    def __bool__(self) -> int: ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...
    @typing.overload
    def scale(self, s: 'QSizeF', mode: Qt.AspectRatioMode) -> None: ...
    @typing.overload
    def scale(self, w: float, h: float, mode: Qt.AspectRatioMode) -> None: ...
    def transpose(self) -> None: ...


class QSocketNotifier(QObject):

    class Type(int):
        Read = ... # type: QSocketNotifier.Type
        Write = ... # type: QSocketNotifier.Type
        Exception = ... # type: QSocketNotifier.Type

    def __init__(self, socket: PyQt5.sip.voidptr, a1: 'QSocketNotifier.Type', parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, a0: typing.Optional[QEvent]) -> bool: ...
    activated: typing.ClassVar[pyqtSignal]
    def setEnabled(self, a0: bool) -> None: ...
    def isEnabled(self) -> bool: ...
    def type(self) -> 'QSocketNotifier.Type': ...
    def socket(self) -> PyQt5.sip.voidptr: ...


class QSortFilterProxyModel(QAbstractProxyModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    recursiveFilteringEnabledChanged: typing.ClassVar[pyqtSignal]
    filterRoleChanged: typing.ClassVar[pyqtSignal]
    sortRoleChanged: typing.ClassVar[pyqtSignal]
    sortLocaleAwareChanged: typing.ClassVar[pyqtSignal]
    sortCaseSensitivityChanged: typing.ClassVar[pyqtSignal]
    filterCaseSensitivityChanged: typing.ClassVar[pyqtSignal]
    dynamicSortFilterChanged: typing.ClassVar[pyqtSignal]
    def invalidateFilter(self) -> None: ...
    def setRecursiveFilteringEnabled(self, recursive: bool) -> None: ...
    def isRecursiveFilteringEnabled(self) -> bool: ...
    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def setSortLocaleAware(self, on: bool) -> None: ...
    def isSortLocaleAware(self) -> bool: ...
    def supportedDropActions(self) -> Qt.DropActions: ...
    def mimeTypes(self) -> typing.List[str]: ...
    def setFilterRole(self, role: int) -> None: ...
    def filterRole(self) -> int: ...
    def sortOrder(self) -> Qt.SortOrder: ...
    def sortColumn(self) -> int: ...
    def setSortRole(self, role: int) -> None: ...
    def sortRole(self) -> int: ...
    def setDynamicSortFilter(self, enable: bool) -> None: ...
    def dynamicSortFilter(self) -> bool: ...
    def setSortCaseSensitivity(self, cs: Qt.CaseSensitivity) -> None: ...
    def sortCaseSensitivity(self) -> Qt.CaseSensitivity: ...
    def sort(self, column: int, order: Qt.SortOrder = ...) -> None: ...
    def match(self, start: QModelIndex, role: int, value: typing.Any, hits: int = ..., flags: typing.Union[Qt.MatchFlags, Qt.MatchFlag] = ...) -> typing.List[QModelIndex]: ...
    def span(self, index: QModelIndex) -> QSize: ...
    def buddy(self, index: QModelIndex) -> QModelIndex: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def canFetchMore(self, parent: QModelIndex) -> bool: ...
    def fetchMore(self, parent: QModelIndex) -> None: ...
    def removeColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def removeRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def dropMimeData(self, data: typing.Optional[QMimeData], action: Qt.DropAction, row: int, column: int, parent: QModelIndex) -> bool: ...
    def mimeData(self, indexes: typing.Iterable[QModelIndex]) -> typing.Optional[QMimeData]: ...
    def setHeaderData(self, section: int, orientation: Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QModelIndex, role: int = ...) -> typing.Any: ...
    def hasChildren(self, parent: QModelIndex = ...) -> bool: ...
    def columnCount(self, parent: QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...
    @typing.overload
    def parent(self, child: QModelIndex) -> QModelIndex: ...
    @typing.overload
    def parent(self) -> typing.Optional[QObject]: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...
    def lessThan(self, left: QModelIndex, right: QModelIndex) -> bool: ...
    def filterAcceptsColumn(self, source_column: int, source_parent: QModelIndex) -> bool: ...
    def filterAcceptsRow(self, source_row: int, source_parent: QModelIndex) -> bool: ...
    def setFilterWildcard(self, pattern: typing.Optional[str]) -> None: ...
    @typing.overload
    def setFilterRegularExpression(self, regularExpression: QRegularExpression) -> None: ...
    @typing.overload
    def setFilterRegularExpression(self, pattern: typing.Optional[str]) -> None: ...
    @typing.overload
    def setFilterRegExp(self, regExp: QRegExp) -> None: ...
    @typing.overload
    def setFilterRegExp(self, pattern: typing.Optional[str]) -> None: ...
    def setFilterFixedString(self, pattern: typing.Optional[str]) -> None: ...
    def invalidate(self) -> None: ...
    def setFilterCaseSensitivity(self, cs: Qt.CaseSensitivity) -> None: ...
    def filterCaseSensitivity(self) -> Qt.CaseSensitivity: ...
    def setFilterKeyColumn(self, column: int) -> None: ...
    def filterKeyColumn(self) -> int: ...
    def filterRegularExpression(self) -> QRegularExpression: ...
    def filterRegExp(self) -> QRegExp: ...
    def mapSelectionFromSource(self, sourceSelection: QItemSelection) -> QItemSelection: ...
    def mapSelectionToSource(self, proxySelection: QItemSelection) -> QItemSelection: ...
    def mapFromSource(self, sourceIndex: QModelIndex) -> QModelIndex: ...
    def mapToSource(self, proxyIndex: QModelIndex) -> QModelIndex: ...
    def setSourceModel(self, sourceModel: typing.Optional[QAbstractItemModel]) -> None: ...


class QStandardPaths(PyQt5.sipsimplewrapper):

    class LocateOption(int):
        LocateFile = ... # type: QStandardPaths.LocateOption
        LocateDirectory = ... # type: QStandardPaths.LocateOption

    class StandardLocation(int):
        DesktopLocation = ... # type: QStandardPaths.StandardLocation
        DocumentsLocation = ... # type: QStandardPaths.StandardLocation
        FontsLocation = ... # type: QStandardPaths.StandardLocation
        ApplicationsLocation = ... # type: QStandardPaths.StandardLocation
        MusicLocation = ... # type: QStandardPaths.StandardLocation
        MoviesLocation = ... # type: QStandardPaths.StandardLocation
        PicturesLocation = ... # type: QStandardPaths.StandardLocation
        TempLocation = ... # type: QStandardPaths.StandardLocation
        HomeLocation = ... # type: QStandardPaths.StandardLocation
        DataLocation = ... # type: QStandardPaths.StandardLocation
        CacheLocation = ... # type: QStandardPaths.StandardLocation
        GenericDataLocation = ... # type: QStandardPaths.StandardLocation
        RuntimeLocation = ... # type: QStandardPaths.StandardLocation
        ConfigLocation = ... # type: QStandardPaths.StandardLocation
        DownloadLocation = ... # type: QStandardPaths.StandardLocation
        GenericCacheLocation = ... # type: QStandardPaths.StandardLocation
        GenericConfigLocation = ... # type: QStandardPaths.StandardLocation
        AppDataLocation = ... # type: QStandardPaths.StandardLocation
        AppLocalDataLocation = ... # type: QStandardPaths.StandardLocation
        AppConfigLocation = ... # type: QStandardPaths.StandardLocation

    class LocateOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __xor__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __ior__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __or__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __iand__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __and__(self, f: typing.Union['QStandardPaths.LocateOptions', 'QStandardPaths.LocateOption']) -> 'QStandardPaths.LocateOptions': ...
        def __invert__(self) -> 'QStandardPaths.LocateOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, a0: 'QStandardPaths') -> None: ...

    @staticmethod
    def setTestModeEnabled(testMode: bool) -> None: ...
    @staticmethod
    def enableTestMode(testMode: bool) -> None: ...
    @staticmethod
    def findExecutable(executableName: typing.Optional[str], paths: typing.Iterable[typing.Optional[str]] = ...) -> str: ...
    @staticmethod
    def displayName(type: 'QStandardPaths.StandardLocation') -> str: ...
    @staticmethod
    def locateAll(type: 'QStandardPaths.StandardLocation', fileName: typing.Optional[str], options: 'QStandardPaths.LocateOptions' = ...) -> typing.List[str]: ...
    @staticmethod
    def locate(type: 'QStandardPaths.StandardLocation', fileName: typing.Optional[str], options: 'QStandardPaths.LocateOptions' = ...) -> str: ...
    @staticmethod
    def standardLocations(type: 'QStandardPaths.StandardLocation') -> typing.List[str]: ...
    @staticmethod
    def writableLocation(type: 'QStandardPaths.StandardLocation') -> str: ...


class QState(QAbstractState):

    class RestorePolicy(int):
        DontRestoreProperties = ... # type: QState.RestorePolicy
        RestoreProperties = ... # type: QState.RestorePolicy

    class ChildMode(int):
        ExclusiveStates = ... # type: QState.ChildMode
        ParallelStates = ... # type: QState.ChildMode

    @typing.overload
    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, childMode: 'QState.ChildMode', parent: typing.Optional['QState'] = ...) -> None: ...

    errorStateChanged: typing.ClassVar[pyqtSignal]
    initialStateChanged: typing.ClassVar[pyqtSignal]
    childModeChanged: typing.ClassVar[pyqtSignal]
    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QEvent]) -> None: ...
    propertiesAssigned: typing.ClassVar[pyqtSignal]
    finished: typing.ClassVar[pyqtSignal]
    def assignProperty(self, object: typing.Optional[QObject], name: typing.Optional[str], value: typing.Any) -> None: ...
    def setChildMode(self, mode: 'QState.ChildMode') -> None: ...
    def childMode(self) -> 'QState.ChildMode': ...
    def setInitialState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def initialState(self) -> typing.Optional[QAbstractState]: ...
    def transitions(self) -> typing.List[QAbstractTransition]: ...
    def removeTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    @typing.overload
    def addTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    @typing.overload
    def addTransition(self, signal: pyqtBoundSignal, target: typing.Optional[QAbstractState]) -> typing.Optional[QSignalTransition]: ...
    @typing.overload
    def addTransition(self, target: typing.Optional[QAbstractState]) -> typing.Optional[QAbstractTransition]: ...
    def setErrorState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def errorState(self) -> typing.Optional[QAbstractState]: ...


class QStateMachine(QState):

    class Error(int):
        NoError = ... # type: QStateMachine.Error
        NoInitialStateError = ... # type: QStateMachine.Error
        NoDefaultStateInHistoryStateError = ... # type: QStateMachine.Error
        NoCommonAncestorForTransitionError = ... # type: QStateMachine.Error
        StateMachineChildModeSetToParallelError = ... # type: QStateMachine.Error

    class EventPriority(int):
        NormalPriority = ... # type: QStateMachine.EventPriority
        HighPriority = ... # type: QStateMachine.EventPriority

    class SignalEvent(QEvent):

        def arguments(self) -> typing.List[typing.Any]: ...
        def signalIndex(self) -> int: ...
        def sender(self) -> typing.Optional[QObject]: ...

    class WrappedEvent(QEvent):

        def event(self) -> typing.Optional[QEvent]: ...
        def object(self) -> typing.Optional[QObject]: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, childMode: QState.ChildMode, parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QEvent]) -> None: ...
    runningChanged: typing.ClassVar[pyqtSignal]
    stopped: typing.ClassVar[pyqtSignal]
    started: typing.ClassVar[pyqtSignal]
    def setRunning(self, running: bool) -> None: ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def eventFilter(self, watched: typing.Optional[QObject], event: typing.Optional[QEvent]) -> bool: ...
    def configuration(self) -> typing.Set[QAbstractState]: ...
    def cancelDelayedEvent(self, id: int) -> bool: ...
    def postDelayedEvent(self, event: typing.Optional[QEvent], delay: int) -> int: ...
    def postEvent(self, event: typing.Optional[QEvent], priority: 'QStateMachine.EventPriority' = ...) -> None: ...
    def setGlobalRestorePolicy(self, restorePolicy: QState.RestorePolicy) -> None: ...
    def globalRestorePolicy(self) -> QState.RestorePolicy: ...
    def removeDefaultAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def defaultAnimations(self) -> typing.List[QAbstractAnimation]: ...
    def addDefaultAnimation(self, animation: typing.Optional[QAbstractAnimation]) -> None: ...
    def setAnimated(self, enabled: bool) -> None: ...
    def isAnimated(self) -> bool: ...
    def isRunning(self) -> bool: ...
    def clearError(self) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QStateMachine.Error': ...
    def removeState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def addState(self, state: typing.Optional[QAbstractState]) -> None: ...


class QStorageInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, path: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, dir: QDir) -> None: ...
    @typing.overload
    def __init__(self, other: 'QStorageInfo') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def subvolume(self) -> QByteArray: ...
    def blockSize(self) -> int: ...
    def isRoot(self) -> bool: ...
    @staticmethod
    def root() -> 'QStorageInfo': ...
    @staticmethod
    def mountedVolumes() -> typing.List['QStorageInfo']: ...
    def refresh(self) -> None: ...
    def isValid(self) -> bool: ...
    def isReady(self) -> bool: ...
    def isReadOnly(self) -> bool: ...
    def bytesAvailable(self) -> int: ...
    def bytesFree(self) -> int: ...
    def bytesTotal(self) -> int: ...
    def displayName(self) -> str: ...
    def name(self) -> str: ...
    def fileSystemType(self) -> QByteArray: ...
    def device(self) -> QByteArray: ...
    def rootPath(self) -> str: ...
    def setPath(self, path: typing.Optional[str]) -> None: ...
    def swap(self, other: 'QStorageInfo') -> None: ...


class QStringListModel(QAbstractListModel):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, strings: typing.Iterable[typing.Optional[str]], parent: typing.Optional[QObject] = ...) -> None: ...

    def setItemData(self, index: QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def itemData(self, index: QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def moveRows(self, sourceParent: QModelIndex, sourceRow: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def sibling(self, row: int, column: int, idx: QModelIndex) -> QModelIndex: ...
    def supportedDropActions(self) -> Qt.DropActions: ...
    def sort(self, column: int, order: Qt.SortOrder = ...) -> None: ...
    def setStringList(self, strings: typing.Iterable[typing.Optional[str]]) -> None: ...
    def stringList(self) -> typing.List[str]: ...
    def removeRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def flags(self, index: QModelIndex) -> Qt.ItemFlags: ...
    def setData(self, index: QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QModelIndex, role: int) -> typing.Any: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...


class QSystemSemaphore(PyQt5.sipsimplewrapper):

    class SystemSemaphoreError(int):
        NoError = ... # type: QSystemSemaphore.SystemSemaphoreError
        PermissionDenied = ... # type: QSystemSemaphore.SystemSemaphoreError
        KeyError = ... # type: QSystemSemaphore.SystemSemaphoreError
        AlreadyExists = ... # type: QSystemSemaphore.SystemSemaphoreError
        NotFound = ... # type: QSystemSemaphore.SystemSemaphoreError
        OutOfResources = ... # type: QSystemSemaphore.SystemSemaphoreError
        UnknownError = ... # type: QSystemSemaphore.SystemSemaphoreError

    class AccessMode(int):
        Open = ... # type: QSystemSemaphore.AccessMode
        Create = ... # type: QSystemSemaphore.AccessMode

    def __init__(self, key: typing.Optional[str], initialValue: int = ..., mode: 'QSystemSemaphore.AccessMode' = ...) -> None: ...

    def errorString(self) -> str: ...
    def error(self) -> 'QSystemSemaphore.SystemSemaphoreError': ...
    def release(self, n: int = ...) -> bool: ...
    def acquire(self) -> bool: ...
    def key(self) -> str: ...
    def setKey(self, key: typing.Optional[str], initialValue: int = ..., mode: 'QSystemSemaphore.AccessMode' = ...) -> None: ...


class QTemporaryDir(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, templateName: typing.Optional[str]) -> None: ...

    def filePath(self, fileName: typing.Optional[str]) -> str: ...
    def errorString(self) -> str: ...
    def path(self) -> str: ...
    def remove(self) -> bool: ...
    def setAutoRemove(self, b: bool) -> None: ...
    def autoRemove(self) -> bool: ...
    def isValid(self) -> bool: ...


class QTemporaryFile(QFile):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, templateName: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QObject]) -> None: ...
    @typing.overload
    def __init__(self, templateName: typing.Optional[str], parent: typing.Optional[QObject]) -> None: ...

    def rename(self, newName: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def createNativeFile(fileName: typing.Optional[str]) -> typing.Optional['QTemporaryFile']: ...
    @typing.overload
    @staticmethod
    def createNativeFile(file: QFile) -> typing.Optional['QTemporaryFile']: ...
    def setFileTemplate(self, name: typing.Optional[str]) -> None: ...
    def fileTemplate(self) -> str: ...
    def fileName(self) -> str: ...
    @typing.overload
    def open(self) -> bool: ...
    @typing.overload
    def open(self, flags: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag]) -> bool: ...
    def setAutoRemove(self, b: bool) -> None: ...
    def autoRemove(self) -> bool: ...


class QTextBoundaryFinder(PyQt5.sipsimplewrapper):

    class BoundaryType(int):
        Grapheme = ... # type: QTextBoundaryFinder.BoundaryType
        Word = ... # type: QTextBoundaryFinder.BoundaryType
        Line = ... # type: QTextBoundaryFinder.BoundaryType
        Sentence = ... # type: QTextBoundaryFinder.BoundaryType

    class BoundaryReason(int):
        NotAtBoundary = ... # type: QTextBoundaryFinder.BoundaryReason
        SoftHyphen = ... # type: QTextBoundaryFinder.BoundaryReason
        BreakOpportunity = ... # type: QTextBoundaryFinder.BoundaryReason
        StartOfItem = ... # type: QTextBoundaryFinder.BoundaryReason
        EndOfItem = ... # type: QTextBoundaryFinder.BoundaryReason
        MandatoryBreak = ... # type: QTextBoundaryFinder.BoundaryReason

    class BoundaryReasons(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __xor__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __ior__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __or__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __iand__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __and__(self, f: typing.Union['QTextBoundaryFinder.BoundaryReasons', 'QTextBoundaryFinder.BoundaryReason']) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __invert__(self) -> 'QTextBoundaryFinder.BoundaryReasons': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QTextBoundaryFinder') -> None: ...
    @typing.overload
    def __init__(self, type: 'QTextBoundaryFinder.BoundaryType', string: typing.Optional[str]) -> None: ...

    def boundaryReasons(self) -> 'QTextBoundaryFinder.BoundaryReasons': ...
    def isAtBoundary(self) -> bool: ...
    def toPreviousBoundary(self) -> int: ...
    def toNextBoundary(self) -> int: ...
    def setPosition(self, position: int) -> None: ...
    def position(self) -> int: ...
    def toEnd(self) -> None: ...
    def toStart(self) -> None: ...
    def string(self) -> str: ...
    def type(self) -> 'QTextBoundaryFinder.BoundaryType': ...
    def isValid(self) -> bool: ...


class QTextCodec(PyQt5.sip.wrapper):

    class ConversionFlag(int):
        DefaultConversion = ... # type: QTextCodec.ConversionFlag
        ConvertInvalidToNull = ... # type: QTextCodec.ConversionFlag
        IgnoreHeader = ... # type: QTextCodec.ConversionFlag

    class ConversionFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __xor__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __ior__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __or__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __iand__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __and__(self, f: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag']) -> 'QTextCodec.ConversionFlags': ...
        def __invert__(self) -> 'QTextCodec.ConversionFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ConverterState(PyQt5.sipsimplewrapper):

        def __init__(self, flags: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag'] = ...) -> None: ...

    def __init__(self) -> None: ...

    def convertFromUnicode(self, in_: typing.Optional[PyQt5.sip.array[str]], state: typing.Optional['QTextCodec.ConverterState']) -> QByteArray: ...
    def convertToUnicode(self, in_: typing.Optional[PyQt5.sip.array[bytes]], state: typing.Optional['QTextCodec.ConverterState']) -> str: ...
    def mibEnum(self) -> int: ...
    def aliases(self) -> typing.List[QByteArray]: ...
    def name(self) -> QByteArray: ...
    def fromUnicode(self, uc: typing.Optional[str]) -> QByteArray: ...
    @typing.overload
    def toUnicode(self, a0: typing.Union[QByteArray, bytes, bytearray]) -> str: ...
    @typing.overload
    def toUnicode(self, chars: typing.Optional[bytes]) -> str: ...
    @typing.overload
    def toUnicode(self, in_: typing.Optional[PyQt5.sip.array[bytes]], state: typing.Optional['QTextCodec.ConverterState'] = ...) -> str: ...
    def canEncode(self, a0: typing.Optional[str]) -> bool: ...
    def makeEncoder(self, flags: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag'] = ...) -> typing.Optional['QTextEncoder']: ...
    def makeDecoder(self, flags: typing.Union['QTextCodec.ConversionFlags', 'QTextCodec.ConversionFlag'] = ...) -> typing.Optional['QTextDecoder']: ...
    @staticmethod
    def setCodecForLocale(c: typing.Optional['QTextCodec']) -> None: ...
    @staticmethod
    def codecForLocale() -> typing.Optional['QTextCodec']: ...
    @staticmethod
    def availableMibs() -> typing.List[int]: ...
    @staticmethod
    def availableCodecs() -> typing.List[QByteArray]: ...
    @typing.overload
    @staticmethod
    def codecForUtfText(ba: typing.Union[QByteArray, bytes, bytearray]) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    @staticmethod
    def codecForUtfText(ba: typing.Union[QByteArray, bytes, bytearray], defaultCodec: typing.Optional['QTextCodec']) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    @staticmethod
    def codecForHtml(ba: typing.Union[QByteArray, bytes, bytearray]) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    @staticmethod
    def codecForHtml(ba: typing.Union[QByteArray, bytes, bytearray], defaultCodec: typing.Optional['QTextCodec']) -> typing.Optional['QTextCodec']: ...
    @staticmethod
    def codecForMib(mib: int) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    @staticmethod
    def codecForName(name: typing.Union[QByteArray, bytes, bytearray]) -> typing.Optional['QTextCodec']: ...
    @typing.overload
    @staticmethod
    def codecForName(name: typing.Optional[str]) -> typing.Optional['QTextCodec']: ...


class QTextEncoder(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self, codec: typing.Optional[QTextCodec]) -> None: ...
    @typing.overload
    def __init__(self, codec: typing.Optional[QTextCodec], flags: typing.Union[QTextCodec.ConversionFlags, QTextCodec.ConversionFlag]) -> None: ...

    def fromUnicode(self, str: typing.Optional[str]) -> QByteArray: ...


class QTextDecoder(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self, codec: typing.Optional[QTextCodec]) -> None: ...
    @typing.overload
    def __init__(self, codec: typing.Optional[QTextCodec], flags: typing.Union[QTextCodec.ConversionFlags, QTextCodec.ConversionFlag]) -> None: ...

    @typing.overload
    def toUnicode(self, chars: typing.Optional[PyQt5.sip.array[bytes]]) -> str: ...
    @typing.overload
    def toUnicode(self, ba: typing.Union[QByteArray, bytes, bytearray]) -> str: ...


class QTextStream(PyQt5.sipsimplewrapper):

    class Status(int):
        Ok = ... # type: QTextStream.Status
        ReadPastEnd = ... # type: QTextStream.Status
        ReadCorruptData = ... # type: QTextStream.Status
        WriteFailed = ... # type: QTextStream.Status

    class NumberFlag(int):
        ShowBase = ... # type: QTextStream.NumberFlag
        ForcePoint = ... # type: QTextStream.NumberFlag
        ForceSign = ... # type: QTextStream.NumberFlag
        UppercaseBase = ... # type: QTextStream.NumberFlag
        UppercaseDigits = ... # type: QTextStream.NumberFlag

    class FieldAlignment(int):
        AlignLeft = ... # type: QTextStream.FieldAlignment
        AlignRight = ... # type: QTextStream.FieldAlignment
        AlignCenter = ... # type: QTextStream.FieldAlignment
        AlignAccountingStyle = ... # type: QTextStream.FieldAlignment

    class RealNumberNotation(int):
        SmartNotation = ... # type: QTextStream.RealNumberNotation
        FixedNotation = ... # type: QTextStream.RealNumberNotation
        ScientificNotation = ... # type: QTextStream.RealNumberNotation

    class NumberFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __xor__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __ior__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __or__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __iand__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __and__(self, f: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> 'QTextStream.NumberFlags': ...
        def __invert__(self) -> 'QTextStream.NumberFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QIODevice]) -> None: ...
    @typing.overload
    def __init__(self, array: typing.Optional[QByteArray], mode: typing.Union[QIODevice.OpenMode, QIODevice.OpenModeFlag] = ...) -> None: ...

    def locale(self) -> QLocale: ...
    def setLocale(self, locale: QLocale) -> None: ...
    @typing.overload
    def __lshift__(self, s: typing.Optional[str]) -> 'QTextStream': ...
    @typing.overload
    def __lshift__(self, array: typing.Union[QByteArray, bytes, bytearray]) -> 'QTextStream': ...
    @typing.overload
    def __lshift__(self, f: float) -> 'QTextStream': ...
    @typing.overload
    def __lshift__(self, i: int) -> 'QTextStream': ...
    @typing.overload
    def __lshift__(self, m: 'QTextStreamManipulator') -> 'QTextStream': ...
    def __rshift__(self, array: QByteArray) -> 'QTextStream': ...
    def pos(self) -> int: ...
    def resetStatus(self) -> None: ...
    def setStatus(self, status: 'QTextStream.Status') -> None: ...
    def status(self) -> 'QTextStream.Status': ...
    def realNumberPrecision(self) -> int: ...
    def setRealNumberPrecision(self, precision: int) -> None: ...
    def realNumberNotation(self) -> 'QTextStream.RealNumberNotation': ...
    def setRealNumberNotation(self, notation: 'QTextStream.RealNumberNotation') -> None: ...
    def integerBase(self) -> int: ...
    def setIntegerBase(self, base: int) -> None: ...
    def numberFlags(self) -> 'QTextStream.NumberFlags': ...
    def setNumberFlags(self, flags: typing.Union['QTextStream.NumberFlags', 'QTextStream.NumberFlag']) -> None: ...
    def fieldWidth(self) -> int: ...
    def setFieldWidth(self, width: int) -> None: ...
    def padChar(self) -> str: ...
    def setPadChar(self, ch: str) -> None: ...
    def fieldAlignment(self) -> 'QTextStream.FieldAlignment': ...
    def setFieldAlignment(self, alignment: 'QTextStream.FieldAlignment') -> None: ...
    def readAll(self) -> str: ...
    def readLine(self, maxLength: int = ...) -> str: ...
    def read(self, maxlen: int) -> str: ...
    def skipWhiteSpace(self) -> None: ...
    def seek(self, pos: int) -> bool: ...
    def flush(self) -> None: ...
    def reset(self) -> None: ...
    def atEnd(self) -> bool: ...
    def device(self) -> typing.Optional[QIODevice]: ...
    def setDevice(self, device: typing.Optional[QIODevice]) -> None: ...
    def generateByteOrderMark(self) -> bool: ...
    def setGenerateByteOrderMark(self, generate: bool) -> None: ...
    def autoDetectUnicode(self) -> bool: ...
    def setAutoDetectUnicode(self, enabled: bool) -> None: ...
    def codec(self) -> typing.Optional[QTextCodec]: ...
    @typing.overload
    def setCodec(self, codec: typing.Optional[QTextCodec]) -> None: ...
    @typing.overload
    def setCodec(self, codecName: typing.Optional[str]) -> None: ...


class QTextStreamManipulator(PyQt5.sipsimplewrapper): ...


class QThread(QObject):

    class Priority(int):
        IdlePriority = ... # type: QThread.Priority
        LowestPriority = ... # type: QThread.Priority
        LowPriority = ... # type: QThread.Priority
        NormalPriority = ... # type: QThread.Priority
        HighPriority = ... # type: QThread.Priority
        HighestPriority = ... # type: QThread.Priority
        TimeCriticalPriority = ... # type: QThread.Priority
        InheritPriority = ... # type: QThread.Priority

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def loopLevel(self) -> int: ...
    def isInterruptionRequested(self) -> bool: ...
    def requestInterruption(self) -> None: ...
    def setEventDispatcher(self, eventDispatcher: typing.Optional[QAbstractEventDispatcher]) -> None: ...
    def eventDispatcher(self) -> typing.Optional[QAbstractEventDispatcher]: ...
    @staticmethod
    def usleep(a0: int) -> None: ...
    @staticmethod
    def msleep(a0: int) -> None: ...
    @staticmethod
    def sleep(a0: int) -> None: ...
    def event(self, event: typing.Optional[QEvent]) -> bool: ...
    @staticmethod
    def setTerminationEnabled(enabled: bool = ...) -> None: ...
    def exec(self) -> int: ...
    def exec_(self) -> int: ...
    def run(self) -> None: ...
    finished: typing.ClassVar[pyqtSignal]
    started: typing.ClassVar[pyqtSignal]
    @typing.overload
    def wait(self, msecs: int = ...) -> bool: ...
    @typing.overload
    def wait(self, deadline: QDeadlineTimer) -> bool: ...
    def quit(self) -> None: ...
    def terminate(self) -> None: ...
    def start(self, priority: 'QThread.Priority' = ...) -> None: ...
    def exit(self, returnCode: int = ...) -> None: ...
    def stackSize(self) -> int: ...
    def setStackSize(self, stackSize: int) -> None: ...
    def priority(self) -> 'QThread.Priority': ...
    def setPriority(self, priority: 'QThread.Priority') -> None: ...
    def isRunning(self) -> bool: ...
    def isFinished(self) -> bool: ...
    @staticmethod
    def yieldCurrentThread() -> None: ...
    @staticmethod
    def idealThreadCount() -> int: ...
    @staticmethod
    def currentThreadId() -> typing.Optional[PyQt5.sip.voidptr]: ...
    @staticmethod
    def currentThread() -> typing.Optional['QThread']: ...


class QThreadPool(QObject):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def contains(self, thread: typing.Optional[QThread]) -> bool: ...
    def stackSize(self) -> int: ...
    def setStackSize(self, stackSize: int) -> None: ...
    def cancel(self, runnable: typing.Optional[QRunnable]) -> None: ...
    def clear(self) -> None: ...
    def waitForDone(self, msecs: int = ...) -> bool: ...
    def releaseThread(self) -> None: ...
    def reserveThread(self) -> None: ...
    def activeThreadCount(self) -> int: ...
    def setMaxThreadCount(self, maxThreadCount: int) -> None: ...
    def maxThreadCount(self) -> int: ...
    def setExpiryTimeout(self, expiryTimeout: int) -> None: ...
    def expiryTimeout(self) -> int: ...
    def tryTake(self, runnable: typing.Optional[QRunnable]) -> bool: ...
    @typing.overload
    def tryStart(self, runnable: typing.Optional[QRunnable]) -> bool: ...
    @typing.overload
    def tryStart(self, functionToRun: typing.Callable[[], None]) -> bool: ...
    @typing.overload
    def start(self, runnable: typing.Optional[QRunnable], priority: int = ...) -> None: ...
    @typing.overload
    def start(self, functionToRun: typing.Callable[[], None], priority: int = ...) -> None: ...
    @staticmethod
    def globalInstance() -> typing.Optional['QThreadPool']: ...


class QTimeLine(QObject):

    class State(int):
        NotRunning = ... # type: QTimeLine.State
        Paused = ... # type: QTimeLine.State
        Running = ... # type: QTimeLine.State

    class Direction(int):
        Forward = ... # type: QTimeLine.Direction
        Backward = ... # type: QTimeLine.Direction

    class CurveShape(int):
        EaseInCurve = ... # type: QTimeLine.CurveShape
        EaseOutCurve = ... # type: QTimeLine.CurveShape
        EaseInOutCurve = ... # type: QTimeLine.CurveShape
        LinearCurve = ... # type: QTimeLine.CurveShape
        SineCurve = ... # type: QTimeLine.CurveShape
        CosineCurve = ... # type: QTimeLine.CurveShape

    def __init__(self, duration: int = ..., parent: typing.Optional[QObject] = ...) -> None: ...

    def setEasingCurve(self, curve: typing.Union[QEasingCurve, QEasingCurve.Type]) -> None: ...
    def easingCurve(self) -> QEasingCurve: ...
    def timerEvent(self, event: typing.Optional[QTimerEvent]) -> None: ...
    valueChanged: typing.ClassVar[pyqtSignal]
    stateChanged: typing.ClassVar[pyqtSignal]
    frameChanged: typing.ClassVar[pyqtSignal]
    finished: typing.ClassVar[pyqtSignal]
    def toggleDirection(self) -> None: ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setPaused(self, paused: bool) -> None: ...
    def setCurrentTime(self, msec: int) -> None: ...
    def resume(self) -> None: ...
    def valueForTime(self, msec: int) -> float: ...
    def frameForTime(self, msec: int) -> int: ...
    def currentValue(self) -> float: ...
    def currentFrame(self) -> int: ...
    def currentTime(self) -> int: ...
    def setCurveShape(self, shape: 'QTimeLine.CurveShape') -> None: ...
    def curveShape(self) -> 'QTimeLine.CurveShape': ...
    def setUpdateInterval(self, interval: int) -> None: ...
    def updateInterval(self) -> int: ...
    def setFrameRange(self, startFrame: int, endFrame: int) -> None: ...
    def setEndFrame(self, frame: int) -> None: ...
    def endFrame(self) -> int: ...
    def setStartFrame(self, frame: int) -> None: ...
    def startFrame(self) -> int: ...
    def setDuration(self, duration: int) -> None: ...
    def duration(self) -> int: ...
    def setDirection(self, direction: 'QTimeLine.Direction') -> None: ...
    def direction(self) -> 'QTimeLine.Direction': ...
    def setLoopCount(self, count: int) -> None: ...
    def loopCount(self) -> int: ...
    def state(self) -> 'QTimeLine.State': ...


class QTimer(QObject):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def remainingTime(self) -> int: ...
    def timerType(self) -> Qt.TimerType: ...
    def setTimerType(self, atype: Qt.TimerType) -> None: ...
    def timerEvent(self, a0: typing.Optional[QTimerEvent]) -> None: ...
    timeout: typing.ClassVar[pyqtSignal]
    def stop(self) -> None: ...
    @typing.overload
    def start(self, msec: int) -> None: ...
    @typing.overload
    def start(self) -> None: ...
    @typing.overload
    @staticmethod
    def singleShot(msec: int, slot: PYQT_SLOT) -> None: ...
    @typing.overload
    @staticmethod
    def singleShot(msec: int, timerType: Qt.TimerType, slot: PYQT_SLOT) -> None: ...
    def setSingleShot(self, asingleShot: bool) -> None: ...
    def isSingleShot(self) -> bool: ...
    def interval(self) -> int: ...
    def setInterval(self, msec: int) -> None: ...
    def timerId(self) -> int: ...
    def isActive(self) -> bool: ...


class QTimeZone(PyQt5.sipsimplewrapper):

    class NameType(int):
        DefaultName = ... # type: QTimeZone.NameType
        LongName = ... # type: QTimeZone.NameType
        ShortName = ... # type: QTimeZone.NameType
        OffsetName = ... # type: QTimeZone.NameType

    class TimeType(int):
        StandardTime = ... # type: QTimeZone.TimeType
        DaylightTime = ... # type: QTimeZone.TimeType
        GenericTime = ... # type: QTimeZone.TimeType

    class OffsetData(PyQt5.sipsimplewrapper):

        abbreviation = ... # type: typing.Optional[str]
        atUtc = ... # type: typing.Union[QDateTime, datetime.datetime]
        daylightTimeOffset = ... # type: int
        offsetFromUtc = ... # type: int
        standardTimeOffset = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTimeZone.OffsetData') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, ianaId: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, offsetSeconds: int) -> None: ...
    @typing.overload
    def __init__(self, zoneId: typing.Union[QByteArray, bytes, bytearray], offsetSeconds: int, name: typing.Optional[str], abbreviation: typing.Optional[str], country: QLocale.Country = ..., comment: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QTimeZone') -> None: ...

    @staticmethod
    def utc() -> 'QTimeZone': ...
    @staticmethod
    def systemTimeZone() -> 'QTimeZone': ...
    @typing.overload
    @staticmethod
    def windowsIdToIanaIds(windowsId: typing.Union[QByteArray, bytes, bytearray]) -> typing.List[QByteArray]: ...
    @typing.overload
    @staticmethod
    def windowsIdToIanaIds(windowsId: typing.Union[QByteArray, bytes, bytearray], country: QLocale.Country) -> typing.List[QByteArray]: ...
    @typing.overload
    @staticmethod
    def windowsIdToDefaultIanaId(windowsId: typing.Union[QByteArray, bytes, bytearray]) -> QByteArray: ...
    @typing.overload
    @staticmethod
    def windowsIdToDefaultIanaId(windowsId: typing.Union[QByteArray, bytes, bytearray], country: QLocale.Country) -> QByteArray: ...
    @staticmethod
    def ianaIdToWindowsId(ianaId: typing.Union[QByteArray, bytes, bytearray]) -> QByteArray: ...
    @typing.overload
    @staticmethod
    def availableTimeZoneIds() -> typing.List[QByteArray]: ...
    @typing.overload
    @staticmethod
    def availableTimeZoneIds(country: QLocale.Country) -> typing.List[QByteArray]: ...
    @typing.overload
    @staticmethod
    def availableTimeZoneIds(offsetSeconds: int) -> typing.List[QByteArray]: ...
    @staticmethod
    def isTimeZoneIdAvailable(ianaId: typing.Union[QByteArray, bytes, bytearray]) -> bool: ...
    @staticmethod
    def systemTimeZoneId() -> QByteArray: ...
    def transitions(self, fromDateTime: typing.Union[QDateTime, datetime.datetime], toDateTime: typing.Union[QDateTime, datetime.datetime]) -> typing.List['QTimeZone.OffsetData']: ...
    def previousTransition(self, beforeDateTime: typing.Union[QDateTime, datetime.datetime]) -> 'QTimeZone.OffsetData': ...
    def nextTransition(self, afterDateTime: typing.Union[QDateTime, datetime.datetime]) -> 'QTimeZone.OffsetData': ...
    def hasTransitions(self) -> bool: ...
    def offsetData(self, forDateTime: typing.Union[QDateTime, datetime.datetime]) -> 'QTimeZone.OffsetData': ...
    def isDaylightTime(self, atDateTime: typing.Union[QDateTime, datetime.datetime]) -> bool: ...
    def hasDaylightTime(self) -> bool: ...
    def daylightTimeOffset(self, atDateTime: typing.Union[QDateTime, datetime.datetime]) -> int: ...
    def standardTimeOffset(self, atDateTime: typing.Union[QDateTime, datetime.datetime]) -> int: ...
    def offsetFromUtc(self, atDateTime: typing.Union[QDateTime, datetime.datetime]) -> int: ...
    def abbreviation(self, atDateTime: typing.Union[QDateTime, datetime.datetime]) -> str: ...
    @typing.overload
    def displayName(self, atDateTime: typing.Union[QDateTime, datetime.datetime], nameType: 'QTimeZone.NameType' = ..., locale: QLocale = ...) -> str: ...
    @typing.overload
    def displayName(self, timeType: 'QTimeZone.TimeType', nameType: 'QTimeZone.NameType' = ..., locale: QLocale = ...) -> str: ...
    def comment(self) -> str: ...
    def country(self) -> QLocale.Country: ...
    def id(self) -> QByteArray: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def swap(self, other: 'QTimeZone') -> None: ...


class QTranslator(QObject):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def filePath(self) -> str: ...
    def language(self) -> str: ...
    def loadFromData(self, data: typing.Optional[PyQt5.sip.array[bytes]], directory: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, fileName: typing.Optional[str], directory: typing.Optional[str] = ..., searchDelimiters: typing.Optional[str] = ..., suffix: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, locale: QLocale, fileName: typing.Optional[str], prefix: typing.Optional[str] = ..., directory: typing.Optional[str] = ..., suffix: typing.Optional[str] = ...) -> bool: ...
    def isEmpty(self) -> bool: ...
    def translate(self, context: typing.Optional[str], sourceText: typing.Optional[str], disambiguation: typing.Optional[str] = ..., n: int = ...) -> str: ...


class QTransposeProxyModel(QAbstractProxyModel):

    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...

    def sort(self, column: int, order: Qt.SortOrder = ...) -> None: ...
    def moveColumns(self, sourceParent: QModelIndex, sourceColumn: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def removeColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def moveRows(self, sourceParent: QModelIndex, sourceRow: int, count: int, destinationParent: QModelIndex, destinationChild: int) -> bool: ...
    def removeRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QModelIndex = ...) -> bool: ...
    def index(self, row: int, column: int, parent: QModelIndex = ...) -> QModelIndex: ...
    def parent(self, index: QModelIndex) -> QModelIndex: ...
    def mapToSource(self, proxyIndex: QModelIndex) -> QModelIndex: ...
    def mapFromSource(self, sourceIndex: QModelIndex) -> QModelIndex: ...
    def itemData(self, index: QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def span(self, index: QModelIndex) -> QSize: ...
    def setItemData(self, index: QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def setHeaderData(self, section: int, orientation: Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...) -> typing.Any: ...
    def columnCount(self, parent: QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QModelIndex = ...) -> int: ...
    def setSourceModel(self, newSourceModel: typing.Optional[QAbstractItemModel]) -> None: ...


class QUrl(PyQt5.sipsimplewrapper):

    class UserInputResolutionOption(int):
        DefaultResolution = ... # type: QUrl.UserInputResolutionOption
        AssumeLocalFile = ... # type: QUrl.UserInputResolutionOption

    class ComponentFormattingOption(int):
        PrettyDecoded = ... # type: QUrl.ComponentFormattingOption
        EncodeSpaces = ... # type: QUrl.ComponentFormattingOption
        EncodeUnicode = ... # type: QUrl.ComponentFormattingOption
        EncodeDelimiters = ... # type: QUrl.ComponentFormattingOption
        EncodeReserved = ... # type: QUrl.ComponentFormattingOption
        DecodeReserved = ... # type: QUrl.ComponentFormattingOption
        FullyEncoded = ... # type: QUrl.ComponentFormattingOption
        FullyDecoded = ... # type: QUrl.ComponentFormattingOption

    class UrlFormattingOption(int):
        None_ = ... # type: QUrl.UrlFormattingOption
        RemoveScheme = ... # type: QUrl.UrlFormattingOption
        RemovePassword = ... # type: QUrl.UrlFormattingOption
        RemoveUserInfo = ... # type: QUrl.UrlFormattingOption
        RemovePort = ... # type: QUrl.UrlFormattingOption
        RemoveAuthority = ... # type: QUrl.UrlFormattingOption
        RemovePath = ... # type: QUrl.UrlFormattingOption
        RemoveQuery = ... # type: QUrl.UrlFormattingOption
        RemoveFragment = ... # type: QUrl.UrlFormattingOption
        PreferLocalFile = ... # type: QUrl.UrlFormattingOption
        StripTrailingSlash = ... # type: QUrl.UrlFormattingOption
        RemoveFilename = ... # type: QUrl.UrlFormattingOption
        NormalizePathSegments = ... # type: QUrl.UrlFormattingOption

    class ParsingMode(int):
        TolerantMode = ... # type: QUrl.ParsingMode
        StrictMode = ... # type: QUrl.ParsingMode
        DecodedMode = ... # type: QUrl.ParsingMode

    class FormattingOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: 'QUrl.FormattingOptions') -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __invert__(self) -> 'QUrl.FormattingOptions': ...
        def __and__(self, mask: int) -> 'QUrl.FormattingOptions': ...
        def __xor__(self, f: 'QUrl.FormattingOptions') -> 'QUrl.FormattingOptions': ...
        def __or__(self, f: 'QUrl.FormattingOptions') -> 'QUrl.FormattingOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...
        def __ixor__(self, f: 'QUrl.FormattingOptions') -> 'QUrl.FormattingOptions': ...
        def __ior__(self, f: 'QUrl.FormattingOptions') -> 'QUrl.FormattingOptions': ...
        def __iand__(self, mask: int) -> 'QUrl.FormattingOptions': ...

    class ComponentFormattingOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __xor__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __ior__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __or__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __iand__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __and__(self, f: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption']) -> 'QUrl.ComponentFormattingOptions': ...
        def __invert__(self) -> 'QUrl.ComponentFormattingOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class UserInputResolutionOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __xor__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __ior__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __or__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __iand__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __and__(self, f: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption']) -> 'QUrl.UserInputResolutionOptions': ...
        def __invert__(self) -> 'QUrl.UserInputResolutionOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, url: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    @typing.overload
    def __init__(self, copy: 'QUrl') -> None: ...

    def __ge__(self, url: 'QUrl') -> bool: ...
    def matches(self, url: 'QUrl', options: typing.Union['QUrl.FormattingOptions', 'QUrl.UrlFormattingOption', 'QUrl.ComponentFormattingOption']) -> bool: ...
    def fileName(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def adjusted(self, options: typing.Union['QUrl.FormattingOptions', 'QUrl.UrlFormattingOption', 'QUrl.ComponentFormattingOption']) -> 'QUrl': ...
    @staticmethod
    def fromStringList(uris: typing.Iterable[typing.Optional[str]], mode: 'QUrl.ParsingMode' = ...) -> typing.List['QUrl']: ...
    @staticmethod
    def toStringList(uris: typing.Iterable['QUrl'], options: 'QUrl.FormattingOptions' = ...) -> typing.List[str]: ...
    def query(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    @typing.overload
    def setQuery(self, query: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    @typing.overload
    def setQuery(self, query: 'QUrlQuery') -> None: ...
    def toDisplayString(self, options: 'QUrl.FormattingOptions' = ...) -> str: ...
    def isLocalFile(self) -> bool: ...
    def topLevelDomain(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def swap(self, other: 'QUrl') -> None: ...
    @typing.overload
    @staticmethod
    def fromUserInput(userInput: typing.Optional[str]) -> 'QUrl': ...
    @typing.overload
    @staticmethod
    def fromUserInput(userInput: typing.Optional[str], workingDirectory: typing.Optional[str], options: typing.Union['QUrl.UserInputResolutionOptions', 'QUrl.UserInputResolutionOption'] = ...) -> 'QUrl': ...
    @staticmethod
    def setIdnWhitelist(a0: typing.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def idnWhitelist() -> typing.List[str]: ...
    @staticmethod
    def toAce(a0: typing.Optional[str]) -> QByteArray: ...
    @staticmethod
    def fromAce(a0: typing.Union[QByteArray, bytes, bytearray]) -> str: ...
    def errorString(self) -> str: ...
    def hasFragment(self) -> bool: ...
    def hasQuery(self) -> bool: ...
    @staticmethod
    def toPercentEncoding(input: typing.Optional[str], exclude: typing.Union[QByteArray, bytes, bytearray] = ..., include: typing.Union[QByteArray, bytes, bytearray] = ...) -> QByteArray: ...
    @staticmethod
    def fromPercentEncoding(a0: typing.Union[QByteArray, bytes, bytearray]) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __lt__(self, url: 'QUrl') -> bool: ...
    def isDetached(self) -> bool: ...
    def detach(self) -> None: ...
    @staticmethod
    def fromEncoded(u: typing.Union[QByteArray, bytes, bytearray], mode: 'QUrl.ParsingMode' = ...) -> 'QUrl': ...
    def toEncoded(self, options: 'QUrl.FormattingOptions' = ...) -> QByteArray: ...
    def toString(self, options: 'QUrl.FormattingOptions' = ...) -> str: ...
    def toLocalFile(self) -> str: ...
    @staticmethod
    def fromLocalFile(localfile: typing.Optional[str]) -> 'QUrl': ...
    def isParentOf(self, url: 'QUrl') -> bool: ...
    def isRelative(self) -> bool: ...
    def resolved(self, relative: 'QUrl') -> 'QUrl': ...
    def fragment(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setFragment(self, fragment: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def path(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setPath(self, path: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def port(self, defaultPort: int = ...) -> int: ...
    def setPort(self, port: int) -> None: ...
    def host(self, a0: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setHost(self, host: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def password(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setPassword(self, password: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def userName(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setUserName(self, userName: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def userInfo(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setUserInfo(self, userInfo: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def authority(self, options: typing.Union['QUrl.ComponentFormattingOptions', 'QUrl.ComponentFormattingOption'] = ...) -> str: ...
    def setAuthority(self, authority: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def scheme(self) -> str: ...
    def setScheme(self, scheme: typing.Optional[str]) -> None: ...
    def clear(self) -> None: ...
    def isEmpty(self) -> bool: ...
    def isValid(self) -> bool: ...
    def setUrl(self, url: typing.Optional[str], mode: 'QUrl.ParsingMode' = ...) -> None: ...
    def url(self, options: 'QUrl.FormattingOptions' = ...) -> str: ...
    def __repr__(self) -> str: ...
    def __hash__(self) -> int: ...


class QUrlQuery(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, url: QUrl) -> None: ...
    @typing.overload
    def __init__(self, queryString: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QUrlQuery') -> None: ...

    def __hash__(self) -> int: ...
    @staticmethod
    def defaultQueryPairDelimiter() -> str: ...
    @staticmethod
    def defaultQueryValueDelimiter() -> str: ...
    def removeAllQueryItems(self, key: typing.Optional[str]) -> None: ...
    def allQueryItemValues(self, key: typing.Optional[str], options: typing.Union[QUrl.ComponentFormattingOptions, QUrl.ComponentFormattingOption] = ...) -> typing.List[str]: ...
    def queryItemValue(self, key: typing.Optional[str], options: typing.Union[QUrl.ComponentFormattingOptions, QUrl.ComponentFormattingOption] = ...) -> str: ...
    def removeQueryItem(self, key: typing.Optional[str]) -> None: ...
    def addQueryItem(self, key: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    def hasQueryItem(self, key: typing.Optional[str]) -> bool: ...
    def queryItems(self, options: typing.Union[QUrl.ComponentFormattingOptions, QUrl.ComponentFormattingOption] = ...) -> typing.List[typing.Tuple[str, str]]: ...
    def setQueryItems(self, query: typing.Iterable[typing.Tuple[typing.Optional[str], typing.Optional[str]]]) -> None: ...
    def queryPairDelimiter(self) -> str: ...
    def queryValueDelimiter(self) -> str: ...
    def setQueryDelimiters(self, valueDelimiter: str, pairDelimiter: str) -> None: ...
    def toString(self, options: typing.Union[QUrl.ComponentFormattingOptions, QUrl.ComponentFormattingOption] = ...) -> str: ...
    def setQuery(self, queryString: typing.Optional[str]) -> None: ...
    def query(self, options: typing.Union[QUrl.ComponentFormattingOptions, QUrl.ComponentFormattingOption] = ...) -> str: ...
    def clear(self) -> None: ...
    def isDetached(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def swap(self, other: 'QUrlQuery') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QUuid(PyQt5.sipsimplewrapper):

    class StringFormat(int):
        WithBraces = ... # type: QUuid.StringFormat
        WithoutBraces = ... # type: QUuid.StringFormat
        Id128 = ... # type: QUuid.StringFormat

    class Version(int):
        VerUnknown = ... # type: QUuid.Version
        Time = ... # type: QUuid.Version
        EmbeddedPOSIX = ... # type: QUuid.Version
        Md5 = ... # type: QUuid.Version
        Name = ... # type: QUuid.Version
        Random = ... # type: QUuid.Version
        Sha1 = ... # type: QUuid.Version

    class Variant(int):
        VarUnknown = ... # type: QUuid.Variant
        NCS = ... # type: QUuid.Variant
        DCE = ... # type: QUuid.Variant
        Microsoft = ... # type: QUuid.Variant
        Reserved = ... # type: QUuid.Variant

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, l: int, w1: int, w2: int, b1: int, b2: int, b3: int, b4: int, b5: int, b6: int, b7: int, b8: int) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QUuid') -> None: ...

    def __le__(self, rhs: 'QUuid') -> bool: ...
    def __ge__(self, rhs: 'QUuid') -> bool: ...
    @staticmethod
    def fromRfc4122(a0: typing.Union[QByteArray, bytes, bytearray]) -> 'QUuid': ...
    def toRfc4122(self) -> QByteArray: ...
    @typing.overload
    def toByteArray(self) -> QByteArray: ...
    @typing.overload
    def toByteArray(self, mode: 'QUuid.StringFormat') -> QByteArray: ...
    def version(self) -> 'QUuid.Version': ...
    def variant(self) -> 'QUuid.Variant': ...
    @typing.overload
    @staticmethod
    def createUuidV5(ns: 'QUuid', baseData: typing.Union[QByteArray, bytes, bytearray]) -> 'QUuid': ...
    @typing.overload
    @staticmethod
    def createUuidV5(ns: 'QUuid', baseData: typing.Optional[str]) -> 'QUuid': ...
    @typing.overload
    @staticmethod
    def createUuidV3(ns: 'QUuid', baseData: typing.Union[QByteArray, bytes, bytearray]) -> 'QUuid': ...
    @typing.overload
    @staticmethod
    def createUuidV3(ns: 'QUuid', baseData: typing.Optional[str]) -> 'QUuid': ...
    @staticmethod
    def createUuid() -> 'QUuid': ...
    def __gt__(self, other: 'QUuid') -> bool: ...
    def __lt__(self, other: 'QUuid') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isNull(self) -> bool: ...
    @typing.overload
    def toString(self) -> str: ...
    @typing.overload
    def toString(self, mode: 'QUuid.StringFormat') -> str: ...
    def __repr__(self) -> str: ...
    def __hash__(self) -> int: ...


class QVariant(PyQt5.sipsimplewrapper):

    class Type(int):
        Invalid = ... # type: QVariant.Type
        Bool = ... # type: QVariant.Type
        Int = ... # type: QVariant.Type
        UInt = ... # type: QVariant.Type
        LongLong = ... # type: QVariant.Type
        ULongLong = ... # type: QVariant.Type
        Double = ... # type: QVariant.Type
        Char = ... # type: QVariant.Type
        Map = ... # type: QVariant.Type
        List = ... # type: QVariant.Type
        String = ... # type: QVariant.Type
        StringList = ... # type: QVariant.Type
        ByteArray = ... # type: QVariant.Type
        BitArray = ... # type: QVariant.Type
        Date = ... # type: QVariant.Type
        Time = ... # type: QVariant.Type
        DateTime = ... # type: QVariant.Type
        Url = ... # type: QVariant.Type
        Locale = ... # type: QVariant.Type
        Rect = ... # type: QVariant.Type
        RectF = ... # type: QVariant.Type
        Size = ... # type: QVariant.Type
        SizeF = ... # type: QVariant.Type
        Line = ... # type: QVariant.Type
        LineF = ... # type: QVariant.Type
        Point = ... # type: QVariant.Type
        PointF = ... # type: QVariant.Type
        RegExp = ... # type: QVariant.Type
        Font = ... # type: QVariant.Type
        Pixmap = ... # type: QVariant.Type
        Brush = ... # type: QVariant.Type
        Color = ... # type: QVariant.Type
        Palette = ... # type: QVariant.Type
        Icon = ... # type: QVariant.Type
        Image = ... # type: QVariant.Type
        Polygon = ... # type: QVariant.Type
        Region = ... # type: QVariant.Type
        Bitmap = ... # type: QVariant.Type
        Cursor = ... # type: QVariant.Type
        SizePolicy = ... # type: QVariant.Type
        KeySequence = ... # type: QVariant.Type
        Pen = ... # type: QVariant.Type
        TextLength = ... # type: QVariant.Type
        TextFormat = ... # type: QVariant.Type
        Matrix = ... # type: QVariant.Type
        Transform = ... # type: QVariant.Type
        Hash = ... # type: QVariant.Type
        Matrix4x4 = ... # type: QVariant.Type
        Vector2D = ... # type: QVariant.Type
        Vector3D = ... # type: QVariant.Type
        Vector4D = ... # type: QVariant.Type
        Quaternion = ... # type: QVariant.Type
        EasingCurve = ... # type: QVariant.Type
        Uuid = ... # type: QVariant.Type
        ModelIndex = ... # type: QVariant.Type
        PolygonF = ... # type: QVariant.Type
        RegularExpression = ... # type: QVariant.Type
        PersistentModelIndex = ... # type: QVariant.Type
        UserType = ... # type: QVariant.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: 'QVariant.Type') -> None: ...
    @typing.overload
    def __init__(self, obj: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional['QVariant']) -> None: ...

    def __ge__(self, v: typing.Any) -> bool: ...
    def __gt__(self, v: typing.Any) -> bool: ...
    def __le__(self, v: typing.Any) -> bool: ...
    def __lt__(self, v: typing.Any) -> bool: ...
    def swap(self, other: typing.Optional['QVariant']) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def nameToType(name: typing.Optional[str]) -> 'QVariant.Type': ...
    @staticmethod
    def typeToName(typeId: int) -> typing.Optional[str]: ...
    def save(self, ds: QDataStream) -> None: ...
    def load(self, ds: QDataStream) -> None: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...
    def isValid(self) -> bool: ...
    def convert(self, targetTypeId: int) -> bool: ...
    def canConvert(self, targetTypeId: int) -> bool: ...
    def typeName(self) -> typing.Optional[str]: ...
    def userType(self) -> int: ...
    def type(self) -> 'QVariant.Type': ...
    def value(self) -> typing.Any: ...


class QVersionNumber(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, seg: typing.Iterable[int]) -> None: ...
    @typing.overload
    def __init__(self, maj: int) -> None: ...
    @typing.overload
    def __init__(self, maj: int, min: int) -> None: ...
    @typing.overload
    def __init__(self, maj: int, min: int, mic: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVersionNumber') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __lt__(self, rhs: 'QVersionNumber') -> bool: ...
    def __le__(self, rhs: 'QVersionNumber') -> bool: ...
    def __gt__(self, rhs: 'QVersionNumber') -> bool: ...
    def __ge__(self, rhs: 'QVersionNumber') -> bool: ...
    def __hash__(self) -> int: ...
    @staticmethod
    def fromString(string: typing.Optional[str]) -> typing.Tuple['QVersionNumber', typing.Optional[int]]: ...
    def toString(self) -> str: ...
    @staticmethod
    def commonPrefix(v1: 'QVersionNumber', v2: 'QVersionNumber') -> 'QVersionNumber': ...
    @staticmethod
    def compare(v1: 'QVersionNumber', v2: 'QVersionNumber') -> int: ...
    def isPrefixOf(self, other: 'QVersionNumber') -> bool: ...
    def segmentCount(self) -> int: ...
    def segmentAt(self, index: int) -> int: ...
    def segments(self) -> typing.List[int]: ...
    def normalized(self) -> 'QVersionNumber': ...
    def microVersion(self) -> int: ...
    def minorVersion(self) -> int: ...
    def majorVersion(self) -> int: ...
    def isNormalized(self) -> bool: ...
    def isNull(self) -> bool: ...


class QWaitCondition(PyQt5.sipsimplewrapper):

    def __init__(self) -> None: ...

    def wakeAll(self) -> None: ...
    def wakeOne(self) -> None: ...
    @typing.overload
    def wait(self, mutex: typing.Optional[QMutex], msecs: int = ...) -> bool: ...
    @typing.overload
    def wait(self, lockedMutex: typing.Optional[QMutex], deadline: QDeadlineTimer) -> bool: ...
    @typing.overload
    def wait(self, readWriteLock: typing.Optional[QReadWriteLock], msecs: int = ...) -> bool: ...
    @typing.overload
    def wait(self, lockedReadWriteLock: typing.Optional[QReadWriteLock], deadline: QDeadlineTimer) -> bool: ...


class QXmlStreamAttribute(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, qualifiedName: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, namespaceUri: typing.Optional[str], name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamAttribute') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isDefault(self) -> bool: ...
    def value(self) -> str: ...
    def prefix(self) -> str: ...
    def qualifiedName(self) -> str: ...
    def name(self) -> str: ...
    def namespaceUri(self) -> str: ...


class QXmlStreamAttributes(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamAttributes') -> None: ...

    def __contains__(self, value: QXmlStreamAttribute) -> int: ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, value: QXmlStreamAttribute) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QXmlStreamAttributes') -> None: ...
    @typing.overload
    def __getitem__(self, i: int) -> QXmlStreamAttribute: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QXmlStreamAttributes': ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, other: 'QXmlStreamAttributes') -> 'QXmlStreamAttributes': ...
    @typing.overload
    def __iadd__(self, value: QXmlStreamAttribute) -> 'QXmlStreamAttributes': ...
    def __ne__(self, other: object): ...
    def size(self) -> int: ...
    def replace(self, i: int, value: QXmlStreamAttribute) -> None: ...
    @typing.overload
    def remove(self, i: int) -> None: ...
    @typing.overload
    def remove(self, i: int, count: int) -> None: ...
    def prepend(self, value: QXmlStreamAttribute) -> None: ...
    def lastIndexOf(self, value: QXmlStreamAttribute, from_: int = ...) -> int: ...
    def last(self) -> QXmlStreamAttribute: ...
    def isEmpty(self) -> bool: ...
    def insert(self, i: int, value: QXmlStreamAttribute) -> None: ...
    def indexOf(self, value: QXmlStreamAttribute, from_: int = ...) -> int: ...
    def first(self) -> QXmlStreamAttribute: ...
    def fill(self, value: QXmlStreamAttribute, size: int = ...) -> None: ...
    def data(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, value: QXmlStreamAttribute) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def contains(self, value: QXmlStreamAttribute) -> bool: ...
    def clear(self) -> None: ...
    def at(self, i: int) -> QXmlStreamAttribute: ...
    @typing.overload
    def hasAttribute(self, qualifiedName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def hasAttribute(self, namespaceUri: typing.Optional[str], name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def append(self, namespaceUri: typing.Optional[str], name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def append(self, qualifiedName: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def append(self, attribute: QXmlStreamAttribute) -> None: ...
    @typing.overload
    def value(self, namespaceUri: typing.Optional[str], name: typing.Optional[str]) -> str: ...
    @typing.overload
    def value(self, qualifiedName: typing.Optional[str]) -> str: ...


class QXmlStreamNamespaceDeclaration(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamNamespaceDeclaration') -> None: ...
    @typing.overload
    def __init__(self, prefix: typing.Optional[str], namespaceUri: typing.Optional[str]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def namespaceUri(self) -> str: ...
    def prefix(self) -> str: ...


class QXmlStreamNotationDeclaration(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamNotationDeclaration') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def publicId(self) -> str: ...
    def systemId(self) -> str: ...
    def name(self) -> str: ...


class QXmlStreamEntityDeclaration(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamEntityDeclaration') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def value(self) -> str: ...
    def publicId(self) -> str: ...
    def systemId(self) -> str: ...
    def notationName(self) -> str: ...
    def name(self) -> str: ...


class QXmlStreamEntityResolver(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QXmlStreamEntityResolver') -> None: ...

    def resolveUndeclaredEntity(self, name: typing.Optional[str]) -> str: ...


class QXmlStreamReader(PyQt5.sipsimplewrapper):

    class Error(int):
        NoError = ... # type: QXmlStreamReader.Error
        UnexpectedElementError = ... # type: QXmlStreamReader.Error
        CustomError = ... # type: QXmlStreamReader.Error
        NotWellFormedError = ... # type: QXmlStreamReader.Error
        PrematureEndOfDocumentError = ... # type: QXmlStreamReader.Error

    class ReadElementTextBehaviour(int):
        ErrorOnUnexpectedElement = ... # type: QXmlStreamReader.ReadElementTextBehaviour
        IncludeChildElements = ... # type: QXmlStreamReader.ReadElementTextBehaviour
        SkipChildElements = ... # type: QXmlStreamReader.ReadElementTextBehaviour

    class TokenType(int):
        NoToken = ... # type: QXmlStreamReader.TokenType
        Invalid = ... # type: QXmlStreamReader.TokenType
        StartDocument = ... # type: QXmlStreamReader.TokenType
        EndDocument = ... # type: QXmlStreamReader.TokenType
        StartElement = ... # type: QXmlStreamReader.TokenType
        EndElement = ... # type: QXmlStreamReader.TokenType
        Characters = ... # type: QXmlStreamReader.TokenType
        Comment = ... # type: QXmlStreamReader.TokenType
        DTD = ... # type: QXmlStreamReader.TokenType
        EntityReference = ... # type: QXmlStreamReader.TokenType
        ProcessingInstruction = ... # type: QXmlStreamReader.TokenType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QIODevice]) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Optional[str]) -> None: ...

    def setEntityExpansionLimit(self, limit: int) -> None: ...
    def entityExpansionLimit(self) -> int: ...
    def skipCurrentElement(self) -> None: ...
    def readNextStartElement(self) -> bool: ...
    def entityResolver(self) -> typing.Optional[QXmlStreamEntityResolver]: ...
    def setEntityResolver(self, resolver: typing.Optional[QXmlStreamEntityResolver]) -> None: ...
    def hasError(self) -> bool: ...
    def error(self) -> 'QXmlStreamReader.Error': ...
    def errorString(self) -> str: ...
    def raiseError(self, message: typing.Optional[str] = ...) -> None: ...
    def dtdSystemId(self) -> str: ...
    def dtdPublicId(self) -> str: ...
    def dtdName(self) -> str: ...
    def entityDeclarations(self) -> typing.List[QXmlStreamEntityDeclaration]: ...
    def notationDeclarations(self) -> typing.List[QXmlStreamNotationDeclaration]: ...
    def addExtraNamespaceDeclarations(self, extraNamespaceDeclaractions: typing.Iterable[QXmlStreamNamespaceDeclaration]) -> None: ...
    def addExtraNamespaceDeclaration(self, extraNamespaceDeclaraction: QXmlStreamNamespaceDeclaration) -> None: ...
    def namespaceDeclarations(self) -> typing.List[QXmlStreamNamespaceDeclaration]: ...
    def text(self) -> str: ...
    def processingInstructionData(self) -> str: ...
    def processingInstructionTarget(self) -> str: ...
    def prefix(self) -> str: ...
    def qualifiedName(self) -> str: ...
    def namespaceUri(self) -> str: ...
    def name(self) -> str: ...
    def readElementText(self, behaviour: 'QXmlStreamReader.ReadElementTextBehaviour' = ...) -> str: ...
    def attributes(self) -> QXmlStreamAttributes: ...
    def characterOffset(self) -> int: ...
    def columnNumber(self) -> int: ...
    def lineNumber(self) -> int: ...
    def documentEncoding(self) -> str: ...
    def documentVersion(self) -> str: ...
    def isStandaloneDocument(self) -> bool: ...
    def isProcessingInstruction(self) -> bool: ...
    def isEntityReference(self) -> bool: ...
    def isDTD(self) -> bool: ...
    def isComment(self) -> bool: ...
    def isCDATA(self) -> bool: ...
    def isWhitespace(self) -> bool: ...
    def isCharacters(self) -> bool: ...
    def isEndElement(self) -> bool: ...
    def isStartElement(self) -> bool: ...
    def isEndDocument(self) -> bool: ...
    def isStartDocument(self) -> bool: ...
    def namespaceProcessing(self) -> bool: ...
    def setNamespaceProcessing(self, a0: bool) -> None: ...
    def tokenString(self) -> str: ...
    def tokenType(self) -> 'QXmlStreamReader.TokenType': ...
    def readNext(self) -> 'QXmlStreamReader.TokenType': ...
    def atEnd(self) -> bool: ...
    def clear(self) -> None: ...
    @typing.overload
    def addData(self, data: typing.Union[QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def addData(self, data: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QIODevice]: ...
    def setDevice(self, device: typing.Optional[QIODevice]) -> None: ...


class QXmlStreamWriter(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QIODevice]) -> None: ...
    @typing.overload
    def __init__(self, array: typing.Optional[typing.Union[QByteArray, bytes, bytearray]]) -> None: ...

    def hasError(self) -> bool: ...
    def writeCurrentToken(self, reader: QXmlStreamReader) -> None: ...
    @typing.overload
    def writeStartElement(self, qualifiedName: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeStartElement(self, namespaceUri: typing.Optional[str], name: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeStartDocument(self) -> None: ...
    @typing.overload
    def writeStartDocument(self, version: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeStartDocument(self, version: typing.Optional[str], standalone: bool) -> None: ...
    def writeProcessingInstruction(self, target: typing.Optional[str], data: typing.Optional[str] = ...) -> None: ...
    def writeDefaultNamespace(self, namespaceUri: typing.Optional[str]) -> None: ...
    def writeNamespace(self, namespaceUri: typing.Optional[str], prefix: typing.Optional[str] = ...) -> None: ...
    def writeEntityReference(self, name: typing.Optional[str]) -> None: ...
    def writeEndElement(self) -> None: ...
    def writeEndDocument(self) -> None: ...
    @typing.overload
    def writeTextElement(self, qualifiedName: typing.Optional[str], text: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeTextElement(self, namespaceUri: typing.Optional[str], name: typing.Optional[str], text: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeEmptyElement(self, qualifiedName: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeEmptyElement(self, namespaceUri: typing.Optional[str], name: typing.Optional[str]) -> None: ...
    def writeDTD(self, dtd: typing.Optional[str]) -> None: ...
    def writeComment(self, text: typing.Optional[str]) -> None: ...
    def writeCharacters(self, text: typing.Optional[str]) -> None: ...
    def writeCDATA(self, text: typing.Optional[str]) -> None: ...
    def writeAttributes(self, attributes: QXmlStreamAttributes) -> None: ...
    @typing.overload
    def writeAttribute(self, qualifiedName: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeAttribute(self, namespaceUri: typing.Optional[str], name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def writeAttribute(self, attribute: QXmlStreamAttribute) -> None: ...
    def autoFormattingIndent(self) -> int: ...
    def setAutoFormattingIndent(self, spaces: int) -> None: ...
    def autoFormatting(self) -> bool: ...
    def setAutoFormatting(self, a0: bool) -> None: ...
    def codec(self) -> typing.Optional[QTextCodec]: ...
    @typing.overload
    def setCodec(self, codec: typing.Optional[QTextCodec]) -> None: ...
    @typing.overload
    def setCodec(self, codecName: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QIODevice]: ...
    def setDevice(self, device: typing.Optional[QIODevice]) -> None: ...


class QSysInfo(PyQt5.sipsimplewrapper):

    class WinVersion(int):
        WV_32s = ... # type: QSysInfo.WinVersion
        WV_95 = ... # type: QSysInfo.WinVersion
        WV_98 = ... # type: QSysInfo.WinVersion
        WV_Me = ... # type: QSysInfo.WinVersion
        WV_DOS_based = ... # type: QSysInfo.WinVersion
        WV_NT = ... # type: QSysInfo.WinVersion
        WV_2000 = ... # type: QSysInfo.WinVersion
        WV_XP = ... # type: QSysInfo.WinVersion
        WV_2003 = ... # type: QSysInfo.WinVersion
        WV_VISTA = ... # type: QSysInfo.WinVersion
        WV_WINDOWS7 = ... # type: QSysInfo.WinVersion
        WV_WINDOWS8 = ... # type: QSysInfo.WinVersion
        WV_WINDOWS8_1 = ... # type: QSysInfo.WinVersion
        WV_WINDOWS10 = ... # type: QSysInfo.WinVersion
        WV_NT_based = ... # type: QSysInfo.WinVersion
        WV_4_0 = ... # type: QSysInfo.WinVersion
        WV_5_0 = ... # type: QSysInfo.WinVersion
        WV_5_1 = ... # type: QSysInfo.WinVersion
        WV_5_2 = ... # type: QSysInfo.WinVersion
        WV_6_0 = ... # type: QSysInfo.WinVersion
        WV_6_1 = ... # type: QSysInfo.WinVersion
        WV_6_2 = ... # type: QSysInfo.WinVersion
        WV_6_3 = ... # type: QSysInfo.WinVersion
        WV_10_0 = ... # type: QSysInfo.WinVersion
        WV_CE = ... # type: QSysInfo.WinVersion
        WV_CENET = ... # type: QSysInfo.WinVersion
        WV_CE_5 = ... # type: QSysInfo.WinVersion
        WV_CE_6 = ... # type: QSysInfo.WinVersion
        WV_CE_based = ... # type: QSysInfo.WinVersion

    class Endian(int):
        BigEndian = ... # type: QSysInfo.Endian
        LittleEndian = ... # type: QSysInfo.Endian
        ByteOrder = ... # type: QSysInfo.Endian

    class Sizes(int):
        WordSize = ... # type: QSysInfo.Sizes

    WindowsVersion = ... # type: 'QSysInfo.WinVersion'

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSysInfo') -> None: ...

    @staticmethod
    def machineHostName() -> str: ...
    @staticmethod
    def productVersion() -> str: ...
    @staticmethod
    def productType() -> str: ...
    @staticmethod
    def prettyProductName() -> str: ...
    @staticmethod
    def kernelVersion() -> str: ...
    @staticmethod
    def kernelType() -> str: ...
    @staticmethod
    def currentCpuArchitecture() -> str: ...
    @staticmethod
    def buildCpuArchitecture() -> str: ...
    @staticmethod
    def buildAbi() -> str: ...
    @staticmethod
    def windowsVersion() -> 'QSysInfo.WinVersion': ...


class QWinEventNotifier(QObject):

    @typing.overload
    def __init__(self, parent: typing.Optional[QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, hEvent: typing.Optional[PyQt5.sip.voidptr], parent: typing.Optional[QObject] = ...) -> None: ...

    def event(self, e: typing.Optional[QEvent]) -> bool: ...
    activated: typing.ClassVar[pyqtSignal]
    def setEnabled(self, enable: bool) -> None: ...
    def setHandle(self, hEvent: typing.Optional[PyQt5.sip.voidptr]) -> None: ...
    def isEnabled(self) -> bool: ...
    def handle(self) -> typing.Optional[PyQt5.sip.voidptr]: ...


PYQT_VERSION = ... # type: int
PYQT_VERSION_STR = ... # type: str
QT_VERSION = ... # type: int
QT_VERSION_STR = ... # type: str


def qSetRealNumberPrecision(precision: int) -> QTextStreamManipulator: ...
def qSetPadChar(ch: str) -> QTextStreamManipulator: ...
def qSetFieldWidth(width: int) -> QTextStreamManipulator: ...
def ws(s: QTextStream) -> QTextStream: ...
def bom(s: QTextStream) -> QTextStream: ...
def reset(s: QTextStream) -> QTextStream: ...
def flush(s: QTextStream) -> QTextStream: ...
def endl(s: QTextStream) -> QTextStream: ...
def center(s: QTextStream) -> QTextStream: ...
def right(s: QTextStream) -> QTextStream: ...
def left(s: QTextStream) -> QTextStream: ...
def scientific(s: QTextStream) -> QTextStream: ...
def fixed(s: QTextStream) -> QTextStream: ...
def lowercasedigits(s: QTextStream) -> QTextStream: ...
def lowercasebase(s: QTextStream) -> QTextStream: ...
def uppercasedigits(s: QTextStream) -> QTextStream: ...
def uppercasebase(s: QTextStream) -> QTextStream: ...
def noforcepoint(s: QTextStream) -> QTextStream: ...
def noforcesign(s: QTextStream) -> QTextStream: ...
def noshowbase(s: QTextStream) -> QTextStream: ...
def forcepoint(s: QTextStream) -> QTextStream: ...
def forcesign(s: QTextStream) -> QTextStream: ...
def showbase(s: QTextStream) -> QTextStream: ...
def hex_(s: QTextStream) -> QTextStream: ...
def dec(s: QTextStream) -> QTextStream: ...
def oct_(s: QTextStream) -> QTextStream: ...
def bin_(s: QTextStream) -> QTextStream: ...
def Q_RETURN_ARG(type: typing.Any) -> QGenericReturnArgument: ...
def Q_ARG(type: typing.Any, data: typing.Any) -> QGenericArgument: ...
def QT_TRANSLATE_NOOP(a0: str, a1: str) -> str: ...
def QT_TR_NOOP_UTF8(a0: str) -> str: ...
def QT_TR_NOOP(a0: str) -> str: ...
def Q_FLAGS(*args: typing.Any) -> None: ...
def Q_FLAG(a0: typing.Union[type, enum.Enum]) -> None: ...
def Q_ENUMS(*args: typing.Any) -> None: ...
def Q_ENUM(a0: typing.Union[type, enum.Enum]) -> None: ...
def Q_CLASSINFO(name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
def qFloatDistance(a: float, b: float) -> int: ...
def qQNaN() -> float: ...
def qSNaN() -> float: ...
def qInf() -> float: ...
def qIsNaN(d: float) -> bool: ...
def qIsFinite(d: float) -> bool: ...
def qIsInf(d: float) -> bool: ...
def qFormatLogMessage(type: QtMsgType, context: QMessageLogContext, buf: typing.Optional[str]) -> str: ...
def qSetMessagePattern(messagePattern: typing.Optional[str]) -> None: ...
def qInstallMessageHandler(a0: typing.Optional[typing.Callable[[QtMsgType, QMessageLogContext, typing.Optional[str]], None]]) -> typing.Optional[typing.Callable[[QtMsgType, QMessageLogContext, typing.Optional[str]], None]]: ...
def qWarning(msg: typing.Optional[str]) -> None: ...
def qInfo(msg: typing.Optional[str]) -> None: ...
def qFatal(msg: typing.Optional[str]) -> None: ...
@typing.overload
def qErrnoWarning(code: int, msg: typing.Optional[str]) -> None: ...
@typing.overload
def qErrnoWarning(msg: typing.Optional[str]) -> None: ...
def qDebug(msg: typing.Optional[str]) -> None: ...
def qCritical(msg: typing.Optional[str]) -> None: ...
def pyqtRestoreInputHook() -> None: ...
def pyqtRemoveInputHook() -> None: ...
def qAddPreRoutine(routine: typing.Callable[[], None]) -> None: ...
def qRemovePostRoutine(a0: typing.Callable[..., None]) -> None: ...
def qAddPostRoutine(a0: typing.Callable[..., None]) -> None: ...
@typing.overload
def qChecksum(s: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
@typing.overload
def qChecksum(s: typing.Optional[PyQt5.sip.array[bytes]], standard: Qt.ChecksumType) -> int: ...
def qUncompress(data: typing.Union[QByteArray, bytes, bytearray]) -> QByteArray: ...
def qCompress(data: typing.Union[QByteArray, bytes, bytearray], compressionLevel: int = ...) -> QByteArray: ...
@typing.overload
def qEnvironmentVariable(varName: typing.Optional[str]) -> str: ...
@typing.overload
def qEnvironmentVariable(varName: typing.Optional[str], defaultValue: typing.Optional[str]) -> str: ...
def pyqtPickleProtocol() -> typing.Optional[int]: ...
def pyqtSetPickleProtocol(a0: typing.Optional[int]) -> None: ...
def qrand() -> int: ...
def qsrand(seed: int) -> None: ...
def qIsNull(d: float) -> bool: ...
def qFuzzyCompare(p1: float, p2: float) -> bool: ...
def qUnregisterResourceData(a0: int, a1: typing.Optional[bytes], a2: typing.Optional[bytes], a3: typing.Optional[bytes]) -> bool: ...
def qRegisterResourceData(a0: int, a1: typing.Optional[bytes], a2: typing.Optional[bytes], a3: typing.Optional[bytes]) -> bool: ...
def qSharedBuild() -> bool: ...
def qVersion() -> typing.Optional[str]: ...
def qRound64(d: float) -> int: ...
def qRound(d: float) -> int: ...
def qAbs(t: float) -> float: ...
