// qwebsocketserver.sip generated by MetaSIP
//
// This file is part of the QtWebSockets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_3_0 -)

class QWebSocketServer : public QObject
{
%TypeHeaderCode
#include <qwebsocketserver.h>
%End

public:
    enum SslMode
    {
%If (PyQt_SSL)
        SecureMode,
%End
        NonSecureMode,
    };

    QWebSocketServer(const QString &serverName, QWebSocketServer::SslMode secureMode, QObject *parent /TransferThis/ = 0);
    virtual ~QWebSocketServer();
    bool listen(const QHostAddress &address = QHostAddress::SpecialAddress::Any, quint16 port = 0);
    void close();
    bool isListening() const;
    void setMaxPendingConnections(int numConnections);
    int maxPendingConnections() const;
    quint16 serverPort() const;
    QHostAddress serverAddress() const;
    QWebSocketServer::SslMode secureMode() const;
    bool setSocketDescriptor(int socketDescriptor);
    int socketDescriptor() const;
    bool hasPendingConnections() const;
    virtual QWebSocket *nextPendingConnection() /Factory/;
    QWebSocketProtocol::CloseCode error() const;
    QString errorString() const;
    void pauseAccepting();
    void resumeAccepting();
    void setServerName(const QString &serverName);
    QString serverName() const;
    void setProxy(const QNetworkProxy &networkProxy);
    QNetworkProxy proxy() const;
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &sslConfiguration);
%End
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
    QList<QWebSocketProtocol::Version> supportedVersions() const;
%If (Qt_5_4_0 -)
    QUrl serverUrl() const;
%End
%If (Qt_5_9_0 -)
    void handleConnection(QTcpSocket *socket) const;
%End

signals:
    void acceptError(QAbstractSocket::SocketError socketError);
    void serverError(QWebSocketProtocol::CloseCode closeCode);
    void originAuthenticationRequired(QWebSocketCorsAuthenticator *pAuthenticator);
    void newConnection();
%If (PyQt_SSL)
    void peerVerifyError(const QSslError &error);
%End
%If (PyQt_SSL)
    void sslErrors(const QList<QSslError> &errors);
%End
    void closed();
%If (Qt_5_8_0 -)
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator *authenticator);
%End
%End

public:
%If (Qt_5_12_0 -)
    bool setNativeDescriptor(qintptr descriptor);
%End
%If (Qt_5_12_0 -)
    qintptr nativeDescriptor() const;
%End
%If (Qt_5_14_0 -)
    void setHandshakeTimeout(int msec);
%End
%If (Qt_5_14_0 -)
    int handshakeTimeoutMS() const;
%End
};

%End
