// qwebenginecookiestore.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_6_0 -)

class QWebEngineCookieStore : public QObject
{
%TypeHeaderCode
#include <qwebenginecookiestore.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QWebEngineCookieStore, &sipType_QWebEngineCookieStore, -1, 1},
    #if QT_VERSION >= 0x050d00
        {sipName_QWebEngineNotification, &sipType_QWebEngineNotification, -1, 2},
    #else
        {0, 0, -1, 2},
    #endif
        {sipName_QWebEngineUrlRequestInterceptor, &sipType_QWebEngineUrlRequestInterceptor, -1, 3},
        {sipName_QWebEngineUrlRequestJob, &sipType_QWebEngineUrlRequestJob, -1, 4},
        {sipName_QWebEngineUrlSchemeHandler, &sipType_QWebEngineUrlSchemeHandler, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    virtual ~QWebEngineCookieStore();
    void setCookie(const QNetworkCookie &cookie, const QUrl &origin = QUrl());
    void deleteCookie(const QNetworkCookie &cookie, const QUrl &origin = QUrl());
    void deleteSessionCookies();
    void deleteAllCookies();
    void loadAllCookies();

signals:
    void cookieAdded(const QNetworkCookie &cookie);
    void cookieRemoved(const QNetworkCookie &cookie);

private:
    explicit QWebEngineCookieStore(QObject *parent = 0);

public:
%If (QtWebEngine_5_11_0 -)

    struct FilterRequest
    {
%TypeHeaderCode
#include <qwebenginecookiestore.h>
%End

        QUrl firstPartyUrl;
        QUrl origin;
        bool thirdParty;
    };

%End
%If (QtWebEngine_5_11_0 -)
    void setCookieFilter(SIP_PYCALLABLE filterCallback /AllowNone,KeepReference,TypeHint="Callable[[FilterRequest], bool]"/ = 0);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->setCookieFilter([a0](const QWebEngineCookieStore::FilterRequest &arg) {
            int res_int = 1;
            
            if (a0)
            {
                SIP_BLOCK_THREADS
            
                PyObject *res;
        
                res = sipCallMethod(NULL, a0, "N",
                        new QWebEngineCookieStore::FilterRequest(arg),
                        sipType_QWebEngineCookieStore_FilterRequest, NULL);
            
                if (!res)
                {
                    res_int = -1;
                }
                else
                {
                    res_int = sipConvertToBool(res);
                
                    Py_DECREF(res);
                }
        
                if (res_int < 0)
                {
                    pyqt5_qtwebenginecore_err_print();
                    res_int = 0;
                }
        
                SIP_UNBLOCK_THREADS
            }
        
            return res_int;
        });
        
        Py_END_ALLOW_THREADS
%End

%End
};

%End

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt5_qtwebenginecore_err_print_t)();
extern pyqt5_qtwebenginecore_err_print_t pyqt5_qtwebenginecore_err_print;
%End

%ModuleCode
// Imports from QtCore.
pyqt5_qtwebenginecore_err_print_t pyqt5_qtwebenginecore_err_print;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qtwebenginecore_err_print = (pyqt5_qtwebenginecore_err_print_t)sipImportSymbol("pyqt5_err_print");
Q_ASSERT(pyqt5_qtwebenginecore_err_print);
%End
