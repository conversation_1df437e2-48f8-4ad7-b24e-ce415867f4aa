// qgraphicssvgitem.sip generated by MetaSIP
//
// This file is part of the QtSvg Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsSvgItem : QGraphicsObject
{
%TypeHeaderCode
#include <qgraphicssvgitem.h>
%End

%ConvertToSubClassCode
    if (sipCpp->type() == 13)
    {
        // Switch to the QObject convertor.
        *sipCppRet = static_cast<QGraphicsSvgItem *>(sipCpp);
        sipType = sipType_QObject;
    }
    else
        sipType = 0;
%End

public:
    QGraphicsSvgItem(QGraphicsItem *parent /TransferThis/ = 0);
    QGraphicsSvgItem(const QString &fileName, QGraphicsItem *parent /TransferThis/ = 0);
    void setSharedRenderer(QSvgRenderer *renderer /KeepReference/);
    QSvgRenderer *renderer() const;
    void setElementId(const QString &id);
    QString elementId() const;
    void setMaximumCacheSize(const QSize &size);
    QSize maximumCacheSize() const;
    virtual QRectF boundingRect() const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    virtual int type() const;
};

%ModuleCode
// This is needed by the %ConvertToSubClassCode.
#include <qgraphicssvgitem.h>
%End
