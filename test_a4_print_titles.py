#!/usr/bin/env python3
"""
اختبار عناوين طباعة A4 للفواتير والمرتجعات
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🖨️ اختبار عناوين طباعة A4 للفواتير والمرتجعات...")
    print("=" * 80)
    print("🔧 التحسينات المطبقة على طباعة A4:")
    print("")
    print("   ✅ عناوين ديناميكية للفواتير:")
    print("     • فاتورة مبيعات (للفواتير العادية)")
    print("     • فاتورة مرتجع مبيعات (للمرتجعات)")
    print("     • فاتورة مرتجع مشتريات (لمرتجع المشتريات)")
    print("")
    print("   ✅ أرقام مميزة:")
    print("     • رقم الفاتورة: #000001 (للفواتير العادية)")
    print("     • رقم المرتجع: R000001 (للمرتجعات)")
    print("     • الفاتورة الأصلية: #000001 (للمرتجعات)")
    print("")
    print("   ✅ ألوان مميزة:")
    print("     • أسود للفواتير العادية")
    print("     • أحمر لمرتجع المبيعات")
    print("     • بنفسجي لمرتجع المشتريات")
    print("")
    print("   ✅ تسمية صحيحة:")
    print("     • العميل (للمبيعات ومرتجعاتها)")
    print("     • المورد (لمرتجع المشتريات)")
    print("")
    print("   ✅ تصميم متسق:")
    print("     • نفس التصميم الاحترافي")
    print("     • معلومات واضحة ومنظمة")
    print("     • سهولة في القراءة والفهم")
    print("=" * 80)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 تشغيل النظام...")
        
        # اختبار الواجهة الرئيسية
        print("🖥️ تشغيل الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.engine = engine
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار عناوين طباعة A4:")
        print("")
        print("   📋 طباعة فاتورة مبيعات عادية:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض الفواتير المحفوظة'")
        print("     2️⃣ اختر أي فاتورة واضغط 'طباعة'")
        print("     3️⃣ اختر 'A4' كنوع الطابعة")
        print("     4️⃣ لاحظ العنوان:")
        print("        • 🧾 فاتورة مبيعات (بالأسود)")
        print("        • رقم الفاتورة: #000001")
        print("        • العميل: [اسم العميل]")
        print("")
        print("   🔄 طباعة فاتورة مرتجع مبيعات:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض فواتير المرتجعات'")
        print("     2️⃣ اختر أي مرتجع واضغط '🖨️'")
        print("     3️⃣ اختر 'A4' كنوع الطابعة")
        print("     4️⃣ لاحظ العنوان:")
        print("        • 🧾 فاتورة مرتجع مبيعات (بالأحمر)")
        print("        • رقم المرتجع: R000001")
        print("        • الفاتورة الأصلية: #000001 (بالأزرق)")
        print("        • العميل: [اسم العميل]")
        print("")
        print("   🔄 طباعة فاتورة مرتجع مشتريات:")
        print("     1️⃣ اذهب لقائمة 'المشتريات' ← 'مرتجع المشتريات'")
        print("     2️⃣ أنشئ مرتجع جديد أو اذهب لعرض المرتجعات")
        print("     3️⃣ اطبع المرتجع واختر 'A4'")
        print("     4️⃣ لاحظ العنوان:")
        print("        • 🧾 فاتورة مرتجع مشتريات (بالبنفسجي)")
        print("        • رقم المرتجع: R000001")
        print("        • الفاتورة الأصلية: #000001")
        print("        • المورد: [اسم المورد]")
        print("")
        print("   🆕 إنشاء مرتجع جديد وطباعته:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'مرتجع المبيعات'")
        print("     2️⃣ ابحث عن فاتورة واختر منتجات")
        print("     3️⃣ اضغط 'حفظ وطباعة'")
        print("     4️⃣ اختر 'A4' واطبع")
        print("     5️⃣ لاحظ العنوان الجديد المميز")
        print("")
        print("   🔍 ما تبحث عنه في طباعة A4:")
        print("     ✅ عنوان واضح ومميز:")
        print("        • 'فاتورة مبيعات' للفواتير العادية")
        print("        • 'فاتورة مرتجع مبيعات' لمرتجع المبيعات")
        print("        • 'فاتورة مرتجع مشتريات' لمرتجع المشتريات")
        print("     ✅ أرقام واضحة:")
        print("        • رقم الفاتورة: #000001")
        print("        • رقم المرتجع: R000001")
        print("        • الفاتورة الأصلية: #000001")
        print("     ✅ ألوان مميزة:")
        print("        • أسود للفواتير العادية")
        print("        • أحمر لمرتجع المبيعات")
        print("        • بنفسجي لمرتجع المشتريات")
        print("        • أزرق للفاتورة الأصلية")
        print("     ✅ تسمية صحيحة:")
        print("        • 'العميل' للمبيعات")
        print("        • 'المورد' للمشتريات")
        print("")
        print("   💡 مقارنة مع الرول:")
        print("     🟢 الآن A4 والرول متطابقان:")
        print("        • نفس العناوين الديناميكية")
        print("        • نفس الأرقام المميزة")
        print("        • نفس الألوان والتسميات")
        print("        • تجربة موحدة ومتسقة")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 80)
    print("📊 ملخص التحسينات:")
    print("")
    print("   🔧 ما تم إضافته لطباعة A4:")
    print("     ✅ عناوين ديناميكية حسب نوع الفاتورة")
    print("     ✅ ألوان مميزة للمرتجعات")
    print("     ✅ رقم المرتجع المميز R000001")
    print("     ✅ رقم الفاتورة الأصلية للمرتجعات")
    print("     ✅ تسمية صحيحة للعميل/المورد")
    print("")
    print("   🎨 النتيجة النهائية:")
    print("     • طباعة A4 والرول متطابقة تماماً")
    print("     • عناوين واضحة ومميزة لكل نوع")
    print("     • أرقام مميزة وواضحة")
    print("     • ألوان تساعد في التمييز")
    print("     • تجربة موحدة ومتسقة")
    print("")
    print("   📋 أنواع الفواتير المدعومة:")
    print("     ✅ فاتورة مبيعات (أسود)")
    print("     ✅ فاتورة مرتجع مبيعات (أحمر)")
    print("     ✅ فاتورة مرتجع مشتريات (بنفسجي)")
    print("     ✅ جميع الأنواع بتصميم احترافي")
    print("")
    print("🎉 التحديث مكتمل ومطبق بنجاح!")
    print("   📱 جرب الطباعة الآن ولاحظ العناوين الجديدة")
    print("   🖨️ تجربة موحدة بين A4 والرول")
    print("   🎯 وضوح أكبر في تمييز أنواع الفواتير")

if __name__ == "__main__":
    main()
