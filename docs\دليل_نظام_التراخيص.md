# 🔐 دليل نظام التراخيص والأكواد

## 🎯 نظرة عامة

تم تطوير نظام ترخيص متكامل وآمن يضمن حماية البرنامج ويوفر تجديد سنوي للاشتراك باستخدام **أكواد التفعيل**.

---

## 🏗️ مكونات النظام

### **📁 الملفات الأساسية:**

#### **1. license_manager.py**
- **الوظيفة:** إدارة التراخيص والتشفير
- **المحتوى:** 
  - فحص صحة الترخيص
  - توليد معرفات الجهاز والعميل
  - التحقق من أكواد التجديد
  - حفظ وتحميل بيانات الترخيص مشفرة

#### **2. license_ui.py**
- **الوظيفة:** واجهة المستخدم للترخيص
- **المحتوى:**
  - نافذة انتهاء الصلاحية
  - نافذة إدخال كود التجديد
  - تحذيرات قبل انتهاء الترخيص
  - وسائل التواصل مع الدعم

#### **3. code_generator_ui.py**
- **الوظيفة:** مولد الأكواد (للمطور فقط)
- **المحتوى:**
  - واجهة توليد أكواد التجديد
  - إرسال الأكواد عبر واتساب
  - حفظ الأكواد في ملفات
  - إدارة بيانات العملاء

#### **4. test_license_system.py**
- **الوظيفة:** اختبار النظام
- **المحتوى:**
  - تطبيق تجريبي لاختبار الترخيص
  - إنشاء تراخيص تجريبية
  - فحص حالة الترخيص
  - محاكاة التطبيق الرئيسي

---

## 🔧 التسطيب والإعداد

### **📦 تسطيب المكتبات المطلوبة:**
```bash
pip install -r requirements_license.txt
```

### **📋 المكتبات المطلوبة:**
- **cryptography** - للتشفير والأمان
- **pyperclip** - لنسخ النصوص إلى الحافظة
- **tkinter** - واجهة المستخدم (مدمجة مع Python)

---

## 🚀 كيفية الاستخدام

### **👨‍💻 للمطور:**

#### **1. توليد كود تجديد:**
```bash
python code_generator_ui.py
```

**خطوات التوليد:**
1. أدخل **كود العميل** (يحصل عليه من العميل)
2. أدخل **رقم الجهاز** (يحصل عليه من العميل)
3. اختر **سنة التجديد** (افتراضي: السنة القادمة)
4. اضغط **"توليد الكود"**
5. انسخ الكود أو أرسله عبر واتساب

#### **2. إدماج النظام في البرنامج:**
```python
from license_ui import check_license_and_show_dialog

# في بداية البرنامج الرئيسي
if not check_license_and_show_dialog():
    print("لم يتم تفعيل الترخيص - إغلاق البرنامج")
    exit()

# باقي كود البرنامج...
```

### **👤 للعميل:**

#### **1. عند انتهاء الصلاحية:**
- يظهر تحذير قبل انتهاء الترخيص بـ 30، 15، 7، 3 أيام
- عند الانتهاء يظهر نافذة انتهاء الصلاحية
- العميل يتواصل مع الدعم ويرسل:
  - **كود العميل**
  - **رقم الجهاز**

#### **2. بعد الدفع:**
- العميل يحصل على **كود التجديد**
- يدخل الكود في نافذة التجديد
- يضغط **"تفعيل"**
- البرنامج يعمل لسنة إضافية

---

## 🔐 آلية الحماية

### **🛡️ مستويات الأمان:**

#### **1. ربط بالجهاز:**
- كل ترخيص مرتبط بجهاز محدد
- يستخدم معرف فريد للجهاز (MAC Address + معلومات النظام)
- لا يعمل الترخيص على جهاز آخر

#### **2. ربط بالعميل:**
- كل عميل له كود فريد
- الكود مولد من معلومات الجهاز
- لا يمكن استخدام كود عميل آخر

#### **3. التشفير:**
- جميع بيانات الترخيص مشفرة
- يستخدم مفتاح تشفير مرتبط بالجهاز
- لا يمكن قراءة أو تعديل الملف يدوياً

#### **4. تاريخ الانتهاء:**
- كل ترخيص له تاريخ انتهاء محدد
- فحص تلقائي عند كل تشغيل
- لا يمكن تعديل التاريخ

---

## 📊 تنسيق كود التجديد

### **🔑 تركيب الكود:**
```
RENEW-2025-ABC123-XYZ789-DEF456
  │     │     │      │      │
  │     │     │      │      └── رقم الجهاز (6 أحرف)
  │     │     │      └────────── كود عشوائي للأمان (6 أحرف)
  │     │     └───────────────── كود العميل (6 أحرف)
  │     └─────────────────────── سنة انتهاء الترخيص
  └───────────────────────────── نوع العملية (RENEW)
```

### **✅ مثال صحيح:**
```
RENEW-2025-A1B2C3-X7Y8Z9-D4E5F6
```

### **❌ أمثلة خاطئة:**
```
RENEW-2024-ABC123-XYZ789-DEF456  # سنة ماضية
TRIAL-2025-ABC123-XYZ789-DEF456  # نوع خاطئ
RENEW-2025-ABC123-XYZ789         # ناقص رقم الجهاز
```

---

## 🔄 سير العمل الكامل

### **📋 خطوات التجديد:**

#### **1. العميل يتواصل:**
```
💬 "انتهت صلاحية البرنامج
🔑 كود العميل: A1B2C3D4E
💻 رقم الجهاز: F6G7H8I9J
💰 أريد التجديد لسنة"
```

#### **2. المطور يولد الكود:**
- يفتح `code_generator_ui.py`
- يدخل بيانات العميل
- يولد الكود: `RENEW-2025-A1B2C3-X7Y8Z9-F6G7H8`

#### **3. إرسال الكود:**
```
💬 "تم استلام الدفعة ✅
🔑 كود التجديد: RENEW-2025-A1B2C3-X7Y8Z9-F6G7H8
📅 صالح حتى: 31/12/2025
📝 ادخل الكود في البرنامج"
```

#### **4. العميل يفعل:**
- يفتح البرنامج
- يدخل كود التجديد
- يضغط "تفعيل"
- ✅ البرنامج يعمل لسنة كاملة

---

## 🧪 الاختبار والتطوير

### **🔬 تشغيل الاختبارات:**
```bash
# اختبار النظام
python test_license_system.py test

# اختبار التطبيق الرئيسي
python test_license_system.py
```

### **🛠️ أدوات التطوير:**

#### **1. إنشاء ترخيص تجريبي:**
```python
from license_manager import LicenseManager

license_manager = LicenseManager()
license_manager.create_initial_license(days=30)  # 30 يوم
```

#### **2. فحص حالة الترخيص:**
```python
status = license_manager.check_license()
print(status)
```

#### **3. توليد كود تجديد:**
```python
from license_manager import LicenseCodeGenerator

code = LicenseCodeGenerator.generate_renewal_code(
    customer_code="A1B2C3D4E",
    machine_id="F6G7H8I9J",
    year=2025
)
print(code)
```

---

## 📱 التكامل مع التطبيق الرئيسي

### **🔗 إضافة فحص الترخيص:**

#### **في بداية main.py:**
```python
from license_ui import check_license_and_show_dialog

def main():
    # فحص الترخيص أولاً
    if not check_license_and_show_dialog():
        print("لم يتم تفعيل الترخيص")
        return
    
    # تشغيل البرنامج الرئيسي
    app = MainApplication()
    app.run()

if __name__ == "__main__":
    main()
```

#### **فحص دوري أثناء التشغيل:**
```python
from license_manager import LicenseManager

def periodic_license_check():
    license_manager = LicenseManager()
    status = license_manager.check_license()
    
    if not status["valid"]:
        # إغلاق البرنامج أو عرض تحذير
        show_license_expired_dialog()
        return False
    
    return True
```

---

## 💡 نصائح مهمة

### **🔒 للأمان:**
1. **احتفظ بنسخة آمنة** من مولد الأكواد
2. **لا تشارك** ملف `code_generator_ui.py` مع العملاء
3. **احفظ أكواد العملاء** في ملف آمن
4. **راقب استخدام الأكواد** لمنع التلاعب

### **📞 لخدمة العملاء:**
1. **اطلب دائماً** كود العميل ورقم الجهاز
2. **تأكد من الدفع** قبل إرسال الكود
3. **احفظ سجل** بجميع التجديدات
4. **قدم دعم سريع** للعملاء

### **🚀 للتطوير:**
1. **اختبر النظام** على أجهزة مختلفة
2. **تأكد من التشفير** يعمل بشكل صحيح
3. **راقب الأداء** وسرعة الاستجابة
4. **حدّث النظام** بانتظام

---

## 🎉 الخلاصة

**تم إنشاء نظام ترخيص متكامل وآمن يوفر:**

- ✅ **حماية قوية** ضد القرصنة
- ✅ **سهولة في الاستخدام** للعملاء
- ✅ **إدارة مرنة** للمطور
- ✅ **دخل مستدام** من التجديدات السنوية
- ✅ **واجهة احترافية** وسهلة
- ✅ **نظام دعم متكامل**

**🚀 النظام جاهز للاستخدام والتطبيق!**
