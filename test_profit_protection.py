#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام حماية الأرباح - منع بيع المنتجات بأسعار أقل من سعر الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("🛡️ اختبار نظام حماية الأرباح")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("🔒 نظام حماية الأرباح الجديد:")
    print("")
    print("   ⚠️ التحقق من الربحية:")
    print("     • منع بيع المنتجات بسعر أقل من سعر الشراء")
    print("     • تنبيهات واضحة ومفصلة")
    print("     • حماية شاملة في جميع الصفحات")
    print("")
    print("   📍 أماكن التطبيق:")
    print("     1️⃣ فاتورة المبيعات - تعديل السعر في الجدول")
    print("     2️⃣ فاتورة المبيعات - نافذة تحديث السعر في المخزون")
    print("     3️⃣ فاتورة المبيعات - نافذة اختيار المنتج")
    print("     4️⃣ إدارة المخزون - إضافة/تعديل منتج")
    print("")
    print("   🎨 تصميم التنبيه:")
    print("     • أيقونة تحذير ⚠️")
    print("     • عنوان واضح: 'سعر غير مربح'")
    print("     • تفاصيل المنتج وسعر الشراء")
    print("     • السعر المطلوب والحد الأدنى المسموح")
    print("     • رسالة توضيحية عن أهمية الربحية")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار نظام حماية الأرباح:")
        print("")
        print("   📋 اختبار في فاتورة المبيعات:")
        print("     1️⃣ اذهب لصفحة 'فاتورة جديدة'")
        print("     2️⃣ أضف منتج للفاتورة")
        print("     3️⃣ حاول تغيير السعر لقيمة أقل من سعر الشراء")
        print("     4️⃣ ستظهر رسالة تحذير تمنع العملية")
        print("")
        print("   🏪 اختبار في إدارة المخزون:")
        print("     1️⃣ اذهب لقسم 'المخزون'")
        print("     2️⃣ اضغط 'إضافة منتج جديد'")
        print("     3️⃣ أدخل سعر شراء (مثلاً: 100 ريال)")
        print("     4️⃣ حاول إدخال سعر بيع أقل (مثلاً: 90 ريال)")
        print("     5️⃣ اضغط 'حفظ' - ستظهر رسالة تحذير")
        print("")
        print("   🔧 اختبار تحديث السعر في المخزون:")
        print("     1️⃣ في فاتورة المبيعات، غير سعر منتج")
        print("     2️⃣ اختر 'نعم، حدث السعر في المخزون'")
        print("     3️⃣ إذا كان السعر غير مربح، ستظهر رسالة تحذير")
        print("")
        print("   📊 ما تتوقع رؤيته:")
        print("     ⚠️ رسالة تحذير تحتوي على:")
        print("        • اسم المنتج")
        print("        • سعر الشراء الحالي")
        print("        • السعر المطلوب (غير المربح)")
        print("        • الحد الأدنى المسموح")
        print("        • رسالة توضيحية عن الربحية")
        print("")
        print("   ✅ النتيجة المتوقعة:")
        print("     • منع حفظ/تحديث السعر غير المربح")
        print("     • الحفاظ على السعر القديم")
        print("     • حماية الأرباح من الأخطاء")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 50)
    print("📊 ملخص نظام حماية الأرباح:")
    print("")
    print("   ✅ ما تم تطبيقه:")
    print("     • تحقق شامل من الربحية")
    print("     • تنبيهات واضحة ومفصلة")
    print("     • حماية في جميع نقاط الإدخال")
    print("     • منع الأخطاء التي تؤثر على الأرباح")
    print("")
    print("   🎯 الفوائد:")
    print("     • حماية الأرباح من الأخطاء")
    print("     • تجنب البيع بخسارة")
    print("     • تنبيهات فورية للمستخدم")
    print("     • ضمان الاستدامة المالية")
    print("")
    print("   💡 نصائح:")
    print("     • تأكد من إدخال أسعار الشراء الصحيحة")
    print("     • راجع الأسعار دورياً")
    print("     • استخدم هامش ربح مناسب")
    print("     • انتبه للتنبيهات وتعامل معها بجدية")
    print("")
    print("🛡️ نظام حماية الأرباح نشط ويعمل!")

if __name__ == "__main__":
    main()
