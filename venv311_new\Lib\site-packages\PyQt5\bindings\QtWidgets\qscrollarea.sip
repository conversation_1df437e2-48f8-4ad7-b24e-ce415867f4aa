// qscrollarea.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScrollArea : QAbstractScrollArea
{
%TypeHeaderCode
#include <qscrollarea.h>
%End

public:
    explicit QScrollArea(QWidget *parent /TransferThis/ = 0);
    virtual ~QScrollArea();
    QWidget *widget() const;
    void setWidget(QWidget *w /Transfer/);
    QWidget *takeWidget() /TransferBack/;
    bool widgetResizable() const;
    void setWidgetResizable(bool resizable);
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment);
    virtual QSize sizeHint() const;
    virtual bool focusNextPrevChild(bool next);
    void ensureVisible(int x, int y, int xMargin = 50, int yMargin = 50);
    void ensureWidgetVisible(QWidget *childWidget, int xMargin = 50, int yMargin = 50);

protected:
    virtual bool event(QEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void scrollContentsBy(int dx, int dy);
%If (Qt_5_2_0 -)
    virtual QSize viewportSizeHint() const;
%End
};
