#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إعداد الشركة
Test Company Setup System
"""

import json
import os

def create_test_company_settings():
    """إنشاء إعدادات شركة تجريبية"""
    
    test_settings = {
        'company_name': 'شركة الأحلام للتجارة',
        'owner_name': 'أحمد محمد علي',
        'phone': '01234567890',
        'email': '<EMAIL>',
        'address': 'شارع النيل، المعادي، القاهرة، مصر',
        'logo_path': None,
        'setup_completed': True
    }
    
    print("🏢 إنشاء إعدادات شركة تجريبية...")
    print(f"📋 اسم الشركة: {test_settings['company_name']}")
    print(f"👤 المالك: {test_settings['owner_name']}")
    print(f"📞 الهاتف: {test_settings['phone']}")
    print(f"📧 الإيميل: {test_settings['email']}")
    print(f"📍 العنوان: {test_settings['address']}")
    
    # حفظ الإعدادات
    try:
        with open('company_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=4)
        
        print("✅ تم حفظ إعدادات الشركة التجريبية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الإعدادات: {e}")
        return False

def test_company_settings_loading():
    """اختبار تحميل إعدادات الشركة"""
    print("\n🔍 اختبار تحميل إعدادات الشركة...")
    
    try:
        from gui.initial_setup import load_company_settings
        
        settings = load_company_settings()
        
        print("📊 الإعدادات المحملة:")
        for key, value in settings.items():
            print(f"   • {key}: {value}")
        
        print("✅ تم تحميل الإعدادات بنجاح!")
        return settings
        
    except Exception as e:
        print(f"❌ خطأ في تحميل الإعدادات: {e}")
        return None

def test_initial_setup_check():
    """اختبار فحص الحاجة للإعداد الأولي"""
    print("\n🔍 اختبار فحص الحاجة للإعداد الأولي...")
    
    try:
        from gui.initial_setup import check_initial_setup_needed
        
        needs_setup = check_initial_setup_needed()
        
        if needs_setup:
            print("⚠️ يحتاج النظام للإعداد الأولي")
        else:
            print("✅ تم إكمال الإعداد الأولي مسبقاً")
        
        return needs_setup
        
    except Exception as e:
        print(f"❌ خطأ في فحص الإعداد الأولي: {e}")
        return True

def show_company_info_summary():
    """عرض ملخص معلومات الشركة"""
    print("\n" + "=" * 60)
    print("📋 ملخص نظام إعداد الشركة")
    print("=" * 60)
    
    # تحميل الإعدادات
    settings = test_company_settings_loading()
    
    if settings:
        print(f"\n🏢 معلومات الشركة:")
        print(f"   📊 اسم الشركة: {settings.get('company_name', 'غير محدد')}")
        print(f"   👤 المالك/المدير: {settings.get('owner_name', 'غير محدد')}")
        print(f"   📞 رقم الهاتف: {settings.get('phone', 'غير محدد')}")
        print(f"   📧 البريد الإلكتروني: {settings.get('email', 'غير محدد')}")
        print(f"   📍 العنوان: {settings.get('address', 'غير محدد')}")
        
        logo_path = settings.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            print(f"   🎨 الشعار: {logo_path} ✅")
        else:
            print(f"   🎨 الشعار: غير محدد")
        
        setup_completed = settings.get('setup_completed', False)
        print(f"   ✅ حالة الإعداد: {'مكتمل' if setup_completed else 'غير مكتمل'}")
    
    print(f"\n💡 كيفية الاستخدام:")
    print(f"   1. عند التشغيل الأول → تظهر نافذة الإعداد الأولي")
    print(f"   2. أدخل معلومات الشركة واللوجو")
    print(f"   3. اضغط 'حفظ وبدء الاستخدام'")
    print(f"   4. ستظهر معلومات الشركة في الواجهة الرئيسية")
    print(f"   5. يمكن تعديل الإعدادات من قائمة 'إعدادات الشركة'")

def test_logo_handling():
    """اختبار التعامل مع الشعارات"""
    print("\n🎨 اختبار التعامل مع الشعارات...")
    
    # فحص مجلد assets
    assets_dir = "assets"
    if not os.path.exists(assets_dir):
        print(f"📁 إنشاء مجلد {assets_dir}...")
        os.makedirs(assets_dir)
    
    # فحص الشعارات الموجودة
    logo_files = []
    for file in os.listdir(assets_dir):
        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
            logo_files.append(file)
    
    if logo_files:
        print(f"🎨 الشعارات الموجودة في {assets_dir}:")
        for logo in logo_files:
            file_path = os.path.join(assets_dir, logo)
            file_size = os.path.getsize(file_path)
            print(f"   • {logo} ({file_size} بايت)")
    else:
        print(f"⚠️ لا توجد شعارات في مجلد {assets_dir}")
        print(f"💡 يمكنك إضافة شعار الشركة من نافذة الإعداد")

def cleanup_test_data():
    """تنظيف البيانات التجريبية"""
    print("\n🧹 تنظيف البيانات التجريبية...")
    
    files_to_remove = [
        'company_settings.json'
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"   ✅ تم حذف {file}")
            except Exception as e:
                print(f"   ❌ خطأ في حذف {file}: {e}")
        else:
            print(f"   ℹ️ {file} غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام إعداد الشركة")
    print("=" * 60)
    
    # اختبار فحص الحاجة للإعداد الأولي
    needs_setup = test_initial_setup_check()
    
    if needs_setup:
        print("\n📝 إنشاء إعدادات تجريبية...")
        create_test_company_settings()
    
    # اختبار تحميل الإعدادات
    test_company_settings_loading()
    
    # اختبار التعامل مع الشعارات
    test_logo_handling()
    
    # عرض الملخص
    show_company_info_summary()
    
    print("\n" + "=" * 60)
    print("🎉 انتهى اختبار نظام إعداد الشركة!")
    print("=" * 60)
    
    # سؤال المستخدم عن التنظيف
    try:
        response = input("\n❓ هل تريد حذف البيانات التجريبية؟ (y/n): ").lower().strip()
        if response in ['y', 'yes', 'نعم']:
            cleanup_test_data()
        else:
            print("ℹ️ تم الاحتفاظ بالبيانات التجريبية")
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء الاختبار")

if __name__ == "__main__":
    main()
