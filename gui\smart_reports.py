from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import (SimpleDocTemplate, Table, TableStyle, Paragraph,
                               Spacer, Image, PageBreak)
from reportlab.graphics.shapes import Drawing, Line
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
import arabic_reshaper
from bidi.algorithm import get_display
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.models import Transaction, TransactionItem, Product, Customer, TransactionType
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import io
import numpy as np

class SmartReportGenerator:
    def __init__(self, engine):
        self.engine = engine
        self.styles = getSampleStyleSheet()
        try:
            # إضافة نمط للنص العربي
            self.styles.add(ParagraphStyle(
                name='Arabic',
                fontName='Arial Unicode MS',  # استخدام خط يدعم العربية
                fontSize=12,
                leading=16,
                alignment=1,  # توسيط
            ))
            self.styles.add(ParagraphStyle(
                name='ArabicTitle',
                fontName='Arial Unicode MS',  # استخدام خط يدعم العربية
                fontSize=16,
                leading=20,
                alignment=1,
                spaceAfter=20,
                textColor=colors.HexColor('#2C3E50')
            ))
        except Exception as e:
            print(f"Error initializing styles: {e}")
            # استخدام النمط الافتراضي في حالة الخطأ
            self.styles.add(ParagraphStyle(
                name='Arabic',
                fontName='Helvetica',  # خط افتراضي
                fontSize=12,
                leading=16,
                alignment=1,
            ))
            self.styles.add(ParagraphStyle(
                name='ArabicTitle',
                fontName='Helvetica-Bold',  # خط افتراضي
                fontSize=16,
                leading=20,
                alignment=1,
                spaceAfter=20,
            ))

    def arabic_text(self, text):
        """معالجة النص العربي"""
        return get_display(arabic_reshaper.reshape(str(text)))

    def create_header(self, title):
        """إنشاء ترويسة التقرير"""
        elements = []
        
        # شعار الشركة (إذا وجد)
        logo_path = os.path.join('images', 'logo.png')
        if os.path.exists(logo_path):
            img = Image(logo_path, width=2*cm, height=2*cm)
            elements.append(img)
        
        # عنوان التقرير
        elements.append(Paragraph(self.arabic_text(title), self.styles['ArabicTitle']))
        elements.append(Spacer(1, 20))
        
        # تاريخ التقرير
        date_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"
        elements.append(Paragraph(self.arabic_text(date_text), self.styles['Arabic']))
        elements.append(Spacer(1, 20))
        
        return elements

    def create_sales_summary(self, start_date, end_date):
        """إنشاء ملخص المبيعات"""
        elements = []
        
        with Session(self.engine) as session:
            # إجمالي المبيعات
            total_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).scalar() or 0
            
            # عدد الفواتير
            invoice_count = session.query(func.count(Transaction.id)).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).scalar() or 0
            
            # متوسط قيمة الفاتورة
            avg_invoice = total_sales / invoice_count if invoice_count > 0 else 0
            
            # المنتجات الأكثر مبيعاً
            top_products = session.query(
                Product.name,
                func.sum(TransactionItem.quantity).label('total_quantity'),
                func.sum(TransactionItem.quantity * TransactionItem.price).label('total_amount')
            ).join(
                TransactionItem
            ).join(
                Transaction
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).group_by(
                Product.id
            ).order_by(
                func.sum(TransactionItem.quantity).desc()
            ).limit(5).all()
            
            # إنشاء جدول الملخص
            summary_data = [
                ['المؤشر', 'القيمة'],
                ['إجمالي المبيعات', f"{total_sales:,.2f}"],
                ['عدد الفواتير', str(invoice_count)],
                ['متوسط قيمة الفاتورة', f"{avg_invoice:,.2f}"]
            ]
            
            summary_table = Table(summary_data, colWidths=[8*cm, 8*cm])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            elements.append(Paragraph(self.arabic_text("ملخص المبيعات"), self.styles['ArabicTitle']))
            elements.append(summary_table)
            elements.append(Spacer(1, 20))
            
            # إضافة مخطط المبيعات
            sales_chart = self.create_sales_chart(session, start_date, end_date)
            elements.append(sales_chart)
            elements.append(Spacer(1, 20))
            
            # جدول المنتجات الأكثر مبيعاً
            top_products_data = [['المنتج', 'الكمية', 'القيمة']]
            for product in top_products:
                top_products_data.append([
                    self.arabic_text(product.name),
                    str(product.total_quantity),
                    f"{product.total_amount:,.2f}"
                ])
            
            top_products_table = Table(top_products_data, colWidths=[8*cm, 4*cm, 4*cm])
            top_products_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            elements.append(Paragraph(self.arabic_text("المنتجات الأكثر مبيعاً"), self.styles['ArabicTitle']))
            elements.append(top_products_table)
        
        return elements

    def create_sales_chart(self, session, start_date, end_date):
        """إنشاء مخطط المبيعات"""
        # جمع بيانات المبيعات
        sales_data = session.query(
            func.date(Transaction.date),
            func.sum(Transaction.total_amount)
        ).filter(
            Transaction.type == TransactionType.SALE,
            Transaction.date.between(start_date, end_date)
        ).group_by(
            func.date(Transaction.date)
        ).all()
        
        dates = [d.strftime('%Y-%m-%d') for d, _ in sales_data]
        amounts = [float(amount) for _, amount in sales_data]
        
        # إنشاء مخطط خطي
        drawing = Drawing(400, 200)
        
        chart = HorizontalLineChart()
        chart.x = 50
        chart.y = 50
        chart.width = 300
        chart.height = 150
        
        chart.data = [amounts]
        chart.categoryAxis.categoryNames = dates
        chart.categoryAxis.labels.boxAnchor = 'ne'
        chart.categoryAxis.labels.angle = 30
        
        chart.valueAxis.valueMin = 0
        chart.valueAxis.valueMax = max(amounts) * 1.1
        chart.valueAxis.valueStep = max(amounts) / 5
        
        drawing.add(chart)
        
        return drawing

    def create_customer_analysis(self, start_date, end_date):
        """تحليل العملاء"""
        elements = []
        
        with Session(self.engine) as session:
            # تصنيف العملاء حسب المبيعات
            customer_sales = session.query(
                Customer.name,
                func.sum(Transaction.total_amount).label('total_sales'),
                func.count(Transaction.id).label('transaction_count')
            ).join(
                Transaction
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).group_by(
                Customer.id
            ).order_by(
                func.sum(Transaction.total_amount).desc()
            ).all()
            
            # إنشاء جدول تحليل العملاء
            customer_data = [['العميل', 'إجمالي المبيعات', 'عدد المعاملات']]
            for customer in customer_sales:
                customer_data.append([
                    self.arabic_text(customer.name),
                    f"{customer.total_sales:,.2f}",
                    str(customer.transaction_count)
                ])
            
            customer_table = Table(customer_data, colWidths=[8*cm, 6*cm, 4*cm])
            customer_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            elements.append(Paragraph(self.arabic_text("تحليل العملاء"), self.styles['ArabicTitle']))
            elements.append(customer_table)
            elements.append(Spacer(1, 20))
            
            # إضافة مخطط توزيع العملاء
            if customer_sales:
                customer_chart = self.create_customer_chart(customer_sales)
                elements.append(customer_chart)
        
        return elements

    def create_customer_chart(self, customer_sales):
        """إنشاء مخطط دائري لتوزيع العملاء"""
        drawing = Drawing(400, 200)
        
        pie = Pie()
        pie.x = 150
        pie.y = 50
        pie.width = 100
        pie.height = 100
        
        # تحديد أكبر 5 عملاء
        top_customers = customer_sales[:5]
        
        pie.data = [float(c.total_sales) for c in top_customers]
        pie.labels = [self.arabic_text(c.name) for c in top_customers]
        
        drawing.add(pie)
        
        return drawing

    def create_inventory_analysis(self):
        """تحليل المخزون"""
        elements = []
        
        with Session(self.engine) as session:
            # تحليل المخزون
            inventory_data = session.query(
                Product.name,
                Product.quantity,
                Product.min_quantity,
                (Product.quantity * Product.purchase_price).label('stock_value')
            ).order_by(
                Product.quantity
            ).all()
            
            # إنشاء جدول المخزون
            inventory_table_data = [['المنتج', 'الكمية الحالية', 'الحد الأدنى', 'القيمة']]
            for product in inventory_data:
                inventory_table_data.append([
                    self.arabic_text(product.name),
                    str(product.quantity),
                    str(product.min_quantity),
                    f"{product.stock_value:,.0f}"
                ])
            
            inventory_table = Table(inventory_table_data, colWidths=[7*cm, 4*cm, 4*cm, 4*cm])
            inventory_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            elements.append(Paragraph(self.arabic_text("تحليل المخزون"), self.styles['ArabicTitle']))
            elements.append(inventory_table)
            
            # إضافة تحذيرات المخزون
            low_stock = [p for p in inventory_data if p.quantity <= p.min_quantity]
            if low_stock:
                elements.append(Spacer(1, 20))
                elements.append(Paragraph(
                    self.arabic_text("تنبيهات المخزون المنخفض"),
                    self.styles['Arabic']
                ))
                
                warnings = []
                for product in low_stock:
                    warnings.append(
                        f"المنتج {product.name} منخفض المخزون "
                        f"(الكمية الحالية: {product.quantity}, "
                        f"الحد الأدنى: {product.min_quantity})"
                    )
                
                for warning in warnings:
                    elements.append(Paragraph(
                        self.arabic_text(warning),
                        self.styles['Arabic']
                    ))
        
        return elements

    def generate_report(self, report_type, start_date, end_date, output_path):
        """إنشاء التقرير الكامل"""
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=30,
            leftMargin=30,
            topMargin=30,
            bottomMargin=30
        )
        
        elements = []
        
        # إضافة الترويسة
        elements.extend(self.create_header(f"تقرير {report_type}"))
        
        # إضافة محتوى التقرير حسب النوع
        if report_type == "المبيعات":
            elements.extend(self.create_sales_summary(start_date, end_date))
        elif report_type == "العملاء":
            elements.extend(self.create_customer_analysis(start_date, end_date))
        elif report_type == "المخزون":
            elements.extend(self.create_inventory_analysis())
        
        # بناء التقرير
        doc.build(elements)
        
        return True