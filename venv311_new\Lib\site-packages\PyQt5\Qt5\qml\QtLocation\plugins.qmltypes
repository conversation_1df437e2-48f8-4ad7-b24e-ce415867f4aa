import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtLocation 5.14'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: {
                "NoLayoutChangeHint": 0,
                "VerticalSortHint": 1,
                "HorizontalSortHint": 2
            }
        }
        Enum {
            name: "CheckIndexOption"
            values: {
                "NoOption": 0,
                "IndexIsValid": 1,
                "DoNotUseParent": 2,
                "ParentIsInvalid": 4
            }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractListModel"; prototype: "QAbstractItemModel" }
    Component {
        name: "QDeclarativeCategory"
        prototype: "QObject"
        exports: ["QtLocation/Category 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Visibility"
            values: {
                "UnspecifiedVisibility": 0,
                "DeviceVisibility": 1,
                "PrivateVisibility": 2,
                "PublicVisibility": 4
            }
        }
        Enum {
            name: "Status"
            values: {
                "Ready": 0,
                "Saving": 1,
                "Removing": 2,
                "Error": 3
            }
        }
        Property { name: "category"; type: "QPlaceCategory" }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "categoryId"; type: "string" }
        Property { name: "name"; type: "string" }
        Property { name: "visibility"; type: "Visibility" }
        Property { name: "icon"; type: "QDeclarativePlaceIcon"; isPointer: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Method { name: "errorString"; type: "string" }
        Method {
            name: "save"
            Parameter { name: "parentId"; type: "string" }
        }
        Method { name: "save" }
        Method { name: "remove" }
    }
    Component {
        name: "QDeclarativeCircleMapItem"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: ["QtLocation/MapCircle 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "center"; type: "QGeoCoordinate" }
        Property { name: "radius"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "centerChanged"
            Parameter { name: "center"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "double" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        name: "QDeclarativeContactDetail"
        prototype: "QObject"
        exports: ["QtLocation/ContactDetail 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "contactDetail"; type: "QPlaceContactDetail" }
        Property { name: "label"; type: "string" }
        Property { name: "value"; type: "string" }
    }
    Component {
        name: "QDeclarativeContactDetails"
        prototype: "QQmlPropertyMap"
        exports: ["QtLocation/ContactDetails 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QDeclarativeGeoCameraCapabilities"
        prototype: "QObject"
        exports: ["QtLocation/CameraCapabilities 5.10"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "minimumZoomLevel"; type: "double"; isReadonly: true }
        Property { name: "maximumZoomLevel"; type: "double"; isReadonly: true }
        Property { name: "minimumTilt"; type: "double"; isReadonly: true }
        Property { name: "maximumTilt"; type: "double"; isReadonly: true }
        Property { name: "minimumFieldOfView"; type: "double"; isReadonly: true }
        Property { name: "maximumFieldOfView"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeGeoManeuver"
        prototype: "QObject"
        exports: [
            "QtLocation/RouteManeuver 5.0",
            "QtLocation/RouteManeuver 5.11",
            "QtLocation/RouteManeuver 5.8"
        ]
        exportMetaObjectRevisions: [0, 11, 0]
        Enum {
            name: "Direction"
            values: {
                "NoDirection": 0,
                "DirectionForward": 1,
                "DirectionBearRight": 2,
                "DirectionLightRight": 3,
                "DirectionRight": 4,
                "DirectionHardRight": 5,
                "DirectionUTurnRight": 6,
                "DirectionUTurnLeft": 7,
                "DirectionHardLeft": 8,
                "DirectionLeft": 9,
                "DirectionLightLeft": 10,
                "DirectionBearLeft": 11
            }
        }
        Property { name: "valid"; type: "bool"; isReadonly: true }
        Property { name: "position"; type: "QGeoCoordinate"; isReadonly: true }
        Property { name: "instructionText"; type: "string"; isReadonly: true }
        Property { name: "direction"; type: "Direction"; isReadonly: true }
        Property { name: "timeToNextInstruction"; type: "int"; isReadonly: true }
        Property { name: "distanceToNextInstruction"; type: "double"; isReadonly: true }
        Property { name: "waypoint"; type: "QGeoCoordinate"; isReadonly: true }
        Property { name: "waypointValid"; type: "bool"; isReadonly: true }
        Property {
            name: "extendedAttributes"
            revision: 11
            type: "QObject"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QDeclarativeGeoMap"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtLocation/Map 5.0",
            "QtLocation/Map 5.11",
            "QtLocation/Map 5.12",
            "QtLocation/Map 5.13",
            "QtLocation/Map 5.14"
        ]
        exportMetaObjectRevisions: [0, 11, 12, 13, 14]
        Property { name: "gesture"; type: "QQuickGeoMapGestureArea"; isReadonly: true; isPointer: true }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "minimumZoomLevel"; type: "double" }
        Property { name: "maximumZoomLevel"; type: "double" }
        Property { name: "zoomLevel"; type: "double" }
        Property { name: "tilt"; type: "double" }
        Property { name: "minimumTilt"; type: "double" }
        Property { name: "maximumTilt"; type: "double" }
        Property { name: "bearing"; type: "double" }
        Property { name: "fieldOfView"; type: "double" }
        Property { name: "minimumFieldOfView"; type: "double" }
        Property { name: "maximumFieldOfView"; type: "double" }
        Property { name: "activeMapType"; type: "QDeclarativeGeoMapType"; isPointer: true }
        Property {
            name: "supportedMapTypes"
            type: "QDeclarativeGeoMapType"
            isList: true
            isReadonly: true
        }
        Property { name: "center"; type: "QGeoCoordinate" }
        Property { name: "mapItems"; type: "QList<QObject*>"; isReadonly: true }
        Property { name: "mapParameters"; type: "QList<QObject*>"; isReadonly: true }
        Property { name: "error"; type: "QGeoServiceProvider::Error"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "visibleRegion"; type: "QGeoShape" }
        Property { name: "copyrightsVisible"; type: "bool" }
        Property { name: "color"; type: "QColor" }
        Property { name: "mapReady"; type: "bool"; isReadonly: true }
        Property { name: "visibleArea"; revision: 12; type: "QRectF" }
        Signal {
            name: "pluginChanged"
            Parameter { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        }
        Signal {
            name: "zoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "double" }
        }
        Signal {
            name: "centerChanged"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "copyrightLinkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "copyrightsVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "bearingChanged"
            Parameter { name: "bearing"; type: "double" }
        }
        Signal {
            name: "tiltChanged"
            Parameter { name: "tilt"; type: "double" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "double" }
        }
        Signal {
            name: "minimumTiltChanged"
            Parameter { name: "minimumTilt"; type: "double" }
        }
        Signal {
            name: "maximumTiltChanged"
            Parameter { name: "maximumTilt"; type: "double" }
        }
        Signal {
            name: "minimumFieldOfViewChanged"
            Parameter { name: "minimumFieldOfView"; type: "double" }
        }
        Signal {
            name: "maximumFieldOfViewChanged"
            Parameter { name: "maximumFieldOfView"; type: "double" }
        }
        Signal {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsImage"; type: "QImage" }
        }
        Signal {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsHtml"; type: "string" }
        }
        Signal {
            name: "mapReadyChanged"
            Parameter { name: "ready"; type: "bool" }
        }
        Signal { name: "mapObjectsChanged"; revision: 11 }
        Signal { name: "visibleRegionChanged"; revision: 14 }
        Method {
            name: "setBearing"
            Parameter { name: "bearing"; type: "double" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "alignCoordinateToPoint"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "removeMapItem"
            Parameter { name: "item"; type: "QDeclarativeGeoMapItemBase"; isPointer: true }
        }
        Method {
            name: "addMapItem"
            Parameter { name: "item"; type: "QDeclarativeGeoMapItemBase"; isPointer: true }
        }
        Method {
            name: "addMapItemGroup"
            Parameter { name: "itemGroup"; type: "QDeclarativeGeoMapItemGroup"; isPointer: true }
        }
        Method {
            name: "removeMapItemGroup"
            Parameter { name: "itemGroup"; type: "QDeclarativeGeoMapItemGroup"; isPointer: true }
        }
        Method {
            name: "removeMapItemView"
            Parameter { name: "itemView"; type: "QDeclarativeGeoMapItemView"; isPointer: true }
        }
        Method {
            name: "addMapItemView"
            Parameter { name: "itemView"; type: "QDeclarativeGeoMapItemView"; isPointer: true }
        }
        Method { name: "clearMapItems" }
        Method {
            name: "addMapParameter"
            Parameter { name: "parameter"; type: "QDeclarativeGeoMapParameter"; isPointer: true }
        }
        Method {
            name: "removeMapParameter"
            Parameter { name: "parameter"; type: "QDeclarativeGeoMapParameter"; isPointer: true }
        }
        Method { name: "clearMapParameters" }
        Method {
            name: "toCoordinate"
            type: "QGeoCoordinate"
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "clipToViewPort"; type: "bool" }
        }
        Method {
            name: "toCoordinate"
            type: "QGeoCoordinate"
            Parameter { name: "position"; type: "QPointF" }
        }
        Method {
            name: "fromCoordinate"
            type: "QPointF"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
            Parameter { name: "clipToViewPort"; type: "bool" }
        }
        Method {
            name: "fromCoordinate"
            type: "QPointF"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method { name: "fitViewportToMapItems" }
        Method { name: "fitViewportToVisibleMapItems" }
        Method {
            name: "pan"
            Parameter { name: "dx"; type: "int" }
            Parameter { name: "dy"; type: "int" }
        }
        Method { name: "prefetchData" }
        Method { name: "clearData" }
        Method {
            name: "fitViewportToGeoShape"
            revision: 13
            Parameter { name: "shape"; type: "QGeoShape" }
            Parameter { name: "margins"; type: "QVariant" }
        }
    }
    Component {
        name: "QDeclarativeGeoMapCopyrightNotice"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtLocation/MapCopyrightNotice 5.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "mapSource"; type: "QDeclarativeGeoMap"; isPointer: true }
        Property { name: "styleSheet"; type: "string" }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "backgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "styleSheetChanged"
            Parameter { name: "styleSheet"; type: "string" }
        }
        Signal { name: "copyrightsVisibleChanged" }
        Method {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsImage"; type: "QImage" }
        }
        Method {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsHtml"; type: "string" }
        }
        Method {
            name: "onCopyrightsStyleSheetChanged"
            Parameter { name: "styleSheet"; type: "string" }
        }
    }
    Component {
        name: "QDeclarativeGeoMapItemBase"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtLocation/GeoMapItemBase 5.0",
            "QtLocation/GeoMapItemBase 5.11",
            "QtLocation/GeoMapItemBase 5.14"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 11, 14]
        Property { name: "geoShape"; type: "QGeoShape" }
        Property { name: "autoFadeIn"; revision: 14; type: "bool" }
        Signal { name: "mapItemOpacityChanged" }
        Signal { name: "addTransitionFinished"; revision: 12 }
        Signal { name: "removeTransitionFinished"; revision: 12 }
    }
    Component {
        name: "QDeclarativeGeoMapItemGroup"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtLocation/MapItemGroup 5.9"]
        exportMetaObjectRevisions: [0]
        Signal { name: "mapItemOpacityChanged" }
        Signal { name: "addTransitionFinished" }
        Signal { name: "removeTransitionFinished" }
    }
    Component {
        name: "QDeclarativeGeoMapItemView"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemGroup"
        exports: ["QtLocation/MapItemView 5.0", "QtLocation/MapItemView 5.12"]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "autoFitViewport"; type: "bool" }
        Property { name: "add"; revision: 12; type: "QQuickTransition"; isPointer: true }
        Property { name: "remove"; revision: 12; type: "QQuickTransition"; isPointer: true }
        Property { name: "mapItems"; revision: 12; type: "QList<QQuickItem*>"; isReadonly: true }
        Property { name: "incubateDelegates"; revision: 12; type: "bool" }
    }
    Component {
        name: "QDeclarativeGeoMapParameter"
        prototype: "QGeoMapParameter"
        exports: [
            "QtLocation/DynamicParameter 5.11",
            "QtLocation/MapParameter 5.9"
        ]
        exportMetaObjectRevisions: [0, 0]
        Signal {
            name: "completed"
            Parameter { type: "QDeclarativeGeoMapParameter"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativeGeoMapQuickItem"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: ["QtLocation/MapQuickItem 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "coordinate"; type: "QGeoCoordinate" }
        Property { name: "anchorPoint"; type: "QPointF" }
        Property { name: "zoomLevel"; type: "double" }
        Property { name: "sourceItem"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        name: "QDeclarativeGeoMapType"
        prototype: "QObject"
        exports: ["QtLocation/MapType 5.0", "QtLocation/MapType 5.5"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "MapStyle"
            values: {
                "NoMap": 0,
                "StreetMap": 1,
                "SatelliteMapDay": 2,
                "SatelliteMapNight": 3,
                "TerrainMap": 4,
                "HybridMap": 5,
                "TransitMap": 6,
                "GrayStreetMap": 7,
                "PedestrianMap": 8,
                "CarNavigationMap": 9,
                "CycleMap": 10,
                "CustomMap": 100
            }
        }
        Property { name: "style"; type: "MapStyle"; isReadonly: true }
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "description"; type: "string"; isReadonly: true }
        Property { name: "mobile"; type: "bool"; isReadonly: true }
        Property { name: "night"; revision: 1; type: "bool"; isReadonly: true }
        Property {
            name: "cameraCapabilities"
            type: "QDeclarativeGeoCameraCapabilities"
            isReadonly: true
            isPointer: true
        }
        Property { name: "metadata"; type: "QVariantMap"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeGeoRoute"
        prototype: "QObject"
        exports: [
            "QtLocation/Route 5.0",
            "QtLocation/Route 5.11",
            "QtLocation/Route 5.12",
            "QtLocation/Route 5.13"
        ]
        exportMetaObjectRevisions: [0, 11, 12, 13]
        Property { name: "bounds"; type: "QGeoRectangle"; isReadonly: true }
        Property { name: "travelTime"; type: "int"; isReadonly: true }
        Property { name: "distance"; type: "double"; isReadonly: true }
        Property { name: "path"; type: "QJSValue" }
        Property { name: "segments"; type: "QDeclarativeGeoRouteSegment"; isList: true; isReadonly: true }
        Property {
            name: "routeQuery"
            revision: 11
            type: "QDeclarativeGeoRouteQuery"
            isReadonly: true
            isPointer: true
        }
        Property { name: "legs"; revision: 12; type: "QList<QObject*>"; isReadonly: true }
        Property {
            name: "extendedAttributes"
            revision: 13
            type: "QObject"
            isReadonly: true
            isPointer: true
        }
        Method {
            name: "equals"
            type: "bool"
            Parameter { name: "other"; type: "QDeclarativeGeoRoute"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativeGeoRouteLeg"
        prototype: "QDeclarativeGeoRoute"
        exports: ["QtLocation/RouteLeg 5.12"]
        exportMetaObjectRevisions: [12]
        Property { name: "legIndex"; type: "int"; isReadonly: true }
        Property { name: "overallRoute"; type: "QObject"; isReadonly: true; isPointer: true }
    }
    Component {
        name: "QDeclarativeGeoRouteModel"
        prototype: "QAbstractListModel"
        exports: ["QtLocation/RouteModel 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Enum {
            name: "RouteError"
            values: {
                "NoError": 0,
                "EngineNotSetError": 1,
                "CommunicationError": 2,
                "ParseError": 3,
                "UnsupportedOptionError": 4,
                "UnknownError": 5,
                "UnknownParameterError": 100,
                "MissingRequiredParameterError": 101
            }
        }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "query"; type: "QDeclarativeGeoRouteQuery"; isPointer: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "autoUpdate"; type: "bool" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "error"; type: "RouteError"; isReadonly: true }
        Property { name: "measurementSystem"; type: "QLocale::MeasurementSystem" }
        Signal { name: "routesChanged" }
        Signal { name: "abortRequested" }
        Method { name: "update" }
        Method {
            name: "get"
            type: "QDeclarativeGeoRoute*"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reset" }
        Method { name: "cancel" }
    }
    Component {
        name: "QDeclarativeGeoRouteQuery"
        defaultProperty: "quickChildren"
        prototype: "QObject"
        exports: [
            "QtLocation/RouteQuery 5.0",
            "QtLocation/RouteQuery 5.11",
            "QtLocation/RouteQuery 5.13"
        ]
        exportMetaObjectRevisions: [0, 11, 13]
        Enum {
            name: "TravelMode"
            values: {
                "CarTravel": 1,
                "PedestrianTravel": 2,
                "BicycleTravel": 4,
                "PublicTransitTravel": 8,
                "TruckTravel": 16
            }
        }
        Enum {
            name: "TravelModes"
            values: {
                "CarTravel": 1,
                "PedestrianTravel": 2,
                "BicycleTravel": 4,
                "PublicTransitTravel": 8,
                "TruckTravel": 16
            }
        }
        Enum {
            name: "FeatureType"
            values: {
                "NoFeature": 0,
                "TollFeature": 1,
                "HighwayFeature": 2,
                "PublicTransitFeature": 4,
                "FerryFeature": 8,
                "TunnelFeature": 16,
                "DirtRoadFeature": 32,
                "ParksFeature": 64,
                "MotorPoolLaneFeature": 128,
                "TrafficFeature": 256
            }
        }
        Enum {
            name: "FeatureWeight"
            values: {
                "NeutralFeatureWeight": 0,
                "PreferFeatureWeight": 1,
                "RequireFeatureWeight": 2,
                "AvoidFeatureWeight": 4,
                "DisallowFeatureWeight": 8
            }
        }
        Enum {
            name: "RouteOptimization"
            values: {
                "ShortestRoute": 1,
                "FastestRoute": 2,
                "MostEconomicRoute": 4,
                "MostScenicRoute": 8
            }
        }
        Enum {
            name: "RouteOptimizations"
            values: {
                "ShortestRoute": 1,
                "FastestRoute": 2,
                "MostEconomicRoute": 4,
                "MostScenicRoute": 8
            }
        }
        Enum {
            name: "SegmentDetail"
            values: {
                "NoSegmentData": 0,
                "BasicSegmentData": 1
            }
        }
        Enum {
            name: "SegmentDetails"
            values: {
                "NoSegmentData": 0,
                "BasicSegmentData": 1
            }
        }
        Enum {
            name: "ManeuverDetail"
            values: {
                "NoManeuvers": 0,
                "BasicManeuvers": 1
            }
        }
        Enum {
            name: "ManeuverDetails"
            values: {
                "NoManeuvers": 0,
                "BasicManeuvers": 1
            }
        }
        Property { name: "numberAlternativeRoutes"; type: "int" }
        Property { name: "travelModes"; type: "TravelModes" }
        Property { name: "routeOptimizations"; type: "RouteOptimizations" }
        Property { name: "segmentDetail"; type: "SegmentDetail" }
        Property { name: "maneuverDetail"; type: "ManeuverDetail" }
        Property { name: "waypoints"; type: "QVariantList" }
        Property { name: "excludedAreas"; type: "QJSValue" }
        Property { name: "featureTypes"; type: "QList<int>"; isReadonly: true }
        Property { name: "extraParameters"; revision: 11; type: "QVariantMap"; isReadonly: true }
        Property { name: "departureTime"; revision: 13; type: "QDateTime" }
        Property { name: "quickChildren"; type: "QObject"; isList: true; isReadonly: true }
        Signal { name: "queryDetailsChanged" }
        Signal { name: "extraParametersChanged"; revision: 11 }
        Method { name: "waypointObjects"; type: "QVariantList" }
        Method {
            name: "addWaypoint"
            Parameter { name: "w"; type: "QVariant" }
        }
        Method {
            name: "removeWaypoint"
            Parameter { name: "waypoint"; type: "QVariant" }
        }
        Method { name: "clearWaypoints" }
        Method {
            name: "addExcludedArea"
            Parameter { name: "area"; type: "QGeoRectangle" }
        }
        Method {
            name: "removeExcludedArea"
            Parameter { name: "area"; type: "QGeoRectangle" }
        }
        Method { name: "clearExcludedAreas" }
        Method {
            name: "setFeatureWeight"
            Parameter { name: "featureType"; type: "FeatureType" }
            Parameter { name: "featureWeight"; type: "FeatureWeight" }
        }
        Method {
            name: "featureWeight"
            type: "int"
            Parameter { name: "featureType"; type: "FeatureType" }
        }
        Method { name: "resetFeatureWeights" }
    }
    Component {
        name: "QDeclarativeGeoRouteSegment"
        prototype: "QObject"
        exports: ["QtLocation/RouteSegment 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "travelTime"; type: "int"; isReadonly: true }
        Property { name: "distance"; type: "double"; isReadonly: true }
        Property { name: "path"; type: "QJSValue"; isReadonly: true }
        Property { name: "maneuver"; type: "QDeclarativeGeoManeuver"; isReadonly: true; isPointer: true }
    }
    Component {
        name: "QDeclarativeGeoServiceProvider"
        defaultProperty: "parameters"
        prototype: "QObject"
        exports: ["QtLocation/Plugin 5.0", "QtLocation/Plugin 5.11"]
        exportMetaObjectRevisions: [0, 11]
        Enum {
            name: "RoutingFeature"
            values: {
                "NoRoutingFeatures": 0,
                "OnlineRoutingFeature": 1,
                "OfflineRoutingFeature": 2,
                "LocalizedRoutingFeature": 4,
                "RouteUpdatesFeature": 8,
                "AlternativeRoutesFeature": 16,
                "ExcludeAreasRoutingFeature": 32,
                "AnyRoutingFeatures": -1
            }
        }
        Enum {
            name: "RoutingFeatures"
            values: {
                "NoRoutingFeatures": 0,
                "OnlineRoutingFeature": 1,
                "OfflineRoutingFeature": 2,
                "LocalizedRoutingFeature": 4,
                "RouteUpdatesFeature": 8,
                "AlternativeRoutesFeature": 16,
                "ExcludeAreasRoutingFeature": 32,
                "AnyRoutingFeatures": -1
            }
        }
        Enum {
            name: "GeocodingFeature"
            values: {
                "NoGeocodingFeatures": 0,
                "OnlineGeocodingFeature": 1,
                "OfflineGeocodingFeature": 2,
                "ReverseGeocodingFeature": 4,
                "LocalizedGeocodingFeature": 8,
                "AnyGeocodingFeatures": -1
            }
        }
        Enum {
            name: "GeocodingFeatures"
            values: {
                "NoGeocodingFeatures": 0,
                "OnlineGeocodingFeature": 1,
                "OfflineGeocodingFeature": 2,
                "ReverseGeocodingFeature": 4,
                "LocalizedGeocodingFeature": 8,
                "AnyGeocodingFeatures": -1
            }
        }
        Enum {
            name: "MappingFeature"
            values: {
                "NoMappingFeatures": 0,
                "OnlineMappingFeature": 1,
                "OfflineMappingFeature": 2,
                "LocalizedMappingFeature": 4,
                "AnyMappingFeatures": -1
            }
        }
        Enum {
            name: "MappingFeatures"
            values: {
                "NoMappingFeatures": 0,
                "OnlineMappingFeature": 1,
                "OfflineMappingFeature": 2,
                "LocalizedMappingFeature": 4,
                "AnyMappingFeatures": -1
            }
        }
        Enum {
            name: "PlacesFeature"
            values: {
                "NoPlacesFeatures": 0,
                "OnlinePlacesFeature": 1,
                "OfflinePlacesFeature": 2,
                "SavePlaceFeature": 4,
                "RemovePlaceFeature": 8,
                "SaveCategoryFeature": 16,
                "RemoveCategoryFeature": 32,
                "PlaceRecommendationsFeature": 64,
                "SearchSuggestionsFeature": 128,
                "LocalizedPlacesFeature": 256,
                "NotificationsFeature": 512,
                "PlaceMatchingFeature": 1024,
                "AnyPlacesFeatures": -1
            }
        }
        Enum {
            name: "PlacesFeatures"
            values: {
                "NoPlacesFeatures": 0,
                "OnlinePlacesFeature": 1,
                "OfflinePlacesFeature": 2,
                "SavePlaceFeature": 4,
                "RemovePlaceFeature": 8,
                "SaveCategoryFeature": 16,
                "RemoveCategoryFeature": 32,
                "PlaceRecommendationsFeature": 64,
                "SearchSuggestionsFeature": 128,
                "LocalizedPlacesFeature": 256,
                "NotificationsFeature": 512,
                "PlaceMatchingFeature": 1024,
                "AnyPlacesFeatures": -1
            }
        }
        Enum {
            name: "NavigationFeatures"
            values: {
                "NoNavigationFeatures": 0,
                "OnlineNavigationFeature": 1,
                "OfflineNavigationFeature": 2,
                "AnyNavigationFeatures": -1
            }
        }
        Property { name: "name"; type: "string" }
        Property { name: "availableServiceProviders"; type: "QStringList"; isReadonly: true }
        Property {
            name: "parameters"
            type: "QDeclarativePluginParameter"
            isList: true
            isReadonly: true
        }
        Property {
            name: "required"
            type: "QDeclarativeGeoServiceProviderRequirements"
            isPointer: true
        }
        Property { name: "locales"; type: "QStringList" }
        Property { name: "preferred"; type: "QStringList" }
        Property { name: "allowExperimental"; type: "bool" }
        Property { name: "isAttached"; type: "bool"; isReadonly: true }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal { name: "attached" }
        Signal {
            name: "preferredChanged"
            Parameter { name: "preferences"; type: "QStringList" }
        }
        Signal {
            name: "allowExperimentalChanged"
            Parameter { name: "allow"; type: "bool" }
        }
        Method {
            name: "supportsRouting"
            type: "bool"
            Parameter { name: "feature"; type: "RoutingFeatures" }
        }
        Method { name: "supportsRouting"; type: "bool" }
        Method {
            name: "supportsGeocoding"
            type: "bool"
            Parameter { name: "feature"; type: "GeocodingFeatures" }
        }
        Method { name: "supportsGeocoding"; type: "bool" }
        Method {
            name: "supportsMapping"
            type: "bool"
            Parameter { name: "feature"; type: "MappingFeatures" }
        }
        Method { name: "supportsMapping"; type: "bool" }
        Method {
            name: "supportsPlaces"
            type: "bool"
            Parameter { name: "feature"; type: "PlacesFeatures" }
        }
        Method { name: "supportsPlaces"; type: "bool" }
        Method {
            name: "supportsNavigation"
            revision: 11
            type: "bool"
            Parameter { name: "feature"; type: "NavigationFeature" }
        }
        Method { name: "supportsNavigation"; revision: 11; type: "bool" }
    }
    Component {
        name: "QDeclarativeGeoServiceProviderRequirements"
        prototype: "QObject"
        exports: ["QtLocation/PluginRequirements 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "mapping"; type: "QDeclarativeGeoServiceProvider::MappingFeatures" }
        Property { name: "routing"; type: "QDeclarativeGeoServiceProvider::RoutingFeatures" }
        Property { name: "geocoding"; type: "QDeclarativeGeoServiceProvider::GeocodingFeatures" }
        Property { name: "places"; type: "QDeclarativeGeoServiceProvider::PlacesFeatures" }
        Property { name: "navigation"; type: "QDeclarativeGeoServiceProvider::NavigationFeatures" }
        Signal {
            name: "mappingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::MappingFeatures" }
        }
        Signal {
            name: "routingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::RoutingFeatures" }
        }
        Signal {
            name: "geocodingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::GeocodingFeatures" }
        }
        Signal {
            name: "placesRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::PlacesFeatures" }
        }
        Signal {
            name: "navigationRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::NavigationFeatures" }
        }
        Signal { name: "requirementsChanged" }
        Method {
            name: "matches"
            type: "bool"
            Parameter { name: "provider"; type: "const QGeoServiceProvider"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativeGeoWaypoint"
        defaultProperty: "quickChildren"
        prototype: "QGeoCoordinateObject"
        exports: ["QtLocation/Waypoint 5.11"]
        exportMetaObjectRevisions: [0]
        Property { name: "latitude"; type: "double" }
        Property { name: "longitude"; type: "double" }
        Property { name: "altitude"; type: "double" }
        Property { name: "isValid"; type: "bool"; isReadonly: true }
        Property { name: "bearing"; type: "double" }
        Property { name: "metadata"; type: "QVariantMap"; isReadonly: true }
        Property { name: "quickChildren"; type: "QObject"; isList: true; isReadonly: true }
        Signal { name: "completed" }
        Signal { name: "waypointDetailsChanged" }
        Signal { name: "extraParametersChanged" }
    }
    Component {
        name: "QDeclarativeGeocodeModel"
        prototype: "QAbstractListModel"
        exports: ["QtLocation/GeocodeModel 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Enum {
            name: "GeocodeError"
            values: {
                "NoError": 0,
                "EngineNotSetError": 1,
                "CommunicationError": 2,
                "ParseError": 3,
                "UnsupportedOptionError": 4,
                "CombinationError": 5,
                "UnknownError": 6,
                "UnknownParameterError": 100,
                "MissingRequiredParameterError": 101
            }
        }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "autoUpdate"; type: "bool" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "limit"; type: "int" }
        Property { name: "offset"; type: "int" }
        Property { name: "query"; type: "QVariant" }
        Property { name: "bounds"; type: "QVariant" }
        Property { name: "error"; type: "GeocodeError"; isReadonly: true }
        Signal { name: "locationsChanged" }
        Method { name: "update" }
        Method {
            name: "get"
            type: "QDeclarativeGeoLocation*"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reset" }
        Method { name: "cancel" }
    }
    Component {
        name: "QDeclarativeMapLineProperties"
        prototype: "QObject"
        Property { name: "width"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        name: "QDeclarativePlace"
        prototype: "QObject"
        exports: ["QtLocation/Place 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "Ready": 0,
                "Saving": 1,
                "Fetching": 2,
                "Removing": 3,
                "Error": 4
            }
        }
        Enum {
            name: "Visibility"
            values: {
                "UnspecifiedVisibility": 0,
                "DeviceVisibility": 1,
                "PrivateVisibility": 2,
                "PublicVisibility": 4
            }
        }
        Property { name: "place"; type: "QPlace" }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "categories"; type: "QDeclarativeCategory"; isList: true; isReadonly: true }
        Property { name: "location"; type: "QDeclarativeGeoLocation"; isPointer: true }
        Property { name: "ratings"; type: "QDeclarativeRatings"; isPointer: true }
        Property { name: "supplier"; type: "QDeclarativeSupplier"; isPointer: true }
        Property { name: "icon"; type: "QDeclarativePlaceIcon"; isPointer: true }
        Property { name: "name"; type: "string" }
        Property { name: "placeId"; type: "string" }
        Property { name: "attribution"; type: "string" }
        Property {
            name: "reviewModel"
            type: "QDeclarativeReviewModel"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "imageModel"
            type: "QDeclarativePlaceImageModel"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "editorialModel"
            type: "QDeclarativePlaceEditorialModel"
            isReadonly: true
            isPointer: true
        }
        Property { name: "extendedAttributes"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "contactDetails"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "detailsFetched"; type: "bool"; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "primaryPhone"; type: "string"; isReadonly: true }
        Property { name: "primaryFax"; type: "string"; isReadonly: true }
        Property { name: "primaryEmail"; type: "string"; isReadonly: true }
        Property { name: "primaryWebsite"; type: "QUrl"; isReadonly: true }
        Property { name: "visibility"; type: "Visibility" }
        Property { name: "favorite"; type: "QDeclarativePlace"; isPointer: true }
        Method { name: "getDetails" }
        Method { name: "save" }
        Method { name: "remove" }
        Method { name: "errorString"; type: "string" }
        Method {
            name: "copyFrom"
            Parameter { name: "original"; type: "QDeclarativePlace"; isPointer: true }
        }
        Method {
            name: "initializeFavorite"
            Parameter { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativePlaceAttribute"
        prototype: "QObject"
        exports: ["QtLocation/PlaceAttribute 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "attribute"; type: "QPlaceAttribute" }
        Property { name: "label"; type: "string" }
        Property { name: "text"; type: "string" }
    }
    Component {
        name: "QDeclarativePlaceContentModel"
        prototype: "QAbstractListModel"
        Property { name: "place"; type: "QDeclarativePlace"; isPointer: true }
        Property { name: "batchSize"; type: "int" }
        Property { name: "totalCount"; type: "int"; isReadonly: true }
    }
    Component {
        name: "QDeclarativePlaceEditorialModel"
        prototype: "QDeclarativePlaceContentModel"
        exports: ["QtLocation/EditorialModel 5.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QDeclarativePlaceIcon"
        prototype: "QObject"
        exports: ["QtLocation/Icon 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "icon"; type: "QPlaceIcon" }
        Property { name: "parameters"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Method {
            name: "url"
            type: "QUrl"
            Parameter { name: "size"; type: "QSize" }
        }
        Method { name: "url"; type: "QUrl" }
    }
    Component {
        name: "QDeclarativePlaceImageModel"
        prototype: "QDeclarativePlaceContentModel"
        exports: ["QtLocation/ImageModel 5.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QDeclarativePlaceUser"
        prototype: "QObject"
        exports: ["QtLocation/User 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "user"; type: "QPlaceUser" }
        Property { name: "userId"; type: "string" }
        Property { name: "name"; type: "string" }
    }
    Component {
        name: "QDeclarativePluginParameter"
        prototype: "QObject"
        exports: ["QtLocation/PluginParameter 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "value"; type: "QVariant" }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal { name: "initialized" }
    }
    Component {
        name: "QDeclarativePolygonMapItem"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: ["QtLocation/MapPolygon 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "QJSValue" }
        Property { name: "color"; type: "QColor" }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
    }
    Component {
        name: "QDeclarativePolylineMapItem"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: ["QtLocation/MapPolyline 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "QJSValue" }
        Property {
            name: "line"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
        Method { name: "pathLength"; type: "int" }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "insertCoordinate"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "replaceCoordinate"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "coordinateAt"
            type: "QGeoCoordinate"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "containsCoordinate"
            type: "bool"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setPath"
            Parameter { name: "path"; type: "QGeoPath" }
        }
    }
    Component {
        name: "QDeclarativeRatings"
        prototype: "QObject"
        exports: ["QtLocation/Ratings 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ratings"; type: "QPlaceRatings" }
        Property { name: "average"; type: "double" }
        Property { name: "maximum"; type: "double" }
        Property { name: "count"; type: "int" }
    }
    Component {
        name: "QDeclarativeRectangleMapItem"
        defaultProperty: "data"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: ["QtLocation/MapRectangle 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "topLeft"; type: "QGeoCoordinate" }
        Property { name: "bottomRight"; type: "QGeoCoordinate" }
        Property { name: "color"; type: "QColor" }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "topLeftChanged"
            Parameter { name: "topLeft"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "bottomRightChanged"
            Parameter { name: "bottomRight"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        name: "QDeclarativeReviewModel"
        prototype: "QDeclarativePlaceContentModel"
        exports: ["QtLocation/ReviewModel 5.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QDeclarativeRouteMapItem"
        defaultProperty: "data"
        prototype: "QDeclarativePolylineMapItem"
        exports: ["QtLocation/MapRoute 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "route"; type: "QDeclarativeGeoRoute"; isPointer: true }
        Signal {
            name: "routeChanged"
            Parameter { name: "route"; type: "const QDeclarativeGeoRoute"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativeSearchModelBase"
        prototype: "QAbstractListModel"
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "searchArea"; type: "QVariant" }
        Property { name: "limit"; type: "int" }
        Property { name: "previousPagesAvailable"; type: "bool"; isReadonly: true }
        Property { name: "nextPagesAvailable"; type: "bool"; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Method { name: "update" }
        Method { name: "cancel" }
        Method { name: "reset" }
        Method { name: "errorString"; type: "string" }
        Method { name: "previousPage" }
        Method { name: "nextPage" }
    }
    Component {
        name: "QDeclarativeSearchResultModel"
        prototype: "QDeclarativeSearchModelBase"
        exports: [
            "QtLocation/PlaceSearchModel 5.0",
            "QtLocation/PlaceSearchModel 5.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "SearchResultType"
            values: {
                "UnknownSearchResult": 0,
                "PlaceResult": 1,
                "ProposedSearchResult": 2
            }
        }
        Enum {
            name: "RelevanceHint"
            values: {
                "UnspecifiedHint": 0,
                "DistanceHint": 1,
                "LexicalPlaceNameHint": 2
            }
        }
        Property { name: "searchTerm"; type: "string" }
        Property { name: "categories"; type: "QDeclarativeCategory"; isList: true; isReadonly: true }
        Property { name: "recommendationId"; type: "string" }
        Property { name: "relevanceHint"; type: "RelevanceHint" }
        Property { name: "visibilityScope"; type: "QDeclarativePlace::Visibility" }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "favoritesPlugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "favoritesMatchParameters"; type: "QVariantMap" }
        Property { name: "incremental"; revision: 12; type: "bool" }
        Signal { name: "rowCountChanged" }
        Signal { name: "dataChanged" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "roleName"; type: "string" }
        }
        Method {
            name: "updateWith"
            Parameter { name: "proposedSearchIndex"; type: "int" }
        }
    }
    Component {
        name: "QDeclarativeSearchSuggestionModel"
        prototype: "QDeclarativeSearchModelBase"
        exports: ["QtLocation/PlaceSearchSuggestionModel 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "searchTerm"; type: "string" }
        Property { name: "suggestions"; type: "QStringList"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeSupplier"
        prototype: "QObject"
        exports: ["QtLocation/Supplier 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "supplier"; type: "QPlaceSupplier" }
        Property { name: "name"; type: "string" }
        Property { name: "supplierId"; type: "string" }
        Property { name: "url"; type: "QUrl" }
        Property { name: "icon"; type: "QDeclarativePlaceIcon"; isPointer: true }
    }
    Component {
        name: "QDeclarativeSupportedCategoriesModel"
        prototype: "QAbstractItemModel"
        exports: ["QtLocation/CategoryModel 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Roles"
            values: {
                "CategoryRole": 256,
                "ParentCategoryRole": 257
            }
        }
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "hierarchical"; type: "bool" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Signal { name: "dataChanged" }
        Method { name: "update" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method { name: "errorString"; type: "string" }
    }
    Component {
        name: "QGeoCoordinateObject"
        prototype: "QObject"
        Property { name: "coordinate"; type: "QGeoCoordinate" }
    }
    Component {
        name: "QGeoMapObject"
        defaultProperty: "quickChildren"
        prototype: "QParameterizableObject"
        Enum {
            name: "Type"
            values: {
                "InvalidType": 0,
                "ViewType": 1,
                "RouteType": 2,
                "RectangleType": 3,
                "CircleType": 4,
                "PolylineType": 5,
                "PolygonType": 6,
                "IconType": 7,
                "UserType": 256
            }
        }
        Property { name: "visible"; type: "bool" }
        Property { name: "type"; type: "Type"; isReadonly: true }
        Property { name: "geoShape"; type: "QGeoShape" }
        Signal { name: "selected" }
        Signal { name: "completed" }
    }
    Component {
        name: "QGeoMapParameter"
        prototype: "QObject"
        Property { name: "type"; type: "string" }
        Signal {
            name: "propertyUpdated"
            Parameter { name: "param"; type: "QGeoMapParameter"; isPointer: true }
            Parameter { name: "propertyName"; type: "const char"; isPointer: true }
        }
    }
    Component {
        name: "QGeoMapPinchEvent"
        prototype: "QObject"
        exports: ["QtLocation/MapPinchEvent 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "center"; type: "QPointF"; isReadonly: true }
        Property { name: "angle"; type: "double"; isReadonly: true }
        Property { name: "point1"; type: "QPointF"; isReadonly: true }
        Property { name: "point2"; type: "QPointF"; isReadonly: true }
        Property { name: "pointCount"; type: "int"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        name: "QParameterizableObject"
        defaultProperty: "quickChildren"
        prototype: "QObject"
        Property { name: "quickChildren"; type: "QObject"; isList: true; isReadonly: true }
    }
    Component {
        name: "QQmlPropertyMap"
        prototype: "QObject"
        exports: ["QtLocation/ExtendedAttributes 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Signal {
            name: "valueChanged"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method { name: "keys"; type: "QStringList" }
    }
    Component {
        name: "QQuickGeoMapGestureArea"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtLocation/MapGestureArea 5.0",
            "QtLocation/MapGestureArea 5.6"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "GeoMapGesture"
            values: {
                "NoGesture": 0,
                "PinchGesture": 1,
                "PanGesture": 2,
                "FlickGesture": 4,
                "RotationGesture": 8,
                "TiltGesture": 16
            }
        }
        Enum {
            name: "AcceptedGestures"
            values: {
                "NoGesture": 0,
                "PinchGesture": 1,
                "PanGesture": 2,
                "FlickGesture": 4,
                "RotationGesture": 8,
                "TiltGesture": 16
            }
        }
        Property { name: "enabled"; type: "bool" }
        Property { name: "pinchActive"; type: "bool"; isReadonly: true }
        Property { name: "panActive"; type: "bool"; isReadonly: true }
        Property { name: "rotationActive"; type: "bool"; isReadonly: true }
        Property { name: "tiltActive"; type: "bool"; isReadonly: true }
        Property { name: "acceptedGestures"; type: "AcceptedGestures" }
        Property { name: "maximumZoomLevelChange"; type: "double" }
        Property { name: "flickDeceleration"; type: "double" }
        Property { name: "preventStealing"; revision: 1; type: "bool" }
        Signal {
            name: "pinchStarted"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchUpdated"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchFinished"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal { name: "panStarted" }
        Signal { name: "panFinished" }
        Signal { name: "flickStarted" }
        Signal { name: "flickFinished" }
        Signal {
            name: "rotationStarted"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "rotationUpdated"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "rotationFinished"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "tiltStarted"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "tiltUpdated"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
        Signal {
            name: "tiltFinished"
            Parameter { name: "pinch"; type: "QGeoMapPinchEvent"; isPointer: true }
        }
    }
}
