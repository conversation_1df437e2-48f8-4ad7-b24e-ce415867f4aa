# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# إضافة المسار الحالي
sys.path.insert(0, os.path.abspath('.'))

# جمع البيانات والملفات المطلوبة
datas = []

# إضافة ملفات الأصول
datas += [('assets', 'assets')]

# إضافة ملفات GUI وCSS
datas += [('gui', 'gui')]

# إضافة ملفات قاعدة البيانات والإعدادات
datas += [('company_settings.json', '.')]
if os.path.exists('accounting.db'):
    datas += [('accounting.db', '.')]

# إضافة ملفات PyQt5
datas += collect_data_files('PyQt5')

# جمع الوحدات المخفية
hiddenimports = []
hiddenimports += collect_submodules('PyQt5')
hiddenimports += collect_submodules('sqlalchemy')
hiddenimports += collect_submodules('reportlab')
hiddenimports += collect_submodules('PIL')
hiddenimports += collect_submodules('openpyxl')
hiddenimports += collect_submodules('xlsxwriter')

# إضافة وحدات إضافية
hiddenimports += [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtPrintSupport',
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.pool',
    'reportlab.pdfgen',
    'reportlab.lib',
    'PIL.Image',
    'PIL.ImageQt',
    'openpyxl.workbook',
    'xlsxwriter.workbook',
    'database.users',
    'database.products',
    'database.customers',
    'database.suppliers',
    'database.transactions',
    'gui.main_window',
    'gui.login',
    'gui.initial_setup',
    'gui.sales',
    'gui.purchases',
    'gui.inventory',
    'gui.contacts',
    'gui.accounting',
    'gui.backup',
    'gui.notifications',
    'gui.audit_log',
    'gui.invoices_view',
    'gui.purchase_invoices_view',
    'gui.activation_dialog',
    'gui.modern_theme',
    'utils.advanced_invoice_printer',
    'utils.company_settings',
    'utils.theme_manager'
]

a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='نظام المحاسبة',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='نظام المحاسبة',
)
