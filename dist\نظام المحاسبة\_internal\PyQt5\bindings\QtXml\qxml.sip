// qxml.sip generated by MetaSIP
//
// This file is part of the QtXml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlNamespaceSupport
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlNamespaceSupport();
    ~QXmlNamespaceSupport();
    void setPrefix(const QString &, const QString &);
    QString prefix(const QString &) const;
    QString uri(const QString &) const;
    void splitName(const QString &, QString &, QString &) const;
    void processName(const QString &, bool, QString &, QString &) const;
    QStringList prefixes() const;
    QStringList prefixes(const QString &) const;
    void pushContext();
    void popContext();
    void reset();

private:
    QXmlNamespaceSupport(const QXmlNamespaceSupport &);
};

class QXmlAttributes
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlAttributes();
%If (Qt_5_8_0 -)
    QXmlAttributes(const QXmlAttributes &);
%End
    virtual ~QXmlAttributes();
    int index(const QString &qName) const;
    int index(const QString &uri, const QString &localPart) const;
    int length() const;
    QString localName(int index) const;
    QString qName(int index) const;
    QString uri(int index) const;
    QString type(int index) const;
    QString type(const QString &qName) const;
    QString type(const QString &uri, const QString &localName) const;
    QString value(int index) const;
    QString value(const QString &qName) const;
    QString value(const QString &uri, const QString &localName) const;
    void clear();
    void append(const QString &qName, const QString &uri, const QString &localPart, const QString &value);
    int count() const /__len__/;
%If (Qt_5_8_0 -)
    void swap(QXmlAttributes &other /Constrained/);
%End
};

class QXmlInputSource
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlInputSource();
    explicit QXmlInputSource(QIODevice *dev);
    virtual ~QXmlInputSource();
    virtual void setData(const QString &dat);
    virtual void setData(const QByteArray &dat);
    virtual void fetchData();
    virtual QString data() const;
    virtual QChar next();
    virtual void reset();
    static const ushort EndOfData;
    static const ushort EndOfDocument;

protected:
    virtual QString fromRawData(const QByteArray &data, bool beginning = false);
};

class QXmlParseException
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlParseException(const QString &name = QString(), int column = -1, int line = -1, const QString &publicId = QString(), const QString &systemId = QString());
    QXmlParseException(const QXmlParseException &other);
    ~QXmlParseException();
    int columnNumber() const;
    int lineNumber() const;
    QString publicId() const;
    QString systemId() const;
    QString message() const;

private:
    QXmlParseException &operator=(const QXmlParseException &);
};

class QXmlReader
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlReader();
    virtual bool feature(const QString &name, bool *ok = 0) const = 0;
    virtual void setFeature(const QString &name, bool value) = 0;
    virtual bool hasFeature(const QString &name) const = 0;
    virtual void *property(const QString &name, bool *ok = 0) const = 0;
    virtual void setProperty(const QString &name, void *value) = 0;
    virtual bool hasProperty(const QString &name) const = 0;
    virtual void setEntityResolver(QXmlEntityResolver *handler /KeepReference/) = 0;
    virtual QXmlEntityResolver *entityResolver() const = 0;
    virtual void setDTDHandler(QXmlDTDHandler *handler /KeepReference/) = 0;
    virtual QXmlDTDHandler *DTDHandler() const = 0;
    virtual void setContentHandler(QXmlContentHandler *handler /KeepReference/) = 0;
    virtual QXmlContentHandler *contentHandler() const = 0;
    virtual void setErrorHandler(QXmlErrorHandler *handler /KeepReference/) = 0;
    virtual QXmlErrorHandler *errorHandler() const = 0;
    virtual void setLexicalHandler(QXmlLexicalHandler *handler /KeepReference/) = 0;
    virtual QXmlLexicalHandler *lexicalHandler() const = 0;
    virtual void setDeclHandler(QXmlDeclHandler *handler /KeepReference/) = 0;
    virtual QXmlDeclHandler *declHandler() const = 0;
    virtual bool parse(const QXmlInputSource &input) = 0;
    virtual bool parse(const QXmlInputSource *input) = 0;
};

class QXmlSimpleReader : public QXmlReader
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlSimpleReader();
    virtual ~QXmlSimpleReader();
    virtual bool feature(const QString &name, bool *ok = 0) const;
    virtual void setFeature(const QString &name, bool value);
    virtual bool hasFeature(const QString &name) const;
    virtual void *property(const QString &name, bool *ok = 0) const;
    virtual void setProperty(const QString &name, void *value);
    virtual bool hasProperty(const QString &name) const;
    virtual void setEntityResolver(QXmlEntityResolver *handler /KeepReference/);
    virtual QXmlEntityResolver *entityResolver() const;
    virtual void setDTDHandler(QXmlDTDHandler *handler);
    virtual QXmlDTDHandler *DTDHandler() const;
    virtual void setContentHandler(QXmlContentHandler *handler /KeepReference/);
    virtual QXmlContentHandler *contentHandler() const;
    virtual void setErrorHandler(QXmlErrorHandler *handler /KeepReference/);
    virtual QXmlErrorHandler *errorHandler() const;
    virtual void setLexicalHandler(QXmlLexicalHandler *handler /KeepReference/);
    virtual QXmlLexicalHandler *lexicalHandler() const;
    virtual void setDeclHandler(QXmlDeclHandler *handler /KeepReference/);
    virtual QXmlDeclHandler *declHandler() const;
    virtual bool parse(const QXmlInputSource *input);
    virtual bool parse(const QXmlInputSource *input, bool incremental);
    virtual bool parseContinue();

private:
    QXmlSimpleReader(const QXmlSimpleReader &);
};

class QXmlLocator
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlLocator();
    virtual ~QXmlLocator();
    virtual int columnNumber() const = 0;
    virtual int lineNumber() const = 0;
};

class QXmlContentHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlContentHandler();
    virtual void setDocumentLocator(QXmlLocator *locator /KeepReference/) = 0;
    virtual bool startDocument() = 0;
    virtual bool endDocument() = 0;
    virtual bool startPrefixMapping(const QString &prefix, const QString &uri) = 0;
    virtual bool endPrefixMapping(const QString &prefix) = 0;
    virtual bool startElement(const QString &namespaceURI, const QString &localName, const QString &qName, const QXmlAttributes &atts) = 0;
    virtual bool endElement(const QString &namespaceURI, const QString &localName, const QString &qName) = 0;
    virtual bool characters(const QString &ch) = 0;
    virtual bool ignorableWhitespace(const QString &ch) = 0;
    virtual bool processingInstruction(const QString &target, const QString &data) = 0;
    virtual bool skippedEntity(const QString &name) = 0;
    virtual QString errorString() const = 0;
};

class QXmlErrorHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlErrorHandler();
    virtual bool warning(const QXmlParseException &exception) = 0;
    virtual bool error(const QXmlParseException &exception) = 0;
    virtual bool fatalError(const QXmlParseException &exception) = 0;
    virtual QString errorString() const = 0;
};

class QXmlDTDHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlDTDHandler();
    virtual bool notationDecl(const QString &name, const QString &publicId, const QString &systemId) = 0;
    virtual bool unparsedEntityDecl(const QString &name, const QString &publicId, const QString &systemId, const QString &notationName) = 0;
    virtual QString errorString() const = 0;
};

class QXmlEntityResolver
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlEntityResolver();
    virtual bool resolveEntity(const QString &publicId, const QString &systemId, QXmlInputSource *&ret) = 0;
    virtual QString errorString() const = 0;
};

class QXmlLexicalHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlLexicalHandler();
    virtual bool startDTD(const QString &name, const QString &publicId, const QString &systemId) = 0;
    virtual bool endDTD() = 0;
    virtual bool startEntity(const QString &name) = 0;
    virtual bool endEntity(const QString &name) = 0;
    virtual bool startCDATA() = 0;
    virtual bool endCDATA() = 0;
    virtual bool comment(const QString &ch) = 0;
    virtual QString errorString() const = 0;
};

class QXmlDeclHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    virtual ~QXmlDeclHandler();
    virtual bool attributeDecl(const QString &eName, const QString &aName, const QString &type, const QString &valueDefault, const QString &value) = 0;
    virtual bool internalEntityDecl(const QString &name, const QString &value) = 0;
    virtual bool externalEntityDecl(const QString &name, const QString &publicId, const QString &systemId) = 0;
    virtual QString errorString() const = 0;
};

class QXmlDefaultHandler : public QXmlContentHandler, public QXmlErrorHandler, public QXmlDTDHandler, public QXmlEntityResolver, public QXmlLexicalHandler, public QXmlDeclHandler
{
%TypeHeaderCode
#include <qxml.h>
%End

public:
    QXmlDefaultHandler();
    virtual ~QXmlDefaultHandler();
    virtual void setDocumentLocator(QXmlLocator *locator /KeepReference/);
    virtual bool startDocument();
    virtual bool endDocument();
    virtual bool startPrefixMapping(const QString &prefix, const QString &uri);
    virtual bool endPrefixMapping(const QString &prefix);
    virtual bool startElement(const QString &namespaceURI, const QString &localName, const QString &qName, const QXmlAttributes &atts);
    virtual bool endElement(const QString &namespaceURI, const QString &localName, const QString &qName);
    virtual bool characters(const QString &ch);
    virtual bool ignorableWhitespace(const QString &ch);
    virtual bool processingInstruction(const QString &target, const QString &data);
    virtual bool skippedEntity(const QString &name);
    virtual bool warning(const QXmlParseException &exception);
    virtual bool error(const QXmlParseException &exception);
    virtual bool fatalError(const QXmlParseException &exception);
    virtual bool notationDecl(const QString &name, const QString &publicId, const QString &systemId);
    virtual bool unparsedEntityDecl(const QString &name, const QString &publicId, const QString &systemId, const QString &notationName);
    virtual bool resolveEntity(const QString &publicId, const QString &systemId, QXmlInputSource *&ret);
    virtual bool startDTD(const QString &name, const QString &publicId, const QString &systemId);
    virtual bool endDTD();
    virtual bool startEntity(const QString &name);
    virtual bool endEntity(const QString &name);
    virtual bool startCDATA();
    virtual bool endCDATA();
    virtual bool comment(const QString &ch);
    virtual bool attributeDecl(const QString &eName, const QString &aName, const QString &type, const QString &valueDefault, const QString &value);
    virtual bool internalEntityDecl(const QString &name, const QString &value);
    virtual bool externalEntityDecl(const QString &name, const QString &publicId, const QString &systemId);
    virtual QString errorString() const;

private:
    QXmlDefaultHandler(const QXmlDefaultHandler &);
};
