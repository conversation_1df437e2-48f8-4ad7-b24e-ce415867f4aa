#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحديث قاعدة البيانات لدعم الباركودات المتعددة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from database.models import Base, ProductBarcode

def main():
    print("🔄 تحديث قاعدة البيانات لدعم الباركودات المتعددة")
    print("=" * 60)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("📊 إنشاء جدول الباركودات المتعددة...")
        
        # إنشاء الجداول الجديدة
        Base.metadata.create_all(engine)
        
        print("✅ تم إنشاء جدول product_barcodes بنجاح!")
        print("")
        print("📋 تفاصيل الجدول الجديد:")
        print("   • id: المعرف الفريد")
        print("   • product_id: معرف المنتج")
        print("   • barcode: الباركود")
        print("   • description: وصف الباركود")
        print("   • is_primary: هل هو الباركود الأساسي")
        print("   • created_at: تاريخ الإنشاء")
        print("   • updated_at: تاريخ التحديث")
        print("")
        
        # التحقق من وجود الجدول
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='product_barcodes'
            """))
            
            if result.fetchone():
                print("✅ تم التأكد من وجود الجدول في قاعدة البيانات")
                
                # عرض هيكل الجدول
                result = conn.execute(text("PRAGMA table_info(product_barcodes)"))
                columns = result.fetchall()
                
                print("")
                print("🏗️ هيكل جدول product_barcodes:")
                for col in columns:
                    print(f"   • {col[1]} ({col[2]})")
                
            else:
                print("❌ فشل في إنشاء الجدول")
                return
        
        print("")
        print("🎯 الخطوات التالية:")
        print("   1️⃣ شغل البرنامج الرئيسي")
        print("   2️⃣ اذهب لقسم 'المخزون'")
        print("   3️⃣ اضغط 'إضافة منتج جديد'")
        print("   4️⃣ ستجد 4 حقول للباركود:")
        print("      • الباركود الأساسي")
        print("      • باركود إضافي 1")
        print("      • باركود إضافي 2")
        print("      • باركود إضافي 3")
        print("   5️⃣ أدخل باركودات مختلفة لنفس المنتج")
        print("   6️⃣ احفظ المنتج")
        print("   7️⃣ جرب البحث بأي من الباركودات")
        print("")
        print("💡 فوائد الباركودات المتعددة:")
        print("   • منتج واحد بباركودات من موردين مختلفين")
        print("   • أحجام مختلفة لنفس المنتج")
        print("   • باركودات داخلية وخارجية")
        print("   • مرونة أكبر في إدارة المخزون")
        print("")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return
    
    print("🎉 تم تحديث قاعدة البيانات بنجاح!")
    print("=" * 60)

if __name__ == "__main__":
    main()
