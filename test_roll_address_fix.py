#!/usr/bin/env python3
"""
اختبار إصلاح عرض العنوان الكامل في طباعة الرول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("📍 اختبار إصلاح عرض العنوان الكامل في طباعة الرول...")
    print("=" * 80)
    print("🔧 المشكلة التي تم إصلاحها:")
    print("")
    print("   ❌ المشكلة الأصلية:")
    print("     • العنوان الطويل لا يظهر كاملاً")
    print("     • يتم قطع النص أو اختصاره بـ '...'")
    print("     • فقدان جزء من معلومات العنوان")
    print("")
    print("   ✅ الحل المطبق:")
    print("     • تقسيم العنوان الطويل إلى أسطر متعددة")
    print("     • حساب ذكي لعرض النص المتاح")
    print("     • عرض العنوان كاملاً بدون فقدان معلومات")
    print("     • تنسيق جميل مع أيقونة في السطر الأول فقط")
    print("")
    print("   🎯 النتيجة المتوقعة:")
    print("     📍 هوم سنتر للأدوات المنزلية")
    print("        كبر صفر - حي النصر - بجانب")
    print("        مسجد الرحمة")
    print("     📞 ***********")
    print("     📧 <EMAIL>")
    print("")
    print("   ✅ المزايا الجديدة:")
    print("     • عرض العنوان كاملاً بدون اختصار")
    print("     • تقسيم ذكي حسب عرض الرول")
    print("     • تنسيق جميل ومنظم")
    print("     • لا فقدان لأي معلومات")
    print("=" * 80)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 تشغيل النظام...")
        
        # اختبار الواجهة الرئيسية
        print("🖥️ تشغيل الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.engine = engine
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار إصلاح العنوان:")
        print("")
        print("   📋 طباعة فاتورة مبيعات:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض الفواتير المحفوظة'")
        print("     2️⃣ اختر أي فاتورة واضغط 'طباعة'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ لاحظ العنوان الآن:")
        print("        📍 هوم سنتر للأدوات المنزلية")
        print("           كبر صفر - حي النصر - بجانب")
        print("           مسجد الرحمة")
        print("        📞 ***********")
        print("")
        print("   🔄 طباعة فاتورة مرتجع:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض فواتير المرتجعات'")
        print("     2️⃣ اختر أي مرتجع واضغط '🖨️'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ لاحظ:")
        print("        • العنوان الكامل يظهر على أسطر متعددة")
        print("        • عنوان 'فاتورة مرتجع مبيعات' بالأحمر")
        print("        • جميع معلومات الشركة واضحة")
        print("")
        print("   🔍 ما تبحث عنه:")
        print("     ✅ العنوان الكامل:")
        print("        • السطر الأول: 📍 + بداية العنوان")
        print("        • الأسطر التالية: مسافات + باقي العنوان")
        print("        • لا يوجد '...' أو اختصار")
        print("        • جميع كلمات العنوان ظاهرة")
        print("     ✅ التنسيق:")
        print("        • أيقونة 📍 في السطر الأول فقط")
        print("        • مسافات في بداية الأسطر التالية")
        print("        • مسافات مناسبة بين الأسطر")
        print("        • ترتيب منطقي للمعلومات")
        print("")
        print("   💡 مقارنة قبل وبعد الإصلاح:")
        print("     🔴 قبل الإصلاح:")
        print("        📍 هوم سنتر للأدوات المنز...")
        print("        (العنوان مقطوع)")
        print("     🟢 بعد الإصلاح:")
        print("        📍 هوم سنتر للأدوات المنزلية")
        print("           كبر صفر - حي النصر - بجانب")
        print("           مسجد الرحمة")
        print("        (العنوان كامل)")
        print("")
        print("   🆕 إنشاء مرتجع جديد وطباعته:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'مرتجع المبيعات'")
        print("     2️⃣ ابحث عن فاتورة واختر منتجات")
        print("     3️⃣ اضغط 'حفظ وطباعة'")
        print("     4️⃣ اختر 'رول' واطبع")
        print("     5️⃣ تأكد من ظهور العنوان كاملاً")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 80)
    print("📊 ملخص الإصلاح:")
    print("")
    print("   🔧 ما تم تحسينه:")
    print("     ✅ تقسيم العنوان الطويل إلى أسطر متعددة")
    print("     ✅ حساب ذكي لعرض النص المتاح")
    print("     ✅ عرض العنوان كاملاً بدون فقدان")
    print("     ✅ تنسيق جميل مع الأيقونات")
    print("")
    print("   🎨 النتيجة النهائية:")
    print("     • العنوان يظهر كاملاً على أسطر متعددة")
    print("     • تنسيق جميل ومنظم")
    print("     • لا فقدان لأي معلومات")
    print("     • يتكيف مع عرض الرول تلقائياً")
    print("")
    print("   📋 يعمل مع:")
    print("     ✅ فواتير المبيعات العادية")
    print("     ✅ فواتير مرتجع المبيعات")
    print("     ✅ فواتير مرتجع المشتريات")
    print("     ✅ جميع أحجام الرول")
    print("")
    print("🎉 تم إصلاح مشكلة العنوان بنجاح!")
    print("   📱 جرب الطباعة الآن وستجد العنوان كاملاً")
    print("   🖨️ تقسيم ذكي للنصوص الطويلة")
    print("   🎯 عرض جميل ومنظم للمعلومات")

if __name__ == "__main__":
    main()
