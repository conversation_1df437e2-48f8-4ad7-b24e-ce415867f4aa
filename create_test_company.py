#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء إعدادات شركة تجريبية
"""

import json

# إعدادات الشركة التجريبية
test_company = {
    'company_name': 'شركة الأحلام للتجارة',
    'owner_name': 'أحمد محمد علي',
    'phone': '01234567890',
    'email': '<EMAIL>',
    'address': 'شارع النيل، المعادي، القاهرة، مصر',
    'logo_path': None,
    'setup_completed': True
}

# حفظ الإعدادات
with open('company_settings.json', 'w', encoding='utf-8') as f:
    json.dump(test_company, f, ensure_ascii=False, indent=4)

print("✅ تم إنشاء إعدادات الشركة التجريبية!")
print(f"🏢 اسم الشركة: {test_company['company_name']}")
print(f"👤 المالك: {test_company['owner_name']}")
