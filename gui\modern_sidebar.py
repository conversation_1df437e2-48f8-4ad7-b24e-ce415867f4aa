from PyQt5.QtWidgets import QFrame, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt

class ModernSidebar(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("modernSidebar")
        self.setFixedWidth(80)
        self.setStyleSheet("""
            QFrame#modernSidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(26, 32, 44, 0.95),
                            stop:0.5 rgba(45, 55, 72, 0.95),
                            stop:1 rgba(26, 32, 44, 0.95));
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
            }
            QFrame#modernSidebar:hover {
                width: 280px;
            }
            QLabel {
                opacity: 0;
                transition: opacity 0.3s;
            }
            QFrame#modernSidebar:hover QLabel {
                opacity: 1;
            }
        """)

        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 30, 10, 30)
        self.layout.setSpacing(20)

        self.setup_header()
        self.setup_navigation()
        self.setup_footer()

    def setup_header(self):
        header = QFrame()
        header_layout = QVBoxLayout(header)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)

        # شعار الشركة
        logo = QLabel("💼")
        logo.setStyleSheet("""
            font-size: 48px;
            color: #667eea;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(102, 126, 234, 0.2),
                        stop:1 rgba(118, 75, 162, 0.2));
            border-radius: 25px;
            padding: 15px;
            min-width: 60px;
            min-height: 60px;
        """)
        logo.setAlignment(Qt.AlignCenter)

        # اسم النظام
        title = QLabel("نظام المحاسبة")
        title.setStyleSheet("""
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin: 10px 0;
        """)
        title.setAlignment(Qt.AlignCenter)

        # نسخة النظام
        version = QLabel("الإصدار 2024")
        version.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        """)
        version.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(logo)
        header_layout.addWidget(title)
        header_layout.addWidget(version)

        self.layout.addWidget(header)

    def setup_navigation(self):
        nav_frame = QFrame()
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setSpacing(8)

        # عناصر القائمة
        menu_items = [
            ("🏠", "الصفحة الرئيسية"),
            ("📄", "فاتورة جديدة"),
            ("📋", "عرض الفواتير"),
            ("🔄", "المرتجعات"),
            ("🛒", "المشتريات"),
            ("📦", "المخزون"),
            ("👥", "العملاء والموردين"),
            ("📊", "التقارير"),
            ("⚙️", "الإعدادات"),
        ]

        for icon, text in menu_items:
            btn = self.create_nav_button(icon, text)
            nav_layout.addWidget(btn)

        self.layout.addWidget(nav_frame)
        self.layout.addStretch()

    def create_nav_button(self, icon, text):
        btn = QPushButton()
        btn.setObjectName("navButton")

        layout = QHBoxLayout()
        layout.setContentsMargins(10, 12, 10, 12)
        layout.setSpacing(15)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 18px; color: white;")
        icon_label.setFixedSize(24, 24)
        icon_label.setAlignment(Qt.AlignCenter)

        text_label = QLabel(text)
        text_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: white;
        """)

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()

        btn.setLayout(layout)
        btn.setFixedHeight(50)
        btn.setStyleSheet("""
            QPushButton#navButton {
                background: transparent;
                border: none;
                border-radius: 12px;
            }
            QPushButton#navButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            QPushButton#navButton:pressed {
                background: rgba(255, 255, 255, 0.2);
            }
        """)

        return btn

    def setup_footer(self):
        footer = QFrame()
        footer_layout = QVBoxLayout(footer)
        footer_layout.setAlignment(Qt.AlignCenter)

        user_info = QLabel("👤 المستخدم")
        user_info.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        """)
        user_info.setAlignment(Qt.AlignCenter)

        footer_layout.addWidget(user_info)
        self.layout.addWidget(footer) 