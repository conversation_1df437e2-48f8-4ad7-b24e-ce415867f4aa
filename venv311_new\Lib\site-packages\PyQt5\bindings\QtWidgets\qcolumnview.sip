// qcolumnview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QColumnView : QAbstractItemView
{
%TypeHeaderCode
#include <qcolumnview.h>
%End

public:
    explicit QColumnView(QWidget *parent /TransferThis/ = 0);
    virtual ~QColumnView();
    QList<int> columnWidths() const;
    QWidget *previewWidget() const;
    bool resizeGripsVisible() const;
    void setColumnWidths(const QList<int> &list);
    void setPreviewWidget(QWidget *widget /Transfer/);
    void setResizeGripsVisible(bool visible);
    virtual QModelIndex indexAt(const QPoint &point) const;
    virtual void scrollTo(const QModelIndex &index, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);
    virtual QSize sizeHint() const;
    virtual QRect visualRect(const QModelIndex &index) const;
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
    virtual void setSelectionModel(QItemSelectionModel *selectionModel /KeepReference/);
    virtual void setRootIndex(const QModelIndex &index);
    virtual void selectAll();

signals:
    void updatePreviewWidget(const QModelIndex &index);

protected:
    virtual QAbstractItemView *createColumn(const QModelIndex &rootIndex);
    void initializeColumn(QAbstractItemView *column) const;
    virtual bool isIndexHidden(const QModelIndex &index) const;
    virtual QModelIndex moveCursor(QAbstractItemView::CursorAction cursorAction, Qt::KeyboardModifiers modifiers);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void setSelection(const QRect &rect, QItemSelectionModel::SelectionFlags command);
    virtual QRegion visualRegionForSelection(const QItemSelection &selection) const;
    virtual int horizontalOffset() const;
    virtual int verticalOffset() const;
    virtual void scrollContentsBy(int dx, int dy);
    virtual void rowsInserted(const QModelIndex &parent, int start, int end);

protected slots:
    virtual void currentChanged(const QModelIndex &current, const QModelIndex &previous);
};
