#!/usr/bin/env python3
"""
اختبار الطباعة المحسنة مع الحفاظ على الألوان والتصميم
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestEnhancedPrintWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار الطباعة المحسنة")
        self.setGeometry(100, 100, 600, 500)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_label = QPushButton("🎨 اختبار الطباعة المحسنة")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
                color: white;
                font-size: 22px;
                font-weight: bold;
                padding: 25px;
                border: none;
                border-radius: 12px;
                margin-bottom: 25px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات التحسينات
        info_label = QPushButton("""
🎨 التحسينات الجديدة للطباعة:

✅ إضافة -webkit-print-color-adjust: exact
✅ إضافة print-color-adjust: exact  
✅ تحسين CSS للطباعة الملونة
✅ الحفاظ على التدرجات اللونية
✅ الحفاظ على الخلفيات الملونة
✅ تحسين عرض الجداول والحدود
✅ تحسين الأيقونات والرموز التعبيرية
        """)
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
                color: #2c3e50;
                font-size: 14px;
                padding: 20px;
                border: 2px solid #27ae60;
                border-radius: 10px;
                text-align: left;
                margin: 10px 0;
                line-height: 1.6;
            }
        """)
        layout.addWidget(info_label)
        
        # زر اختبار الفاتورة رقم 1
        test_btn1 = QPushButton("🧾 اختبار الطباعة المحسنة - فاتورة رقم 1")
        test_btn1.setStyleSheet(self.get_button_style("#27AE60"))
        test_btn1.clicked.connect(lambda: self.test_enhanced_printing(1))
        layout.addWidget(test_btn1)
        
        # زر اختبار الفاتورة رقم 7
        test_btn7 = QPushButton("🧾 اختبار الطباعة المحسنة - فاتورة رقم 7")
        test_btn7.setStyleSheet(self.get_button_style("#3498DB"))
        test_btn7.clicked.connect(lambda: self.test_enhanced_printing(7))
        layout.addWidget(test_btn7)
        
        # زر مقارنة قبل وبعد
        compare_btn = QPushButton("🔄 مقارنة التصميم قبل وبعد التحسين")
        compare_btn.setStyleSheet(self.get_button_style("#E67E22"))
        compare_btn.clicked.connect(self.show_comparison)
        layout.addWidget(compare_btn)
        
        # تعليمات الاختبار
        instructions_label = QPushButton("""
📋 تعليمات الاختبار:

1️⃣ اضغط على زر "اختبار الطباعة المحسنة"
2️⃣ ستظهر نافذة الطباعة الجديدة
3️⃣ لاحظ الألوان الجميلة في المعاينة
4️⃣ جرب "حفظ PDF" - ستجد الألوان محفوظة!
5️⃣ جرب "طباعة" - ستطبع بالألوان الكاملة!

🎯 النتيجة المتوقعة:
• ترويسة خضراء جميلة
• جدول منتجات أخضر متدرج  
• تفاصيل فاتورة زرقاء أنيقة
• أيقونات ملونة واضحة
        """)
        instructions_label.setEnabled(False)
        instructions_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                color: #856404;
                font-size: 13px;
                padding: 15px;
                border: 2px solid #ffc107;
                border-radius: 8px;
                text-align: left;
                margin: 10px 0;
                line-height: 1.5;
            }
        """)
        layout.addWidget(instructions_label)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#E74C3C"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def get_button_style(self, color):
        """إرجاع نمط الأزرار"""
        return f"""
            QPushButton {{
                background: linear-gradient(135deg, {color} 0%, {color}DD 100%);
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 18px;
                border: none;
                border-radius: 10px;
                min-height: 60px;
                margin: 8px;
            }}
            QPushButton:hover {{
                background: linear-gradient(135deg, {color}EE 0%, {color}CC 100%);
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: linear-gradient(135deg, {color}BB 0%, {color}AA 100%);
            }}
        """
    
    def test_enhanced_printing(self, invoice_id):
        """اختبار الطباعة المحسنة"""
        try:
            from utils.new_design_invoice_printer import show_new_design_print_dialog
            show_new_design_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار الطباعة:\n{str(e)}")
    
    def show_comparison(self):
        """عرض مقارنة قبل وبعد"""
        comparison_text = """
🔄 مقارنة التصميم:

❌ قبل التحسين:
• ألوان باهتة أو مفقودة في PDF
• تصميم أبيض وأسود عند الطباعة
• فقدان التدرجات اللونية
• جداول بسيطة بدون ألوان

✅ بعد التحسين:
• ألوان زاهية ومحفوظة في PDF
• طباعة ملونة كاملة
• تدرجات لونية جميلة
• جداول خضراء وزرقاء أنيقة
• أيقونات ملونة واضحة

🎯 التقنيات المستخدمة:
• -webkit-print-color-adjust: exact
• print-color-adjust: exact
• تحسين CSS للطباعة
• تحسين الألوان والخلفيات
        """
        
        QMessageBox.information(self, "مقارنة التصميم", comparison_text)

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم عام
    app.setStyleSheet("""
        QMainWindow {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        QWidget {
            font-family: 'Arial', 'Tahoma', sans-serif;
        }
    """)
    
    print("🎨 بدء اختبار الطباعة المحسنة...")
    print("=" * 60)
    print("✅ تم تطبيق التحسينات التالية:")
    print("   • إضافة -webkit-print-color-adjust: exact")
    print("   • إضافة print-color-adjust: exact")
    print("   • تحسين CSS للطباعة الملونة")
    print("   • الحفاظ على التدرجات اللونية")
    print("=" * 60)
    print("🎯 افتح النافذة لاختبار الطباعة المحسنة")
    
    window = TestEnhancedPrintWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
