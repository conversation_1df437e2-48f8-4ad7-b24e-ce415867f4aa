import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtMultimedia 5.15'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: {
                "NoLayoutChangeHint": 0,
                "VerticalSortHint": 1,
                "HorizontalSortHint": 2
            }
        }
        Enum {
            name: "CheckIndexOption"
            values: {
                "NoOption": 0,
                "IndexIsValid": 1,
                "DoNotUseParent": 2,
                "ParentIsInvalid": 4
            }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractListModel"; prototype: "QAbstractItemModel" }
    Component {
        name: "QAbstractVideoFilter"
        prototype: "QObject"
        Property { name: "active"; type: "bool" }
    }
    Component {
        name: "QAbstractVideoSurface"
        prototype: "QObject"
        Property { name: "nativeResolution"; type: "QSize"; isReadonly: true }
        Signal {
            name: "activeChanged"
            Parameter { name: "active"; type: "bool" }
        }
        Signal {
            name: "surfaceFormatChanged"
            Parameter { name: "format"; type: "QVideoSurfaceFormat" }
        }
        Signal { name: "supportedFormatsChanged" }
        Signal {
            name: "nativeResolutionChanged"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        name: "QCamera"
        prototype: "QMediaObject"
        Enum {
            name: "Status"
            values: {
                "UnavailableStatus": 0,
                "UnloadedStatus": 1,
                "LoadingStatus": 2,
                "UnloadingStatus": 3,
                "LoadedStatus": 4,
                "StandbyStatus": 5,
                "StartingStatus": 6,
                "StoppingStatus": 7,
                "ActiveStatus": 8
            }
        }
        Enum {
            name: "State"
            values: {
                "UnloadedState": 0,
                "LoadedState": 1,
                "ActiveState": 2
            }
        }
        Enum {
            name: "CaptureMode"
            values: {
                "CaptureViewfinder": 0,
                "CaptureStillImage": 1,
                "CaptureVideo": 2
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "CameraError": 1,
                "InvalidRequestError": 2,
                "ServiceMissingError": 3,
                "NotSupportedFeatureError": 4
            }
        }
        Enum {
            name: "LockStatus"
            values: {
                "Unlocked": 0,
                "Searching": 1,
                "Locked": 2
            }
        }
        Enum {
            name: "LockChangeReason"
            values: {
                "UserRequest": 0,
                "LockAcquired": 1,
                "LockFailed": 2,
                "LockLost": 3,
                "LockTemporaryLost": 4
            }
        }
        Enum {
            name: "LockType"
            values: {
                "NoLock": 0,
                "LockExposure": 1,
                "LockWhiteBalance": 2,
                "LockFocus": 4
            }
        }
        Enum {
            name: "Position"
            values: {
                "UnspecifiedPosition": 0,
                "BackFace": 1,
                "FrontFace": 2
            }
        }
        Property { name: "state"; type: "QCamera::State"; isReadonly: true }
        Property { name: "status"; type: "QCamera::Status"; isReadonly: true }
        Property { name: "captureMode"; type: "QCamera::CaptureModes" }
        Property { name: "lockStatus"; type: "QCamera::LockStatus"; isReadonly: true }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "QCamera::State" }
        }
        Signal {
            name: "captureModeChanged"
            Parameter { type: "QCamera::CaptureModes" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "QCamera::Status" }
        }
        Signal { name: "locked" }
        Signal { name: "lockFailed" }
        Signal {
            name: "lockStatusChanged"
            Parameter { name: "status"; type: "QCamera::LockStatus" }
            Parameter { name: "reason"; type: "QCamera::LockChangeReason" }
        }
        Signal {
            name: "lockStatusChanged"
            Parameter { name: "lock"; type: "QCamera::LockType" }
            Parameter { name: "status"; type: "QCamera::LockStatus" }
            Parameter { name: "reason"; type: "QCamera::LockChangeReason" }
        }
        Signal {
            name: "error"
            Parameter { type: "QCamera::Error" }
        }
        Method {
            name: "setCaptureMode"
            Parameter { name: "mode"; type: "QCamera::CaptureModes" }
        }
        Method { name: "load" }
        Method { name: "unload" }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "searchAndLock" }
        Method { name: "unlock" }
        Method {
            name: "searchAndLock"
            Parameter { name: "locks"; type: "QCamera::LockTypes" }
        }
        Method {
            name: "unlock"
            Parameter { name: "locks"; type: "QCamera::LockTypes" }
        }
    }
    Component {
        name: "QDeclarativeAudio"
        prototype: "QObject"
        exports: [
            "QtMultimedia/Audio 5.0",
            "QtMultimedia/Audio 5.11",
            "QtMultimedia/Audio 5.6",
            "QtMultimedia/Audio 5.9",
            "QtMultimedia/MediaPlayer 5.0",
            "QtMultimedia/MediaPlayer 5.11",
            "QtMultimedia/MediaPlayer 5.15",
            "QtMultimedia/MediaPlayer 5.6",
            "QtMultimedia/MediaPlayer 5.9"
        ]
        exportMetaObjectRevisions: [0, 3, 1, 2, 0, 3, 15, 1, 2]
        Enum {
            name: "Status"
            values: {
                "UnknownStatus": 0,
                "NoMedia": 1,
                "Loading": 2,
                "Loaded": 3,
                "Stalled": 4,
                "Buffering": 5,
                "Buffered": 6,
                "EndOfMedia": 7,
                "InvalidMedia": 8
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "ResourceError": 1,
                "FormatError": 2,
                "NetworkError": 3,
                "AccessDenied": 4,
                "ServiceMissing": 5
            }
        }
        Enum {
            name: "Loop"
            values: {
                "Infinite": -1
            }
        }
        Enum {
            name: "PlaybackState"
            values: {
                "PlayingState": 1,
                "PausedState": 2,
                "StoppedState": 0
            }
        }
        Enum {
            name: "Availability"
            values: {
                "Available": 0,
                "Busy": 2,
                "Unavailable": 1,
                "ResourceMissing": 3
            }
        }
        Enum {
            name: "AudioRole"
            values: {
                "UnknownRole": 0,
                "AccessibilityRole": 7,
                "AlarmRole": 4,
                "CustomRole": 10,
                "GameRole": 9,
                "MusicRole": 1,
                "NotificationRole": 5,
                "RingtoneRole": 6,
                "SonificationRole": 8,
                "VideoRole": 2,
                "VoiceCommunicationRole": 3
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "playlist"; revision: 1; type: "QDeclarativePlaylist"; isPointer: true }
        Property { name: "loops"; type: "int" }
        Property { name: "playbackState"; type: "PlaybackState"; isReadonly: true }
        Property { name: "autoPlay"; type: "bool" }
        Property { name: "autoLoad"; type: "bool" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "duration"; type: "int"; isReadonly: true }
        Property { name: "position"; type: "int"; isReadonly: true }
        Property { name: "volume"; type: "double" }
        Property { name: "muted"; type: "bool" }
        Property { name: "hasAudio"; type: "bool"; isReadonly: true }
        Property { name: "hasVideo"; type: "bool"; isReadonly: true }
        Property { name: "bufferProgress"; type: "double"; isReadonly: true }
        Property { name: "seekable"; type: "bool"; isReadonly: true }
        Property { name: "playbackRate"; type: "double" }
        Property { name: "error"; type: "Error"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property {
            name: "metaData"
            type: "QDeclarativeMediaMetaData"
            isReadonly: true
            isPointer: true
        }
        Property { name: "mediaObject"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "availability"; type: "Availability"; isReadonly: true }
        Property { name: "audioRole"; revision: 1; type: "AudioRole" }
        Property { name: "customAudioRole"; revision: 3; type: "string" }
        Property { name: "notifyInterval"; revision: 2; type: "int" }
        Property { name: "videoOutput"; revision: 15; type: "QVariant" }
        Signal { name: "playlistChanged"; revision: 1 }
        Signal { name: "loopCountChanged" }
        Signal { name: "paused" }
        Signal { name: "stopped" }
        Signal { name: "playing" }
        Signal { name: "audioRoleChanged"; revision: 1 }
        Signal { name: "customAudioRoleChanged"; revision: 3 }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "availability"; type: "Availability" }
        }
        Signal {
            name: "error"
            Parameter { name: "error"; type: "QDeclarativeAudio::Error" }
            Parameter { name: "errorString"; type: "string" }
        }
        Signal { name: "notifyIntervalChanged"; revision: 2 }
        Signal { name: "videoOutputChanged"; revision: 15 }
        Method { name: "play" }
        Method { name: "pause" }
        Method { name: "stop" }
        Method {
            name: "seek"
            Parameter { name: "position"; type: "int" }
        }
        Method { name: "supportedAudioRoles"; revision: 1; type: "QJSValue" }
    }
    Component {
        name: "QDeclarativeCamera"
        prototype: "QObject"
        exports: [
            "QtMultimedia/Camera 5.0",
            "QtMultimedia/Camera 5.4",
            "QtMultimedia/Camera 5.5"
        ]
        exportMetaObjectRevisions: [0, 1, 2]
        Enum {
            name: "Position"
            values: {
                "UnspecifiedPosition": 0,
                "BackFace": 1,
                "FrontFace": 2
            }
        }
        Enum {
            name: "CaptureMode"
            values: {
                "CaptureViewfinder": 0,
                "CaptureStillImage": 1,
                "CaptureVideo": 2
            }
        }
        Enum {
            name: "State"
            values: {
                "ActiveState": 2,
                "LoadedState": 1,
                "UnloadedState": 0
            }
        }
        Enum {
            name: "Status"
            values: {
                "UnavailableStatus": 0,
                "UnloadedStatus": 1,
                "LoadingStatus": 2,
                "UnloadingStatus": 3,
                "LoadedStatus": 4,
                "StandbyStatus": 5,
                "StartingStatus": 6,
                "StoppingStatus": 7,
                "ActiveStatus": 8
            }
        }
        Enum {
            name: "LockStatus"
            values: {
                "Unlocked": 0,
                "Searching": 1,
                "Locked": 2
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "CameraError": 1,
                "InvalidRequestError": 2,
                "ServiceMissingError": 3,
                "NotSupportedFeatureError": 4
            }
        }
        Enum {
            name: "FlashMode"
            values: {
                "FlashAuto": 1,
                "FlashOff": 2,
                "FlashOn": 4,
                "FlashRedEyeReduction": 8,
                "FlashFill": 16,
                "FlashTorch": 32,
                "FlashVideoLight": 64,
                "FlashSlowSyncFrontCurtain": 128,
                "FlashSlowSyncRearCurtain": 256,
                "FlashManual": 512
            }
        }
        Enum {
            name: "ExposureMode"
            values: {
                "ExposureAuto": 0,
                "ExposureManual": 1,
                "ExposurePortrait": 2,
                "ExposureNight": 3,
                "ExposureBacklight": 4,
                "ExposureSpotlight": 5,
                "ExposureSports": 6,
                "ExposureSnow": 7,
                "ExposureBeach": 8,
                "ExposureLargeAperture": 9,
                "ExposureSmallAperture": 10,
                "ExposureAction": 11,
                "ExposureLandscape": 12,
                "ExposureNightPortrait": 13,
                "ExposureTheatre": 14,
                "ExposureSunset": 15,
                "ExposureSteadyPhoto": 16,
                "ExposureFireworks": 17,
                "ExposureParty": 18,
                "ExposureCandlelight": 19,
                "ExposureBarcode": 20,
                "ExposureModeVendor": 1000
            }
        }
        Enum {
            name: "MeteringMode"
            values: {
                "MeteringMatrix": 1,
                "MeteringAverage": 2,
                "MeteringSpot": 3
            }
        }
        Enum {
            name: "FocusMode"
            values: {
                "FocusManual": 1,
                "FocusHyperfocal": 2,
                "FocusInfinity": 4,
                "FocusAuto": 8,
                "FocusContinuous": 16,
                "FocusMacro": 32
            }
        }
        Enum {
            name: "FocusPointMode"
            values: {
                "FocusPointAuto": 0,
                "FocusPointCenter": 1,
                "FocusPointFaceDetection": 2,
                "FocusPointCustom": 3
            }
        }
        Enum {
            name: "FocusAreaStatus"
            values: {
                "FocusAreaUnused": 1,
                "FocusAreaSelected": 2,
                "FocusAreaFocused": 3
            }
        }
        Enum {
            name: "Availability"
            values: {
                "Available": 0,
                "Busy": 2,
                "Unavailable": 1,
                "ResourceMissing": 3
            }
        }
        Property { name: "deviceId"; revision: 1; type: "string" }
        Property { name: "position"; revision: 1; type: "Position" }
        Property { name: "displayName"; revision: 1; type: "string"; isReadonly: true }
        Property { name: "orientation"; revision: 1; type: "int"; isReadonly: true }
        Property { name: "captureMode"; type: "CaptureMode" }
        Property { name: "cameraState"; type: "State" }
        Property { name: "cameraStatus"; type: "Status"; isReadonly: true }
        Property { name: "lockStatus"; type: "LockStatus"; isReadonly: true }
        Property { name: "errorCode"; type: "Error"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "availability"; type: "Availability"; isReadonly: true }
        Property { name: "opticalZoom"; type: "double" }
        Property { name: "maximumOpticalZoom"; type: "double"; isReadonly: true }
        Property { name: "digitalZoom"; type: "double" }
        Property { name: "maximumDigitalZoom"; type: "double"; isReadonly: true }
        Property { name: "mediaObject"; type: "QObject"; isReadonly: true; isPointer: true }
        Property {
            name: "imageCapture"
            type: "QDeclarativeCameraCapture"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "videoRecorder"
            type: "QDeclarativeCameraRecorder"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "exposure"
            type: "QDeclarativeCameraExposure"
            isReadonly: true
            isPointer: true
        }
        Property { name: "flash"; type: "QDeclarativeCameraFlash"; isReadonly: true; isPointer: true }
        Property { name: "focus"; type: "QDeclarativeCameraFocus"; isReadonly: true; isPointer: true }
        Property {
            name: "imageProcessing"
            type: "QDeclarativeCameraImageProcessing"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "metaData"
            revision: 1
            type: "QDeclarativeMediaMetaData"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "viewfinder"
            revision: 1
            type: "QDeclarativeCameraViewfinder"
            isReadonly: true
            isPointer: true
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QDeclarativeCamera::Error" }
            Parameter { name: "errorString"; type: "string" }
        }
        Signal { name: "deviceIdChanged"; revision: 1 }
        Signal { name: "positionChanged"; revision: 1 }
        Signal { name: "displayNameChanged"; revision: 1 }
        Signal { name: "orientationChanged"; revision: 1 }
        Signal {
            name: "cameraStateChanged"
            Parameter { type: "QDeclarativeCamera::State" }
        }
        Signal {
            name: "opticalZoomChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "digitalZoomChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "maximumOpticalZoomChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "maximumDigitalZoomChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "availability"; type: "Availability" }
        }
        Method {
            name: "setCaptureMode"
            Parameter { name: "mode"; type: "CaptureMode" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "setCameraState"
            Parameter { name: "state"; type: "State" }
        }
        Method { name: "searchAndLock" }
        Method { name: "unlock" }
        Method {
            name: "setOpticalZoom"
            Parameter { type: "double" }
        }
        Method {
            name: "setDigitalZoom"
            Parameter { type: "double" }
        }
        Method {
            name: "supportedViewfinderResolutions"
            revision: 2
            type: "QJSValue"
            Parameter { name: "minimumFrameRate"; type: "double" }
            Parameter { name: "maximumFrameRate"; type: "double" }
        }
        Method {
            name: "supportedViewfinderResolutions"
            revision: 2
            type: "QJSValue"
            Parameter { name: "minimumFrameRate"; type: "double" }
        }
        Method { name: "supportedViewfinderResolutions"; revision: 2; type: "QJSValue" }
        Method {
            name: "supportedViewfinderFrameRateRanges"
            revision: 2
            type: "QJSValue"
            Parameter { name: "resolution"; type: "QJSValue" }
        }
        Method { name: "supportedViewfinderFrameRateRanges"; revision: 2; type: "QJSValue" }
    }
    Component {
        name: "QDeclarativeCameraCapture"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CameraCapture 5.0",
            "QtMultimedia/CameraCapture 5.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Property { name: "ready"; type: "bool"; isReadonly: true }
        Property { name: "capturedImagePath"; type: "string"; isReadonly: true }
        Property { name: "resolution"; type: "QSize" }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "supportedResolutions"; revision: 1; type: "QVariantList"; isReadonly: true }
        Signal {
            name: "readyForCaptureChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "imageExposed"
            Parameter { name: "requestId"; type: "int" }
        }
        Signal {
            name: "imageCaptured"
            Parameter { name: "requestId"; type: "int" }
            Parameter { name: "preview"; type: "string" }
        }
        Signal {
            name: "imageMetadataAvailable"
            Parameter { name: "requestId"; type: "int" }
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal {
            name: "imageSaved"
            Parameter { name: "requestId"; type: "int" }
            Parameter { name: "path"; type: "string" }
        }
        Signal {
            name: "captureFailed"
            Parameter { name: "requestId"; type: "int" }
            Parameter { name: "message"; type: "string" }
        }
        Signal {
            name: "resolutionChanged"
            Parameter { type: "QSize" }
        }
        Method { name: "capture"; type: "int" }
        Method {
            name: "captureToLocation"
            type: "int"
            Parameter { name: "location"; type: "string" }
        }
        Method { name: "cancelCapture" }
        Method {
            name: "setResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setMetadata"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        name: "QDeclarativeCameraExposure"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CameraExposure 5.0",
            "QtMultimedia/CameraExposure 5.11"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "ExposureMode"
            values: {
                "ExposureAuto": 0,
                "ExposureManual": 1,
                "ExposurePortrait": 2,
                "ExposureNight": 3,
                "ExposureBacklight": 4,
                "ExposureSpotlight": 5,
                "ExposureSports": 6,
                "ExposureSnow": 7,
                "ExposureBeach": 8,
                "ExposureLargeAperture": 9,
                "ExposureSmallAperture": 10,
                "ExposureAction": 11,
                "ExposureLandscape": 12,
                "ExposureNightPortrait": 13,
                "ExposureTheatre": 14,
                "ExposureSunset": 15,
                "ExposureSteadyPhoto": 16,
                "ExposureFireworks": 17,
                "ExposureParty": 18,
                "ExposureCandlelight": 19,
                "ExposureBarcode": 20,
                "ExposureModeVendor": 1000
            }
        }
        Enum {
            name: "MeteringMode"
            values: {
                "MeteringMatrix": 1,
                "MeteringAverage": 2,
                "MeteringSpot": 3
            }
        }
        Property { name: "exposureCompensation"; type: "double" }
        Property { name: "iso"; type: "int"; isReadonly: true }
        Property { name: "shutterSpeed"; type: "double"; isReadonly: true }
        Property { name: "aperture"; type: "double"; isReadonly: true }
        Property { name: "manualShutterSpeed"; type: "double" }
        Property { name: "manualAperture"; type: "double" }
        Property { name: "manualIso"; type: "double" }
        Property { name: "exposureMode"; type: "ExposureMode" }
        Property { name: "supportedExposureModes"; revision: 1; type: "QVariantList"; isReadonly: true }
        Property { name: "spotMeteringPoint"; type: "QPointF" }
        Property { name: "meteringMode"; type: "MeteringMode" }
        Signal {
            name: "isoSensitivityChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "apertureChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "shutterSpeedChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "manualIsoSensitivityChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "manualApertureChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "manualShutterSpeedChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "exposureCompensationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "exposureModeChanged"
            Parameter { type: "ExposureMode" }
        }
        Signal {
            name: "meteringModeChanged"
            Parameter { type: "MeteringMode" }
        }
        Signal {
            name: "spotMeteringPointChanged"
            Parameter { type: "QPointF" }
        }
        Method {
            name: "setExposureMode"
            Parameter { type: "ExposureMode" }
        }
        Method {
            name: "setExposureCompensation"
            Parameter { name: "ev"; type: "double" }
        }
        Method {
            name: "setManualAperture"
            Parameter { type: "double" }
        }
        Method {
            name: "setManualShutterSpeed"
            Parameter { type: "double" }
        }
        Method {
            name: "setManualIsoSensitivity"
            Parameter { name: "iso"; type: "int" }
        }
        Method { name: "setAutoAperture" }
        Method { name: "setAutoShutterSpeed" }
        Method { name: "setAutoIsoSensitivity" }
    }
    Component {
        name: "QDeclarativeCameraFlash"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CameraFlash 5.0",
            "QtMultimedia/CameraFlash 5.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "FlashMode"
            values: {
                "FlashAuto": 1,
                "FlashOff": 2,
                "FlashOn": 4,
                "FlashRedEyeReduction": 8,
                "FlashFill": 16,
                "FlashTorch": 32,
                "FlashVideoLight": 64,
                "FlashSlowSyncFrontCurtain": 128,
                "FlashSlowSyncRearCurtain": 256,
                "FlashManual": 512
            }
        }
        Property { name: "ready"; type: "bool"; isReadonly: true }
        Property { name: "mode"; type: "FlashMode" }
        Property { name: "supportedModes"; revision: 1; type: "QVariantList"; isReadonly: true }
        Signal {
            name: "flashReady"
            Parameter { name: "status"; type: "bool" }
        }
        Signal {
            name: "flashModeChanged"
            Parameter { type: "FlashMode" }
        }
        Method {
            name: "setFlashMode"
            Parameter { type: "FlashMode" }
        }
    }
    Component {
        name: "QDeclarativeCameraFocus"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CameraFocus 5.0",
            "QtMultimedia/CameraFocus 5.11"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "FocusMode"
            values: {
                "FocusManual": 1,
                "FocusHyperfocal": 2,
                "FocusInfinity": 4,
                "FocusAuto": 8,
                "FocusContinuous": 16,
                "FocusMacro": 32
            }
        }
        Enum {
            name: "FocusPointMode"
            values: {
                "FocusPointAuto": 0,
                "FocusPointCenter": 1,
                "FocusPointFaceDetection": 2,
                "FocusPointCustom": 3
            }
        }
        Property { name: "focusMode"; type: "FocusMode" }
        Property { name: "supportedFocusModes"; revision: 1; type: "QVariantList"; isReadonly: true }
        Property { name: "focusPointMode"; type: "FocusPointMode" }
        Property { name: "supportedFocusPointModes"; revision: 1; type: "QVariantList"; isReadonly: true }
        Property { name: "customFocusPoint"; type: "QPointF" }
        Property { name: "focusZones"; type: "QObject"; isReadonly: true; isPointer: true }
        Signal {
            name: "focusModeChanged"
            Parameter { type: "FocusMode" }
        }
        Signal {
            name: "focusPointModeChanged"
            Parameter { type: "FocusPointMode" }
        }
        Signal {
            name: "customFocusPointChanged"
            Parameter { type: "QPointF" }
        }
        Method {
            name: "setFocusMode"
            Parameter { type: "FocusMode" }
        }
        Method {
            name: "setFocusPointMode"
            Parameter { name: "mode"; type: "FocusPointMode" }
        }
        Method {
            name: "setCustomFocusPoint"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "isFocusModeSupported"
            type: "bool"
            Parameter { name: "mode"; type: "FocusMode" }
        }
        Method {
            name: "isFocusPointModeSupported"
            type: "bool"
            Parameter { name: "mode"; type: "FocusPointMode" }
        }
    }
    Component {
        name: "QDeclarativeCameraImageProcessing"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CameraImageProcessing 5.0",
            "QtMultimedia/CameraImageProcessing 5.11",
            "QtMultimedia/CameraImageProcessing 5.5",
            "QtMultimedia/CameraImageProcessing 5.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 3, 1, 2]
        Enum {
            name: "WhiteBalanceMode"
            values: {
                "WhiteBalanceAuto": 0,
                "WhiteBalanceManual": 1,
                "WhiteBalanceSunlight": 2,
                "WhiteBalanceCloudy": 3,
                "WhiteBalanceShade": 4,
                "WhiteBalanceTungsten": 5,
                "WhiteBalanceFluorescent": 6,
                "WhiteBalanceFlash": 7,
                "WhiteBalanceSunset": 8,
                "WhiteBalanceVendor": 1000
            }
        }
        Enum {
            name: "ColorFilter"
            values: {
                "ColorFilterNone": 0,
                "ColorFilterGrayscale": 1,
                "ColorFilterNegative": 2,
                "ColorFilterSolarize": 3,
                "ColorFilterSepia": 4,
                "ColorFilterPosterize": 5,
                "ColorFilterWhiteboard": 6,
                "ColorFilterBlackboard": 7,
                "ColorFilterAqua": 8,
                "ColorFilterVendor": 1000
            }
        }
        Property { name: "whiteBalanceMode"; type: "WhiteBalanceMode" }
        Property { name: "manualWhiteBalance"; type: "double" }
        Property { name: "brightness"; revision: 2; type: "double" }
        Property { name: "contrast"; type: "double" }
        Property { name: "saturation"; type: "double" }
        Property { name: "sharpeningLevel"; type: "double" }
        Property { name: "denoisingLevel"; type: "double" }
        Property { name: "colorFilter"; revision: 1; type: "ColorFilter" }
        Property { name: "available"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "supportedColorFilters"; revision: 3; type: "QVariantList"; isReadonly: true }
        Property {
            name: "supportedWhiteBalanceModes"
            revision: 3
            type: "QVariantList"
            isReadonly: true
        }
        Signal {
            name: "whiteBalanceModeChanged"
            Parameter { type: "QDeclarativeCameraImageProcessing::WhiteBalanceMode" }
        }
        Signal {
            name: "manualWhiteBalanceChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "brightnessChanged"
            revision: 2
            Parameter { type: "double" }
        }
        Signal {
            name: "contrastChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "saturationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "sharpeningLevelChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "denoisingLevelChanged"
            Parameter { type: "double" }
        }
        Method {
            name: "setWhiteBalanceMode"
            Parameter { name: "mode"; type: "QDeclarativeCameraImageProcessing::WhiteBalanceMode" }
        }
        Method {
            name: "setManualWhiteBalance"
            Parameter { name: "colorTemp"; type: "double" }
        }
        Method {
            name: "setBrightness"
            revision: 2
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setContrast"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setSaturation"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setSharpeningLevel"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setDenoisingLevel"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setColorFilter"
            Parameter { name: "colorFilter"; type: "ColorFilter" }
        }
    }
    Component {
        name: "QDeclarativeCameraRecorder"
        prototype: "QObject"
        exports: ["QtMultimedia/CameraRecorder 5.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RecorderState"
            values: {
                "StoppedState": 0,
                "RecordingState": 1
            }
        }
        Enum {
            name: "RecorderStatus"
            values: {
                "UnavailableStatus": 0,
                "UnloadedStatus": 1,
                "LoadingStatus": 2,
                "LoadedStatus": 3,
                "StartingStatus": 4,
                "RecordingStatus": 5,
                "PausedStatus": 6,
                "FinalizingStatus": 7
            }
        }
        Enum {
            name: "EncodingMode"
            values: {
                "ConstantQualityEncoding": 0,
                "ConstantBitRateEncoding": 1,
                "AverageBitRateEncoding": 2
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "ResourceError": 1,
                "FormatError": 2,
                "OutOfSpaceError": 3
            }
        }
        Property { name: "recorderState"; type: "RecorderState" }
        Property { name: "recorderStatus"; type: "RecorderStatus"; isReadonly: true }
        Property { name: "videoCodec"; type: "string" }
        Property { name: "resolution"; type: "QSize" }
        Property { name: "frameRate"; type: "double" }
        Property { name: "videoBitRate"; type: "int" }
        Property { name: "videoEncodingMode"; type: "EncodingMode" }
        Property { name: "audioCodec"; type: "string" }
        Property { name: "audioBitRate"; type: "int" }
        Property { name: "audioChannels"; type: "int" }
        Property { name: "audioSampleRate"; type: "int" }
        Property { name: "audioEncodingMode"; type: "EncodingMode" }
        Property { name: "mediaContainer"; type: "string" }
        Property { name: "duration"; type: "qlonglong"; isReadonly: true }
        Property { name: "outputLocation"; type: "string" }
        Property { name: "actualLocation"; type: "string"; isReadonly: true }
        Property { name: "muted"; type: "bool" }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "errorCode"; type: "Error"; isReadonly: true }
        Signal {
            name: "recorderStateChanged"
            Parameter { name: "state"; type: "QDeclarativeCameraRecorder::RecorderState" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "qlonglong" }
        }
        Signal {
            name: "mutedChanged"
            Parameter { name: "muted"; type: "bool" }
        }
        Signal {
            name: "outputLocationChanged"
            Parameter { name: "location"; type: "string" }
        }
        Signal {
            name: "actualLocationChanged"
            Parameter { name: "location"; type: "string" }
        }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QDeclarativeCameraRecorder::Error" }
            Parameter { name: "errorString"; type: "string" }
        }
        Signal {
            name: "metaDataChanged"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal {
            name: "captureResolutionChanged"
            Parameter { type: "QSize" }
        }
        Signal {
            name: "audioCodecChanged"
            Parameter { name: "codec"; type: "string" }
        }
        Signal {
            name: "videoCodecChanged"
            Parameter { name: "codec"; type: "string" }
        }
        Signal {
            name: "mediaContainerChanged"
            Parameter { name: "container"; type: "string" }
        }
        Signal {
            name: "frameRateChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "videoBitRateChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "audioBitRateChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "audioChannelsChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "audioSampleRateChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "audioEncodingModeChanged"
            Parameter { name: "encodingMode"; type: "EncodingMode" }
        }
        Signal {
            name: "videoEncodingModeChanged"
            Parameter { name: "encodingMode"; type: "EncodingMode" }
        }
        Method {
            name: "setOutputLocation"
            Parameter { name: "location"; type: "string" }
        }
        Method { name: "record" }
        Method { name: "stop" }
        Method {
            name: "setRecorderState"
            Parameter { name: "state"; type: "QDeclarativeCameraRecorder::RecorderState" }
        }
        Method {
            name: "setMuted"
            Parameter { name: "muted"; type: "bool" }
        }
        Method {
            name: "setMetadata"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "setCaptureResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setAudioCodec"
            Parameter { name: "codec"; type: "string" }
        }
        Method {
            name: "setVideoCodec"
            Parameter { name: "codec"; type: "string" }
        }
        Method {
            name: "setMediaContainer"
            Parameter { name: "container"; type: "string" }
        }
        Method {
            name: "setFrameRate"
            Parameter { name: "frameRate"; type: "double" }
        }
        Method {
            name: "setVideoBitRate"
            Parameter { name: "rate"; type: "int" }
        }
        Method {
            name: "setAudioBitRate"
            Parameter { name: "rate"; type: "int" }
        }
        Method {
            name: "setAudioChannels"
            Parameter { name: "channels"; type: "int" }
        }
        Method {
            name: "setAudioSampleRate"
            Parameter { name: "rate"; type: "int" }
        }
        Method {
            name: "setVideoEncodingMode"
            Parameter { name: "encodingMode"; type: "EncodingMode" }
        }
        Method {
            name: "setAudioEncodingMode"
            Parameter { name: "encodingMode"; type: "EncodingMode" }
        }
    }
    Component {
        name: "QDeclarativeCameraViewfinder"
        prototype: "QObject"
        exports: ["QtMultimedia/CameraViewfinder 5.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "resolution"; type: "QSize" }
        Property { name: "minimumFrameRate"; type: "double" }
        Property { name: "maximumFrameRate"; type: "double" }
    }
    Component {
        name: "QDeclarativeMediaMetaData"
        prototype: "QObject"
        Property { name: "title"; type: "QVariant" }
        Property { name: "subTitle"; type: "QVariant" }
        Property { name: "author"; type: "QVariant" }
        Property { name: "comment"; type: "QVariant" }
        Property { name: "description"; type: "QVariant" }
        Property { name: "category"; type: "QVariant" }
        Property { name: "genre"; type: "QVariant" }
        Property { name: "year"; type: "QVariant" }
        Property { name: "date"; type: "QVariant" }
        Property { name: "userRating"; type: "QVariant" }
        Property { name: "keywords"; type: "QVariant" }
        Property { name: "language"; type: "QVariant" }
        Property { name: "publisher"; type: "QVariant" }
        Property { name: "copyright"; type: "QVariant" }
        Property { name: "parentalRating"; type: "QVariant" }
        Property { name: "ratingOrganization"; type: "QVariant" }
        Property { name: "size"; type: "QVariant" }
        Property { name: "mediaType"; type: "QVariant" }
        Property { name: "duration"; type: "QVariant" }
        Property { name: "audioBitRate"; type: "QVariant" }
        Property { name: "audioCodec"; type: "QVariant" }
        Property { name: "averageLevel"; type: "QVariant" }
        Property { name: "channelCount"; type: "QVariant" }
        Property { name: "peakValue"; type: "QVariant" }
        Property { name: "sampleRate"; type: "QVariant" }
        Property { name: "albumTitle"; type: "QVariant" }
        Property { name: "albumArtist"; type: "QVariant" }
        Property { name: "contributingArtist"; type: "QVariant" }
        Property { name: "composer"; type: "QVariant" }
        Property { name: "conductor"; type: "QVariant" }
        Property { name: "lyrics"; type: "QVariant" }
        Property { name: "mood"; type: "QVariant" }
        Property { name: "trackNumber"; type: "QVariant" }
        Property { name: "trackCount"; type: "QVariant" }
        Property { name: "coverArtUrlSmall"; type: "QVariant" }
        Property { name: "coverArtUrlLarge"; type: "QVariant" }
        Property { name: "resolution"; type: "QVariant" }
        Property { name: "pixelAspectRatio"; type: "QVariant" }
        Property { name: "videoFrameRate"; type: "QVariant" }
        Property { name: "videoBitRate"; type: "QVariant" }
        Property { name: "videoCodec"; type: "QVariant" }
        Property { name: "posterUrl"; type: "QVariant" }
        Property { name: "chapterNumber"; type: "QVariant" }
        Property { name: "director"; type: "QVariant" }
        Property { name: "leadPerformer"; type: "QVariant" }
        Property { name: "writer"; type: "QVariant" }
        Property { name: "cameraManufacturer"; type: "QVariant" }
        Property { name: "cameraModel"; type: "QVariant" }
        Property { name: "event"; type: "QVariant" }
        Property { name: "subject"; type: "QVariant" }
        Property { name: "orientation"; type: "QVariant" }
        Property { name: "exposureTime"; type: "QVariant" }
        Property { name: "fNumber"; type: "QVariant" }
        Property { name: "exposureProgram"; type: "QVariant" }
        Property { name: "isoSpeedRatings"; type: "QVariant" }
        Property { name: "exposureBiasValue"; type: "QVariant" }
        Property { name: "dateTimeOriginal"; type: "QVariant" }
        Property { name: "dateTimeDigitized"; type: "QVariant" }
        Property { name: "subjectDistance"; type: "QVariant" }
        Property { name: "meteringMode"; type: "QVariant" }
        Property { name: "lightSource"; type: "QVariant" }
        Property { name: "flash"; type: "QVariant" }
        Property { name: "focalLength"; type: "QVariant" }
        Property { name: "exposureMode"; type: "QVariant" }
        Property { name: "whiteBalance"; type: "QVariant" }
        Property { name: "digitalZoomRatio"; type: "QVariant" }
        Property { name: "focalLengthIn35mmFilm"; type: "QVariant" }
        Property { name: "sceneCaptureType"; type: "QVariant" }
        Property { name: "gainControl"; type: "QVariant" }
        Property { name: "contrast"; type: "QVariant" }
        Property { name: "saturation"; type: "QVariant" }
        Property { name: "sharpness"; type: "QVariant" }
        Property { name: "deviceSettingDescription"; type: "QVariant" }
        Property { name: "gpsLatitude"; type: "QVariant" }
        Property { name: "gpsLongitude"; type: "QVariant" }
        Property { name: "gpsAltitude"; type: "QVariant" }
        Property { name: "gpsTimeStamp"; type: "QVariant" }
        Property { name: "gpsSatellites"; type: "QVariant" }
        Property { name: "gpsStatus"; type: "QVariant" }
        Property { name: "gpsDOP"; type: "QVariant" }
        Property { name: "gpsSpeed"; type: "QVariant" }
        Property { name: "gpsTrack"; type: "QVariant" }
        Property { name: "gpsTrackRef"; type: "QVariant" }
        Property { name: "gpsImgDirection"; type: "QVariant" }
        Property { name: "gpsImgDirectionRef"; type: "QVariant" }
        Property { name: "gpsMapDatum"; type: "QVariant" }
        Property { name: "gpsProcessingMethod"; type: "QVariant" }
        Property { name: "gpsAreaInformation"; type: "QVariant" }
        Signal { name: "metaDataChanged" }
    }
    Component {
        name: "QDeclarativeMultimediaGlobal"
        prototype: "QObject"
        exports: ["QtMultimedia/QtMultimedia 5.4"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Enum {
            name: "VolumeScale"
            values: {
                "LinearVolumeScale": 0,
                "CubicVolumeScale": 1,
                "LogarithmicVolumeScale": 2,
                "DecibelVolumeScale": 3
            }
        }
        Property { name: "defaultCamera"; type: "QJSValue"; isReadonly: true }
        Property { name: "availableCameras"; type: "QJSValue"; isReadonly: true }
        Method {
            name: "convertVolume"
            type: "double"
            Parameter { name: "volume"; type: "double" }
            Parameter { name: "from"; type: "VolumeScale" }
            Parameter { name: "to"; type: "VolumeScale" }
        }
    }
    Component {
        name: "QDeclarativePlaylist"
        defaultProperty: "items"
        prototype: "QAbstractListModel"
        exports: ["QtMultimedia/Playlist 5.6", "QtMultimedia/Playlist 5.7"]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "PlaybackMode"
            values: {
                "CurrentItemOnce": 0,
                "CurrentItemInLoop": 1,
                "Sequential": 2,
                "Loop": 3,
                "Random": 4
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "FormatError": 1,
                "FormatNotSupportedError": 2,
                "NetworkError": 3,
                "AccessDeniedError": 4
            }
        }
        Property { name: "playbackMode"; type: "PlaybackMode" }
        Property { name: "currentItemSource"; type: "QUrl"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "itemCount"; type: "int"; isReadonly: true }
        Property { name: "readOnly"; type: "bool"; isReadonly: true }
        Property { name: "error"; type: "Error"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "items"; type: "QDeclarativePlaylistItem"; isList: true; isReadonly: true }
        Signal {
            name: "itemAboutToBeInserted"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Signal {
            name: "itemInserted"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Signal {
            name: "itemAboutToBeRemoved"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Signal {
            name: "itemRemoved"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Signal { name: "loaded" }
        Signal { name: "loadFailed" }
        Signal {
            name: "error"
            Parameter { name: "error"; type: "QDeclarativePlaylist::Error" }
            Parameter { name: "errorString"; type: "string" }
        }
        Method {
            name: "itemSource"
            type: "QUrl"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "nextIndex"
            type: "int"
            Parameter { name: "steps"; type: "int" }
        }
        Method { name: "nextIndex"; type: "int" }
        Method {
            name: "previousIndex"
            type: "int"
            Parameter { name: "steps"; type: "int" }
        }
        Method { name: "previousIndex"; type: "int" }
        Method { name: "next" }
        Method { name: "previous" }
        Method { name: "shuffle" }
        Method {
            name: "load"
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "format"; type: "string" }
        }
        Method {
            name: "load"
            Parameter { name: "location"; type: "QUrl" }
        }
        Method {
            name: "save"
            type: "bool"
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "format"; type: "string" }
        }
        Method {
            name: "save"
            type: "bool"
            Parameter { name: "location"; type: "QUrl" }
        }
        Method {
            name: "addItem"
            type: "bool"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "addItems"
            revision: 1
            type: "bool"
            Parameter { name: "sources"; type: "QList<QUrl>" }
        }
        Method {
            name: "insertItem"
            type: "bool"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "insertItems"
            revision: 1
            type: "bool"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "sources"; type: "QList<QUrl>" }
        }
        Method {
            name: "moveItem"
            revision: 1
            type: "bool"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "removeItem"
            type: "bool"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "removeItems"
            revision: 1
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "clear"; type: "bool" }
    }
    Component {
        name: "QDeclarativePlaylistItem"
        prototype: "QObject"
        exports: ["QtMultimedia/PlaylistItem 5.6"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QUrl" }
    }
    Component {
        name: "QDeclarativeRadio"
        prototype: "QObject"
        exports: ["QtMultimedia/Radio 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "State"
            values: {
                "ActiveState": 0,
                "StoppedState": 1
            }
        }
        Enum {
            name: "Band"
            values: {
                "AM": 0,
                "FM": 1,
                "SW": 2,
                "LW": 3,
                "FM2": 4
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "ResourceError": 1,
                "OpenError": 2,
                "OutOfRangeError": 3
            }
        }
        Enum {
            name: "StereoMode"
            values: {
                "ForceStereo": 0,
                "ForceMono": 1,
                "Auto": 2
            }
        }
        Enum {
            name: "SearchMode"
            values: {
                "SearchFast": 0,
                "SearchGetStationId": 1
            }
        }
        Enum {
            name: "Availability"
            values: {
                "Available": 0,
                "Busy": 2,
                "Unavailable": 1,
                "ResourceMissing": 3
            }
        }
        Property { name: "state"; type: "State"; isReadonly: true }
        Property { name: "band"; type: "Band" }
        Property { name: "frequency"; type: "int" }
        Property { name: "stereo"; type: "bool"; isReadonly: true }
        Property { name: "stereoMode"; type: "StereoMode" }
        Property { name: "signalStrength"; type: "int"; isReadonly: true }
        Property { name: "volume"; type: "int" }
        Property { name: "muted"; type: "bool" }
        Property { name: "searching"; type: "bool"; isReadonly: true }
        Property { name: "frequencyStep"; type: "int"; isReadonly: true }
        Property { name: "minimumFrequency"; type: "int"; isReadonly: true }
        Property { name: "maximumFrequency"; type: "int"; isReadonly: true }
        Property { name: "antennaConnected"; type: "bool"; isReadonly: true }
        Property { name: "availability"; type: "Availability"; isReadonly: true }
        Property { name: "radioData"; type: "QDeclarativeRadioData"; isReadonly: true; isPointer: true }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "QDeclarativeRadio::State" }
        }
        Signal {
            name: "bandChanged"
            Parameter { name: "band"; type: "QDeclarativeRadio::Band" }
        }
        Signal {
            name: "frequencyChanged"
            Parameter { name: "frequency"; type: "int" }
        }
        Signal {
            name: "stereoStatusChanged"
            Parameter { name: "stereo"; type: "bool" }
        }
        Signal {
            name: "searchingChanged"
            Parameter { name: "searching"; type: "bool" }
        }
        Signal {
            name: "signalStrengthChanged"
            Parameter { name: "signalStrength"; type: "int" }
        }
        Signal {
            name: "volumeChanged"
            Parameter { name: "volume"; type: "int" }
        }
        Signal {
            name: "mutedChanged"
            Parameter { name: "muted"; type: "bool" }
        }
        Signal {
            name: "stationFound"
            Parameter { name: "frequency"; type: "int" }
            Parameter { name: "stationId"; type: "string" }
        }
        Signal {
            name: "antennaConnectedChanged"
            Parameter { name: "connectionStatus"; type: "bool" }
        }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "availability"; type: "Availability" }
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QDeclarativeRadio::Error" }
        }
        Method {
            name: "setBand"
            Parameter { name: "band"; type: "QDeclarativeRadio::Band" }
        }
        Method {
            name: "setFrequency"
            Parameter { name: "frequency"; type: "int" }
        }
        Method {
            name: "setStereoMode"
            Parameter { name: "stereoMode"; type: "QDeclarativeRadio::StereoMode" }
        }
        Method {
            name: "setVolume"
            Parameter { name: "volume"; type: "int" }
        }
        Method {
            name: "setMuted"
            Parameter { name: "muted"; type: "bool" }
        }
        Method { name: "cancelScan" }
        Method { name: "scanDown" }
        Method { name: "scanUp" }
        Method { name: "tuneUp" }
        Method { name: "tuneDown" }
        Method {
            name: "searchAllStations"
            Parameter { name: "searchMode"; type: "QDeclarativeRadio::SearchMode" }
        }
        Method { name: "searchAllStations" }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "isAvailable"; type: "bool" }
    }
    Component {
        name: "QDeclarativeRadioData"
        prototype: "QObject"
        exports: ["QtMultimedia/RadioData 5.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "ResourceError": 1,
                "OpenError": 2,
                "OutOfRangeError": 3
            }
        }
        Enum {
            name: "ProgramType"
            values: {
                "Undefined": 0,
                "News": 1,
                "CurrentAffairs": 2,
                "Information": 3,
                "Sport": 4,
                "Education": 5,
                "Drama": 6,
                "Culture": 7,
                "Science": 8,
                "Varied": 9,
                "PopMusic": 10,
                "RockMusic": 11,
                "EasyListening": 12,
                "LightClassical": 13,
                "SeriousClassical": 14,
                "OtherMusic": 15,
                "Weather": 16,
                "Finance": 17,
                "ChildrensProgrammes": 18,
                "SocialAffairs": 19,
                "Religion": 20,
                "PhoneIn": 21,
                "Travel": 22,
                "Leisure": 23,
                "JazzMusic": 24,
                "CountryMusic": 25,
                "NationalMusic": 26,
                "OldiesMusic": 27,
                "FolkMusic": 28,
                "Documentary": 29,
                "AlarmTest": 30,
                "Alarm": 31,
                "Talk": 32,
                "ClassicRock": 33,
                "AdultHits": 34,
                "SoftRock": 35,
                "Top40": 36,
                "Soft": 37,
                "Nostalgia": 38,
                "Classical": 39,
                "RhythmAndBlues": 40,
                "SoftRhythmAndBlues": 41,
                "Language": 42,
                "ReligiousMusic": 43,
                "ReligiousTalk": 44,
                "Personality": 45,
                "Public": 46,
                "College": 47
            }
        }
        Enum {
            name: "Availability"
            values: {
                "Available": 0,
                "Busy": 2,
                "Unavailable": 1,
                "ResourceMissing": 3
            }
        }
        Property { name: "stationId"; type: "string"; isReadonly: true }
        Property { name: "programType"; type: "QDeclarativeRadioData::ProgramType"; isReadonly: true }
        Property { name: "programTypeName"; type: "string"; isReadonly: true }
        Property { name: "stationName"; type: "string"; isReadonly: true }
        Property { name: "radioText"; type: "string"; isReadonly: true }
        Property { name: "alternativeFrequenciesEnabled"; type: "bool" }
        Property { name: "availability"; type: "Availability"; isReadonly: true }
        Signal {
            name: "stationIdChanged"
            Parameter { name: "stationId"; type: "string" }
        }
        Signal {
            name: "programTypeChanged"
            Parameter { name: "programType"; type: "QDeclarativeRadioData::ProgramType" }
        }
        Signal {
            name: "programTypeNameChanged"
            Parameter { name: "programTypeName"; type: "string" }
        }
        Signal {
            name: "stationNameChanged"
            Parameter { name: "stationName"; type: "string" }
        }
        Signal {
            name: "radioTextChanged"
            Parameter { name: "radioText"; type: "string" }
        }
        Signal {
            name: "alternativeFrequenciesEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "availability"; type: "Availability" }
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QDeclarativeRadioData::Error" }
        }
        Method {
            name: "setAlternativeFrequenciesEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method { name: "isAvailable"; type: "bool" }
    }
    Component {
        name: "QDeclarativeTorch"
        prototype: "QObject"
        exports: ["QtMultimedia/Torch 5.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "enabled"; type: "bool" }
        Property { name: "power"; type: "int" }
    }
    Component {
        name: "QDeclarativeVideoOutput"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtMultimedia/VideoOutput 5.0",
            "QtMultimedia/VideoOutput 5.13",
            "QtMultimedia/VideoOutput 5.15",
            "QtMultimedia/VideoOutput 5.2"
        ]
        exportMetaObjectRevisions: [0, 13, 15, 2]
        Enum {
            name: "FlushMode"
            values: {
                "EmptyFrame": 0,
                "FirstFrame": 1,
                "LastFrame": 2
            }
        }
        Enum {
            name: "FillMode"
            values: {
                "Stretch": 0,
                "PreserveAspectFit": 1,
                "PreserveAspectCrop": 2
            }
        }
        Property { name: "source"; type: "QObject"; isPointer: true }
        Property { name: "fillMode"; type: "FillMode" }
        Property { name: "orientation"; type: "int" }
        Property { name: "autoOrientation"; revision: 2; type: "bool" }
        Property { name: "sourceRect"; type: "QRectF"; isReadonly: true }
        Property { name: "contentRect"; type: "QRectF"; isReadonly: true }
        Property { name: "filters"; type: "QAbstractVideoFilter"; isList: true; isReadonly: true }
        Property { name: "flushMode"; revision: 13; type: "FlushMode" }
        Property {
            name: "videoSurface"
            revision: 15
            type: "QAbstractVideoSurface"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "fillModeChanged"
            Parameter { type: "QDeclarativeVideoOutput::FillMode" }
        }
        Method {
            name: "mapPointToItem"
            type: "QPointF"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapRectToItem"
            type: "QRectF"
            Parameter { name: "rectangle"; type: "QRectF" }
        }
        Method {
            name: "mapNormalizedPointToItem"
            type: "QPointF"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapNormalizedRectToItem"
            type: "QRectF"
            Parameter { name: "rectangle"; type: "QRectF" }
        }
        Method {
            name: "mapPointToSource"
            type: "QPointF"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapRectToSource"
            type: "QRectF"
            Parameter { name: "rectangle"; type: "QRectF" }
        }
        Method {
            name: "mapPointToSourceNormalized"
            type: "QPointF"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapRectToSourceNormalized"
            type: "QRectF"
            Parameter { name: "rectangle"; type: "QRectF" }
        }
    }
    Component {
        name: "QMediaObject"
        prototype: "QObject"
        Property { name: "notifyInterval"; type: "int" }
        Signal {
            name: "notifyIntervalChanged"
            Parameter { name: "milliSeconds"; type: "int" }
        }
        Signal {
            name: "metaDataAvailableChanged"
            Parameter { name: "available"; type: "bool" }
        }
        Signal { name: "metaDataChanged" }
        Signal {
            name: "metaDataChanged"
            Parameter { name: "key"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "available"; type: "bool" }
        }
        Signal {
            name: "availabilityChanged"
            Parameter { name: "availability"; type: "QMultimedia::AvailabilityStatus" }
        }
    }
    Component { name: "QSGVideoItemSurface"; prototype: "QAbstractVideoSurface" }
    Component {
        name: "QSoundEffect"
        prototype: "QObject"
        exports: [
            "QtMultimedia/SoundEffect 5.0",
            "QtMultimedia/SoundEffect 5.3",
            "QtMultimedia/SoundEffect 5.8"
        ]
        exportMetaObjectRevisions: [0, 0, 0]
        Enum {
            name: "Loop"
            values: {
                "Infinite": -2
            }
        }
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "loops"; type: "int" }
        Property { name: "loopsRemaining"; type: "int"; isReadonly: true }
        Property { name: "volume"; type: "double" }
        Property { name: "muted"; type: "bool" }
        Property { name: "playing"; type: "bool"; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "category"; type: "string" }
        Signal { name: "loopCountChanged" }
        Signal { name: "loadedChanged" }
        Method { name: "play" }
        Method { name: "stop" }
    }
}
