#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من main.py لاختبار التشغيل
"""

from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
import sys
import os
from sqlalchemy import create_engine

# استيراد نظام التراخيص
try:
    from license_ui import check_license_and_show_dialog
    LICENSE_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: نظام التراخيص غير متوفر - {e}")
    LICENSE_SYSTEM_AVAILABLE = False

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.dirname(__file__), relative_path)

class SimpleMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام المحاسبة العصري")
        self.setGeometry(100, 100, 800, 600)
        
        # إنشاء واجهة بسيطة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان ترحيبي
        welcome_label = QLabel("🎉 مرحباً بك في نظام المحاسبة العصري")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 20px;
            }
        """)
        layout.addWidget(welcome_label)
        
        # معلومات النظام
        info_label = QLabel("""
        ✅ تم تشغيل النظام بنجاح
        📊 قاعدة البيانات متصلة
        🔐 الترخيص مفعل
        🎨 التصميم محمل
        
        يمكنك الآن استخدام جميع ميزات النظام
        """)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #34495e;
                padding: 15px;
                background-color: #d5dbdb;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)

def main():
    app = None
    try:
        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("🚀 بدء تشغيل النظام...")

        # فحص الترخيص أولاً
        if LICENSE_SYSTEM_AVAILABLE:
            print("🔐 فحص الترخيص...")
            if not check_license_and_show_dialog():
                print("❌ لم يتم تفعيل الترخيص - إغلاق البرنامج")
                QMessageBox.critical(
                    None,
                    "خطأ في الترخيص",
                    "لم يتم تفعيل الترخيص.\n\n"
                    "للتجديد، راسل:\n"
                    "📧 <EMAIL>\n\n"
                    "أرسل كود العميل ورقم الجهاز مع إثبات الدفع"
                )
                return 1
            print("✅ تم التحقق من الترخيص بنجاح")
        else:
            print("⚠️ تحذير: نظام التراخيص غير متوفر - تشغيل البرنامج بدون حماية")

        # تهيئة قاعدة البيانات
        db_path = resource_path('accounting.db')
        print(f"🔍 مسار قاعدة البيانات الفعلي: {db_path}")
        
        if os.path.exists(db_path):
            print("✅ قاعدة البيانات موجودة")
        else:
            print("⚠️ قاعدة البيانات غير موجودة")

        # إنشاء النافذة الرئيسية
        window = SimpleMainWindow()
        window.show()

        print("🚀 تم تشغيل البرنامج بنجاح!")
        return app.exec_()
        
    except Exception as e:
        error_msg = f"حدث خطأ أثناء تشغيل البرنامج: {str(e)}"
        print(f"❌ {error_msg}")
        if app:
            QMessageBox.critical(None, "خطأ", error_msg)
        return 1

if __name__ == "__main__":
    sys.exit(main())
