import sqlite3
import json

# فحص قاعدة البيانات
conn = sqlite3.connect('accounting.db')
cursor = conn.cursor()

# فحص الجداول الموجودة
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print('الجداول الموجودة:')
for table in tables:
    print(f'  - {table[0]}')

# فحص بعض البيانات
try:
    cursor.execute('SELECT COUNT(*) FROM products')
    products_count = cursor.fetchone()[0]
    print(f'عدد المنتجات: {products_count}')
    
    cursor.execute('SELECT COUNT(*) FROM invoices')
    invoices_count = cursor.fetchone()[0]
    print(f'عدد الفواتير: {invoices_count}')
    
    cursor.execute('SELECT COUNT(*) FROM inventory')
    inventory_count = cursor.fetchone()[0]
    print(f'عدد عناصر المخزون: {inventory_count}')
except Exception as e:
    print(f'خطأ في فحص البيانات: {e}')

conn.close()

# فحص ملف إعدادات الشركة
try:
    with open('company_settings.json', 'r', encoding='utf-8') as f:
        settings = json.load(f)
    print('\nإعدادات الشركة:')
    print(f'  اسم الشركة: {settings["company_name"]}')
    print(f'  الإعداد مكتمل: {settings["setup_completed"]}')
except Exception as e:
    print(f'خطأ في قراءة إعدادات الشركة: {e}')

print('\n✅ فحص البيانات مكتمل')
