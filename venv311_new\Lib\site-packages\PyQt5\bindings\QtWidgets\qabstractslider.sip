// qabstractslider.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractSlider : QWidget
{
%TypeHeaderCode
#include <qabstractslider.h>
%End

public:
    explicit QAbstractSlider(QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractSlider();
    Qt::Orientation orientation() const;
    void setMinimum(int);
    int minimum() const;
    void setMaximum(int);
    int maximum() const;
    void setRange(int min, int max);
    void setSingleStep(int);
    int singleStep() const;
    void setPageStep(int);
    int pageStep() const;
    void setTracking(bool enable);
    bool hasTracking() const;
    void setSliderDown(bool);
    bool isSliderDown() const;
    void setSliderPosition(int);
    int sliderPosition() const;
    void setInvertedAppearance(bool);
    bool invertedAppearance() const;
    void setInvertedControls(bool);
    bool invertedControls() const;

    enum SliderAction
    {
        SliderNoAction,
        SliderSingleStepAdd,
        SliderSingleStepSub,
        SliderPageStepAdd,
        SliderPageStepSub,
        SliderToMinimum,
        SliderToMaximum,
        SliderMove,
    };

    int value() const;
    void triggerAction(QAbstractSlider::SliderAction action);

public slots:
    void setValue(int);
    void setOrientation(Qt::Orientation);

signals:
    void valueChanged(int value);
    void sliderPressed();
    void sliderMoved(int position);
    void sliderReleased();
    void rangeChanged(int min, int max);
    void actionTriggered(int action);

protected:
    void setRepeatAction(QAbstractSlider::SliderAction action, int thresholdTime = 500, int repeatTime = 50);
    QAbstractSlider::SliderAction repeatAction() const;

    enum SliderChange
    {
        SliderRangeChange,
        SliderOrientationChange,
        SliderStepsChange,
        SliderValueChange,
    };

    virtual void sliderChange(QAbstractSlider::SliderChange change);
    virtual bool event(QEvent *e);
    virtual void keyPressEvent(QKeyEvent *ev);
    virtual void timerEvent(QTimerEvent *);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void changeEvent(QEvent *e);
};
