#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام الإعدادات الشامل
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("⚙️ اختبار نظام الإعدادات الشامل")
    print("=" * 80)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("✅ المميزات الجديدة:")
    print("")
    print("   🌐 إعدادات اللغة:")
    print("     • العربية (افتراضي)")
    print("     • الإنجليزية")
    print("     • دعم اتجاه النص (RTL/LTR)")
    print("")
    print("   🎨 إعدادات المظهر:")
    print("     • المظهر الفاتح (افتراضي)")
    print("     • المظهر الداكن")
    print("     • حجم الخط (10-24)")
    print("     • فتح النوافذ بحجم كامل")
    print("")
    print("   💰 إعدادات العملة:")
    print("     • جنيه مصري (افتراضي)")
    print("     • ريال سعودي")
    print("     • دولار أمريكي")
    print("     • يورو")
    print("     • 20+ عملة عربية وعالمية")
    print("     • تخصيص رمز العملة")
    print("     • موضع الرمز (قبل/بعد)")
    print("     • عدد الخانات العشرية")
    print("")
    print("   ⌨️ اختصارات لوحة المفاتيح:")
    print("     • إظهار/إخفاء المكسب (Ctrl+T افتراضي)")
    print("     • فاتورة جديدة (Ctrl+N)")
    print("     • حفظ (Ctrl+S)")
    print("     • طباعة (Ctrl+P)")
    print("     • البحث (Ctrl+F)")
    print("     • تخصيص أي اختصار")
    print("")
    print("   ⚙️ إعدادات عامة:")
    print("     • النسخ الاحتياطي التلقائي")
    print("     • فترة النسخ الاحتياطي")
    print("     • إظهار الإشعارات")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 اختبر الآن:")
        print("")
        print("   ⚙️ فتح نافذة الإعدادات:")
        print("     1️⃣ من القائمة: النظام → إعدادات التطبيق")
        print("     2️⃣ أو اضغط Alt+S (إذا كان مفعل)")
        print("")
        print("   🌐 تبويب المظهر واللغة:")
        print("     • غير اللغة من عربي لإنجليزي")
        print("     • غير المظهر من فاتح لداكن")
        print("     • جرب أحجام خطوط مختلفة")
        print("     • فعل/ألغ فتح النوافذ بحجم كامل")
        print("")
        print("   💰 تبويب العملة:")
        print("     • اختر عملة مختلفة (ريال سعودي مثلاً)")
        print("     • غير رمز العملة")
        print("     • غير موضع الرمز (قبل/بعد)")
        print("     • غير عدد الخانات العشرية")
        print("     • شاهد المعاينة المباشرة")
        print("")
        print("   ⌨️ تبويب الاختصارات:")
        print("     • غير اختصار المكسب من Ctrl+T لأي شيء آخر")
        print("     • جرب Alt+M أو Shift+P أو F2")
        print("     • غير اختصارات أخرى")
        print("     • اضغط 🔄 لإعادة تعيين أي اختصار")
        print("")
        print("   ⚙️ تبويب الإعدادات العامة:")
        print("     • فعل/ألغ النسخ الاحتياطي التلقائي")
        print("     • غير فترة النسخ الاحتياطي")
        print("     • فعل/ألغ الإشعارات")
        print("")
        print("   💾 حفظ وتطبيق:")
        print("     • اضغط 'حفظ' لتطبيق الإعدادات")
        print("     • شاهد التغييرات الفورية")
        print("     • جرب الاختصارات الجديدة")
        print("")
        print("   🔄 إعادة تعيين:")
        print("     • اضغط 'إعادة تعيين' للعودة للافتراضي")
        print("     • تأكيد الإعادة")
        print("")
        print("   📤📥 تصدير/استيراد:")
        print("     • اضغط 'تصدير' لحفظ الإعدادات في ملف")
        print("     • اضغط 'استيراد' لتحميل إعدادات محفوظة")
        print("")
        print("   ✅ ما يجب أن تراه:")
        print("     • نافذة إعدادات جميلة ومنظمة")
        print("     • 4 تبويبات واضحة")
        print("     • معاينة مباشرة للعملة")
        print("     • حقول اختصارات تفاعلية")
        print("     • تطبيق فوري للإعدادات")
        print("")
        print("🎉 نظام إعدادات متكامل ومتقدم!")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 80)
    print("📊 ملخص نظام الإعدادات:")
    print("")
    print("   ✅ قاعدة البيانات:")
    print("     • جدول app_settings")
    print("     • دعم أنواع مختلفة (string, boolean, integer, json)")
    print("     • فئات منظمة (language, appearance, currency, shortcuts)")
    print("     • قيم افتراضية محددة")
    print("")
    print("   ✅ مدير الإعدادات:")
    print("     • تحميل وحفظ الإعدادات")
    print("     • ذاكرة مؤقتة للأداء")
    print("     • تنسيق العملة التلقائي")
    print("     • إدارة الاختصارات")
    print("     • تصدير/استيراد الإعدادات")
    print("")
    print("   ✅ واجهة المستخدم:")
    print("     • نافذة إعدادات احترافية")
    print("     • 4 تبويبات منظمة")
    print("     • معاينة مباشرة")
    print("     • تطبيق فوري")
    print("     • إعادة تعيين آمنة")
    print("")
    print("   ✅ التكامل:")
    print("     • دمج مع النافذة الرئيسية")
    print("     • تطبيق الإعدادات على كامل التطبيق")
    print("     • اختصارات لوحة مفاتيح عملية")
    print("     • دعم المظاهر المختلفة")
    print("")
    print("   🎯 الفوائد:")
    print("     • تخصيص كامل للتطبيق")
    print("     • سهولة في الاستخدام")
    print("     • مرونة في الإعدادات")
    print("     • حفظ تفضيلات المستخدم")
    print("     • تجربة مستخدم محسنة")
    print("")
    print("   💡 العملات المدعومة:")
    print("     • جنيه مصري (ج.م)")
    print("     • ريال سعودي (ر.س)")
    print("     • دولار أمريكي ($)")
    print("     • يورو (€)")
    print("     • جنيه إسترليني (£)")
    print("     • درهم إماراتي (د.إ)")
    print("     • دينار كويتي (د.ك)")
    print("     • ريال قطري (ر.ق)")
    print("     • دينار بحريني (د.ب)")
    print("     • ريال عماني (ر.ع)")
    print("     • دينار أردني (د.أ)")
    print("     • ليرة لبنانية (ل.ل)")
    print("     • ليرة تركية (₺)")
    print("     • درهم مغربي (د.م)")
    print("     • دينار جزائري (د.ج)")
    print("     • دينار تونسي (د.ت)")
    print("     • دينار ليبي (د.ل)")
    print("     • جنيه سوداني (ج.س)")
    print("     • دينار عراقي (د.ع)")
    print("     • ليرة سورية (ل.س)")
    print("     • ريال يمني (ر.ي)")
    print("")
    print("🎉 نظام إعدادات متكامل وجاهز للاستخدام!")

if __name__ == "__main__":
    main()
