#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعداد الأولي للشركة
Initial Company Setup Dialog
"""

import os
import json
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit, QTextEdit, QFileDialog,
    QMessageBox, QFrame, QGridLayout, QGroupBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap
from datetime import datetime

class InitialSetupDialog(QDialog):
    setup_completed = pyqtSignal(dict)  # إشارة عند اكتمال الإعداد
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.company_data = {}
        self.logo_path = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏢 إعداد الشركة - المرة الأولى")
        self.setFixedSize(900, 1260)  # زيادة الطول 20% (1050→1260)
        self.setModal(True)
        
        # تطبيق التصميم
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 15px;
            }
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 12px;
                margin: 15px 0;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background: white;
                border-radius: 5px;
            }
            QLineEdit, QTextEdit {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                padding: 12px;
                font-size: 16px;
                background: white;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498db;
                background: #f8f9fa;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            QPushButton:pressed {
                background: #1f618d;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(25)  # زيادة المسافات (15→25)
        layout.setContentsMargins(30, 30, 30, 30)  # زيادة الهوامش (20→30)
        
        # العنوان الرئيسي
        title_label = QLabel("🏢 مرحباً بك في برنامج المحاسبة!")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 3px solid #3498db;
        """)
        layout.addWidget(title_label)
        
        # رسالة ترحيبية
        welcome_label = QLabel("يرجى إدخال معلومات شركتك لتخصيص البرنامج")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 8px 0;")
        layout.addWidget(welcome_label)
        
        # مجموعة معلومات الشركة
        company_group = QGroupBox("📋 معلومات الشركة")
        company_layout = QGridLayout(company_group)
        
        # اسم الشركة
        company_layout.addWidget(QLabel("🏢 اسم الشركة:"), 0, 0)
        self.company_name_edit = QLineEdit()
        self.company_name_edit.setPlaceholderText("مثال: شركة الأحلام للتجارة")
        company_layout.addWidget(self.company_name_edit, 0, 1)
        
        # اسم المالك/المدير
        company_layout.addWidget(QLabel("👤 اسم المالك/المدير:"), 1, 0)
        self.owner_name_edit = QLineEdit()
        self.owner_name_edit.setPlaceholderText("مثال: أحمد محمد")
        company_layout.addWidget(self.owner_name_edit, 1, 1)
        
        # رقم الهاتف
        company_layout.addWidget(QLabel("📞 رقم الهاتف:"), 2, 0)
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("مثال: 01234567890")
        company_layout.addWidget(self.phone_edit, 2, 1)
        
        # البريد الإلكتروني
        company_layout.addWidget(QLabel("📧 البريد الإلكتروني:"), 3, 0)
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("مثال: <EMAIL>")
        company_layout.addWidget(self.email_edit, 3, 1)
        
        # العنوان
        company_layout.addWidget(QLabel("📍 العنوان:"), 4, 0)
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("مثال: شارع النيل، المعادي، القاهرة")
        self.address_edit.setMaximumHeight(120)
        company_layout.addWidget(self.address_edit, 4, 1)

        # خيار تفعيل السنة
        company_layout.addWidget(QLabel("🔑 تفعيل السنة:"), 5, 0)
        self.activate_year_edit = QLineEdit()
        self.activate_year_edit.setPlaceholderText("أدخل كود التفعيل للسنة")
        company_layout.addWidget(self.activate_year_edit, 5, 1)
        
        layout.addWidget(company_group)
        
        # مجموعة اللوجو
        logo_group = QGroupBox("🎨 شعار الشركة")
        logo_layout = QVBoxLayout(logo_group)
        
        # منطقة عرض اللوجو (زيادة 50%)
        self.logo_frame = QFrame()
        self.logo_frame.setFixedSize(300, 225)  # زيادة 50% (200→300, 150→225)
        self.logo_frame.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                background: white;
            }
        """)
        
        logo_frame_layout = QVBoxLayout(self.logo_frame)
        self.logo_label = QLabel("📷\nاضغط لاختيار الشعار")
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setStyleSheet("""
            color: #7f8c8d;
            font-size: 14px;
            border: none;
        """)
        logo_frame_layout.addWidget(self.logo_label)
        
        # توسيط اللوجو
        logo_center_layout = QHBoxLayout()
        logo_center_layout.addStretch()
        logo_center_layout.addWidget(self.logo_frame)
        logo_center_layout.addStretch()
        logo_layout.addLayout(logo_center_layout)
        
        # أزرار اللوجو
        logo_buttons_layout = QHBoxLayout()
        
        self.choose_logo_btn = QPushButton("📁 اختيار شعار")
        self.choose_logo_btn.clicked.connect(self.choose_logo)
        logo_buttons_layout.addWidget(self.choose_logo_btn)
        
        self.remove_logo_btn = QPushButton("🗑️ إزالة الشعار")
        self.remove_logo_btn.clicked.connect(self.remove_logo)
        self.remove_logo_btn.setEnabled(False)
        logo_buttons_layout.addWidget(self.remove_logo_btn)
        
        logo_layout.addLayout(logo_buttons_layout)
        layout.addWidget(logo_group)
        
        # الأزرار السفلية
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 حفظ وبدء الاستخدام")
        self.save_btn.clicked.connect(self.save_settings)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                font-size: 18px;
                padding: 18px 35px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        
        self.skip_btn = QPushButton("⏭️ تخطي (يمكن الإعداد لاحقاً)")
        self.skip_btn.clicked.connect(self.skip_setup)
        self.skip_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                font-size: 16px;
                padding: 15px 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        
        buttons_layout.addWidget(self.skip_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        
        # إضافة أزرار التحكم في النافذة (تكبير، تصغير، إغلاق)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        # إضافة زر الشاشة الكاملة في شريط العنوان
        self.fullscreen_btn = QPushButton("🔳")
        self.fullscreen_btn.setFixedSize(30, 30)
        self.fullscreen_btn.setToolTip("فتح في شاشة كاملة (F11)")
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        self.fullscreen_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                color: #2c3e50;
            }
            QPushButton:hover {
                background: #ecf0f1;
                border-color: #3498db;
            }
            QPushButton:pressed {
                background: #d5dbdb;
            }
        """)

        # إضافة الزر في أعلى يمين النافذة
        fullscreen_layout = QHBoxLayout()
        fullscreen_layout.addStretch()
        fullscreen_layout.addWidget(self.fullscreen_btn)
        fullscreen_layout.setContentsMargins(0, 5, 10, 0)

        # إدراج layout الشاشة الكاملة في أعلى النافذة
        layout.insertLayout(0, fullscreen_layout)

        # إضافة اختصار لوحة المفاتيح للشاشة الكاملة (F11)
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

    def toggle_fullscreen(self):
        """تبديل وضع الشاشة الكاملة"""
        if self.isFullScreen():
            # العودة للحجم العادي
            self.showNormal()
            self.fullscreen_btn.setText("🔳")
            self.fullscreen_btn.setToolTip("فتح في شاشة كاملة (F11)")
        else:
            # فتح في شاشة كاملة
            self.showFullScreen()
            self.fullscreen_btn.setText("🗗")
            self.fullscreen_btn.setToolTip("العودة للحجم العادي (F11)")

    def choose_logo(self):
        """اختيار شعار الشركة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار شعار الشركة",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            try:
                # تحميل وعرض الصورة
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # تصغير الصورة لتناسب الإطار الأكبر
                    scaled_pixmap = pixmap.scaled(
                        270, 195,  # زيادة 50% (180→270, 130→195)
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.logo_label.setPixmap(scaled_pixmap)
                    self.logo_label.setText("")
                    
                    # حفظ مسار الصورة
                    self.logo_path = file_path
                    self.remove_logo_btn.setEnabled(True)
                    
                    # تحديث تصميم الإطار
                    self.logo_frame.setStyleSheet("""
                        QFrame {
                            border: 2px solid #27ae60;
                            border-radius: 10px;
                            background: white;
                        }
                    """)
                else:
                    QMessageBox.warning(self, "خطأ", "لا يمكن تحميل الصورة المختارة")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الصورة:\n{str(e)}")
    
    def remove_logo(self):
        """إزالة الشعار"""
        self.logo_label.clear()
        self.logo_label.setText("📷\nاضغط لاختيار الشعار")
        self.logo_path = None
        self.remove_logo_btn.setEnabled(False)
        
        # إعادة تصميم الإطار الأصلي
        self.logo_frame.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                background: white;
            }
        """)
    
    def save_settings(self):
        """حفظ إعدادات الشركة في ملف company_settings.json وتحديث البرنامج فوراً"""
        company_data = {
            "company_name": self.company_name_edit.text().strip(),
            "owner_name": self.owner_name_edit.text().strip(),
            "phone": self.phone_edit.text().strip(),
            "email": self.email_edit.text().strip(),
            "address": self.address_edit.toPlainText().strip(),
            "logo_path": self.logo_path,
            "activation_code": self.activate_year_edit.text().strip(),
            "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "setup_completed": True
        }
        settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
        try:
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(company_data, f, ensure_ascii=False, indent=4)
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            from main import resource_path
            from database.users import init_db
            db_path = resource_path('accounting.db')
            if not os.path.exists(db_path):
                from sqlalchemy import create_engine
                engine = create_engine(f'sqlite:///{db_path}', echo=True)
                init_db(engine)
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الشركة بنجاح!")
            self.setup_completed.emit(company_data)  # إشارة لإعادة تحميل الإعدادات في الواجهة الرئيسية
            self.accept()  # إغلاق نافذة الإعدادات بعد الحفظ
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def skip_setup(self):
        """تخطي الإعداد"""
        reply = QMessageBox.question(
            self,
            "تأكيد التخطي",
            "هل تريد تخطي إعداد الشركة؟\nيمكنك إعدادها لاحقاً من قائمة النظام.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إنشاء إعدادات افتراضية
            self.company_data = {
                'company_name': 'شركة المحاسبة',
                'owner_name': 'المدير العام',
                'phone': '',
                'email': '',
                'address': '',
                'logo_path': None,
                'setup_completed': False
            }
            
            # حفظ الإعدادات الافتراضية
            try:
                settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(self.company_data, f, ensure_ascii=False, indent=4)
                # إنشاء قاعدة البيانات إذا لم تكن موجودة
                from main import resource_path
                from database.users import init_db
                import os
                db_path = resource_path('accounting.db')
                if not os.path.exists(db_path):
                    from sqlalchemy import create_engine
                    engine = create_engine(f'sqlite:///{db_path}', echo=True)
                    init_db(engine)
                self.setup_completed.emit(self.company_data)
                self.accept()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")

def check_initial_setup_needed():
    """فحص ما إذا كان الإعداد الأولي مطلوب"""
    settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
    if not os.path.exists(settings_file):
        return True
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        # إذا لم يكتمل الإعداد من قبل
        return not settings.get('setup_completed', False)
    except:
        return True

def load_company_settings():
    """تحميل إعدادات الشركة"""
    settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
    default_settings = {
        'company_name': 'شركة المحاسبة',
        'owner_name': 'المدير العام',
        'phone': '',
        'email': '',
        'address': '',
        'logo_path': None,
        'setup_completed': False
    }
    if not os.path.exists(settings_file):
        return default_settings
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        # دمج الإعدادات مع الافتراضية
        for key in default_settings:
            if key not in settings:
                settings[key] = default_settings[key]
        return settings
    except:
        return default_settings
