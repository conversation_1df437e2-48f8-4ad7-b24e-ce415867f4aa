// qabstractseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qabstractseries.h>
%End

    class QAbstractSeries : public QObject /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qabstractseries.h>
%End

    public:
        enum SeriesType
        {
            SeriesTypeLine,
            SeriesTypeArea,
            SeriesTypeBar,
            SeriesTypeStackedBar,
            SeriesTypePercentBar,
            SeriesTypePie,
            SeriesTypeScatter,
            SeriesTypeSpline,
%If (QtChart_1_1_0 -)
            SeriesTypeHorizontalBar,
%End
%If (QtChart_1_1_0 -)
            SeriesTypeHorizontalStackedBar,
%End
%If (QtChart_1_1_0 -)
            SeriesTypeHorizontalPercentBar,
%End
%If (QtChart_1_3_0 -)
            SeriesTypeBoxPlot,
%End
%If (QtChart_5_8_0 -)
            SeriesTypeCandlestick,
%End
        };

%If (QtChart_2_0_0 -)
// %ConvertToSubClassCode for v2 and later
%ConvertToSubClassCode
static struct class_graph {
    const char *name;
    sipTypeDef **type;
    int yes, no;
} graph[] = {
    {sipName_QtCharts__QAbstractAxis, &sipType_QtCharts_QAbstractAxis, 19, 1},
    {sipName_QtCharts__QAbstractSeries, &sipType_QtCharts_QAbstractSeries, 24, 2},
    {sipName_QtCharts__QLegendMarker, &sipType_QtCharts_QLegendMarker, 39, 3},
    {sipName_QtCharts__QBarSet, &sipType_QtCharts_QBarSet, -1, 4},
    {sipName_QtCharts__QBoxSet, &sipType_QtCharts_QBoxSet, -1, 5},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QCandlestickModelMapper, &sipType_QtCharts_QCandlestickModelMapper, 45, 6},
#else
    {0, 0, 45, 6},
#endif
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QCandlestickSet, &sipType_QtCharts_QCandlestickSet, -1, 7},
#else
    {0, 0, -1, 7},
#endif
    {sipName_QtCharts__QChart, &sipType_QtCharts_QChart, 47, 8},
    {sipName_QtCharts__QChartView, &sipType_QtCharts_QChartView, -1, 9},
    {sipName_QtCharts__QHBarModelMapper, &sipType_QtCharts_QHBarModelMapper, -1, 10},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QHBoxPlotModelMapper, &sipType_QtCharts_QHBoxPlotModelMapper, -1, 11},
#else
    {0, 0, -1, 11},
#endif
    {sipName_QtCharts__QHPieModelMapper, &sipType_QtCharts_QHPieModelMapper, -1, 12},
    {sipName_QtCharts__QHXYModelMapper, &sipType_QtCharts_QHXYModelMapper, -1, 13},
    {sipName_QtCharts__QLegend, &sipType_QtCharts_QLegend, -1, 14},
    {sipName_QtCharts__QPieSlice, &sipType_QtCharts_QPieSlice, -1, 15},
    {sipName_QtCharts__QVBarModelMapper, &sipType_QtCharts_QVBarModelMapper, -1, 16},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QVBoxPlotModelMapper, &sipType_QtCharts_QVBoxPlotModelMapper, -1, 17},
#else
    {0, 0, -1, 17},
#endif
    {sipName_QtCharts__QVPieModelMapper, &sipType_QtCharts_QVPieModelMapper, -1, 18},
    {sipName_QtCharts__QVXYModelMapper, &sipType_QtCharts_QVXYModelMapper, -1, -1},
    {sipName_QtCharts__QBarCategoryAxis, &sipType_QtCharts_QBarCategoryAxis, -1, 20},
    {sipName_QtCharts__QValueAxis, &sipType_QtCharts_QValueAxis, 23, 21},
    {sipName_QtCharts__QDateTimeAxis, &sipType_QtCharts_QDateTimeAxis, -1, 22},
    {sipName_QtCharts__QLogValueAxis, &sipType_QtCharts_QLogValueAxis, -1, -1},
    {sipName_QtCharts__QCategoryAxis, &sipType_QtCharts_QCategoryAxis, -1, -1},
    {sipName_QtCharts__QAbstractBarSeries, &sipType_QtCharts_QAbstractBarSeries, 30, 25},
    {sipName_QtCharts__QAreaSeries, &sipType_QtCharts_QAreaSeries, -1, 26},
    {sipName_QtCharts__QBoxPlotSeries, &sipType_QtCharts_QBoxPlotSeries, -1, 27},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QCandlestickSeries, &sipType_QtCharts_QCandlestickSeries, -1, 28},
#else
    {0, 0, -1, 28},
#endif
    {sipName_QtCharts__QXYSeries, &sipType_QtCharts_QXYSeries, 36, 29},
    {sipName_QtCharts__QPieSeries, &sipType_QtCharts_QPieSeries, -1, -1},
    {sipName_QtCharts__QBarSeries, &sipType_QtCharts_QBarSeries, -1, 31},
    {sipName_QtCharts__QHorizontalBarSeries, &sipType_QtCharts_QHorizontalBarSeries, -1, 32},
    {sipName_QtCharts__QHorizontalPercentBarSeries, &sipType_QtCharts_QHorizontalPercentBarSeries, -1, 33},
    {sipName_QtCharts__QHorizontalStackedBarSeries, &sipType_QtCharts_QHorizontalStackedBarSeries, -1, 34},
    {sipName_QtCharts__QPercentBarSeries, &sipType_QtCharts_QPercentBarSeries, -1, 35},
    {sipName_QtCharts__QStackedBarSeries, &sipType_QtCharts_QStackedBarSeries, -1, -1},
    {sipName_QtCharts__QLineSeries, &sipType_QtCharts_QLineSeries, 38, 37},
    {sipName_QtCharts__QScatterSeries, &sipType_QtCharts_QScatterSeries, -1, -1},
    {sipName_QtCharts__QSplineSeries, &sipType_QtCharts_QSplineSeries, -1, -1},
    {sipName_QtCharts__QAreaLegendMarker, &sipType_QtCharts_QAreaLegendMarker, -1, 40},
    {sipName_QtCharts__QBarLegendMarker, &sipType_QtCharts_QBarLegendMarker, -1, 41},
    {sipName_QtCharts__QBoxPlotLegendMarker, &sipType_QtCharts_QBoxPlotLegendMarker, -1, 42},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QCandlestickLegendMarker, &sipType_QtCharts_QCandlestickLegendMarker, -1, 43},
#else
    {0, 0, -1, 43},
#endif
    {sipName_QtCharts__QPieLegendMarker, &sipType_QtCharts_QPieLegendMarker, -1, 44},
    {sipName_QtCharts__QXYLegendMarker, &sipType_QtCharts_QXYLegendMarker, -1, -1},
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QHCandlestickModelMapper, &sipType_QtCharts_QHCandlestickModelMapper, -1, 46},
#else
    {0, 0, -1, 46},
#endif
#if QTCHARTS_VERSION >= 0x050800
    {sipName_QtCharts__QVCandlestickModelMapper, &sipType_QtCharts_QVCandlestickModelMapper, -1, -1},
#else
    {0, 0, -1, -1},
#endif
    {sipName_QtCharts__QPolarChart, &sipType_QtCharts_QPolarChart, -1, -1},
};

int i = 0;

sipType = NULL;

do
{
    struct class_graph *cg = &graph[i];

    if (cg->name != NULL && sipCpp->inherits(cg->name))
    {
        sipType = *cg->type;
        i = cg->yes;
    }
    else
        i = cg->no;
}
while (i >= 0);
%End
%End
%If (- QtChart_2_0_0)
// %ConvertToSubClassCode for v1
%ConvertToSubClassCode
static struct class_graph {
    const char *name;
    sipTypeDef **type;
    int yes, no;
} graph[] = {
    {"QtCommercialChart::QAbstractSeries", &sipType_QAbstractSeries, 15, 1},
    {"QtCommercialChart::QAbstractAxis", &sipType_QAbstractAxis, 29, 2},
    {"QtCommercialChart::QChart", &sipType_QChart, 34, 3},
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QLegendMarker", &sipType_QLegendMarker, 35, 4},
#else
    {0, 0, 35, 4},
#endif
    {"QtCommercialChart::QBarSet", &sipType_QBarSet, -1, 5},
    {"QtCommercialChart::QHXYModelMapper", &sipType_QHXYModelMapper, -1, 6},
    {"QtCommercialChart::QHBarModelMapper", &sipType_QHBarModelMapper, -1, 7},
    {"QtCommercialChart::QVBarModelMapper", &sipType_QVBarModelMapper, -1, 8},
    {"QtCommercialChart::QLegend", &sipType_QLegend, -1, 9},
    {"QtCommercialChart::QPieSlice", &sipType_QPieSlice, -1, 10},
    {"QtCommercialChart::QChartView", &sipType_QChartView, -1, 11},
    {"QtCommercialChart::QVPieModelMapper", &sipType_QVPieModelMapper, -1, 12},
#if QTCOMMERCIALCHART_VERSION >= 0x010300
    {"QtCommercialChart::QBoxSet", &sipType_QBoxSet, -1, 13},
#else
    {0, 0, -1, 13},
#endif
    {"QtCommercialChart::QHPieModelMapper", &sipType_QHPieModelMapper, -1, 14},
    {"QtCommercialChart::QVXYModelMapper", &sipType_QVXYModelMapper, -1, -1},
    {"QtCommercialChart::QAreaSeries", &sipType_QAreaSeries, -1, 16},
    {"QtCommercialChart::QXYSeries", &sipType_QXYSeries, 20, 17},
    {"QtCommercialChart::QPieSeries", &sipType_QPieSeries, -1, 18},
    {"QtCommercialChart::QAbstractBarSeries", &sipType_QAbstractBarSeries, 23, 19},
#if QTCOMMERCIALCHART_VERSION >= 0x010300
    {"QtCommercialChart::QBoxPlotSeries", &sipType_QBoxPlotSeries, -1, -1},
#else
    {0, 0, -1, -1},
#endif
    {"QtCommercialChart::QScatterSeries", &sipType_QScatterSeries, -1, 21},
    {"QtCommercialChart::QLineSeries", &sipType_QLineSeries, 22, -1},
    {"QtCommercialChart::QSplineSeries", &sipType_QSplineSeries, -1, -1},
    {"QtCommercialChart::QPercentBarSeries", &sipType_QPercentBarSeries, -1, 24},
    {"QtCommercialChart::QHorizontalPercentBarSeries", &sipType_QHorizontalPercentBarSeries, -1, 25},
    {"QtCommercialChart::QHorizontalStackedBarSeries", &sipType_QHorizontalStackedBarSeries, -1, 26},
    {"QtCommercialChart::QBarSeries", &sipType_QBarSeries, -1, 27},
    {"QtCommercialChart::QHorizontalBarSeries", &sipType_QHorizontalBarSeries, -1, 28},
    {"QtCommercialChart::QStackedBarSeries", &sipType_QStackedBarSeries, -1, -1},
    {"QtCommercialChart::QBarCategoryAxis", &sipType_QBarCategoryAxis, -1, 30},
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QLogValueAxis", &sipType_QLogValueAxis, -1, 31},
#else
    {0, 0, -1, 31},
#endif
    {"QtCommercialChart::QDateTimeAxis", &sipType_QDateTimeAxis, -1, 32},
    {"QtCommercialChart::QValueAxis", &sipType_QValueAxis, 33, -1},
    {"QtCommercialChart::QCategoryAxis", &sipType_QCategoryAxis, -1, -1},
#if QTCOMMERCIALCHART_VERSION >= 0x010300
    {"QtCommercialChart::QPolarChart", &sipType_QPolarChart, -1, -1},
#else
    {0, 0, -1, -1},
#endif
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QXYLegendMarker", &sipType_QXYLegendMarker, -1, 36},
#else
    {0, 0, -1, 36},
#endif
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QPieLegendMarker", &sipType_QPieLegendMarker, -1, 37},
#else
    {0, 0, -1, 37},
#endif
#if QTCOMMERCIALCHART_VERSION >= 0x010300
    {"QtCommercialChart::QBoxPlotLegendMarker", &sipType_QBoxPlotLegendMarker, -1, 38},
#else
    {0, 0, -1, 38},
#endif
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QAreaLegendMarker", &sipType_QAreaLegendMarker, -1, 39},
#else
    {0, 0, -1, 39},
#endif
#if QTCOMMERCIALCHART_VERSION >= 0x010200
    {"QtCommercialChart::QBarLegendMarker", &sipType_QBarLegendMarker, -1, -1},
#else
    {0, 0, -1, -1},
#endif
};

int i = 0;

sipType = NULL;

do
{
    struct class_graph *cg = &graph[i];

    if (cg->name != NULL && sipCpp->inherits(cg->name))
    {
        sipType = *cg->type;
        i = cg->yes;
    }
    else
        i = cg->no;
}
while (i >= 0);
%End
%End
        virtual ~QAbstractSeries();
        virtual QtCharts::QAbstractSeries::SeriesType type() const = 0;
        void setName(const QString &name);
        QString name() const;
        void setVisible(bool visible = true);
        bool isVisible() const;
        QtCharts::QChart *chart() const;
        void show();
        void hide();

    signals:
        void nameChanged();
        void visibleChanged();

    public:
%If (QtChart_1_2_0 -)
        qreal opacity() const;
%End
%If (QtChart_1_2_0 -)
        void setOpacity(qreal opacity);
%End
%If (QtChart_1_2_0 -)
        bool attachAxis(QtCharts::QAbstractAxis *axis);
%End
%If (QtChart_1_2_0 -)
        bool detachAxis(QtCharts::QAbstractAxis *axis);
%End
%If (QtChart_1_2_0 -)
        QList<QtCharts::QAbstractAxis *> attachedAxes();
%End

    signals:
%If (QtChart_1_2_0 -)
        void opacityChanged();
%End

    public:
%If (QtChart_2_1_0 -)
        void setUseOpenGL(bool enable = true);
%End
%If (QtChart_2_1_0 -)
        bool useOpenGL() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void useOpenGLChanged();
%End
    };
};
