import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Controls.Fusion 2.15'

Module {
    dependencies: ["QtQuick.Controls 2.0"]
    Component {
        name: "QQuickFusionBusyIndicator"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor" }
        Property { name: "running"; type: "bool" }
    }
    Component {
        name: "QQuickFusionDial"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.Fusion.impl/DialImpl 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "highlight"; type: "bool" }
        Property { name: "palette"; type: "QPalette" }
    }
    Component {
        name: "QQuickFusionKnob"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.Fusion.impl/KnobImpl 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "palette"; type: "QPalette" }
    }
    Component {
        name: "QQuickFusionStyle"
        prototype: "QObject"
        exports: ["QtQuick.Controls.Fusion.impl/Fusion 2.3"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Property { name: "lightShade"; type: "QColor"; isReadonly: true }
        Property { name: "darkShade"; type: "QColor"; isReadonly: true }
        Property { name: "topShadow"; type: "QColor"; isReadonly: true }
        Property { name: "innerContrastLine"; type: "QColor"; isReadonly: true }
        Method {
            name: "highlight"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "highlightedText"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "outline"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "highlightedOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "tabFrameColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "down"; type: "bool" }
            Parameter { name: "hovered"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "down"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
            Parameter { name: "highlighted"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
            Parameter { name: "highlighted"; type: "bool" }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
        Method {
            name: "gradientStart"
            type: "QColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "gradientStop"
            type: "QColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "mergedColors"
            type: "QColor"
            Parameter { name: "colorA"; type: "QColor" }
            Parameter { name: "colorB"; type: "QColor" }
            Parameter { name: "factor"; type: "int" }
        }
        Method {
            name: "mergedColors"
            type: "QColor"
            Parameter { name: "colorA"; type: "QColor" }
            Parameter { name: "colorB"; type: "QColor" }
        }
        Method {
            name: "grooveColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QPalette" }
        }
    }
    Component {
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        Enum {
            name: "Flags"
            values: {
                "ItemClipsChildrenToShape": 1,
                "ItemAcceptsInputMethod": 2,
                "ItemIsFocusScope": 4,
                "ItemHasContents": 8,
                "ItemAcceptsDrops": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickPaintedItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Enum {
            name: "RenderTarget"
            values: {
                "Image": 0,
                "FramebufferObject": 1,
                "InvertedYFramebufferObject": 2
            }
        }
        Enum {
            name: "PerformanceHints"
            values: {
                "FastFBOResizing": 1
            }
        }
        Property { name: "contentsSize"; type: "QSize" }
        Property { name: "fillColor"; type: "QColor" }
        Property { name: "contentsScale"; type: "double" }
        Property { name: "renderTarget"; type: "RenderTarget" }
        Property { name: "textureSize"; type: "QSize" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/ButtonPanel 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/ButtonPanel 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "highlighted"; type: "bool" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/CheckIndicator 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/CheckIndicator 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "pressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "checkMarkColor"; type: "QColor"; isReadonly: true }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/RadioIndicator 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/RadioIndicator 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "pressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "checkMarkColor"; type: "QColor"; isReadonly: true }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/SliderGroove 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/SliderGroove 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "offset"; type: "double" }
        Property { name: "progress"; type: "double" }
        Property { name: "visualProgress"; type: "double" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/SliderHandle 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/SliderHandle 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "palette"; type: "QVariant" }
        Property { name: "pressed"; type: "bool" }
        Property { name: "hovered"; type: "bool" }
        Property { name: "vertical"; type: "bool" }
        Property { name: "visualFocus"; type: "bool" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Fusion.impl/SwitchIndicator 2.3"
        exports: ["QtQuick.Controls.Fusion.impl/SwitchIndicator 2.3"]
        exportMetaObjectRevisions: [3]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "pressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "checkMarkColor"; type: "QColor"; isReadonly: true }
    }
}
