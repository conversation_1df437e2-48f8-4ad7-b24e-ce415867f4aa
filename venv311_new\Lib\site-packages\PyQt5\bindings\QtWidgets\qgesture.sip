// qgesture.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGesture : QObject
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    explicit QGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QGesture();
    Qt::GestureType gestureType() const;
    Qt::GestureState state() const;
    QPointF hotSpot() const;
    void setHotSpot(const QPointF &value);
    bool hasHotSpot() const;
    void unsetHotSpot();

    enum GestureCancelPolicy
    {
        CancelNone,
        CancelAllInContext,
    };

    void setGestureCancelPolicy(QGesture::GestureCancelPolicy policy);
    QGesture::GestureCancelPolicy gestureCancelPolicy() const;
};

class QPanGesture : QGesture
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    explicit QPanGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QPanGesture();
    QPointF lastOffset() const;
    QPointF offset() const;
    QPointF delta() const;
    qreal acceleration() const;
    void setLastOffset(const QPointF &value);
    void setOffset(const QPointF &value);
    void setAcceleration(qreal value);
};

class QPinchGesture : QGesture
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    enum ChangeFlag
    {
        ScaleFactorChanged,
        RotationAngleChanged,
        CenterPointChanged,
    };

    typedef QFlags<QPinchGesture::ChangeFlag> ChangeFlags;
    explicit QPinchGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QPinchGesture();
    QPinchGesture::ChangeFlags totalChangeFlags() const;
    void setTotalChangeFlags(QPinchGesture::ChangeFlags value);
    QPinchGesture::ChangeFlags changeFlags() const;
    void setChangeFlags(QPinchGesture::ChangeFlags value);
    QPointF startCenterPoint() const;
    QPointF lastCenterPoint() const;
    QPointF centerPoint() const;
    void setStartCenterPoint(const QPointF &value);
    void setLastCenterPoint(const QPointF &value);
    void setCenterPoint(const QPointF &value);
    qreal totalScaleFactor() const;
    qreal lastScaleFactor() const;
    qreal scaleFactor() const;
    void setTotalScaleFactor(qreal value);
    void setLastScaleFactor(qreal value);
    void setScaleFactor(qreal value);
    qreal totalRotationAngle() const;
    qreal lastRotationAngle() const;
    qreal rotationAngle() const;
    void setTotalRotationAngle(qreal value);
    void setLastRotationAngle(qreal value);
    void setRotationAngle(qreal value);
};

class QSwipeGesture : QGesture
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    enum SwipeDirection
    {
        NoDirection,
        Left,
        Right,
        Up,
        Down,
    };

    explicit QSwipeGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QSwipeGesture();
    QSwipeGesture::SwipeDirection horizontalDirection() const;
    QSwipeGesture::SwipeDirection verticalDirection() const;
    qreal swipeAngle() const;
    void setSwipeAngle(qreal value);
};

class QTapGesture : QGesture
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    explicit QTapGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QTapGesture();
    QPointF position() const;
    void setPosition(const QPointF &pos);
};

class QTapAndHoldGesture : QGesture
{
%TypeHeaderCode
#include <qgesture.h>
%End

public:
    explicit QTapAndHoldGesture(QObject *parent /TransferThis/ = 0);
    virtual ~QTapAndHoldGesture();
    QPointF position() const;
    void setPosition(const QPointF &pos);
    static void setTimeout(int msecs);
    static int timeout();
};

class QGestureEvent : QEvent
{
%TypeHeaderCode
#include <qgesture.h>
%End

%ConvertToSubClassCode
    sipType = ((sipCpp->type() == QEvent::Gesture) ? sipType_QGestureEvent : 0);
%End

public:
    explicit QGestureEvent(const QList<QGesture *> &gestures);
    virtual ~QGestureEvent();
    QList<QGesture *> gestures() const;
    QGesture *gesture(Qt::GestureType type) const;
    QList<QGesture *> activeGestures() const;
    QList<QGesture *> canceledGestures() const;
    void setAccepted(bool accepted);
    bool isAccepted() const;
    void accept();
    void ignore();
    void setAccepted(QGesture *, bool);
    void accept(QGesture *);
    void ignore(QGesture *);
    bool isAccepted(QGesture *) const;
    void setAccepted(Qt::GestureType, bool);
    void accept(Qt::GestureType);
    void ignore(Qt::GestureType);
    bool isAccepted(Qt::GestureType) const;
    QWidget *widget() const;
    QPointF mapToGraphicsScene(const QPointF &gesturePoint) const;
};
