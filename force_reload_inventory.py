#!/usr/bin/env python3
"""
إجبار إعادة تحميل ملف المخزون المحدث
"""

import sys
import os
import shutil
import importlib
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def force_clean_cache():
    """حذف جميع ملفات cache بقوة"""
    print("🧹 حذف جميع ملفات cache...")
    
    cache_patterns = [
        "gui/__pycache__",
        "__pycache__",
        "database/__pycache__",
        "*.pyc",
        "*.pyo"
    ]
    
    # حذف مجلدات cache
    for pattern in cache_patterns:
        if pattern.endswith("__pycache__"):
            if os.path.exists(pattern):
                try:
                    shutil.rmtree(pattern)
                    print(f"✅ تم حذف {pattern}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف {pattern}: {e}")
    
    # حذف ملفات .pyc في جميع المجلدات
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                try:
                    os.remove(os.path.join(root, file))
                    print(f"✅ تم حذف {file}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف {file}: {e}")

def main():
    print("🔄 إجبار إعادة تحميل ملف المخزون المحدث...")
    print("=" * 80)
    
    # حذف cache
    force_clean_cache()
    
    # إعداد التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        print("🔄 إعادة تحميل الوحدات...")
        
        # إزالة الوحدات من الذاكرة
        modules_to_remove = []
        for module_name in sys.modules:
            if module_name.startswith('gui.'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
            print(f"🗑️ تم حذف {module_name} من الذاكرة")
        
        # استيراد جديد
        print("📦 استيراد ملف المخزون الجديد...")
        from gui.inventory import InventoryWidget
        
        # التحقق من التحديثات
        print("🔍 التحقق من التحديثات...")
        
        # إنشاء نافذة اختبار
        from PyQt5.QtWidgets import QMainWindow
        from sqlalchemy import create_engine
        
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        main_window = QMainWindow()
        main_window.setWindowTitle("🎯 اختبار المخزون المحدث - إصدار جديد")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # إنشاء widget المخزون
        inventory_widget = InventoryWidget(engine)
        main_window.setCentralWidget(inventory_widget)
        
        # عرض النافذة
        main_window.show()
        
        # رسالة تأكيد
        QMessageBox.information(
            main_window,
            "✅ تم تحديث المخزون بنجاح!",
            """
🎉 تم تحميل ملف المخزون المحدث بنجاح!

📋 التحديثات الجديدة:
✅ تبويب "📦 المخزون"
✅ تبويب "⚙️ إدارة الأصناف"
✅ عمود "المكسب" بلون أخضر فاتح
✅ التحديث التلقائي للقيم
✅ حسابات دقيقة للأرباح

🔍 ما تشاهده الآن:
• تبويبان في أعلى الصفحة
• عمود "المكسب" في الجداول
• أزرار تعديل وحذف في تبويب "إدارة الأصناف"

🎯 اختبر الآن:
1️⃣ انقر على تبويب "⚙️ إدارة الأصناف"
2️⃣ لاحظ عمود "المكسب" الأخضر
3️⃣ اضغط "إضافة صنف جديد"
4️⃣ أدخل البيانات ولاحظ التحديث الفوري
            """
        )
        
        print("✅ تم فتح نافذة المخزون المحدثة!")
        print("")
        print("🎯 الآن يجب أن تشاهد:")
        print("   📋 تبويبين: '📦 المخزون' و '⚙️ إدارة الأصناف'")
        print("   📊 عمود 'المكسب' بلون أخضر فاتح")
        print("   ⚡ تحديث فوري للقيم عند إضافة/تعديل المنتجات")
        print("   🎨 واجهة محسنة مع أزرار تعديل وحذف")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        
        QMessageBox.critical(
            None,
            "خطأ في التحديث",
            f"حدث خطأ أثناء تحميل المخزون المحدث:\n{str(e)}\n\nيرجى المحاولة مرة أخرى."
        )
    
    print("🎯 انتهى الاختبار")
    print("=" * 80)
    print("📊 ملخص التحديث:")
    print("")
    print("   ✅ تم حذف جميع ملفات cache")
    print("   ✅ تم إعادة تحميل الوحدات")
    print("   ✅ تم تحميل ملف المخزون المحدث")
    print("   ✅ تم عرض النافذة مع التحديثات")
    print("")
    print("🎉 إذا ظهرت التحديثات، فالمشكلة محلولة!")
    print("   إذا لم تظهر، فهناك مشكلة أخرى نحتاج لحلها.")

if __name__ == "__main__":
    main()
