#!/usr/bin/env python3
"""
اختبار إصلاح إحداثيات النص في الترويسة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار إصلاح إحداثيات النص في الترويسة...")
    print("=" * 60)
    print("🔧 الإصلاحات المطبقة:")
    print("   • استخدام drawText(x, y, text) بدلاً من drawText(x, y, w, h, align, text)")
    print("   • تحديد إحداثيات Y صحيحة داخل الترويسة")
    print("   • بداية النص من y + 50 (داخل الترويسة)")
    print("   • التأكد من عدم تجاوز حدود الترويسة")
    print("   • ارتفاع ترويسة 180px")
    print("   • مساحة كافية للنصوص")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع الإحداثيات المصححة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من الترويسة:")
        print("   • هل النص ظاهر كاملاً؟")
        print("   • هل النص داخل الترويسة الخضراء؟")
        print("   • هل جميع التفاصيل مقروءة؟")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 النتيجة المتوقعة:")
    print("   ✅ نص كامل وواضح داخل الترويسة")
    print("   ✅ لا يوجد قطع في النص")
    print("   ✅ جميع التفاصيل ظاهرة")
    print("   ✅ إحداثيات صحيحة")

if __name__ == "__main__":
    main()
