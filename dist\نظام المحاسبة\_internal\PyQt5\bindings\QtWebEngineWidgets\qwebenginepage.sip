// qwebenginepage.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEnginePage : public QObject
{
%TypeHeaderCode
#include <qwebenginepage.h>
%End

public:
    enum WebAction
    {
        NoWebAction,
        Back,
        Forward,
        Stop,
        Reload,
        Cut,
        Copy,
        Paste,
        Undo,
        Redo,
        SelectAll,
        ReloadAndBypassCache,
        PasteAndMatchStyle,
%If (QtWebEngine_5_6_0 -)
        OpenLinkInThisWindow,
%End
%If (QtWebEngine_5_6_0 -)
        OpenLinkInNewWindow,
%End
%If (QtWebEngine_5_6_0 -)
        OpenLinkInNewTab,
%End
%If (QtWebEngine_5_6_0 -)
        CopyLinkToClipboard,
%End
%If (QtWebEngine_5_6_0 -)
        DownloadLinkToDisk,
%End
%If (QtWebEngine_5_6_0 -)
        CopyImageToClipboard,
%End
%If (QtWebEngine_5_6_0 -)
        CopyImageUrlToClipboard,
%End
%If (QtWebEngine_5_6_0 -)
        DownloadImageToDisk,
%End
%If (QtWebEngine_5_6_0 -)
        CopyMediaUrlToClipboard,
%End
%If (QtWebEngine_5_6_0 -)
        ToggleMediaControls,
%End
%If (QtWebEngine_5_6_0 -)
        ToggleMediaLoop,
%End
%If (QtWebEngine_5_6_0 -)
        ToggleMediaPlayPause,
%End
%If (QtWebEngine_5_6_0 -)
        ToggleMediaMute,
%End
%If (QtWebEngine_5_6_0 -)
        DownloadMediaToDisk,
%End
%If (QtWebEngine_5_6_0 -)
        InspectElement,
%End
%If (QtWebEngine_5_6_0 -)
        ExitFullScreen,
%End
%If (QtWebEngine_5_6_0 -)
        RequestClose,
%End
%If (QtWebEngine_5_7_0 -)
        Unselect,
%End
%If (QtWebEngine_5_7_0 -)
        SavePage,
%End
%If (QtWebEngine_5_7_0 -)
        OpenLinkInNewBackgroundTab,
%End
%If (QtWebEngine_5_8_0 -)
        ViewSource,
%End
%If (QtWebEngine_5_10_0 -)
        ToggleBold,
%End
%If (QtWebEngine_5_10_0 -)
        ToggleItalic,
%End
%If (QtWebEngine_5_10_0 -)
        ToggleUnderline,
%End
%If (QtWebEngine_5_10_0 -)
        ToggleStrikethrough,
%End
%If (QtWebEngine_5_10_0 -)
        AlignLeft,
%End
%If (QtWebEngine_5_10_0 -)
        AlignCenter,
%End
%If (QtWebEngine_5_10_0 -)
        AlignRight,
%End
%If (QtWebEngine_5_10_0 -)
        AlignJustified,
%End
%If (QtWebEngine_5_10_0 -)
        Indent,
%End
%If (QtWebEngine_5_10_0 -)
        Outdent,
%End
%If (QtWebEngine_5_10_0 -)
        InsertOrderedList,
%End
%If (QtWebEngine_5_10_0 -)
        InsertUnorderedList,
%End
    };

    enum FindFlag
    {
        FindBackward,
        FindCaseSensitively,
    };

    typedef QFlags<QWebEnginePage::FindFlag> FindFlags;

    enum WebWindowType
    {
        WebBrowserWindow,
        WebBrowserTab,
        WebDialog,
%If (QtWebEngine_5_7_0 -)
        WebBrowserBackgroundTab,
%End
    };

    enum PermissionPolicy
    {
        PermissionUnknown,
        PermissionGrantedByUser,
        PermissionDeniedByUser,
    };

    enum Feature
    {
%If (QtWebEngine_5_13_0 -)
        Notifications,
%End
        Geolocation,
        MediaAudioCapture,
        MediaVideoCapture,
        MediaAudioVideoCapture,
%If (QtWebEngine_5_5_0 -)
        MouseLock,
%End
%If (QtWebEngine_5_10_0 -)
        DesktopVideoCapture,
%End
%If (QtWebEngine_5_10_0 -)
        DesktopAudioVideoCapture,
%End
    };

    enum FileSelectionMode
    {
        FileSelectOpen,
        FileSelectOpenMultiple,
    };

    enum JavaScriptConsoleMessageLevel
    {
        InfoMessageLevel,
        WarningMessageLevel,
        ErrorMessageLevel,
    };

    explicit QWebEnginePage(QObject *parent /TransferThis/ = 0);
%If (QtWebEngine_5_5_0 -)
    QWebEnginePage(QWebEngineProfile *profile, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QWebEnginePage();
    QWebEngineHistory *history() const;
    void setView(QWidget *view);
    QWidget *view() const;
    bool hasSelection() const;
    QString selectedText() const;
    QAction *action(QWebEnginePage::WebAction action) const;
    virtual void triggerAction(QWebEnginePage::WebAction action, bool checked = false);
    virtual bool event(QEvent *);
    void findText(const QString &subString, QWebEnginePage::FindFlags options = QWebEnginePage::FindFlags(), SIP_PYCALLABLE resultCallback /AllowNone,TypeHint="Callable[[bool], None]"/ = 0);
%MethodCode
        // Make sure any callable doesn't get garbage collected until it is invoked.
        Py_XINCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->findText(*a0, *a1, [a2](bool arg) {
            if (a2)
            {
                SIP_BLOCK_THREADS
                
                PyObject *res;
        
                res = PyObject_CallFunctionObjArgs(a2, (arg ? Py_True : Py_False), NULL);
        
                Py_DECREF(a2);
        
                if (!res)
                    pyqt5_qtwebenginewidgets_err_print();
                else
                    Py_DECREF(res);
        
                SIP_UNBLOCK_THREADS
            }
        });
        
        Py_END_ALLOW_THREADS
%End

    QMenu *createStandardContextMenu() /Factory/;
    void setFeaturePermission(const QUrl &securityOrigin, QWebEnginePage::Feature feature, QWebEnginePage::PermissionPolicy policy);
    void load(const QUrl &url);
%If (QtWebEngine_5_9_0 -)
    void load(const QWebEngineHttpRequest &request);
%End
    void setHtml(const QString &html, const QUrl &baseUrl = QUrl());
    void setContent(const QByteArray &data, const QString &mimeType = QString(), const QUrl &baseUrl = QUrl());
    void toHtml(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QString], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->toHtml([a0](const QString &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QString(arg), sipType_QString, NULL);
        
            Py_DECREF(a0);
            
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
            
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    void toPlainText(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QString], None]"/) const;
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->toPlainText([a0](const QString &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QString(arg), sipType_QString, NULL);
        
            Py_DECREF(a0);
            
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
            
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    QString title() const;
    void setUrl(const QUrl &url);
    QUrl url() const;
    QUrl requestedUrl() const;
    QUrl iconUrl() const;
    qreal zoomFactor() const;
    void setZoomFactor(qreal factor);
%If (QtWebEngine_5_7_0 -)
    void runJavaScript(const QString &scriptSource, quint32 worldId);
%End
%If (QtWebEngine_5_7_0 -)
    void runJavaScript(const QString &scriptSource, quint32 worldId, SIP_PYCALLABLE resultCallback);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a2);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, a1, [a2](const QVariant &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a2, "N", new QVariant(arg), sipType_QVariant, NULL);
        
            Py_DECREF(a2);
        
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

%End
    void runJavaScript(const QString &scriptSource);
    void runJavaScript(const QString &scriptSource, SIP_PYCALLABLE resultCallback /TypeHint="Callable[[Any], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a1);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->runJavaScript(*a0, [a1](const QVariant &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a1, "N", new QVariant(arg), sipType_QVariant, NULL);
        
            Py_DECREF(a1);
        
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    QWebEngineSettings *settings() const;

signals:
    void loadStarted();
    void loadProgress(int progress);
    void loadFinished(bool ok);
    void linkHovered(const QString &url);
    void selectionChanged();
    void geometryChangeRequested(const QRect &geom);
    void windowCloseRequested();
    void featurePermissionRequested(const QUrl &securityOrigin, QWebEnginePage::Feature feature);
    void featurePermissionRequestCanceled(const QUrl &securityOrigin, QWebEnginePage::Feature feature);
    void authenticationRequired(const QUrl &requestUrl, QAuthenticator *authenticator);
    void proxyAuthenticationRequired(const QUrl &requestUrl, QAuthenticator *authenticator, const QString &proxyHost);
    void titleChanged(const QString &title);
    void urlChanged(const QUrl &url);
    void iconUrlChanged(const QUrl &url);

protected:
    virtual QWebEnginePage *createWindow(QWebEnginePage::WebWindowType type);
    virtual QStringList chooseFiles(QWebEnginePage::FileSelectionMode mode, const QStringList &oldFiles, const QStringList &acceptedMimeTypes);
    virtual void javaScriptAlert(const QUrl &securityOrigin, const QString &msg);
    virtual bool javaScriptConfirm(const QUrl &securityOrigin, const QString &msg);
    virtual bool javaScriptPrompt(const QUrl &securityOrigin, const QString &msg, const QString &defaultValue, QString *result /Out/);
    virtual void javaScriptConsoleMessage(QWebEnginePage::JavaScriptConsoleMessageLevel level, const QString &message, int lineNumber, const QString &sourceID);
    virtual bool certificateError(const QWebEngineCertificateError &certificateError /NoCopy/);

public:
%If (QtWebEngine_5_5_0 -)

    enum NavigationType
    {
        NavigationTypeLinkClicked,
        NavigationTypeTyped,
        NavigationTypeFormSubmitted,
        NavigationTypeBackForward,
        NavigationTypeReload,
%If (QtWebEngine_5_14_0 -)
        NavigationTypeRedirect,
%End
        NavigationTypeOther,
    };

%End
%If (QtWebEngine_5_5_0 -)
    QWebEngineProfile *profile() const;
%End
%If (QtWebEngine_5_5_0 -)
    QWebEngineScriptCollection &scripts();
%End
%If (QtWebEngine_5_5_0 -)
%If (PyQt_WebChannel)
    QWebChannel *webChannel() const;
%End
%End
%If (QtWebEngine_5_5_0 -)
%If (PyQt_WebChannel)
    void setWebChannel(QWebChannel *);
%End
%End
%If (QtWebEngine_5_7_0 -)
%If (PyQt_WebChannel)
    void setWebChannel(QWebChannel *, uint worldId);
%End
%End

protected:
%If (QtWebEngine_5_5_0 -)
    virtual bool acceptNavigationRequest(const QUrl &url, QWebEnginePage::NavigationType type, bool isMainFrame);
%End

public:
%If (QtWebEngine_5_6_0 -)

    enum RenderProcessTerminationStatus
    {
        NormalTerminationStatus,
        AbnormalTerminationStatus,
        CrashedTerminationStatus,
        KilledTerminationStatus,
    };

%End
%If (QtWebEngine_5_6_0 -)
    QColor backgroundColor() const;
%End
%If (QtWebEngine_5_6_0 -)
    void setBackgroundColor(const QColor &color);
%End

signals:
%If (QtWebEngine_5_6_0 -)
    void fullScreenRequested(QWebEngineFullScreenRequest fullScreenRequest);
%End
%If (QtWebEngine_5_6_0 -)
    void renderProcessTerminated(QWebEnginePage::RenderProcessTerminationStatus terminationStatus /ScopesStripped=1/, int exitCode);
%End

public:
%If (QtWebEngine_5_7_0 -)
    QIcon icon() const;
%End
%If (QtWebEngine_5_7_0 -)
    QPointF scrollPosition() const;
%End
%If (QtWebEngine_5_7_0 -)
    QSizeF contentsSize() const;
%End
%If (QtWebEngine_5_7_0 -)
    bool isAudioMuted() const;
%End
%If (QtWebEngine_5_7_0 -)
    void setAudioMuted(bool muted);
%End
%If (QtWebEngine_5_7_0 -)
    bool recentlyAudible() const;
%End
%If (QtWebEngine_5_7_0 -)
    void printToPdf(const QString &filePath, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::PageSizeId::A4), QPageLayout::Orientation::Portrait, QMarginsF()));
%End
%If (QtWebEngine_5_7_0 -)
    void printToPdf(SIP_PYCALLABLE resultCallback /TypeHint="Callable[[QByteArray], None]"/, const QPageLayout &pageLayout = QPageLayout(QPageSize(QPageSize::PageSizeId::A4), QPageLayout::Orientation::Portrait, QMarginsF()));
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->printToPdf([a0](const QByteArray &arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a0, "N", new QByteArray(arg), sipType_QByteArray, NULL);
        
            Py_DECREF(a0);
        
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        }, *a1);
        
        Py_END_ALLOW_THREADS
%End

%End
%If (QtWebEngine_5_7_0 -)
    const QWebEngineContextMenuData &contextMenuData() const;
%End

signals:
%If (QtWebEngine_5_7_0 -)
    void iconChanged(const QIcon &icon);
%End
%If (QtWebEngine_5_7_0 -)
    void scrollPositionChanged(const QPointF &position);
%End
%If (QtWebEngine_5_7_0 -)
    void contentsSizeChanged(const QSizeF &size);
%End
%If (QtWebEngine_5_7_0 -)
    void audioMutedChanged(bool muted);
%End
%If (QtWebEngine_5_7_0 -)
    void recentlyAudibleChanged(bool recentlyAudible);
%End
%If (QtWebEngine_5_9_0 -)
    void pdfPrintingFinished(const QString &filePath, bool success);
%End

public:
%If (QtWebEngine_5_8_0 -)
    void replaceMisspelledWord(const QString &replacement);
%End
%If (QtWebEngine_5_8_0 -)
    void save(const QString &filePath, QWebEngineDownloadItem::SavePageFormat format = QWebEngineDownloadItem::MimeHtmlSaveFormat) const /ReleaseGIL/;
%End
%If (QtWebEngine_5_8_0 -)
%If (Py_v3)
%If (PyQt_Printer)
    void print(QPrinter *printer, SIP_PYCALLABLE resultCallback /TypeHint="Callable[[bool], None]"/);
%MethodCode
        // Make sure the callable doesn't get garbage collected until it is invoked.
        Py_INCREF(a1);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->print(a0, [a1](bool arg) {
            SIP_BLOCK_THREADS
            
            PyObject *res;
        
            res = sipCallMethod(NULL, a1, "b", arg);
        
            Py_DECREF(a1);
        
            if (!res)
                pyqt5_qtwebenginewidgets_err_print();
            else
                Py_DECREF(res);
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

%End
%End
%End
%If (QtWebEngine_5_10_0 -)
    void download(const QUrl &url, const QString &filename = QString());
%End
%If (QtWebEngine_5_11_0 -)
    void setInspectedPage(QWebEnginePage *page);
%End
%If (QtWebEngine_5_11_0 -)
    QWebEnginePage *inspectedPage() const;
%End
%If (QtWebEngine_5_11_0 -)
    void setDevToolsPage(QWebEnginePage *page);
%End
%If (QtWebEngine_5_11_0 -)
    QWebEnginePage *devToolsPage() const;
%End

signals:
%If (QtWebEngine_5_11_0 -)
    void quotaRequested(QWebEngineQuotaRequest quotaRequest);
%End
%If (QtWebEngine_5_11_0 -)
    void registerProtocolHandlerRequested(QWebEngineRegisterProtocolHandlerRequest request);
%End
%If (QtWebEngine_5_12_0 -)
%If (PyQt_SSL)
    void selectClientCertificate(QWebEngineClientCertificateSelection clientCertSelection);
%End
%End
%If (QtWebEngine_5_12_0 -)
    void printRequested();
%End

public:
%If (QtWebEngine_5_13_0 -)
    void setUrlRequestInterceptor(QWebEngineUrlRequestInterceptor *interceptor);
%End
%If (QtWebEngine_5_14_0 -)

    enum class LifecycleState
    {
        Active,
        Frozen,
        Discarded,
    };

%End
%If (QtWebEngine_5_14_0 -)
    QWebEnginePage::LifecycleState lifecycleState() const;
%End
%If (QtWebEngine_5_14_0 -)
    void setLifecycleState(QWebEnginePage::LifecycleState state);
%End
%If (QtWebEngine_5_14_0 -)
    QWebEnginePage::LifecycleState recommendedState() const;
%End
%If (QtWebEngine_5_14_0 -)
    bool isVisible() const;
%End
%If (QtWebEngine_5_14_0 -)
    void setVisible(bool visible);
%End

signals:
%If (QtWebEngine_5_14_0 -)
    void visibleChanged(bool visible);
%End
%If (QtWebEngine_5_14_0 -)
    void lifecycleStateChanged(QWebEnginePage::LifecycleState state);
%End
%If (QtWebEngine_5_14_0 -)
    void recommendedStateChanged(QWebEnginePage::LifecycleState state);
%End
%If (QtWebEngine_5_14_0 -)
    void findTextFinished(const QWebEngineFindTextResult &result);
%End

public:
%If (QtWebEngine_5_15_0 -)
    qint64 renderProcessPid() const;
%End

signals:
%If (QtWebEngine_5_15_0 -)
    void renderProcessPidChanged(qint64 pid);
%End
};

%If (QtWebEngine_5_11_0 -)
QFlags<QWebEnginePage::FindFlag> operator|(QWebEnginePage::FindFlag f1, QFlags<QWebEnginePage::FindFlag> f2);
%End
