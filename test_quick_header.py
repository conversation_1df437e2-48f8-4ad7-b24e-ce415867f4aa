#!/usr/bin/env python3
"""
اختبار سريع للترويسة المحسنة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class QuickHeaderTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار سريع للترويسة المحسنة")
        self.setGeometry(200, 200, 500, 400)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_label = QPushButton("🎯 اختبار سريع للترويسة المحسنة")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات التحسينات
        info_label = QPushButton("""
✅ التحسينات المطبقة:

🔧 حساب المساحة الديناميكي
📏 تقسيم النص الطويل تلقائياً
🖼️ تحميل اللوجو المخصص
📐 قياس عرض النص قبل الرسم
🎨 توزيع أفضل للمساحات
📱 تجاوب مع أطوال النصوص المختلفة

🎯 النتيجة: نص كامل وواضح بدون قطع!
        """)
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
                color: #2c3e50;
                font-size: 14px;
                padding: 20px;
                border: 2px solid #95a5a6;
                border-radius: 8px;
                text-align: left;
                margin: 15px 0;
                line-height: 1.6;
            }
        """)
        layout.addWidget(info_label)
        
        # أزرار الاختبار
        test_btn1 = QPushButton("🧾 اختبار فاتورة رقم 1")
        test_btn1.setStyleSheet(self.get_button_style("#3498db"))
        test_btn1.clicked.connect(lambda: self.test_invoice(1))
        layout.addWidget(test_btn1)
        
        test_btn7 = QPushButton("🧾 اختبار فاتورة رقم 7")
        test_btn7.setStyleSheet(self.get_button_style("#e74c3c"))
        test_btn7.clicked.connect(lambda: self.test_invoice(7))
        layout.addWidget(test_btn7)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#95a5a6"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background: linear-gradient(135deg, {color} 0%, {color}DD 100%);
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: linear-gradient(135deg, {color}EE 0%, {color}CC 100%);
            }}
            QPushButton:pressed {{
                background: linear-gradient(135deg, {color}BB 0%, {color}AA 100%);
            }}
        """
    
    def test_invoice(self, invoice_id):
        """اختبار الفاتورة"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار الفاتورة:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    app.setStyleSheet("""
        QMainWindow {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
        QWidget {
            font-family: 'Arial', 'Tahoma', sans-serif;
        }
    """)
    
    print("🎯 اختبار سريع للترويسة المحسنة...")
    print("=" * 50)
    print("✅ تحسينات مطبقة:")
    print("   • حساب المساحة الديناميكي")
    print("   • تقسيم النص الطويل")
    print("   • تحميل اللوجو المخصص")
    print("   • قياس عرض النص")
    print("=" * 50)
    print("🎯 افتح النافذة للاختبار")
    
    window = QuickHeaderTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
