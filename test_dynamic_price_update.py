#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار خاصية تحديث أسعار المنتجات الديناميكي في فاتورة المبيعات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("💰 اختبار خاصية تحديث أسعار المنتجات الديناميكي")
    print("=" * 65)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("🔧 الخاصية الجديدة:")
    print("")
    print("   💡 تحديث السعر الذكي:")
    print("     • عند تغيير سعر منتج في الفاتورة")
    print("     • تظهر نافذة منبثقة تسأل:")
    print("       'هل تريد تحديث السعر في المخزون؟'")
    print("     • خيارين:")
    print("       ✅ نعم: يحدث السعر في المخزون")
    print("       ❌ لا: يبقى التغيير فقط في الفاتورة")
    print("")
    print("   🎯 الفوائد:")
    print("     • تحديث أسعار المنتجات بسهولة")
    print("     • مرونة في التعامل مع الأسعار الطارئة")
    print("     • تجنب الأخطاء في إدخال الأسعار")
    print("     • توفير الوقت والجهد")
    print("")
    print("   🎨 تصميم النافذة:")
    print("     • أيقونة مميزة 💰")
    print("     • معلومات واضحة عن المنتج")
    print("     • مقارنة بين السعر القديم والجديد")
    print("     • أزرار ملونة وواضحة")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار الخاصية الجديدة:")
        print("")
        print("   📋 إنشاء فاتورة جديدة:")
        print("     1️⃣ اضغط على 'فاتورة جديدة' من الصفحة الرئيسية")
        print("     2️⃣ أو اذهب لقائمة 'المبيعات'")
        print("")
        print("   🛒 إضافة منتجات:")
        print("     1️⃣ ابحث عن منتج وأضفه للفاتورة")
        print("     2️⃣ أو استخدم الباركود")
        print("     3️⃣ تأكد من وجود منتجات في الجدول")
        print("")
        print("   💰 اختبار تحديث السعر:")
        print("     1️⃣ انقر على خانة 'السعر' لأي منتج")
        print("     2️⃣ غير السعر إلى قيمة جديدة")
        print("     3️⃣ اضغط Enter أو انقر خارج الخانة")
        print("     4️⃣ ستظهر نافذة منبثقة تسأل:")
        print("        'هل تريد تحديث السعر في المخزون؟'")
        print("")
        print("   🔍 اختبار الخيارات:")
        print("     ✅ اختر 'نعم' لتحديث السعر في المخزون:")
        print("        • سيتم تحديث سعر المنتج نهائياً")
        print("        • ستظهر رسالة تأكيد")
        print("        • تحقق من المخزون لرؤية السعر الجديد")
        print("")
        print("     ❌ اختر 'لا' للتغيير المؤقت:")
        print("        • السعر يتغير فقط في هذه الفاتورة")
        print("        • سعر المنتج في المخزون يبقى كما هو")
        print("")
        print("   📊 التحقق من النتائج:")
        print("     • اذهب لقسم 'المخزون' لرؤية الأسعار")
        print("     • قارن الأسعار قبل وبعد التحديث")
        print("     • جرب إنشاء فاتورة جديدة لرؤية السعر المحدث")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 65)
    print("📊 ملخص الخاصية الجديدة:")
    print("")
    print("   ✅ ما تم إضافته:")
    print("     • نافذة تأكيد عند تغيير السعر")
    print("     • خيار تحديث السعر في المخزون")
    print("     • خيار التغيير المؤقت فقط")
    print("     • تصميم جميل ومفهوم")
    print("")
    print("   🎯 الاستخدامات:")
    print("     • تحديث أسعار المنتجات بسرعة")
    print("     • التعامل مع العروض الخاصة")
    print("     • تصحيح أخطاء الأسعار")
    print("     • المرونة في التسعير")
    print("")
    print("   💡 نصائح:")
    print("     • استخدم 'نعم' لتحديث السعر نهائياً")
    print("     • استخدم 'لا' للعروض المؤقتة")
    print("     • تحقق من المخزون بعد التحديث")
    print("     • جرب الخاصية مع منتجات مختلفة")
    print("")
    print("🎉 الخاصية جاهزة للاستخدام!")

if __name__ == "__main__":
    main()
