# The PEP 484 type hints stub file for the QtChart module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQtChart.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtWidgets

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt5.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt5.sip.Buffer, int, None]


class QAbstractAxis(QtCore.QObject):

    class AxisType(int):
        AxisTypeNoAxis = ... # type: QAbstractAxis.AxisType
        AxisTypeValue = ... # type: QAbstractAxis.AxisType
        AxisTypeBarCategory = ... # type: QAbstractAxis.AxisType
        AxisTypeCategory = ... # type: QAbstractAxis.AxisType
        AxisTypeDateTime = ... # type: QAbstractAxis.AxisType
        AxisTypeLogValue = ... # type: QAbstractAxis.AxisType

    class AxisTypes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __xor__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __ior__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __or__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __iand__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __and__(self, f: typing.Union['QAbstractAxis.AxisTypes', 'QAbstractAxis.AxisType']) -> 'QAbstractAxis.AxisTypes': ...
        def __invert__(self) -> 'QAbstractAxis.AxisTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    labelsEditableChanged: typing.ClassVar[QtCore.pyqtSignal]
    def labelsEditable(self) -> bool: ...
    def setLabelsEditable(self, editable: bool = ...) -> None: ...
    reverseChanged: typing.ClassVar[QtCore.pyqtSignal]
    minorGridLineColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    gridLineColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    minorGridLinePenChanged: typing.ClassVar[QtCore.pyqtSignal]
    minorGridVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isReverse(self) -> bool: ...
    def setReverse(self, reverse: bool = ...) -> None: ...
    def minorGridLineColor(self) -> QtGui.QColor: ...
    def setMinorGridLineColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def gridLineColor(self) -> QtGui.QColor: ...
    def setGridLineColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def minorGridLinePen(self) -> QtGui.QPen: ...
    def setMinorGridLinePen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def setMinorGridLineVisible(self, visible: bool = ...) -> None: ...
    def isMinorGridLineVisible(self) -> bool: ...
    shadesBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    shadesPenChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    titleTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    gridLinePenChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsAngleChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    linePenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def alignment(self) -> QtCore.Qt.Alignment: ...
    def titleText(self) -> str: ...
    def setTitleText(self, title: typing.Optional[str]) -> None: ...
    def titleFont(self) -> QtGui.QFont: ...
    def setTitleFont(self, font: QtGui.QFont) -> None: ...
    def titleBrush(self) -> QtGui.QBrush: ...
    def setTitleBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def setTitleVisible(self, visible: bool = ...) -> None: ...
    def isTitleVisible(self) -> bool: ...
    shadesBorderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    shadesColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    shadesVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    gridVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    lineVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def orientation(self) -> QtCore.Qt.Orientation: ...
    def hide(self) -> None: ...
    def show(self) -> None: ...
    def setRange(self, min: typing.Any, max: typing.Any) -> None: ...
    def setMax(self, max: typing.Any) -> None: ...
    def setMin(self, min: typing.Any) -> None: ...
    def shadesBorderColor(self) -> QtGui.QColor: ...
    def setShadesBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def shadesColor(self) -> QtGui.QColor: ...
    def setShadesColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def shadesBrush(self) -> QtGui.QBrush: ...
    def setShadesBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def shadesPen(self) -> QtGui.QPen: ...
    def setShadesPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def setShadesVisible(self, visible: bool = ...) -> None: ...
    def shadesVisible(self) -> bool: ...
    def labelsColor(self) -> QtGui.QColor: ...
    def setLabelsColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def labelsAngle(self) -> int: ...
    def setLabelsAngle(self, angle: int) -> None: ...
    def labelsFont(self) -> QtGui.QFont: ...
    def setLabelsFont(self, font: QtGui.QFont) -> None: ...
    def labelsBrush(self) -> QtGui.QBrush: ...
    def setLabelsBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def labelsVisible(self) -> bool: ...
    def gridLinePen(self) -> QtGui.QPen: ...
    def setGridLinePen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def setGridLineVisible(self, visible: bool = ...) -> None: ...
    def isGridLineVisible(self) -> bool: ...
    def linePenColor(self) -> QtGui.QColor: ...
    def setLinePenColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def linePen(self) -> QtGui.QPen: ...
    def setLinePen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def setLineVisible(self, visible: bool = ...) -> None: ...
    def isLineVisible(self) -> bool: ...
    def setVisible(self, visible: bool = ...) -> None: ...
    def isVisible(self) -> bool: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QAbstractSeries(QtCore.QObject):

    class SeriesType(int):
        SeriesTypeLine = ... # type: QAbstractSeries.SeriesType
        SeriesTypeArea = ... # type: QAbstractSeries.SeriesType
        SeriesTypeBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypeStackedBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypePercentBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypePie = ... # type: QAbstractSeries.SeriesType
        SeriesTypeScatter = ... # type: QAbstractSeries.SeriesType
        SeriesTypeSpline = ... # type: QAbstractSeries.SeriesType
        SeriesTypeHorizontalBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypeHorizontalStackedBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypeHorizontalPercentBar = ... # type: QAbstractSeries.SeriesType
        SeriesTypeBoxPlot = ... # type: QAbstractSeries.SeriesType
        SeriesTypeCandlestick = ... # type: QAbstractSeries.SeriesType

    useOpenGLChanged: typing.ClassVar[QtCore.pyqtSignal]
    def useOpenGL(self) -> bool: ...
    def setUseOpenGL(self, enable: bool = ...) -> None: ...
    opacityChanged: typing.ClassVar[QtCore.pyqtSignal]
    def attachedAxes(self) -> typing.List['QAbstractAxis']: ...
    def detachAxis(self, axis: typing.Optional['QAbstractAxis']) -> bool: ...
    def attachAxis(self, axis: typing.Optional['QAbstractAxis']) -> bool: ...
    def setOpacity(self, opacity: float) -> None: ...
    def opacity(self) -> float: ...
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    nameChanged: typing.ClassVar[QtCore.pyqtSignal]
    def hide(self) -> None: ...
    def show(self) -> None: ...
    def chart(self) -> typing.Optional['QChart']: ...
    def isVisible(self) -> bool: ...
    def setVisible(self, visible: bool = ...) -> None: ...
    def name(self) -> str: ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QAbstractBarSeries('QAbstractSeries'):

    class LabelsPosition(int):
        LabelsCenter = ... # type: QAbstractBarSeries.LabelsPosition
        LabelsInsideEnd = ... # type: QAbstractBarSeries.LabelsPosition
        LabelsInsideBase = ... # type: QAbstractBarSeries.LabelsPosition
        LabelsOutsideEnd = ... # type: QAbstractBarSeries.LabelsPosition

    labelsPrecisionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def labelsPrecision(self) -> int: ...
    def setLabelsPrecision(self, precision: int) -> None: ...
    labelsAngleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def labelsAngle(self) -> float: ...
    def setLabelsAngle(self, angle: float) -> None: ...
    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    labelsPositionChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    def labelsPosition(self) -> 'QAbstractBarSeries.LabelsPosition': ...
    def setLabelsPosition(self, position: 'QAbstractBarSeries.LabelsPosition') -> None: ...
    def labelsFormat(self) -> str: ...
    def setLabelsFormat(self, format: typing.Optional[str]) -> None: ...
    barsetsRemoved: typing.ClassVar[QtCore.pyqtSignal]
    barsetsAdded: typing.ClassVar[QtCore.pyqtSignal]
    labelsVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    countChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def take(self, set: typing.Optional['QBarSet']) -> bool: ...
    def isLabelsVisible(self) -> bool: ...
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def clear(self) -> None: ...
    def barSets(self) -> typing.List['QBarSet']: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def insert(self, index: int, set: typing.Optional['QBarSet']) -> bool: ...
    def remove(self, set: typing.Optional['QBarSet']) -> bool: ...
    @typing.overload
    def append(self, set: typing.Optional['QBarSet']) -> bool: ...
    @typing.overload
    def append(self, sets: typing.Iterable['QBarSet']) -> bool: ...
    def barWidth(self) -> float: ...
    def setBarWidth(self, width: float) -> None: ...


class QLegendMarker(QtCore.QObject):

    class LegendMarkerType(int):
        LegendMarkerTypeArea = ... # type: QLegendMarker.LegendMarkerType
        LegendMarkerTypeBar = ... # type: QLegendMarker.LegendMarkerType
        LegendMarkerTypePie = ... # type: QLegendMarker.LegendMarkerType
        LegendMarkerTypeXY = ... # type: QLegendMarker.LegendMarkerType
        LegendMarkerTypeBoxPlot = ... # type: QLegendMarker.LegendMarkerType
        LegendMarkerTypeCandlestick = ... # type: QLegendMarker.LegendMarkerType

    shapeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setShape(self, shape: 'QLegend.MarkerShape') -> None: ...
    def shape(self) -> 'QLegend.MarkerShape': ...
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    fontChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def series(self) -> typing.Optional['QAbstractSeries']: ...
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setFont(self, font: QtGui.QFont) -> None: ...
    def font(self) -> QtGui.QFont: ...
    def setLabelBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def labelBrush(self) -> QtGui.QBrush: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...
    def label(self) -> str: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QAreaLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QAreaSeries'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def series(self) -> typing.Optional['QAreaSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QAreaSeries('QAbstractSeries'):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, upperSeries: typing.Optional['QLineSeries'], lowerSeries: typing.Optional['QLineSeries'] = ...) -> None: ...

    pointLabelsClippingChanged: typing.ClassVar[QtCore.pyqtSignal]
    def pointLabelsClipping(self) -> bool: ...
    def setPointLabelsClipping(self, enable: bool = ...) -> None: ...
    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsVisibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    def pointLabelsColor(self) -> QtGui.QColor: ...
    def setPointLabelsColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def pointLabelsFont(self) -> QtGui.QFont: ...
    def setPointLabelsFont(self, font: QtGui.QFont) -> None: ...
    def pointLabelsVisible(self) -> bool: ...
    def setPointLabelsVisible(self, visible: bool = ...) -> None: ...
    def pointLabelsFormat(self) -> str: ...
    def setPointLabelsFormat(self, format: typing.Optional[str]) -> None: ...
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    selected: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    def borderColor(self) -> QtGui.QColor: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def pointsVisible(self) -> bool: ...
    def setPointsVisible(self, visible: bool = ...) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def lowerSeries(self) -> typing.Optional['QLineSeries']: ...
    def setLowerSeries(self, series: typing.Optional['QLineSeries']) -> None: ...
    def upperSeries(self) -> typing.Optional['QLineSeries']: ...
    def setUpperSeries(self, series: typing.Optional['QLineSeries']) -> None: ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QBarCategoryAxis('QAbstractAxis'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    countChanged: typing.ClassVar[QtCore.pyqtSignal]
    rangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    maxChanged: typing.ClassVar[QtCore.pyqtSignal]
    minChanged: typing.ClassVar[QtCore.pyqtSignal]
    categoriesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setRange(self, minCategory: typing.Optional[str], maxCategory: typing.Optional[str]) -> None: ...
    def max(self) -> str: ...
    def setMax(self, maxCategory: typing.Optional[str]) -> None: ...
    def min(self) -> str: ...
    def setMin(self, minCategory: typing.Optional[str]) -> None: ...
    def at(self, index: int) -> str: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def categories(self) -> typing.List[str]: ...
    def setCategories(self, categories: typing.Iterable[typing.Optional[str]]) -> None: ...
    def clear(self) -> None: ...
    def replace(self, oldCategory: typing.Optional[str], newCategory: typing.Optional[str]) -> None: ...
    def insert(self, index: int, category: typing.Optional[str]) -> None: ...
    def remove(self, category: typing.Optional[str]) -> None: ...
    @typing.overload
    def append(self, categories: typing.Iterable[typing.Optional[str]]) -> None: ...
    @typing.overload
    def append(self, category: typing.Optional[str]) -> None: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QBarLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QAbstractBarSeries'], barset: typing.Optional['QBarSet'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def barset(self) -> typing.Optional['QBarSet']: ...
    def series(self) -> typing.Optional['QAbstractBarSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QBarSet(QtCore.QObject):

    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    labelColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    valueChanged: typing.ClassVar[QtCore.pyqtSignal]
    valuesRemoved: typing.ClassVar[QtCore.pyqtSignal]
    valuesAdded: typing.ClassVar[QtCore.pyqtSignal]
    labelFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setLabelColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def labelColor(self) -> QtGui.QColor: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def borderColor(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def __getitem__(self, index: int) -> float: ...
    def at(self, index: int) -> float: ...
    def remove(self, index: int, count: int = ...) -> None: ...
    def label(self) -> str: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...
    def labelFont(self) -> QtGui.QFont: ...
    def setLabelFont(self, font: QtGui.QFont) -> None: ...
    def labelBrush(self) -> QtGui.QBrush: ...
    def setLabelBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def sum(self) -> float: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def replace(self, index: int, value: float) -> None: ...
    def insert(self, index: int, value: float) -> None: ...
    def __lshift__(self, value: float) -> 'QBarSet': ...
    @typing.overload
    def append(self, value: float) -> None: ...
    @typing.overload
    def append(self, values: typing.Iterable[float]) -> None: ...


class QBoxPlotLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QBoxPlotSeries'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def series(self) -> typing.Optional['QBoxPlotSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QBoxPlotSeries('QAbstractSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    boxsetsRemoved: typing.ClassVar[QtCore.pyqtSignal]
    boxsetsAdded: typing.ClassVar[QtCore.pyqtSignal]
    boxWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    boxOutlineVisibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    countChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def boxWidth(self) -> float: ...
    def setBoxWidth(self, width: float) -> None: ...
    def boxOutlineVisible(self) -> bool: ...
    def setBoxOutlineVisible(self, visible: bool) -> None: ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...
    def clear(self) -> None: ...
    def boxSets(self) -> typing.List['QBoxSet']: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def insert(self, index: int, box: typing.Optional['QBoxSet']) -> bool: ...
    def take(self, box: typing.Optional['QBoxSet']) -> bool: ...
    def remove(self, box: typing.Optional['QBoxSet']) -> bool: ...
    @typing.overload
    def append(self, box: typing.Optional['QBoxSet']) -> bool: ...
    @typing.overload
    def append(self, boxes: typing.Iterable['QBoxSet']) -> bool: ...


class QBoxSet(QtCore.QObject):

    class ValuePositions(int):
        LowerExtreme = ... # type: QBoxSet.ValuePositions
        LowerQuartile = ... # type: QBoxSet.ValuePositions
        Median = ... # type: QBoxSet.ValuePositions
        UpperQuartile = ... # type: QBoxSet.ValuePositions
        UpperExtreme = ... # type: QBoxSet.ValuePositions

    @typing.overload
    def __init__(self, label: typing.Optional[str] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, le: float, lq: float, m: float, uq: float, ue: float, label: typing.Optional[str] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    cleared: typing.ClassVar[QtCore.pyqtSignal]
    valueChanged: typing.ClassVar[QtCore.pyqtSignal]
    valuesChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def __getitem__(self, index: int) -> float: ...
    def at(self, index: int) -> float: ...
    def setValue(self, index: int, value: float) -> None: ...
    def __lshift__(self, value: float) -> 'QBoxSet': ...
    def label(self) -> str: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...
    def clear(self) -> None: ...
    @typing.overload
    def append(self, value: float) -> None: ...
    @typing.overload
    def append(self, values: typing.Iterable[float]) -> None: ...


class QCandlestickLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QCandlestickSeries'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def series(self) -> typing.Optional['QCandlestickSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QCandlestickModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def lastSetSection(self) -> int: ...
    def setLastSetSection(self, lastSetSection: int) -> None: ...
    def firstSetSection(self) -> int: ...
    def setFirstSetSection(self, firstSetSection: int) -> None: ...
    def close(self) -> int: ...
    def setClose(self, close: int) -> None: ...
    def low(self) -> int: ...
    def setLow(self, low: int) -> None: ...
    def high(self) -> int: ...
    def setHigh(self, high: int) -> None: ...
    def open(self) -> int: ...
    def setOpen(self, open: int) -> None: ...
    def timestamp(self) -> int: ...
    def setTimestamp(self, timestamp: int) -> None: ...
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def orientation(self) -> QtCore.Qt.Orientation: ...
    def series(self) -> typing.Optional['QCandlestickSeries']: ...
    def setSeries(self, series: typing.Optional['QCandlestickSeries']) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...


class QCandlestickSeries('QAbstractSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    decreasingColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    increasingColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    capsVisibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    capsWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    bodyOutlineVisibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    bodyWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumColumnWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumColumnWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    countChanged: typing.ClassVar[QtCore.pyqtSignal]
    candlestickSetsRemoved: typing.ClassVar[QtCore.pyqtSignal]
    candlestickSetsAdded: typing.ClassVar[QtCore.pyqtSignal]
    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def decreasingColor(self) -> QtGui.QColor: ...
    def setDecreasingColor(self, decreasingColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def increasingColor(self) -> QtGui.QColor: ...
    def setIncreasingColor(self, increasingColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def capsVisible(self) -> bool: ...
    def setCapsVisible(self, capsVisible: bool) -> None: ...
    def capsWidth(self) -> float: ...
    def setCapsWidth(self, capsWidth: float) -> None: ...
    def bodyOutlineVisible(self) -> bool: ...
    def setBodyOutlineVisible(self, bodyOutlineVisible: bool) -> None: ...
    def bodyWidth(self) -> float: ...
    def setBodyWidth(self, bodyWidth: float) -> None: ...
    def minimumColumnWidth(self) -> float: ...
    def setMinimumColumnWidth(self, minimumColumnWidth: float) -> None: ...
    def maximumColumnWidth(self) -> float: ...
    def setMaximumColumnWidth(self, maximumColumnWidth: float) -> None: ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def sets(self) -> typing.List['QCandlestickSet']: ...
    def clear(self) -> None: ...
    def take(self, set: typing.Optional['QCandlestickSet']) -> bool: ...
    def insert(self, index: int, set: typing.Optional['QCandlestickSet']) -> bool: ...
    @typing.overload
    def remove(self, set: typing.Optional['QCandlestickSet']) -> bool: ...
    @typing.overload
    def remove(self, sets: typing.Iterable['QCandlestickSet']) -> bool: ...
    @typing.overload
    def append(self, set: typing.Optional['QCandlestickSet']) -> bool: ...
    @typing.overload
    def append(self, sets: typing.Iterable['QCandlestickSet']) -> bool: ...


class QCandlestickSet(QtCore.QObject):

    @typing.overload
    def __init__(self, timestamp: float = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, open: float, high: float, low: float, close: float, timestamp: float = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    closeChanged: typing.ClassVar[QtCore.pyqtSignal]
    lowChanged: typing.ClassVar[QtCore.pyqtSignal]
    highChanged: typing.ClassVar[QtCore.pyqtSignal]
    openChanged: typing.ClassVar[QtCore.pyqtSignal]
    timestampChanged: typing.ClassVar[QtCore.pyqtSignal]
    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def close(self) -> float: ...
    def setClose(self, close: float) -> None: ...
    def low(self) -> float: ...
    def setLow(self, low: float) -> None: ...
    def high(self) -> float: ...
    def setHigh(self, high: float) -> None: ...
    def open(self) -> float: ...
    def setOpen(self, open: float) -> None: ...
    def timestamp(self) -> float: ...
    def setTimestamp(self, timestamp: float) -> None: ...


class QValueAxis('QAbstractAxis'):

    class TickType(int):
        TicksDynamic = ... # type: QValueAxis.TickType
        TicksFixed = ... # type: QValueAxis.TickType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    tickTypeChanged: typing.ClassVar[QtCore.pyqtSignal]
    tickAnchorChanged: typing.ClassVar[QtCore.pyqtSignal]
    tickIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    def tickType(self) -> 'QValueAxis.TickType': ...
    def setTickType(self, type: 'QValueAxis.TickType') -> None: ...
    def tickInterval(self) -> float: ...
    def setTickInterval(self, insterval: float) -> None: ...
    def tickAnchor(self) -> float: ...
    def setTickAnchor(self, anchor: float) -> None: ...
    minorTickCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    def minorTickCount(self) -> int: ...
    def setMinorTickCount(self, count: int) -> None: ...
    labelFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    tickCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    rangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    maxChanged: typing.ClassVar[QtCore.pyqtSignal]
    minChanged: typing.ClassVar[QtCore.pyqtSignal]
    def applyNiceNumbers(self) -> None: ...
    def labelFormat(self) -> str: ...
    def setLabelFormat(self, format: typing.Optional[str]) -> None: ...
    def tickCount(self) -> int: ...
    def setTickCount(self, count: int) -> None: ...
    def setRange(self, min: float, max: float) -> None: ...
    def max(self) -> float: ...
    def setMax(self, max: float) -> None: ...
    def min(self) -> float: ...
    def setMin(self, min: float) -> None: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QCategoryAxis('QValueAxis'):

    class AxisLabelsPosition(int):
        AxisLabelsPositionCenter = ... # type: QCategoryAxis.AxisLabelsPosition
        AxisLabelsPositionOnValue = ... # type: QCategoryAxis.AxisLabelsPosition

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    labelsPositionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setLabelsPosition(self, position: 'QCategoryAxis.AxisLabelsPosition') -> None: ...
    def labelsPosition(self) -> 'QCategoryAxis.AxisLabelsPosition': ...
    categoriesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def categoriesLabels(self) -> typing.List[str]: ...
    def endValue(self, categoryLabel: typing.Optional[str]) -> float: ...
    def setStartValue(self, min: float) -> None: ...
    def startValue(self, categoryLabel: typing.Optional[str] = ...) -> float: ...
    def replaceLabel(self, oldLabel: typing.Optional[str], newLabel: typing.Optional[str]) -> None: ...
    def remove(self, label: typing.Optional[str]) -> None: ...
    def append(self, label: typing.Optional[str], categoryEndValue: float) -> None: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QChart(QtWidgets.QGraphicsWidget):

    class ChartType(int):
        ChartTypeUndefined = ... # type: QChart.ChartType
        ChartTypeCartesian = ... # type: QChart.ChartType
        ChartTypePolar = ... # type: QChart.ChartType

    class AnimationOption(int):
        NoAnimation = ... # type: QChart.AnimationOption
        GridAxisAnimations = ... # type: QChart.AnimationOption
        SeriesAnimations = ... # type: QChart.AnimationOption
        AllAnimations = ... # type: QChart.AnimationOption

    class ChartTheme(int):
        ChartThemeLight = ... # type: QChart.ChartTheme
        ChartThemeBlueCerulean = ... # type: QChart.ChartTheme
        ChartThemeDark = ... # type: QChart.ChartTheme
        ChartThemeBrownSand = ... # type: QChart.ChartTheme
        ChartThemeBlueNcs = ... # type: QChart.ChartTheme
        ChartThemeHighContrast = ... # type: QChart.ChartTheme
        ChartThemeBlueIcy = ... # type: QChart.ChartTheme
        ChartThemeQt = ... # type: QChart.ChartTheme

    class AnimationOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __xor__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __ior__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __or__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __iand__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __and__(self, f: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> 'QChart.AnimationOptions': ...
        def __invert__(self) -> 'QChart.AnimationOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtWidgets.QGraphicsItem] = ..., flags: typing.Union[QtCore.Qt.WindowFlags, QtCore.Qt.WindowType] = ...) -> None: ...

    def animationEasingCurve(self) -> QtCore.QEasingCurve: ...
    def setAnimationEasingCurve(self, curve: typing.Union[QtCore.QEasingCurve, QtCore.QEasingCurve.Type]) -> None: ...
    def animationDuration(self) -> int: ...
    def setAnimationDuration(self, msecs: int) -> None: ...
    plotAreaChanged: typing.ClassVar[QtCore.pyqtSignal]
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def localizeNumbers(self) -> bool: ...
    def setLocalizeNumbers(self, localize: bool) -> None: ...
    def chartType(self) -> 'QChart.ChartType': ...
    def isPlotAreaBackgroundVisible(self) -> bool: ...
    def setPlotAreaBackgroundVisible(self, visible: bool = ...) -> None: ...
    def plotAreaBackgroundPen(self) -> QtGui.QPen: ...
    def setPlotAreaBackgroundPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def plotAreaBackgroundBrush(self) -> QtGui.QBrush: ...
    def setPlotAreaBackgroundBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def setPlotArea(self, rect: QtCore.QRectF) -> None: ...
    def isZoomed(self) -> bool: ...
    def zoomReset(self) -> None: ...
    def backgroundRoundness(self) -> float: ...
    def setBackgroundRoundness(self, diameter: float) -> None: ...
    def mapToPosition(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint], series: typing.Optional['QAbstractSeries'] = ...) -> QtCore.QPointF: ...
    def mapToValue(self, position: typing.Union[QtCore.QPointF, QtCore.QPoint], series: typing.Optional['QAbstractSeries'] = ...) -> QtCore.QPointF: ...
    def margins(self) -> QtCore.QMargins: ...
    def setMargins(self, margins: QtCore.QMargins) -> None: ...
    def axes(self, orientation: typing.Union[QtCore.Qt.Orientations, QtCore.Qt.Orientation] = ..., series: typing.Optional['QAbstractSeries'] = ...) -> typing.List['QAbstractAxis']: ...
    def removeAxis(self, axis: typing.Optional['QAbstractAxis']) -> None: ...
    def addAxis(self, axis: typing.Optional['QAbstractAxis'], alignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def plotArea(self) -> QtCore.QRectF: ...
    def scroll(self, dx: float, dy: float) -> None: ...
    def isDropShadowEnabled(self) -> bool: ...
    def setDropShadowEnabled(self, enabled: bool = ...) -> None: ...
    def createDefaultAxes(self) -> None: ...
    def axisY(self, series: typing.Optional['QAbstractSeries'] = ...) -> typing.Optional['QAbstractAxis']: ...
    def axisX(self, series: typing.Optional['QAbstractSeries'] = ...) -> typing.Optional['QAbstractAxis']: ...
    def setAxisY(self, axis: typing.Optional['QAbstractAxis'], series: typing.Optional['QAbstractSeries'] = ...) -> None: ...
    def setAxisX(self, axis: typing.Optional['QAbstractAxis'], series: typing.Optional['QAbstractSeries'] = ...) -> None: ...
    def legend(self) -> typing.Optional['QLegend']: ...
    def zoom(self, factor: float) -> None: ...
    def zoomOut(self) -> None: ...
    @typing.overload
    def zoomIn(self) -> None: ...
    @typing.overload
    def zoomIn(self, rect: QtCore.QRectF) -> None: ...
    def animationOptions(self) -> 'QChart.AnimationOptions': ...
    def setAnimationOptions(self, options: typing.Union['QChart.AnimationOptions', 'QChart.AnimationOption']) -> None: ...
    def isBackgroundVisible(self) -> bool: ...
    def setBackgroundVisible(self, visible: bool = ...) -> None: ...
    def backgroundPen(self) -> QtGui.QPen: ...
    def setBackgroundPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def backgroundBrush(self) -> QtGui.QBrush: ...
    def setBackgroundBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def titleBrush(self) -> QtGui.QBrush: ...
    def setTitleBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def titleFont(self) -> QtGui.QFont: ...
    def setTitleFont(self, font: QtGui.QFont) -> None: ...
    def title(self) -> str: ...
    def setTitle(self, title: typing.Optional[str]) -> None: ...
    def theme(self) -> 'QChart.ChartTheme': ...
    def setTheme(self, theme: 'QChart.ChartTheme') -> None: ...
    def series(self) -> typing.List['QAbstractSeries']: ...
    def removeAllSeries(self) -> None: ...
    def removeSeries(self, series: typing.Optional['QAbstractSeries']) -> None: ...
    def addSeries(self, series: typing.Optional['QAbstractSeries']) -> None: ...


class QChartView(QtWidgets.QGraphicsView):

    class RubberBand(int):
        NoRubberBand = ... # type: QChartView.RubberBand
        VerticalRubberBand = ... # type: QChartView.RubberBand
        HorizontalRubberBand = ... # type: QChartView.RubberBand
        RectangleRubberBand = ... # type: QChartView.RubberBand

    class RubberBands(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __xor__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __ior__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __or__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __iand__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __and__(self, f: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> 'QChartView.RubberBands': ...
        def __invert__(self) -> 'QChartView.RubberBands': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...
    @typing.overload
    def __init__(self, chart: typing.Optional['QChart'], parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...

    def wheelEvent(self, event: typing.Optional[QtGui.QWheelEvent]) -> None: ...
    def mouseReleaseEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseMoveEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def resizeEvent(self, event: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    def chart(self) -> typing.Optional['QChart']: ...
    def setChart(self, chart: typing.Optional['QChart']) -> None: ...
    def rubberBand(self) -> 'QChartView.RubberBands': ...
    def setRubberBand(self, rubberBands: typing.Union['QChartView.RubberBands', 'QChartView.RubberBand']) -> None: ...


class QDateTimeAxis('QAbstractAxis'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    tickCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    formatChanged: typing.ClassVar[QtCore.pyqtSignal]
    rangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    maxChanged: typing.ClassVar[QtCore.pyqtSignal]
    minChanged: typing.ClassVar[QtCore.pyqtSignal]
    def tickCount(self) -> int: ...
    def setTickCount(self, count: int) -> None: ...
    def format(self) -> str: ...
    def setFormat(self, format: typing.Optional[str]) -> None: ...
    def setRange(self, min: typing.Union[QtCore.QDateTime, datetime.datetime], max: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def max(self) -> QtCore.QDateTime: ...
    def setMax(self, max: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def min(self) -> QtCore.QDateTime: ...
    def setMin(self, min: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QHBarModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    columnCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastBarSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstBarSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setColumnCount(self, columnCount: int) -> None: ...
    def columnCount(self) -> int: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def firstColumn(self) -> int: ...
    def setSeries(self, series: typing.Optional['QAbstractBarSeries']) -> None: ...
    def series(self) -> typing.Optional['QAbstractBarSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setLastBarSetRow(self, lastBarSetRow: int) -> None: ...
    def lastBarSetRow(self) -> int: ...
    def setFirstBarSetRow(self, firstBarSetRow: int) -> None: ...
    def firstBarSetRow(self) -> int: ...


class QHBoxPlotModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    columnCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastBoxSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstBoxSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setColumnCount(self, rowCount: int) -> None: ...
    def columnCount(self) -> int: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def firstColumn(self) -> int: ...
    def setLastBoxSetRow(self, lastBoxSetRow: int) -> None: ...
    def lastBoxSetRow(self) -> int: ...
    def setFirstBoxSetRow(self, firstBoxSetRow: int) -> None: ...
    def firstBoxSetRow(self) -> int: ...
    def setSeries(self, series: typing.Optional['QBoxPlotSeries']) -> None: ...
    def series(self) -> typing.Optional['QBoxPlotSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...


class QHCandlestickModelMapper('QCandlestickModelMapper'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    lastSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstSetRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    closeColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    lowColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    highColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    openColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    timestampColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    def lastSetRow(self) -> int: ...
    def setLastSetRow(self, lastSetRow: int) -> None: ...
    def firstSetRow(self) -> int: ...
    def setFirstSetRow(self, firstSetRow: int) -> None: ...
    def closeColumn(self) -> int: ...
    def setCloseColumn(self, closeColumn: int) -> None: ...
    def lowColumn(self) -> int: ...
    def setLowColumn(self, lowColumn: int) -> None: ...
    def highColumn(self) -> int: ...
    def setHighColumn(self, highColumn: int) -> None: ...
    def openColumn(self) -> int: ...
    def setOpenColumn(self, openColumn: int) -> None: ...
    def timestampColumn(self) -> int: ...
    def setTimestampColumn(self, timestampColumn: int) -> None: ...
    def orientation(self) -> QtCore.Qt.Orientation: ...


class QHorizontalBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QHorizontalPercentBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QHorizontalStackedBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QHPieModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    columnCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    valuesRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setColumnCount(self, columnCount: int) -> None: ...
    def columnCount(self) -> int: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def firstColumn(self) -> int: ...
    def setSeries(self, series: typing.Optional['QPieSeries']) -> None: ...
    def series(self) -> typing.Optional['QPieSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setLabelsRow(self, labelsRow: int) -> None: ...
    def labelsRow(self) -> int: ...
    def setValuesRow(self, valuesRow: int) -> None: ...
    def valuesRow(self) -> int: ...


class QHXYModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    columnCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    yRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    xRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setColumnCount(self, columnCount: int) -> None: ...
    def columnCount(self) -> int: ...
    def setFirstColumn(self, firstColumn: int) -> None: ...
    def firstColumn(self) -> int: ...
    def setSeries(self, series: typing.Optional['QXYSeries']) -> None: ...
    def series(self) -> typing.Optional['QXYSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setYRow(self, yRow: int) -> None: ...
    def yRow(self) -> int: ...
    def setXRow(self, xRow: int) -> None: ...
    def xRow(self) -> int: ...


class QLegend(QtWidgets.QGraphicsWidget):

    class MarkerShape(int):
        MarkerShapeDefault = ... # type: QLegend.MarkerShape
        MarkerShapeRectangle = ... # type: QLegend.MarkerShape
        MarkerShapeCircle = ... # type: QLegend.MarkerShape
        MarkerShapeFromSeries = ... # type: QLegend.MarkerShape

    markerShapeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setMarkerShape(self, shape: 'QLegend.MarkerShape') -> None: ...
    def markerShape(self) -> 'QLegend.MarkerShape': ...
    showToolTipsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setShowToolTips(self, show: bool) -> None: ...
    def showToolTips(self) -> bool: ...
    reverseMarkersChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setReverseMarkers(self, reverseMarkers: bool = ...) -> None: ...
    def reverseMarkers(self) -> bool: ...
    def showEvent(self, event: typing.Optional[QtGui.QShowEvent]) -> None: ...
    def hideEvent(self, event: typing.Optional[QtGui.QHideEvent]) -> None: ...
    labelColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    fontChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    backgroundVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def markers(self, series: typing.Optional['QAbstractSeries'] = ...) -> typing.List['QLegendMarker']: ...
    def labelColor(self) -> QtGui.QColor: ...
    def setLabelColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def labelBrush(self) -> QtGui.QBrush: ...
    def setLabelBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def font(self) -> QtGui.QFont: ...
    def setFont(self, font: QtGui.QFont) -> None: ...
    def borderColor(self) -> QtGui.QColor: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def isBackgroundVisible(self) -> bool: ...
    def setBackgroundVisible(self, visible: bool = ...) -> None: ...
    def isAttachedToChart(self) -> bool: ...
    def attachToChart(self) -> None: ...
    def detachFromChart(self) -> None: ...
    def alignment(self) -> QtCore.Qt.Alignment: ...
    def setAlignment(self, alignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def paint(self, painter: typing.Optional[QtGui.QPainter], option: typing.Optional[QtWidgets.QStyleOptionGraphicsItem], widget: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...


class QXYSeries('QAbstractSeries'):

    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointsRemoved: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsClippingChanged: typing.ClassVar[QtCore.pyqtSignal]
    def pointLabelsClipping(self) -> bool: ...
    def setPointLabelsClipping(self, enable: bool = ...) -> None: ...
    def pointsVector(self) -> typing.List[QtCore.QPointF]: ...
    def removePoints(self, index: int, count: int) -> None: ...
    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsVisibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    pointLabelsFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    def pointLabelsColor(self) -> QtGui.QColor: ...
    def setPointLabelsColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def pointLabelsFont(self) -> QtGui.QFont: ...
    def setPointLabelsFont(self, font: QtGui.QFont) -> None: ...
    def pointLabelsVisible(self) -> bool: ...
    def setPointLabelsVisible(self, visible: bool = ...) -> None: ...
    def pointLabelsFormat(self) -> str: ...
    def setPointLabelsFormat(self, format: typing.Optional[str]) -> None: ...
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    pointsReplaced: typing.ClassVar[QtCore.pyqtSignal]
    pointAdded: typing.ClassVar[QtCore.pyqtSignal]
    pointRemoved: typing.ClassVar[QtCore.pyqtSignal]
    pointReplaced: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    def at(self, index: int) -> QtCore.QPointF: ...
    def pointsVisible(self) -> bool: ...
    def setPointsVisible(self, visible: bool = ...) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    @typing.overload
    def __lshift__(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QXYSeries': ...
    @typing.overload
    def __lshift__(self, points: typing.Iterable[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> 'QXYSeries': ...
    def points(self) -> typing.List[QtCore.QPointF]: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def clear(self) -> None: ...
    def insert(self, index: int, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def remove(self, x: float, y: float) -> None: ...
    @typing.overload
    def remove(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def remove(self, index: int) -> None: ...
    @typing.overload
    def replace(self, oldX: float, oldY: float, newX: float, newY: float) -> None: ...
    @typing.overload
    def replace(self, oldPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], newPoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def replace(self, points: typing.Iterable[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def replace(self, index: int, newX: float, newY: float) -> None: ...
    @typing.overload
    def replace(self, index: int, newPoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def append(self, x: float, y: float) -> None: ...
    @typing.overload
    def append(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def append(self, points: typing.Iterable[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...


class QLineSeries('QXYSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QLogValueAxis('QAbstractAxis'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    minorTickCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    tickCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    def minorTickCount(self) -> int: ...
    def setMinorTickCount(self, minorTickCount: int) -> None: ...
    def tickCount(self) -> int: ...
    baseChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    rangeChanged: typing.ClassVar[QtCore.pyqtSignal]
    maxChanged: typing.ClassVar[QtCore.pyqtSignal]
    minChanged: typing.ClassVar[QtCore.pyqtSignal]
    def base(self) -> float: ...
    def setBase(self, base: float) -> None: ...
    def labelFormat(self) -> str: ...
    def setLabelFormat(self, format: typing.Optional[str]) -> None: ...
    def setRange(self, min: float, max: float) -> None: ...
    def max(self) -> float: ...
    def setMax(self, max: float) -> None: ...
    def min(self) -> float: ...
    def setMin(self, min: float) -> None: ...
    def type(self) -> 'QAbstractAxis.AxisType': ...


class QPercentBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QPieLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QPieSeries'], slice: typing.Optional['QPieSlice'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def slice(self) -> typing.Optional['QPieSlice']: ...
    def series(self) -> typing.Optional['QPieSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


class QPieSeries('QAbstractSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    def setLabelsPosition(self, position: 'QPieSlice.LabelPosition') -> None: ...
    def holeSize(self) -> float: ...
    def setHoleSize(self, holeSize: float) -> None: ...
    def take(self, slice: typing.Optional['QPieSlice']) -> bool: ...
    sumChanged: typing.ClassVar[QtCore.pyqtSignal]
    countChanged: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    removed: typing.ClassVar[QtCore.pyqtSignal]
    added: typing.ClassVar[QtCore.pyqtSignal]
    def setLabelsVisible(self, visible: bool = ...) -> None: ...
    def pieEndAngle(self) -> float: ...
    def setPieEndAngle(self, endAngle: float) -> None: ...
    def pieStartAngle(self) -> float: ...
    def setPieStartAngle(self, startAngle: float) -> None: ...
    def pieSize(self) -> float: ...
    def setPieSize(self, relativeSize: float) -> None: ...
    def verticalPosition(self) -> float: ...
    def setVerticalPosition(self, relativePosition: float) -> None: ...
    def horizontalPosition(self) -> float: ...
    def setHorizontalPosition(self, relativePosition: float) -> None: ...
    def sum(self) -> float: ...
    def isEmpty(self) -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def slices(self) -> typing.List['QPieSlice']: ...
    def clear(self) -> None: ...
    def remove(self, slice: typing.Optional['QPieSlice']) -> bool: ...
    def insert(self, index: int, slice: typing.Optional['QPieSlice']) -> bool: ...
    def __lshift__(self, slice: typing.Optional['QPieSlice']) -> 'QPieSeries': ...
    @typing.overload
    def append(self, slice: typing.Optional['QPieSlice']) -> bool: ...
    @typing.overload
    def append(self, slices: typing.Iterable['QPieSlice']) -> bool: ...
    @typing.overload
    def append(self, label: typing.Optional[str], value: float) -> typing.Optional['QPieSlice']: ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QPieSlice(QtCore.QObject):

    class LabelPosition(int):
        LabelOutside = ... # type: QPieSlice.LabelPosition
        LabelInsideHorizontal = ... # type: QPieSlice.LabelPosition
        LabelInsideTangential = ... # type: QPieSlice.LabelPosition
        LabelInsideNormal = ... # type: QPieSlice.LabelPosition

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, label: typing.Optional[str], value: float, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    doubleClicked: typing.ClassVar[QtCore.pyqtSignal]
    released: typing.ClassVar[QtCore.pyqtSignal]
    pressed: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    clicked: typing.ClassVar[QtCore.pyqtSignal]
    labelColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    angleSpanChanged: typing.ClassVar[QtCore.pyqtSignal]
    startAngleChanged: typing.ClassVar[QtCore.pyqtSignal]
    percentageChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelFontChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelBrushChanged: typing.ClassVar[QtCore.pyqtSignal]
    brushChanged: typing.ClassVar[QtCore.pyqtSignal]
    penChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelVisibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    valueChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setLabelPosition(self, position: 'QPieSlice.LabelPosition') -> None: ...
    def labelPosition(self) -> 'QPieSlice.LabelPosition': ...
    def series(self) -> typing.Optional['QPieSeries']: ...
    def angleSpan(self) -> float: ...
    def startAngle(self) -> float: ...
    def percentage(self) -> float: ...
    def explodeDistanceFactor(self) -> float: ...
    def setExplodeDistanceFactor(self, factor: float) -> None: ...
    def labelArmLengthFactor(self) -> float: ...
    def setLabelArmLengthFactor(self, factor: float) -> None: ...
    def labelFont(self) -> QtGui.QFont: ...
    def setLabelFont(self, font: QtGui.QFont) -> None: ...
    def setLabelColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def labelColor(self) -> QtGui.QColor: ...
    def labelBrush(self) -> QtGui.QBrush: ...
    def setLabelBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def brush(self) -> QtGui.QBrush: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def setBorderWidth(self, width: int) -> None: ...
    def borderWidth(self) -> int: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def borderColor(self) -> QtGui.QColor: ...
    def pen(self) -> QtGui.QPen: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def isExploded(self) -> bool: ...
    def setExploded(self, exploded: bool = ...) -> None: ...
    def isLabelVisible(self) -> bool: ...
    def setLabelVisible(self, visible: bool = ...) -> None: ...
    def value(self) -> float: ...
    def setValue(self, value: float) -> None: ...
    def label(self) -> str: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...


class QPolarChart('QChart'):

    class PolarOrientation(int):
        PolarOrientationRadial = ... # type: QPolarChart.PolarOrientation
        PolarOrientationAngular = ... # type: QPolarChart.PolarOrientation

    class PolarOrientations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __xor__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __ior__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __or__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __iand__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __and__(self, f: typing.Union['QPolarChart.PolarOrientations', 'QPolarChart.PolarOrientation']) -> 'QPolarChart.PolarOrientations': ...
        def __invert__(self) -> 'QPolarChart.PolarOrientations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtWidgets.QGraphicsItem] = ..., flags: typing.Union[QtCore.Qt.WindowFlags, QtCore.Qt.WindowType] = ...) -> None: ...

    @staticmethod
    def axisPolarOrientation(axis: typing.Optional['QAbstractAxis']) -> 'QPolarChart.PolarOrientation': ...
    def axes(self, polarOrientation: 'QPolarChart.PolarOrientations' = ..., series: typing.Optional['QAbstractSeries'] = ...) -> typing.List['QAbstractAxis']: ...
    def addAxis(self, axis: typing.Optional['QAbstractAxis'], polarOrientation: 'QPolarChart.PolarOrientation') -> None: ...


class QScatterSeries('QXYSeries'):

    class MarkerShape(int):
        MarkerShapeCircle = ... # type: QScatterSeries.MarkerShape
        MarkerShapeRectangle = ... # type: QScatterSeries.MarkerShape

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    markerSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    markerShapeChanged: typing.ClassVar[QtCore.pyqtSignal]
    borderColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    def borderColor(self) -> QtGui.QColor: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def setBrush(self, brush: typing.Union[QtGui.QBrush, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor], QtGui.QGradient]) -> None: ...
    def brush(self) -> QtGui.QBrush: ...
    def setPen(self, pen: typing.Union[QtGui.QPen, typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]]) -> None: ...
    def setMarkerSize(self, size: float) -> None: ...
    def markerSize(self) -> float: ...
    def setMarkerShape(self, shape: 'QScatterSeries.MarkerShape') -> None: ...
    def markerShape(self) -> 'QScatterSeries.MarkerShape': ...
    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QSplineSeries('QLineSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QStackedBarSeries('QAbstractBarSeries'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def type(self) -> 'QAbstractSeries.SeriesType': ...


class QVBarModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    rowCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastBarSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstBarSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setRowCount(self, rowCount: int) -> None: ...
    def rowCount(self) -> int: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def firstRow(self) -> int: ...
    def setSeries(self, series: typing.Optional['QAbstractBarSeries']) -> None: ...
    def series(self) -> typing.Optional['QAbstractBarSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setLastBarSetColumn(self, lastBarSetColumn: int) -> None: ...
    def lastBarSetColumn(self) -> int: ...
    def setFirstBarSetColumn(self, firstBarSetColumn: int) -> None: ...
    def firstBarSetColumn(self) -> int: ...


class QVBoxPlotModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    rowCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastBoxSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstBoxSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setRowCount(self, rowCount: int) -> None: ...
    def rowCount(self) -> int: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def firstRow(self) -> int: ...
    def setLastBoxSetColumn(self, lastBoxSetColumn: int) -> None: ...
    def lastBoxSetColumn(self) -> int: ...
    def setFirstBoxSetColumn(self, firstBoxSetColumn: int) -> None: ...
    def firstBoxSetColumn(self) -> int: ...
    def setSeries(self, series: typing.Optional['QBoxPlotSeries']) -> None: ...
    def series(self) -> typing.Optional['QBoxPlotSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...


class QVCandlestickModelMapper('QCandlestickModelMapper'):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    lastSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstSetColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    closeRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    lowRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    highRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    openRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    timestampRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    def lastSetColumn(self) -> int: ...
    def setLastSetColumn(self, lastSetColumn: int) -> None: ...
    def firstSetColumn(self) -> int: ...
    def setFirstSetColumn(self, firstSetColumn: int) -> None: ...
    def closeRow(self) -> int: ...
    def setCloseRow(self, closeRow: int) -> None: ...
    def lowRow(self) -> int: ...
    def setLowRow(self, lowRow: int) -> None: ...
    def highRow(self) -> int: ...
    def setHighRow(self, highRow: int) -> None: ...
    def openRow(self) -> int: ...
    def setOpenRow(self, openRow: int) -> None: ...
    def timestampRow(self) -> int: ...
    def setTimestampRow(self, timestampRow: int) -> None: ...
    def orientation(self) -> QtCore.Qt.Orientation: ...


class QVPieModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    rowCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    labelsColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    valuesColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setRowCount(self, rowCount: int) -> None: ...
    def rowCount(self) -> int: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def firstRow(self) -> int: ...
    def setSeries(self, series: typing.Optional['QPieSeries']) -> None: ...
    def series(self) -> typing.Optional['QPieSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setLabelsColumn(self, labelsColumn: int) -> None: ...
    def labelsColumn(self) -> int: ...
    def setValuesColumn(self, valuesColumn: int) -> None: ...
    def valuesColumn(self) -> int: ...


class QVXYModelMapper(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    rowCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    firstRowChanged: typing.ClassVar[QtCore.pyqtSignal]
    yColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    xColumnChanged: typing.ClassVar[QtCore.pyqtSignal]
    modelReplaced: typing.ClassVar[QtCore.pyqtSignal]
    seriesReplaced: typing.ClassVar[QtCore.pyqtSignal]
    def setRowCount(self, rowCount: int) -> None: ...
    def rowCount(self) -> int: ...
    def setFirstRow(self, firstRow: int) -> None: ...
    def firstRow(self) -> int: ...
    def setSeries(self, series: typing.Optional['QXYSeries']) -> None: ...
    def series(self) -> typing.Optional['QXYSeries']: ...
    def setModel(self, model: typing.Optional[QtCore.QAbstractItemModel]) -> None: ...
    def model(self) -> typing.Optional[QtCore.QAbstractItemModel]: ...
    def setYColumn(self, yColumn: int) -> None: ...
    def yColumn(self) -> int: ...
    def setXColumn(self, xColumn: int) -> None: ...
    def xColumn(self) -> int: ...


class QXYLegendMarker('QLegendMarker'):

    def __init__(self, series: typing.Optional['QXYSeries'], legend: typing.Optional['QLegend'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def series(self) -> typing.Optional['QXYSeries']: ...
    def type(self) -> 'QLegendMarker.LegendMarkerType': ...


PYQT_CHART_VERSION = ... # type: int
PYQT_CHART_VERSION_STR = ... # type: str
