// qcandlestickmodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_5_8_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qcandlestickmodelmapper.h>
%End

    class QCandlestickModelMapper : public QObject
    {
%TypeHeaderCode
#include <qcandlestickmodelmapper.h>
%End

    public:
        explicit QCandlestickModelMapper(QObject *parent /TransferThis/ = 0);
        void setModel(QAbstractItemModel *model /KeepReference/);
        QAbstractItemModel *model() const;
        void setSeries(QtCharts::QCandlestickSeries *series);
        QtCharts::QCandlestickSeries *series() const;
        virtual Qt::Orientation orientation() const = 0;

    signals:
        void modelReplaced();
        void seriesReplaced();

    protected:
        void setTimestamp(int timestamp);
        int timestamp() const;
        void setOpen(int open);
        int open() const;
        void setHigh(int high);
        int high() const;
        void setLow(int low);
        int low() const;
        void setClose(int close);
        int close() const;
        void setFirstSetSection(int firstSetSection);
        int firstSetSection() const;
        void setLastSetSection(int lastSetSection);
        int lastSetSection() const;
    };
};

%End
