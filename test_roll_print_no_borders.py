#!/usr/bin/env python3
"""
اختبار طباعة الرول بدون حدود
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🖨️ اختبار طباعة الرول بدون حدود...")
    print("=" * 60)
    print("🔧 التحسينات المطبقة:")
    print("")
    print("   ✅ إزالة الحدود من معلومات البراند:")
    print("     • لا توجد حدود حول اللوجو")
    print("     • لا توجد خلفية أو حدود حول الترويسة")
    print("     • عرض نظيف ومباشر للمعلومات")
    print("")
    print("   ✅ إزالة الحدود من تفاصيل الفاتورة:")
    print("     • لا توجد حدود حول قسم التفاصيل")
    print("     • لا توجد خلفية ملونة")
    print("     • عرض مباشر للمعلومات المالية")
    print("")
    print("   ✅ تطبيق على جميع أنواع الفواتير:")
    print("     • فواتير المبيعات العادية")
    print("     • فواتير مرتجع المبيعات")
    print("     • فواتير مرتجع المشتريات")
    print("")
    print("   🎨 النتيجة:")
    print("     • تصميم نظيف وبسيط")
    print("     • توفير في الحبر والورق")
    print("     • مظهر احترافي ومرتب")
    print("     • سهولة في القراءة")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 تشغيل النظام...")
        
        # اختبار الواجهة الرئيسية
        print("🖥️ تشغيل الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.engine = engine
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار طباعة الرول بدون حدود:")
        print("")
        print("   📋 طباعة فاتورة مبيعات:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض الفواتير المحفوظة'")
        print("     2️⃣ اختر أي فاتورة واضغط 'طباعة'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ لاحظ التصميم النظيف بدون حدود:")
        print("        • لا توجد حدود حول معلومات الشركة")
        print("        • لا توجد حدود حول تفاصيل الفاتورة")
        print("        • تصميم نظيف ومرتب")
        print("")
        print("   🔄 طباعة فاتورة مرتجع:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'عرض فواتير المرتجعات'")
        print("     2️⃣ اختر أي مرتجع واضغط '🖨️'")
        print("     3️⃣ اختر 'رول' كنوع الطابعة")
        print("     4️⃣ لاحظ:")
        print("        • عنوان 'فاتورة مرتجع مبيعات' بدون حدود")
        print("        • رقم المرتجع R000001 واضح")
        print("        • رقم الفاتورة الأصلية #000001")
        print("        • تصميم نظيف بدون حدود")
        print("")
        print("   🆕 إنشاء مرتجع جديد وطباعته:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'مرتجع المبيعات'")
        print("     2️⃣ ابحث عن فاتورة واختر منتجات")
        print("     3️⃣ اضغط 'حفظ وطباعة'")
        print("     4️⃣ اختر 'رول' واطبع")
        print("     5️⃣ لاحظ التصميم الجديد النظيف")
        print("")
        print("   🔍 ما تبحث عنه:")
        print("     ❌ لا توجد مربعات أو حدود حول:")
        print("        • معلومات الشركة والبراند")
        print("        • قسم تفاصيل الفاتورة")
        print("        • اللوجو")
        print("     ✅ يوجد فقط:")
        print("        • النصوص والمعلومات مباشرة")
        print("        • خطوط فاصلة بسيطة")
        print("        • تصميم نظيف ومرتب")
        print("")
        print("   💡 مقارنة:")
        print("     🔴 قبل التحديث:")
        print("        • حدود سوداء حول كل قسم")
        print("        • خلفيات ملونة")
        print("        • مظهر مزدحم")
        print("     🟢 بعد التحديث:")
        print("        • تصميم نظيف بدون حدود")
        print("        • خلفية بيضاء فقط")
        print("        • مظهر احترافي ومرتب")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 ملخص التحسينات:")
    print("")
    print("   🔧 ما تم تغييره:")
    print("     ✅ إزالة drawRoundedRect من ترويسة الرول")
    print("     ✅ إزالة الحدود من حول اللوجو")
    print("     ✅ إزالة drawRoundedRect من قسم التفاصيل")
    print("     ✅ الاحتفاظ بالمحتوى والتنسيق")
    print("")
    print("   🎨 النتيجة النهائية:")
    print("     • تصميم رول نظيف وبسيط")
    print("     • بدون حدود أو خلفيات ملونة")
    print("     • سهولة في القراءة والطباعة")
    print("     • توفير في استهلاك الحبر")
    print("     • مظهر احترافي ومرتب")
    print("")
    print("   📋 يعمل مع:")
    print("     ✅ فواتير المبيعات العادية")
    print("     ✅ فواتير مرتجع المبيعات")
    print("     ✅ فواتير مرتجع المشتريات")
    print("     ✅ جميع أنواع الطباعة")
    print("")
    print("🎉 التحديث مكتمل ومطبق بنجاح!")
    print("   📱 جرب الطباعة الآن ولاحظ الفرق")
    print("   🖨️ تصميم نظيف وبسيط للرول")
    print("   💰 توفير في الحبر والورق")

if __name__ == "__main__":
    main()
