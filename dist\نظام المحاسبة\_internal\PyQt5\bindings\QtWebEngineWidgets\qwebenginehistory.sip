// qwebenginehistory.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_4_0 -)

class QWebEngineHistoryItem
{
%TypeHeaderCode
#include <qwebenginehistory.h>
%End

public:
    QWebEngineHistoryItem(const QWebEngineHistoryItem &other);
    ~QWebEngineHistoryItem();
    QUrl originalUrl() const;
    QUrl url() const;
    QString title() const;
    QDateTime lastVisited() const;
    QUrl iconUrl() const;
    bool isValid() const;
%If (QtWebEngine_5_6_0 -)
    void swap(QWebEngineHistoryItem &other /Constrained/);
%End
};

%End
%If (QtWebEngine_5_4_0 -)

class QWebEngineHistory /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginehistory.h>
%End

public:
    void clear();
    QList<QWebEngineHistoryItem> items() const;
    QList<QWebEngineHistoryItem> backItems(int maxItems) const;
    QList<QWebEngineHistoryItem> forwardItems(int maxItems) const;
    bool canGoBack() const;
    bool canGoForward() const;
    void back();
    void forward();
    void goToItem(const QWebEngineHistoryItem &item);
    QWebEngineHistoryItem backItem() const;
    QWebEngineHistoryItem currentItem() const;
    QWebEngineHistoryItem forwardItem() const;
    QWebEngineHistoryItem itemAt(int i) const;
    int currentItemIndex() const;
    int count() const /__len__/;

private:
    ~QWebEngineHistory();
    QWebEngineHistory(const QWebEngineHistory &);
};

%End
%If (QtWebEngine_5_4_0 -)
QDataStream &operator<<(QDataStream &, const QWebEngineHistory & /Constrained/) /ReleaseGIL/;
%End
%If (QtWebEngine_5_4_0 -)
QDataStream &operator>>(QDataStream &, QWebEngineHistory & /Constrained/) /ReleaseGIL/;
%End
