#!/usr/bin/env python3
"""
تشغيل البرنامج الأساسي مع إجبار استخدام ملف المخزون الجديد
"""

import sys
import os
import shutil
import importlib

def force_clean_all_cache():
    """حذف جميع ملفات cache بقوة"""
    print("🧹 حذف جميع ملفات cache...")
    
    # حذف مجلدات cache
    cache_dirs = ["gui/__pycache__", "__pycache__", "database/__pycache__"]
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✅ تم حذف {cache_dir}")
            except Exception as e:
                print(f"⚠️ خطأ في حذف {cache_dir}: {e}")
    
    # حذف ملفات .pyc في جميع المجلدات
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                try:
                    os.remove(os.path.join(root, file))
                    print(f"✅ تم حذف {file}")
                except:
                    pass

def force_reload_modules():
    """إجبار إعادة تحميل جميع الوحدات"""
    print("🔄 إجبار إعادة تحميل جميع الوحدات...")
    
    # إزالة جميع وحدات gui من الذاكرة
    modules_to_remove = []
    for module_name in list(sys.modules.keys()):
        if module_name.startswith('gui.') or 'inventory' in module_name.lower():
            modules_to_remove.append(module_name)
    
    for module_name in modules_to_remove:
        if module_name in sys.modules:
            del sys.modules[module_name]
            print(f"🗑️ تم حذف {module_name} من الذاكرة")
    
    # إعادة تعيين cache
    importlib.invalidate_caches()

def main():
    print("🚀 تشغيل البرنامج الأساسي مع ملف المخزون الجديد!")
    print("=" * 90)
    
    # حذف cache
    force_clean_all_cache()
    
    # إجبار إعادة تحميل الوحدات
    force_reload_modules()
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    
    # إعداد التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        print("📦 استيراد البرنامج الأساسي مع ملف المخزون الجديد...")
        
        # استيراد البرنامج الأساسي
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        print("✅ تم تشغيل البرنامج الأساسي بنجاح!")
        print("")
        print("🎯 الآن اذهب لقسم المخزون وستجد:")
        print("   📋 تبويبين واضحين: '📦 المخزون' و '⚙️ إدارة الأصناف'")
        print("   💰 عمود 'المكسب' بلون أخضر فاتح")
        print("   ⚡ تحديث فوري للقيم عند إضافة/تعديل المنتجات")
        print("   🎨 واجهة جميلة ومتجاوبة")
        print("   ⚙️ أزرار تعديل وحذف في تبويب إدارة الأصناف")
        print("")
        print("🔥 جميع التحديثات موجودة الآن!")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
