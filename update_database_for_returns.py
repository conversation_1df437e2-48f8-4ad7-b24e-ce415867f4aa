#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لدعم نظام المرتجعات
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔧 تحديث قاعدة البيانات لدعم نظام المرتجعات...")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        with Session(engine) as session:
            print("📊 فحص الجداول الحالية...")
            
            # فحص جدول transactions
            result = session.execute(text("PRAGMA table_info(transactions)"))
            columns = [row[1] for row in result.fetchall()]
            print(f"   ✅ أعمدة جدول transactions: {len(columns)} عمود")
            
            # التحقق من وجود العمود الجديد
            if 'original_transaction_id' not in columns:
                print("   🔧 إضافة عمود original_transaction_id...")
                session.execute(text("""
                    ALTER TABLE transactions 
                    ADD COLUMN original_transaction_id INTEGER 
                    REFERENCES transactions(id)
                """))
                session.commit()
                print("   ✅ تم إضافة العمود بنجاح!")
            else:
                print("   ✅ العمود original_transaction_id موجود مسبقاً")
            
            # التحقق من أنواع المعاملات
            print("📋 فحص أنواع المعاملات...")
            from database.models import TransactionType
            
            print(f"   ✅ SALE = '{TransactionType.SALE.value}'")
            print(f"   ✅ PURCHASE = '{TransactionType.PURCHASE.value}'")
            print(f"   ✅ EXPENSE = '{TransactionType.EXPENSE.value}'")
            print(f"   ✅ INCOME = '{TransactionType.INCOME.value}'")
            print(f"   🆕 SALE_RETURN = '{TransactionType.SALE_RETURN.value}'")
            print(f"   🆕 PURCHASE_RETURN = '{TransactionType.PURCHASE_RETURN.value}'")
            
            # اختبار إنشاء معاملة مرتجع تجريبية
            print("🧪 اختبار إنشاء معاملة مرتجع...")
            from database.models import Transaction
            
            # البحث عن فاتورة مبيعات موجودة
            existing_sale = session.query(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).first()
            
            if existing_sale:
                print(f"   📋 وجدت فاتورة مبيعات رقم: {existing_sale.id}")
                
                # إنشاء مرتجع تجريبي
                test_return = Transaction(
                    type=TransactionType.SALE_RETURN,
                    total_amount=100.0,
                    paid_amount=100.0,
                    customer_id=existing_sale.customer_id,
                    original_transaction_id=existing_sale.id,
                    notes="مرتجع تجريبي للاختبار"
                )
                session.add(test_return)
                session.commit()
                
                print(f"   ✅ تم إنشاء مرتجع تجريبي رقم: {test_return.id}")
                
                # حذف المرتجع التجريبي
                session.delete(test_return)
                session.commit()
                print("   🗑️ تم حذف المرتجع التجريبي")
                
            else:
                print("   ⚠️ لا توجد فواتير مبيعات للاختبار")
            
            print("✅ تم تحديث قاعدة البيانات بنجاح!")
            
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False
    
    print("=" * 60)
    print("📋 ملخص التحديثات:")
    print("   ✅ إضافة أنواع معاملات جديدة:")
    print("     • SALE_RETURN (مرتجع_مبيعات)")
    print("     • PURCHASE_RETURN (مرتجع_مشتريات)")
    print("   ✅ إضافة عمود original_transaction_id")
    print("   ✅ اختبار إنشاء معاملات المرتجع")
    print("   ✅ قاعدة البيانات جاهزة لنظام المرتجعات")
    print("")
    print("🎯 الخطوات التالية:")
    print("   1️⃣ تشغيل التطبيق الرئيسي")
    print("   2️⃣ الذهاب لقائمة المبيعات أو المشتريات")
    print("   3️⃣ اختيار مرتجع المبيعات أو مرتجع المشتريات")
    print("   4️⃣ البدء في استخدام النظام الجديد")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 تم التحديث بنجاح! يمكنك الآن استخدام نظام المرتجعات.")
    else:
        print("\n❌ فشل التحديث. يرجى مراجعة الأخطاء أعلاه.")
