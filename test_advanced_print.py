#!/usr/bin/env python3
"""
اختبار نظام الطباعة المتقدم مع الرسم المباشر
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestAdvancedPrintWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نظام الطباعة المتقدم")
        self.setGeometry(100, 100, 700, 600)
        
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title_label = QPushButton("🎨 نظام الطباعة المتقدم - رسم مباشر بالألوان")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 30px;
                border: none;
                border-radius: 15px;
                margin-bottom: 25px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات النظام الجديد
        info_label = QPushButton("""
🚀 النظام الجديد - مميزات متقدمة:

✅ رسم مباشر باستخدام QPainter
✅ ألوان زاهية محفوظة 100%
✅ تدرجات لونية جميلة
✅ تصميم احترافي متقدم
✅ معاينة فورية ملونة
✅ طباعة وحفظ PDF بجودة عالية
✅ دعم طابعات A4 والرول
✅ تحكم كامل في التصميم

🎯 التقنية المستخدمة:
• QPainter للرسم المباشر
• QLinearGradient للتدرجات
• QColor للألوان الزاهية
• QPrinter للطباعة عالية الجودة
        """)
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
                color: #2c3e50;
                font-size: 14px;
                padding: 25px;
                border: 3px solid #27ae60;
                border-radius: 12px;
                text-align: left;
                margin: 15px 0;
                line-height: 1.8;
            }
        """)
        layout.addWidget(info_label)
        
        # زر اختبار الفاتورة رقم 1
        test_btn1 = QPushButton("🧾 اختبار النظام المتقدم - فاتورة رقم 1")
        test_btn1.setStyleSheet(self.get_button_style("#27AE60"))
        test_btn1.clicked.connect(lambda: self.test_advanced_printing(1))
        layout.addWidget(test_btn1)
        
        # زر اختبار الفاتورة رقم 7
        test_btn7 = QPushButton("🧾 اختبار النظام المتقدم - فاتورة رقم 7")
        test_btn7.setStyleSheet(self.get_button_style("#3498DB"))
        test_btn7.clicked.connect(lambda: self.test_advanced_printing(7))
        layout.addWidget(test_btn7)
        
        # زر مقارنة الأنظمة
        compare_btn = QPushButton("🔄 مقارنة النظام القديم والجديد")
        compare_btn.setStyleSheet(self.get_button_style("#E67E22"))
        compare_btn.clicked.connect(self.show_comparison)
        layout.addWidget(compare_btn)
        
        # تعليمات مفصلة
        instructions_label = QPushButton("""
📋 تعليمات الاختبار المفصلة:

1️⃣ اضغط على "اختبار النظام المتقدم"
2️⃣ ستظهر نافذة جديدة مع معاينة ملونة فورية
3️⃣ لاحظ الألوان الزاهية والتدرجات الجميلة:
   • ترويسة خضراء متدرجة مع لوجو
   • عنوان أزرق متدرج للفاتورة
   • جدول أخضر متدرج للمنتجات
   • تفاصيل زرقاء متدرجة للمعلومات
4️⃣ جرب "حفظ PDF" - ستحصل على ملف ملون بالكامل!
5️⃣ جرب "طباعة" - ستطبع بالألوان الكاملة!

🎯 النتيجة المتوقعة:
• معاينة ملونة فورية
• PDF ملون بجودة عالية
• طباعة ملونة احترافية
• تصميم جميل ومتقدم
        """)
        instructions_label.setEnabled(False)
        instructions_label.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                color: #856404;
                font-size: 13px;
                padding: 20px;
                border: 3px solid #ffc107;
                border-radius: 10px;
                text-align: left;
                margin: 15px 0;
                line-height: 1.6;
            }
        """)
        layout.addWidget(instructions_label)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#E74C3C"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def get_button_style(self, color):
        """إرجاع نمط الأزرار"""
        return f"""
            QPushButton {{
                background: linear-gradient(135deg, {color} 0%, {color}DD 100%);
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 12px;
                min-height: 70px;
                margin: 10px;
            }}
            QPushButton:hover {{
                background: linear-gradient(135deg, {color}EE 0%, {color}CC 100%);
                transform: translateY(-3px);
            }}
            QPushButton:pressed {{
                background: linear-gradient(135deg, {color}BB 0%, {color}AA 100%);
            }}
        """
    
    def test_advanced_printing(self, invoice_id):
        """اختبار النظام المتقدم"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار النظام المتقدم:\n{str(e)}")
    
    def show_comparison(self):
        """عرض مقارنة بين الأنظمة"""
        comparison_text = """
🔄 مقارنة شاملة بين الأنظمة:

❌ النظام القديم (QTextBrowser + HTML):
• ألوان باهتة أو مفقودة
• تصميم محدود بقيود CSS
• فقدان التدرجات عند الطباعة
• جودة منخفضة في PDF
• عدم دعم الطباعة الملونة
• تحكم محدود في التصميم

✅ النظام الجديد (QPainter + رسم مباشر):
• ألوان زاهية 100% محفوظة
• تدرجات لونية جميلة
• تحكم كامل في كل بكسل
• جودة عالية في PDF والطباعة
• دعم كامل للطباعة الملونة
• تصميم احترافي متقدم
• معاينة فورية ملونة
• أداء أفضل وسرعة أعلى

🎯 التقنيات المتقدمة:
• QPainter: رسم مباشر عالي الجودة
• QLinearGradient: تدرجات لونية متقدمة
• QColor: ألوان دقيقة ومتنوعة
• QPrinter: طباعة احترافية
• Anti-aliasing: تنعيم الحواف
• High Resolution: دقة عالية

🚀 النتيجة:
تحسن جذري في جودة الطباعة والتصميم!
        """
        
        QMessageBox.information(self, "مقارنة الأنظمة", comparison_text)

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم عام
    app.setStyleSheet("""
        QMainWindow {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        QWidget {
            font-family: 'Arial', 'Tahoma', sans-serif;
        }
    """)
    
    print("🚀 بدء اختبار نظام الطباعة المتقدم...")
    print("=" * 70)
    print("✅ النظام الجديد يستخدم:")
    print("   • QPainter للرسم المباشر")
    print("   • QLinearGradient للتدرجات اللونية")
    print("   • QColor للألوان الزاهية")
    print("   • QPrinter للطباعة عالية الجودة")
    print("=" * 70)
    print("🎯 افتح النافذة لاختبار النظام المتقدم")
    
    window = TestAdvancedPrintWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
