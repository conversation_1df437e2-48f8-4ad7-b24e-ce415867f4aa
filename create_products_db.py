#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات للأدوات المنزلية من الشركات المصرية المعروفة
"""

import pandas as pd
import random
import os

def create_products_database():
    """إنشاء قاعدة بيانات شاملة للأدوات المنزلية المصرية"""
    
    # الشركات المصرية المعروفة
    companies = [
        "أحد", "فريش", "توشيبا العربي", "يونيون إير", "كريازي", 
        "زانوسي", "إنديست", "أوليمبيك", "وايت ويل", "بيكو",
        "شارب العربي", "هيتاشي", "إل جي", "سامسونج", "الأسكا"
    ]
    
    # المنتجات حسب الفئات
    products_data = [
        # أجهزة كهربائية كبيرة
        {"name": "ثلاجة", "category": "أجهزة كهربائية كبيرة", "price_range": (8000, 25000), "unit": "قطعة"},
        {"name": "غسالة ملابس", "category": "أجهزة كهربائية كبيرة", "price_range": (6000, 18000), "unit": "قطعة"},
        {"name": "غسالة أطباق", "category": "أجهزة كهربائية كبيرة", "price_range": (7000, 15000), "unit": "قطعة"},
        {"name": "فريزر", "category": "أجهزة كهربائية كبيرة", "price_range": (5000, 12000), "unit": "قطعة"},
        {"name": "مكيف هواء", "category": "أجهزة كهربائية كبيرة", "price_range": (4000, 20000), "unit": "قطعة"},
        {"name": "سخان مياه", "category": "أجهزة كهربائية كبيرة", "price_range": (1500, 8000), "unit": "قطعة"},
        
        # أجهزة كهربائية صغيرة
        {"name": "مكواة بخار", "category": "أجهزة كهربائية صغيرة", "price_range": (200, 1500), "unit": "قطعة"},
        {"name": "مجفف شعر", "category": "أجهزة كهربائية صغيرة", "price_range": (150, 800), "unit": "قطعة"},
        {"name": "خلاط كهربائي", "category": "أجهزة كهربائية صغيرة", "price_range": (300, 2000), "unit": "قطعة"},
        {"name": "محضر طعام", "category": "أجهزة كهربائية صغيرة", "price_range": (500, 3000), "unit": "قطعة"},
        {"name": "مايكروويف", "category": "أجهزة كهربائية صغيرة", "price_range": (1200, 5000), "unit": "قطعة"},
        {"name": "فرن كهربائي", "category": "أجهزة كهربائية صغيرة", "price_range": (800, 4000), "unit": "قطعة"},
        {"name": "غلاية كهربائية", "category": "أجهزة كهربائية صغيرة", "price_range": (100, 600), "unit": "قطعة"},
        {"name": "محمصة خبز", "category": "أجهزة كهربائية صغيرة", "price_range": (200, 1000), "unit": "قطعة"},
        {"name": "مقلاة هوائية", "category": "أجهزة كهربائية صغيرة", "price_range": (800, 3500), "unit": "قطعة"},
        {"name": "عصارة فواكه", "category": "أجهزة كهربائية صغيرة", "price_range": (300, 1500), "unit": "قطعة"},
        
        # أدوات مطبخ
        {"name": "طقم أواني طبخ", "category": "أدوات مطبخ", "price_range": (300, 2000), "unit": "طقم"},
        {"name": "طقم سكاكين", "category": "أدوات مطبخ", "price_range": (100, 800), "unit": "طقم"},
        {"name": "لوح تقطيع خشبي", "category": "أدوات مطبخ", "price_range": (50, 300), "unit": "قطعة"},
        {"name": "مصفاة معدنية", "category": "أدوات مطبخ", "price_range": (30, 200), "unit": "قطعة"},
        {"name": "مقلاة تيفال", "category": "أدوات مطبخ", "price_range": (150, 800), "unit": "قطعة"},
        {"name": "حلة ضغط", "category": "أدوات مطبخ", "price_range": (400, 1500), "unit": "قطعة"},
        {"name": "طقم تقديم", "category": "أدوات مطبخ", "price_range": (200, 1000), "unit": "طقم"},
        {"name": "علب حفظ طعام", "category": "أدوات مطبخ", "price_range": (80, 400), "unit": "طقم"},
        {"name": "ترمس حراري", "category": "أدوات مطبخ", "price_range": (100, 500), "unit": "قطعة"},
        {"name": "إبريق شاي", "category": "أدوات مطبخ", "price_range": (80, 400), "unit": "قطعة"},
        
        # أدوات تنظيف
        {"name": "مكنسة كهربائية", "category": "أدوات تنظيف", "price_range": (800, 4000), "unit": "قطعة"},
        {"name": "ممسحة أرضيات", "category": "أدوات تنظيف", "price_range": (50, 300), "unit": "قطعة"},
        {"name": "دلو تنظيف", "category": "أدوات تنظيف", "price_range": (30, 150), "unit": "قطعة"},
        {"name": "فرشاة تنظيف", "category": "أدوات تنظيف", "price_range": (20, 100), "unit": "قطعة"},
        {"name": "منظف متعدد الأغراض", "category": "أدوات تنظيف", "price_range": (25, 80), "unit": "عبوة"},
        {"name": "مسحوق غسيل", "category": "أدوات تنظيف", "price_range": (30, 120), "unit": "عبوة"},
        {"name": "معطر جو", "category": "أدوات تنظيف", "price_range": (15, 60), "unit": "عبوة"},
        {"name": "إسفنجة تنظيف", "category": "أدوات تنظيف", "price_range": (10, 40), "unit": "عبوة"},
        {"name": "قفازات مطاطية", "category": "أدوات تنظيف", "price_range": (15, 50), "unit": "زوج"},
        {"name": "أكياس قمامة", "category": "أدوات تنظيف", "price_range": (20, 80), "unit": "عبوة"},
        
        # أدوات حمام
        {"name": "دش حمام", "category": "أدوات حمام", "price_range": (200, 1000), "unit": "قطعة"},
        {"name": "خلاط مياه", "category": "أدوات حمام", "price_range": (300, 1500), "unit": "قطعة"},
        {"name": "مرآة حمام", "category": "أدوات حمام", "price_range": (100, 600), "unit": "قطعة"},
        {"name": "رف حمام", "category": "أدوات حمام", "price_range": (80, 400), "unit": "قطعة"},
        {"name": "ستارة حمام", "category": "أدوات حمام", "price_range": (50, 250), "unit": "قطعة"},
        {"name": "سجادة حمام", "category": "أدوات حمام", "price_range": (40, 200), "unit": "قطعة"},
        {"name": "موزع صابون", "category": "أدوات حمام", "price_range": (30, 150), "unit": "قطعة"},
        {"name": "فرشاة أسنان كهربائية", "category": "أدوات حمام", "price_range": (200, 800), "unit": "قطعة"},
        {"name": "منشفة قطنية", "category": "أدوات حمام", "price_range": (60, 300), "unit": "قطعة"},
        {"name": "سلة غسيل", "category": "أدوات حمام", "price_range": (50, 250), "unit": "قطعة"},
        
        # إضاءة وكهرباء
        {"name": "لمبة LED", "category": "إضاءة وكهرباء", "price_range": (15, 80), "unit": "قطعة"},
        {"name": "نجفة كريستال", "category": "إضاءة وكهرباء", "price_range": (500, 3000), "unit": "قطعة"},
        {"name": "أباجورة طاولة", "category": "إضاءة وكهرباء", "price_range": (100, 600), "unit": "قطعة"},
        {"name": "كشاف LED", "category": "إضاءة وكهرباء", "price_range": (80, 400), "unit": "قطعة"},
        {"name": "مفتاح كهرباء", "category": "إضاءة وكهرباء", "price_range": (20, 100), "unit": "قطعة"},
        {"name": "مقبس كهرباء", "category": "إضاءة وكهرباء", "price_range": (25, 120), "unit": "قطعة"},
        {"name": "سلك كهرباء", "category": "إضاءة وكهرباء", "price_range": (30, 150), "unit": "متر"},
        {"name": "لمبة توفير", "category": "إضاءة وكهرباء", "price_range": (25, 100), "unit": "قطعة"},
        {"name": "بطارية قلوية", "category": "إضاءة وكهرباء", "price_range": (10, 50), "unit": "عبوة"},
        {"name": "شاحن هاتف", "category": "إضاءة وكهرباء", "price_range": (50, 200), "unit": "قطعة"}
    ]
    
    # إنشاء قائمة المنتجات النهائية
    final_products = []
    product_id = 1
    
    for product in products_data:
        for company in companies:
            # إنشاء 2-3 موديلات لكل منتج من كل شركة
            models = ["عادي", "فاخر", "مطور"]
            num_models = random.randint(1, 3)
            
            for i in range(num_models):
                model = models[i]
                
                # إنشاء الكود
                company_code = company.replace(" ", "").replace("العربي", "")[:4].upper()
                product_code = f"{company_code}_{product_id:04d}"
                
                # إنشاء الاسم
                product_name = f"{product['name']} {company} - {model}"
                
                # تحديد الأسعار
                min_price, max_price = product['price_range']
                purchase_price = random.randint(min_price, max_price)
                sale_price = int(purchase_price * random.uniform(1.25, 1.75))
                
                # الكمية والحد الأدنى
                quantity = random.randint(10, 100)
                min_quantity = random.randint(3, 15)
                
                # الباركود
                barcode = f"629{random.randint(*********, *********)}"
                
                # الوصف
                descriptions = [
                    f"منتج عالي الجودة من شركة {company} المصرية",
                    f"تصميم عصري ومتين، صناعة {company}",
                    f"منتج موثوق وعملي من {company}",
                    f"جودة ممتازة وضمان شامل من {company}",
                    f"تقنية متطورة وتصميم أنيق من {company}"
                ]
                
                final_product = {
                    "Code": product_code,
                    "Name": product_name,
                    "Description": random.choice(descriptions),
                    "Category": product['category'],
                    "Purchase_Price": purchase_price,
                    "Sale_Price": sale_price,
                    "Quantity": quantity,
                    "Min_Quantity": min_quantity,
                    "Unit": product['unit'],
                    "Barcode": barcode,
                    "Company": company,
                    "Model": model
                }
                
                final_products.append(final_product)
                product_id += 1
                
                # توقف عند 250 منتج
                if len(final_products) >= 250:
                    break
            
            if len(final_products) >= 250:
                break
        
        if len(final_products) >= 250:
            break
    
    return final_products

# إنشاء البيانات
print("🔄 جاري إنشاء قاعدة بيانات الأدوات المنزلية...")
products = create_products_database()

# إنشاء DataFrame
df = pd.DataFrame(products)

# إنشاء مجلد البيانات
os.makedirs("sample_data", exist_ok=True)

# حفظ في Excel
excel_file = "sample_data/منتجات_الأدوات_المنزلية_المصرية.xlsx"
df.to_excel(excel_file, index=False, engine='openpyxl')

# حفظ في CSV
csv_file = "sample_data/منتجات_الأدوات_المنزلية_المصرية.csv"
df.to_csv(csv_file, index=False, encoding='utf-8-sig')

print(f"\n✅ تم إنشاء قاعدة البيانات بنجاح!")
print(f"📁 ملف Excel: {excel_file}")
print(f"📁 ملف CSV: {csv_file}")
print(f"📊 عدد المنتجات: {len(products)}")
print(f"🏢 عدد الشركات: {len(set(df['Company']))}")
print(f"📂 عدد الفئات: {len(set(df['Category']))}")

# عرض إحصائيات
print(f"\n📈 إحصائيات الأسعار:")
print(f"💰 متوسط سعر الشراء: {df['Purchase_Price'].mean():.0f} جنيه")
print(f"💰 متوسط سعر البيع: {df['Sale_Price'].mean():.0f} جنيه")
print(f"📦 إجمالي الكميات: {df['Quantity'].sum()} قطعة")

print(f"\n🏭 الشركات المتضمنة:")
for company in sorted(set(df['Company'])):
    count = len(df[df['Company'] == company])
    print(f"   • {company}: {count} منتج")
