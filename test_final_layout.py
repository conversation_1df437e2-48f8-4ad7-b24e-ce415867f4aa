#!/usr/bin/env python3
"""
اختبار التخطيط النهائي المحسن
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار التخطيط النهائي المحسن...")
    print("=" * 60)
    print("🔧 الإصلاحات المطبقة:")
    print("   A4:")
    print("     ✅ نقل الإجمالي النهائي خارج قسم التفاصيل")
    print("     ✅ إضافة مسافة 20px بين التفاصيل والإجمالي")
    print("     ✅ زيادة ارتفاع الإجمالي إلى 50px")
    print("     ✅ زيادة حجم خط الإجمالي إلى 18px")
    print("     ✅ منع التداخل مع النصوص الأخرى")
    print("")
    print("   الرول:")
    print("     ✅ إزالة الحدود من الإجمالي النهائي")
    print("     ✅ إزالة الخلفية من الإجمالي")
    print("     ✅ زيادة حجم خط الإجمالي إلى 12px")
    print("     ✅ مظهر نظيف وبسيط للإجمالي")
    print("     ✅ الحفاظ على قسم التفاصيل مع الحدود")
    print("=" * 60)
    print("📐 التخطيط الجديد:")
    print("   A4:")
    print("     1️⃣ الترويسة (180px)")
    print("     2️⃣ عنوان الفاتورة (60px)")
    print("     3️⃣ جدول المنتجات (متغير)")
    print("     4️⃣ قسم التفاصيل (180px)")
    print("     5️⃣ مسافة فاصلة (20px)")
    print("     6️⃣ الإجمالي النهائي (50px) ← منفصل!")
    print("")
    print("   الرول:")
    print("     1️⃣ الترويسة (120px)")
    print("     2️⃣ معلومات الفاتورة (متغير)")
    print("     3️⃣ المنتجات (متغير)")
    print("     4️⃣ قسم التفاصيل (120px) ← مع حدود")
    print("     5️⃣ الإجمالي النهائي (25px) ← بدون حدود")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع التخطيط المحسن...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من الإصلاحات:")
        print("   A4:")
        print("     • تأكد أن الإجمالي النهائي منفصل عن التفاصيل")
        print("     • لاحظ عدم وجود تداخل في النصوص")
        print("     • تحقق من وضوح جميع المعلومات")
        print("     • لاحظ الحجم الأكبر للإجمالي النهائي")
        print("   الرول:")
        print("     • تأكد أن قسم التفاصيل له حدود واضحة")
        print("     • لاحظ أن الإجمالي النهائي بدون حدود")
        print("     • تحقق من النظافة والبساطة")
        print("     • لاحظ الخط الأكبر للإجمالي")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة التخطيط:")
    print("   قبل الإصلاح:")
    print("     A4:")
    print("       ❌ الإجمالي يغطي على التفاصيل")
    print("       ❌ تداخل في النصوص")
    print("       ❌ صعوبة في القراءة")
    print("     الرول:")
    print("       ❌ الإجمالي له حدود غير ضرورية")
    print("       ❌ مظهر مزدحم")
    print("")
    print("   بعد الإصلاح:")
    print("     A4:")
    print("       ✅ الإجمالي منفصل ووا<|im_start|>ح")
    print("       ✅ لا يوجد تداخل")
    print("       ✅ سهولة في القراءة")
    print("       ✅ تخطيط منطقي ومنظم")
    print("     الرول:")
    print("       ✅ الإجمالي نظيف وبسيط")
    print("       ✅ مظهر غير مزدحم")
    print("       ✅ تركيز على المحتوى")
    print("       ✅ توازن بصري أفضل")
    print("")
    print("🎯 الفوائد:")
    print("   ✅ وضوح أكبر في المعلومات")
    print("   ✅ سهولة في القراءة")
    print("   ✅ مظهر احترافي ومنظم")
    print("   ✅ عدم وجود تداخل أو تشويش")
    print("   ✅ تجربة مستخدم محسنة")

if __name__ == "__main__":
    main()
