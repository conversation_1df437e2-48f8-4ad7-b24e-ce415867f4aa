#!/usr/bin/env python3
"""
اختبار الترويسة الأكبر المحسنة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار الترويسة الأكبر المحسنة...")
    print("=" * 60)
    print("🚀 التحسينات الجديدة:")
    print("   • ارتفاع الترويسة: 180px (بدلاً من 120px)")
    print("   • حجم اللوجو: 120x120 (بدلاً من 90x90)")
    print("   • خط اسم الشركة: 18px غامق")
    print("   • خط التفاصيل: 13px")
    print("   • ارتفاع الأسطر: 25px لاسم الشركة، 20px للتفاصيل")
    print("   • تقسيم العنوان إلى أسطر متعددة")
    print("   • مساحة أكبر للنصوص")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع الترويسة الأكبر...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من الترويسة الجديدة:")
        print("   • هل اسم الشركة ظاهر كاملاً؟")
        print("   • هل العنوان مقسم على أسطر متعددة؟")
        print("   • هل الهاتف والبريد الإلكتروني ظاهران؟")
        print("   • هل اللوجو أكبر وأوضح؟")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 النتيجة المتوقعة:")
    print("   ✅ ترويسة أكبر بارتفاع 180px")
    print("   ✅ نصوص كاملة وواضحة")
    print("   ✅ لوجو أكبر وأوضح")
    print("   ✅ تقسيم ذكي للنصوص الطويلة")
    print("   ✅ مساحة كافية لجميع التفاصيل")

if __name__ == "__main__":
    main()
