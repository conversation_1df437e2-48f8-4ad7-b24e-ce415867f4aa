import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "qwavefrontmesh.h"
        name: "QWavefrontMesh"
        prototype: "QQuickShaderEffectMesh"
        exports: ["Qt.labs.wavefrontmesh/WavefrontMesh 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Error"
            values: [
                "NoError",
                "InvalidSourceError",
                "UnsupportedFaceShapeError",
                "UnsupportedIndexSizeError",
                "FileNotFoundError",
                "NoAttributesError",
                "MissingPositionAttributeError",
                "MissingTextureCoordinateAttributeError",
                "MissingPositionAndTextureCoordinateAttributesError",
                "TooManyAttributesError",
                "InvalidPlaneDefinitionError"
            ]
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "lastError"; type: "Error"; isReadonly: true }
        Property { name: "projectionPlaneV"; type: "QVector3D" }
        Property { name: "projectionPlaneW"; type: "QVector3D" }
        Method { name: "readData" }
    }
}
