# The PEP 484 type hints stub file for the QtNetwork module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QOcspRevocationReason(int):
    None_ = ... # type: QOcspRevocationReason
    Unspecified = ... # type: QOcspRevocationReason
    KeyCompromise = ... # type: QOcspRevocationReason
    CACompromise = ... # type: QOcspRevocationReason
    AffiliationChanged = ... # type: QOcspRevocationReason
    Superseded = ... # type: QOcspRevocationReason
    CessationOfOperation = ... # type: QOcspRevocationReason
    CertificateHold = ... # type: QOcspRevocationReason
    RemoveFromCRL = ... # type: QOcspRevocationReason


class QOcspCertificateStatus(int):
    Good = ... # type: QOcspCertificateStatus
    Revoked = ... # type: QOcspCertificateStatus
    Unknown = ... # type: QOcspCertificateStatus


class QNetworkCacheMetaData(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkCacheMetaData') -> None: ...

    def swap(self, other: 'QNetworkCacheMetaData') -> None: ...
    def setAttributes(self, attributes: typing.Dict['QNetworkRequest.Attribute', typing.Any]) -> None: ...
    def attributes(self) -> typing.Dict['QNetworkRequest.Attribute', typing.Any]: ...
    def setSaveToDisk(self, allow: bool) -> None: ...
    def saveToDisk(self) -> bool: ...
    def setExpirationDate(self, dateTime: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def expirationDate(self) -> QtCore.QDateTime: ...
    def setLastModified(self, dateTime: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def lastModified(self) -> QtCore.QDateTime: ...
    def setRawHeaders(self, headers: typing.Iterable[typing.Tuple[typing.Union[QtCore.QByteArray, bytes, bytearray], typing.Union[QtCore.QByteArray, bytes, bytearray]]]) -> None: ...
    def rawHeaders(self) -> typing.List[typing.Tuple[QtCore.QByteArray, QtCore.QByteArray]]: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QAbstractNetworkCache(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def clear(self) -> None: ...
    def insert(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def prepare(self, metaData: QNetworkCacheMetaData) -> typing.Optional[QtCore.QIODevice]: ...
    def cacheSize(self) -> int: ...
    def remove(self, url: QtCore.QUrl) -> bool: ...
    def data(self, url: QtCore.QUrl) -> typing.Optional[QtCore.QIODevice]: ...
    def updateMetaData(self, metaData: QNetworkCacheMetaData) -> None: ...
    def metaData(self, url: QtCore.QUrl) -> QNetworkCacheMetaData: ...


class QAbstractSocket(QtCore.QIODevice):

    class PauseMode(int):
        PauseNever = ... # type: QAbstractSocket.PauseMode
        PauseOnSslErrors = ... # type: QAbstractSocket.PauseMode

    class BindFlag(int):
        DefaultForPlatform = ... # type: QAbstractSocket.BindFlag
        ShareAddress = ... # type: QAbstractSocket.BindFlag
        DontShareAddress = ... # type: QAbstractSocket.BindFlag
        ReuseAddressHint = ... # type: QAbstractSocket.BindFlag

    class SocketOption(int):
        LowDelayOption = ... # type: QAbstractSocket.SocketOption
        KeepAliveOption = ... # type: QAbstractSocket.SocketOption
        MulticastTtlOption = ... # type: QAbstractSocket.SocketOption
        MulticastLoopbackOption = ... # type: QAbstractSocket.SocketOption
        TypeOfServiceOption = ... # type: QAbstractSocket.SocketOption
        SendBufferSizeSocketOption = ... # type: QAbstractSocket.SocketOption
        ReceiveBufferSizeSocketOption = ... # type: QAbstractSocket.SocketOption
        PathMtuSocketOption = ... # type: QAbstractSocket.SocketOption

    class SocketState(int):
        UnconnectedState = ... # type: QAbstractSocket.SocketState
        HostLookupState = ... # type: QAbstractSocket.SocketState
        ConnectingState = ... # type: QAbstractSocket.SocketState
        ConnectedState = ... # type: QAbstractSocket.SocketState
        BoundState = ... # type: QAbstractSocket.SocketState
        ListeningState = ... # type: QAbstractSocket.SocketState
        ClosingState = ... # type: QAbstractSocket.SocketState

    class SocketError(int):
        ConnectionRefusedError = ... # type: QAbstractSocket.SocketError
        RemoteHostClosedError = ... # type: QAbstractSocket.SocketError
        HostNotFoundError = ... # type: QAbstractSocket.SocketError
        SocketAccessError = ... # type: QAbstractSocket.SocketError
        SocketResourceError = ... # type: QAbstractSocket.SocketError
        SocketTimeoutError = ... # type: QAbstractSocket.SocketError
        DatagramTooLargeError = ... # type: QAbstractSocket.SocketError
        NetworkError = ... # type: QAbstractSocket.SocketError
        AddressInUseError = ... # type: QAbstractSocket.SocketError
        SocketAddressNotAvailableError = ... # type: QAbstractSocket.SocketError
        UnsupportedSocketOperationError = ... # type: QAbstractSocket.SocketError
        UnfinishedSocketOperationError = ... # type: QAbstractSocket.SocketError
        ProxyAuthenticationRequiredError = ... # type: QAbstractSocket.SocketError
        SslHandshakeFailedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionRefusedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionClosedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionTimeoutError = ... # type: QAbstractSocket.SocketError
        ProxyNotFoundError = ... # type: QAbstractSocket.SocketError
        ProxyProtocolError = ... # type: QAbstractSocket.SocketError
        OperationError = ... # type: QAbstractSocket.SocketError
        SslInternalError = ... # type: QAbstractSocket.SocketError
        SslInvalidUserDataError = ... # type: QAbstractSocket.SocketError
        TemporaryError = ... # type: QAbstractSocket.SocketError
        UnknownSocketError = ... # type: QAbstractSocket.SocketError

    class NetworkLayerProtocol(int):
        IPv4Protocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        IPv6Protocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        AnyIPProtocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        UnknownNetworkLayerProtocol = ... # type: QAbstractSocket.NetworkLayerProtocol

    class SocketType(int):
        TcpSocket = ... # type: QAbstractSocket.SocketType
        UdpSocket = ... # type: QAbstractSocket.SocketType
        SctpSocket = ... # type: QAbstractSocket.SocketType
        UnknownSocketType = ... # type: QAbstractSocket.SocketType

    class BindMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __xor__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __ior__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __or__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __iand__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __and__(self, f: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag']) -> 'QAbstractSocket.BindMode': ...
        def __invert__(self) -> 'QAbstractSocket.BindMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class PauseModes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __xor__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __ior__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __or__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __iand__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __and__(self, f: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> 'QAbstractSocket.PauseModes': ...
        def __invert__(self) -> 'QAbstractSocket.PauseModes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, socketType: 'QAbstractSocket.SocketType', parent: typing.Optional[QtCore.QObject]) -> None: ...

    def setProtocolTag(self, tag: typing.Optional[str]) -> None: ...
    def protocolTag(self) -> str: ...
    @typing.overload
    def bind(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int = ..., mode: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag'] = ...) -> bool: ...
    @typing.overload
    def bind(self, port: int = ..., mode: typing.Union['QAbstractSocket.BindMode', 'QAbstractSocket.BindFlag'] = ...) -> bool: ...
    def setPauseMode(self, pauseMode: typing.Union['QAbstractSocket.PauseModes', 'QAbstractSocket.PauseMode']) -> None: ...
    def pauseMode(self) -> 'QAbstractSocket.PauseModes': ...
    def resume(self) -> None: ...
    def socketOption(self, option: 'QAbstractSocket.SocketOption') -> typing.Any: ...
    def setSocketOption(self, option: 'QAbstractSocket.SocketOption', value: typing.Any) -> None: ...
    def setPeerName(self, name: typing.Optional[str]) -> None: ...
    def setPeerAddress(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    def setPeerPort(self, port: int) -> None: ...
    def setLocalAddress(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    def setLocalPort(self, port: int) -> None: ...
    def setSocketError(self, socketError: 'QAbstractSocket.SocketError') -> None: ...
    def setSocketState(self, state: 'QAbstractSocket.SocketState') -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def readData(self, maxlen: int) -> bytes: ...
    proxyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    hostFound: typing.ClassVar[QtCore.pyqtSignal]
    def proxy(self) -> 'QNetworkProxy': ...
    def setProxy(self, networkProxy: 'QNetworkProxy') -> None: ...
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    def flush(self) -> bool: ...
    def atEnd(self) -> bool: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> 'QAbstractSocket.SocketState': ...
    def socketType(self) -> 'QAbstractSocket.SocketType': ...
    def socketDescriptor(self) -> PyQt5.sip.voidptr: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt5.sip.voidptr, state: 'QAbstractSocket.SocketState' = ..., mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> bool: ...
    def abort(self) -> None: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def peerName(self) -> str: ...
    def peerAddress(self) -> 'QHostAddress': ...
    def peerPort(self) -> int: ...
    def localAddress(self) -> 'QHostAddress': ...
    def localPort(self) -> int: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isValid(self) -> bool: ...
    def disconnectFromHost(self) -> None: ...
    @typing.overload
    def connectToHost(self, hostName: typing.Optional[str], port: int, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ..., protocol: 'QAbstractSocket.NetworkLayerProtocol' = ...) -> None: ...
    @typing.overload
    def connectToHost(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...


class QAuthenticator(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAuthenticator') -> None: ...

    def setOption(self, opt: typing.Optional[str], value: typing.Any) -> None: ...
    def options(self) -> typing.Dict[str, typing.Any]: ...
    def option(self, opt: typing.Optional[str]) -> typing.Any: ...
    def isNull(self) -> bool: ...
    def realm(self) -> str: ...
    def setPassword(self, password: typing.Optional[str]) -> None: ...
    def password(self) -> str: ...
    def setUser(self, user: typing.Optional[str]) -> None: ...
    def user(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QDnsDomainNameRecord(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsDomainNameRecord') -> None: ...

    def value(self) -> str: ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsDomainNameRecord') -> None: ...


class QDnsHostAddressRecord(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsHostAddressRecord') -> None: ...

    def value(self) -> 'QHostAddress': ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsHostAddressRecord') -> None: ...


class QDnsMailExchangeRecord(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsMailExchangeRecord') -> None: ...

    def timeToLive(self) -> int: ...
    def preference(self) -> int: ...
    def name(self) -> str: ...
    def exchange(self) -> str: ...
    def swap(self, other: 'QDnsMailExchangeRecord') -> None: ...


class QDnsServiceRecord(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsServiceRecord') -> None: ...

    def weight(self) -> int: ...
    def timeToLive(self) -> int: ...
    def target(self) -> str: ...
    def priority(self) -> int: ...
    def port(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsServiceRecord') -> None: ...


class QDnsTextRecord(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsTextRecord') -> None: ...

    def values(self) -> typing.List[QtCore.QByteArray]: ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsTextRecord') -> None: ...


class QDnsLookup(QtCore.QObject):

    class Type(int):
        A = ... # type: QDnsLookup.Type
        AAAA = ... # type: QDnsLookup.Type
        ANY = ... # type: QDnsLookup.Type
        CNAME = ... # type: QDnsLookup.Type
        MX = ... # type: QDnsLookup.Type
        NS = ... # type: QDnsLookup.Type
        PTR = ... # type: QDnsLookup.Type
        SRV = ... # type: QDnsLookup.Type
        TXT = ... # type: QDnsLookup.Type

    class Error(int):
        NoError = ... # type: QDnsLookup.Error
        ResolverError = ... # type: QDnsLookup.Error
        OperationCancelledError = ... # type: QDnsLookup.Error
        InvalidRequestError = ... # type: QDnsLookup.Error
        InvalidReplyError = ... # type: QDnsLookup.Error
        ServerFailureError = ... # type: QDnsLookup.Error
        ServerRefusedError = ... # type: QDnsLookup.Error
        NotFoundError = ... # type: QDnsLookup.Error

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QDnsLookup.Type', name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QDnsLookup.Type', name: typing.Optional[str], nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    nameserverChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setNameserver(self, nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    def nameserver(self) -> 'QHostAddress': ...
    typeChanged: typing.ClassVar[QtCore.pyqtSignal]
    nameChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def lookup(self) -> None: ...
    def abort(self) -> None: ...
    def textRecords(self) -> typing.List[QDnsTextRecord]: ...
    def serviceRecords(self) -> typing.List[QDnsServiceRecord]: ...
    def pointerRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def nameServerRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def mailExchangeRecords(self) -> typing.List[QDnsMailExchangeRecord]: ...
    def hostAddressRecords(self) -> typing.List[QDnsHostAddressRecord]: ...
    def canonicalNameRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def setType(self, a0: 'QDnsLookup.Type') -> None: ...
    def type(self) -> 'QDnsLookup.Type': ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def isFinished(self) -> bool: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QDnsLookup.Error': ...


class QHostAddress(PyQt5.sipsimplewrapper):

    class ConversionModeFlag(int):
        ConvertV4MappedToIPv4 = ... # type: QHostAddress.ConversionModeFlag
        ConvertV4CompatToIPv4 = ... # type: QHostAddress.ConversionModeFlag
        ConvertUnspecifiedAddress = ... # type: QHostAddress.ConversionModeFlag
        ConvertLocalHost = ... # type: QHostAddress.ConversionModeFlag
        TolerantConversion = ... # type: QHostAddress.ConversionModeFlag
        StrictConversion = ... # type: QHostAddress.ConversionModeFlag

    class SpecialAddress(int):
        Null = ... # type: QHostAddress.SpecialAddress
        Broadcast = ... # type: QHostAddress.SpecialAddress
        LocalHost = ... # type: QHostAddress.SpecialAddress
        LocalHostIPv6 = ... # type: QHostAddress.SpecialAddress
        AnyIPv4 = ... # type: QHostAddress.SpecialAddress
        AnyIPv6 = ... # type: QHostAddress.SpecialAddress
        Any = ... # type: QHostAddress.SpecialAddress

    class ConversionMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __xor__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __ior__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __or__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __iand__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __and__(self, f: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag']) -> 'QHostAddress.ConversionMode': ...
        def __invert__(self) -> 'QHostAddress.ConversionMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, address: 'QHostAddress.SpecialAddress') -> None: ...
    @typing.overload
    def __init__(self, ip4Addr: int) -> None: ...
    @typing.overload
    def __init__(self, address: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, ip6Addr: typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]) -> None: ...
    @typing.overload
    def __init__(self, copy: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...

    def isBroadcast(self) -> bool: ...
    def isUniqueLocalUnicast(self) -> bool: ...
    def isSiteLocal(self) -> bool: ...
    def isLinkLocal(self) -> bool: ...
    def isGlobal(self) -> bool: ...
    def isEqual(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], mode: typing.Union['QHostAddress.ConversionMode', 'QHostAddress.ConversionModeFlag'] = ...) -> bool: ...
    def isMulticast(self) -> bool: ...
    def swap(self, other: 'QHostAddress') -> None: ...
    @staticmethod
    def parseSubnet(subnet: typing.Optional[str]) -> typing.Tuple['QHostAddress', int]: ...
    def isLoopback(self) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], netmask: int) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: typing.Tuple[typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], int]) -> bool: ...
    def __hash__(self) -> int: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setScopeId(self, id: typing.Optional[str]) -> None: ...
    def scopeId(self) -> str: ...
    def toString(self) -> str: ...
    def toIPv6Address(self) -> typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]: ...
    def toIPv4Address(self) -> int: ...
    def protocol(self) -> QAbstractSocket.NetworkLayerProtocol: ...
    @typing.overload
    def setAddress(self, address: 'QHostAddress.SpecialAddress') -> None: ...
    @typing.overload
    def setAddress(self, ip4Addr: int) -> None: ...
    @typing.overload
    def setAddress(self, address: typing.Optional[str]) -> bool: ...
    @typing.overload
    def setAddress(self, ip6Addr: typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]) -> None: ...


class QHostInfo(PyQt5.sipsimplewrapper):

    class HostInfoError(int):
        NoError = ... # type: QHostInfo.HostInfoError
        HostNotFound = ... # type: QHostInfo.HostInfoError
        UnknownError = ... # type: QHostInfo.HostInfoError

    @typing.overload
    def __init__(self, id: int = ...) -> None: ...
    @typing.overload
    def __init__(self, d: 'QHostInfo') -> None: ...

    def swap(self, other: 'QHostInfo') -> None: ...
    @staticmethod
    def localDomainName() -> str: ...
    @staticmethod
    def localHostName() -> str: ...
    @staticmethod
    def fromName(name: typing.Optional[str]) -> 'QHostInfo': ...
    @staticmethod
    def abortHostLookup(lookupId: int) -> None: ...
    @staticmethod
    def lookupHost(name: typing.Optional[str], slot: PYQT_SLOT) -> int: ...
    def lookupId(self) -> int: ...
    def setLookupId(self, id: int) -> None: ...
    def setErrorString(self, errorString: typing.Optional[str]) -> None: ...
    def errorString(self) -> str: ...
    def setError(self, error: 'QHostInfo.HostInfoError') -> None: ...
    def error(self) -> 'QHostInfo.HostInfoError': ...
    def setAddresses(self, addresses: typing.Iterable[typing.Union[QHostAddress, QHostAddress.SpecialAddress]]) -> None: ...
    def addresses(self) -> typing.List[QHostAddress]: ...
    def setHostName(self, name: typing.Optional[str]) -> None: ...
    def hostName(self) -> str: ...


class QHstsPolicy(PyQt5.sipsimplewrapper):

    class PolicyFlag(int):
        IncludeSubDomains = ... # type: QHstsPolicy.PolicyFlag

    class PolicyFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __xor__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __ior__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __or__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __iand__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __and__(self, f: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag']) -> 'QHstsPolicy.PolicyFlags': ...
        def __invert__(self) -> 'QHstsPolicy.PolicyFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, expiry: typing.Union[QtCore.QDateTime, datetime.datetime], flags: typing.Union['QHstsPolicy.PolicyFlags', 'QHstsPolicy.PolicyFlag'], host: typing.Optional[str], mode: QtCore.QUrl.ParsingMode = ...) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QHstsPolicy') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def isExpired(self) -> bool: ...
    def includesSubDomains(self) -> bool: ...
    def setIncludesSubDomains(self, include: bool) -> None: ...
    def expiry(self) -> QtCore.QDateTime: ...
    def setExpiry(self, expiry: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def host(self, options: typing.Union[QtCore.QUrl.ComponentFormattingOptions, QtCore.QUrl.ComponentFormattingOption] = ...) -> str: ...
    def setHost(self, host: typing.Optional[str], mode: QtCore.QUrl.ParsingMode = ...) -> None: ...
    def swap(self, other: 'QHstsPolicy') -> None: ...


class QHttp2Configuration(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttp2Configuration') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QHttp2Configuration') -> None: ...
    def maxFrameSize(self) -> int: ...
    def setMaxFrameSize(self, size: int) -> bool: ...
    def streamReceiveWindowSize(self) -> int: ...
    def setStreamReceiveWindowSize(self, size: int) -> bool: ...
    def sessionReceiveWindowSize(self) -> int: ...
    def setSessionReceiveWindowSize(self, size: int) -> bool: ...
    def huffmanCompressionEnabled(self) -> bool: ...
    def setHuffmanCompressionEnabled(self, enable: bool) -> None: ...
    def serverPushEnabled(self) -> bool: ...
    def setServerPushEnabled(self, enable: bool) -> None: ...


class QHttpPart(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttpPart') -> None: ...

    def swap(self, other: 'QHttpPart') -> None: ...
    def setBodyDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def setBody(self, body: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray], headerValue: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QHttpMultiPart(QtCore.QObject):

    class ContentType(int):
        MixedType = ... # type: QHttpMultiPart.ContentType
        RelatedType = ... # type: QHttpMultiPart.ContentType
        FormDataType = ... # type: QHttpMultiPart.ContentType
        AlternativeType = ... # type: QHttpMultiPart.ContentType

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, contentType: 'QHttpMultiPart.ContentType', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setBoundary(self, boundary: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def boundary(self) -> QtCore.QByteArray: ...
    def setContentType(self, contentType: 'QHttpMultiPart.ContentType') -> None: ...
    def append(self, httpPart: QHttpPart) -> None: ...


class QLocalServer(QtCore.QObject):

    class SocketOption(int):
        UserAccessOption = ... # type: QLocalServer.SocketOption
        GroupAccessOption = ... # type: QLocalServer.SocketOption
        OtherAccessOption = ... # type: QLocalServer.SocketOption
        WorldAccessOption = ... # type: QLocalServer.SocketOption

    class SocketOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __xor__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __ior__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __or__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __iand__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __and__(self, f: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> 'QLocalServer.SocketOptions': ...
        def __invert__(self) -> 'QLocalServer.SocketOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def socketDescriptor(self) -> PyQt5.sip.voidptr: ...
    def socketOptions(self) -> 'QLocalServer.SocketOptions': ...
    def setSocketOptions(self, options: typing.Union['QLocalServer.SocketOptions', 'QLocalServer.SocketOption']) -> None: ...
    def incomingConnection(self, socketDescriptor: PyQt5.sip.voidptr) -> None: ...
    newConnection: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def removeServer(name: typing.Optional[str]) -> bool: ...
    def waitForNewConnection(self, msecs: int = ...) -> typing.Tuple[bool, typing.Optional[bool]]: ...
    def setMaxPendingConnections(self, numConnections: int) -> None: ...
    def serverError(self) -> QAbstractSocket.SocketError: ...
    def fullServerName(self) -> str: ...
    def serverName(self) -> str: ...
    def nextPendingConnection(self) -> typing.Optional['QLocalSocket']: ...
    def maxPendingConnections(self) -> int: ...
    @typing.overload
    def listen(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def listen(self, socketDescriptor: PyQt5.sip.voidptr) -> bool: ...
    def isListening(self) -> bool: ...
    def hasPendingConnections(self) -> bool: ...
    def errorString(self) -> str: ...
    def close(self) -> None: ...


class QLocalSocket(QtCore.QIODevice):

    class LocalSocketState(int):
        UnconnectedState = ... # type: QLocalSocket.LocalSocketState
        ConnectingState = ... # type: QLocalSocket.LocalSocketState
        ConnectedState = ... # type: QLocalSocket.LocalSocketState
        ClosingState = ... # type: QLocalSocket.LocalSocketState

    class LocalSocketError(int):
        ConnectionRefusedError = ... # type: QLocalSocket.LocalSocketError
        PeerClosedError = ... # type: QLocalSocket.LocalSocketError
        ServerNotFoundError = ... # type: QLocalSocket.LocalSocketError
        SocketAccessError = ... # type: QLocalSocket.LocalSocketError
        SocketResourceError = ... # type: QLocalSocket.LocalSocketError
        SocketTimeoutError = ... # type: QLocalSocket.LocalSocketError
        DatagramTooLargeError = ... # type: QLocalSocket.LocalSocketError
        ConnectionError = ... # type: QLocalSocket.LocalSocketError
        UnsupportedSocketOperationError = ... # type: QLocalSocket.LocalSocketError
        OperationError = ... # type: QLocalSocket.LocalSocketError
        UnknownSocketError = ... # type: QLocalSocket.LocalSocketError

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def writeData(self, a0: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def state(self) -> 'QLocalSocket.LocalSocketState': ...
    def socketDescriptor(self) -> PyQt5.sip.voidptr: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt5.sip.voidptr, state: 'QLocalSocket.LocalSocketState' = ..., mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> bool: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def isValid(self) -> bool: ...
    def flush(self) -> bool: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def close(self) -> None: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isSequential(self) -> bool: ...
    def abort(self) -> None: ...
    def fullServerName(self) -> str: ...
    def setServerName(self, name: typing.Optional[str]) -> None: ...
    def serverName(self) -> str: ...
    def open(self, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> bool: ...
    def disconnectFromServer(self) -> None: ...
    @typing.overload
    def connectToServer(self, name: typing.Optional[str], mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...
    @typing.overload
    def connectToServer(self, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> None: ...


class QNetworkAccessManager(QtCore.QObject):

    class NetworkAccessibility(int):
        UnknownAccessibility = ... # type: QNetworkAccessManager.NetworkAccessibility
        NotAccessible = ... # type: QNetworkAccessManager.NetworkAccessibility
        Accessible = ... # type: QNetworkAccessManager.NetworkAccessibility

    class Operation(int):
        HeadOperation = ... # type: QNetworkAccessManager.Operation
        GetOperation = ... # type: QNetworkAccessManager.Operation
        PutOperation = ... # type: QNetworkAccessManager.Operation
        PostOperation = ... # type: QNetworkAccessManager.Operation
        DeleteOperation = ... # type: QNetworkAccessManager.Operation
        CustomOperation = ... # type: QNetworkAccessManager.Operation

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setTransferTimeout(self, timeout: int = ...) -> None: ...
    def transferTimeout(self) -> int: ...
    def setAutoDeleteReplies(self, autoDelete: bool) -> None: ...
    def autoDeleteReplies(self) -> bool: ...
    def isStrictTransportSecurityStoreEnabled(self) -> bool: ...
    def enableStrictTransportSecurityStore(self, enabled: bool, storeDir: typing.Optional[str] = ...) -> None: ...
    def redirectPolicy(self) -> 'QNetworkRequest.RedirectPolicy': ...
    def setRedirectPolicy(self, policy: 'QNetworkRequest.RedirectPolicy') -> None: ...
    def strictTransportSecurityHosts(self) -> typing.List[QHstsPolicy]: ...
    def addStrictTransportSecurityHosts(self, knownHosts: typing.Iterable[QHstsPolicy]) -> None: ...
    def isStrictTransportSecurityEnabled(self) -> bool: ...
    def setStrictTransportSecurityEnabled(self, enabled: bool) -> None: ...
    def clearConnectionCache(self) -> None: ...
    def supportedSchemesImplementation(self) -> typing.List[str]: ...
    def connectToHost(self, hostName: typing.Optional[str], port: int = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int = ..., sslConfiguration: 'QSslConfiguration' = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, sslConfiguration: 'QSslConfiguration', peerName: typing.Optional[str]) -> None: ...
    def supportedSchemes(self) -> typing.List[str]: ...
    def clearAccessCache(self) -> None: ...
    def networkAccessible(self) -> 'QNetworkAccessManager.NetworkAccessibility': ...
    def setNetworkAccessible(self, accessible: 'QNetworkAccessManager.NetworkAccessibility') -> None: ...
    def activeConfiguration(self) -> 'QNetworkConfiguration': ...
    def configuration(self) -> 'QNetworkConfiguration': ...
    def setConfiguration(self, config: 'QNetworkConfiguration') -> None: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray], data: typing.Optional[QtCore.QIODevice] = ...) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray], data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray], multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    def deleteResource(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    def setCache(self, cache: typing.Optional[QAbstractNetworkCache]) -> None: ...
    def cache(self) -> typing.Optional[QAbstractNetworkCache]: ...
    def setProxyFactory(self, factory: typing.Optional['QNetworkProxyFactory']) -> None: ...
    def proxyFactory(self) -> typing.Optional['QNetworkProxyFactory']: ...
    def createRequest(self, op: 'QNetworkAccessManager.Operation', request: 'QNetworkRequest', device: typing.Optional[QtCore.QIODevice] = ...) -> 'QNetworkReply': ...
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    networkAccessibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    authenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    proxyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def put(self, request: 'QNetworkRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def put(self, request: 'QNetworkRequest', data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def put(self, request: 'QNetworkRequest', multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    def get(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    def head(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    def setCookieJar(self, cookieJar: typing.Optional['QNetworkCookieJar']) -> None: ...
    def cookieJar(self) -> typing.Optional['QNetworkCookieJar']: ...
    def setProxy(self, proxy: 'QNetworkProxy') -> None: ...
    def proxy(self) -> 'QNetworkProxy': ...


class QNetworkConfigurationManager(QtCore.QObject):

    class Capability(int):
        CanStartAndStopInterfaces = ... # type: QNetworkConfigurationManager.Capability
        DirectConnectionRouting = ... # type: QNetworkConfigurationManager.Capability
        SystemSessionSupport = ... # type: QNetworkConfigurationManager.Capability
        ApplicationLevelRoaming = ... # type: QNetworkConfigurationManager.Capability
        ForcedRoaming = ... # type: QNetworkConfigurationManager.Capability
        DataStatistics = ... # type: QNetworkConfigurationManager.Capability
        NetworkSessionRequired = ... # type: QNetworkConfigurationManager.Capability

    class Capabilities(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __xor__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __ior__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __or__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __iand__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __and__(self, f: typing.Union['QNetworkConfigurationManager.Capabilities', 'QNetworkConfigurationManager.Capability']) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __invert__(self) -> 'QNetworkConfigurationManager.Capabilities': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    updateCompleted: typing.ClassVar[QtCore.pyqtSignal]
    onlineStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    configurationChanged: typing.ClassVar[QtCore.pyqtSignal]
    configurationRemoved: typing.ClassVar[QtCore.pyqtSignal]
    configurationAdded: typing.ClassVar[QtCore.pyqtSignal]
    def isOnline(self) -> bool: ...
    def updateConfigurations(self) -> None: ...
    def configurationFromIdentifier(self, identifier: typing.Optional[str]) -> 'QNetworkConfiguration': ...
    def allConfigurations(self, flags: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag'] = ...) -> typing.List['QNetworkConfiguration']: ...
    def defaultConfiguration(self) -> 'QNetworkConfiguration': ...
    def capabilities(self) -> 'QNetworkConfigurationManager.Capabilities': ...


class QNetworkConfiguration(PyQt5.sipsimplewrapper):

    class BearerType(int):
        BearerUnknown = ... # type: QNetworkConfiguration.BearerType
        BearerEthernet = ... # type: QNetworkConfiguration.BearerType
        BearerWLAN = ... # type: QNetworkConfiguration.BearerType
        Bearer2G = ... # type: QNetworkConfiguration.BearerType
        BearerCDMA2000 = ... # type: QNetworkConfiguration.BearerType
        BearerWCDMA = ... # type: QNetworkConfiguration.BearerType
        BearerHSPA = ... # type: QNetworkConfiguration.BearerType
        BearerBluetooth = ... # type: QNetworkConfiguration.BearerType
        BearerWiMAX = ... # type: QNetworkConfiguration.BearerType
        BearerEVDO = ... # type: QNetworkConfiguration.BearerType
        BearerLTE = ... # type: QNetworkConfiguration.BearerType
        Bearer3G = ... # type: QNetworkConfiguration.BearerType
        Bearer4G = ... # type: QNetworkConfiguration.BearerType

    class StateFlag(int):
        Undefined = ... # type: QNetworkConfiguration.StateFlag
        Defined = ... # type: QNetworkConfiguration.StateFlag
        Discovered = ... # type: QNetworkConfiguration.StateFlag
        Active = ... # type: QNetworkConfiguration.StateFlag

    class Purpose(int):
        UnknownPurpose = ... # type: QNetworkConfiguration.Purpose
        PublicPurpose = ... # type: QNetworkConfiguration.Purpose
        PrivatePurpose = ... # type: QNetworkConfiguration.Purpose
        ServiceSpecificPurpose = ... # type: QNetworkConfiguration.Purpose

    class Type(int):
        InternetAccessPoint = ... # type: QNetworkConfiguration.Type
        ServiceNetwork = ... # type: QNetworkConfiguration.Type
        UserChoice = ... # type: QNetworkConfiguration.Type
        Invalid = ... # type: QNetworkConfiguration.Type

    class StateFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __xor__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __ior__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __or__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __iand__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __and__(self, f: typing.Union['QNetworkConfiguration.StateFlags', 'QNetworkConfiguration.StateFlag']) -> 'QNetworkConfiguration.StateFlags': ...
        def __invert__(self) -> 'QNetworkConfiguration.StateFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkConfiguration') -> None: ...

    def setConnectTimeout(self, timeout: int) -> bool: ...
    def connectTimeout(self) -> int: ...
    def swap(self, other: 'QNetworkConfiguration') -> None: ...
    def isValid(self) -> bool: ...
    def name(self) -> str: ...
    def children(self) -> typing.List['QNetworkConfiguration']: ...
    def isRoamingAvailable(self) -> bool: ...
    def identifier(self) -> str: ...
    def bearerTypeFamily(self) -> 'QNetworkConfiguration.BearerType': ...
    def bearerTypeName(self) -> str: ...
    def bearerType(self) -> 'QNetworkConfiguration.BearerType': ...
    def purpose(self) -> 'QNetworkConfiguration.Purpose': ...
    def type(self) -> 'QNetworkConfiguration.Type': ...
    def state(self) -> 'QNetworkConfiguration.StateFlags': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QNetworkCookie(PyQt5.sipsimplewrapper):

    class RawForm(int):
        NameAndValueOnly = ... # type: QNetworkCookie.RawForm
        Full = ... # type: QNetworkCookie.RawForm

    @typing.overload
    def __init__(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray] = ..., value: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkCookie') -> None: ...

    def normalize(self, url: QtCore.QUrl) -> None: ...
    def hasSameIdentifier(self, other: 'QNetworkCookie') -> bool: ...
    def swap(self, other: 'QNetworkCookie') -> None: ...
    def setHttpOnly(self, enable: bool) -> None: ...
    def isHttpOnly(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def parseCookies(cookieString: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.List['QNetworkCookie']: ...
    def toRawForm(self, form: 'QNetworkCookie.RawForm' = ...) -> QtCore.QByteArray: ...
    def setValue(self, value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def value(self) -> QtCore.QByteArray: ...
    def setName(self, cookieName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def name(self) -> QtCore.QByteArray: ...
    def setPath(self, path: typing.Optional[str]) -> None: ...
    def path(self) -> str: ...
    def setDomain(self, domain: typing.Optional[str]) -> None: ...
    def domain(self) -> str: ...
    def setExpirationDate(self, date: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def expirationDate(self) -> QtCore.QDateTime: ...
    def isSessionCookie(self) -> bool: ...
    def setSecure(self, enable: bool) -> None: ...
    def isSecure(self) -> bool: ...


class QNetworkCookieJar(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def validateCookie(self, cookie: QNetworkCookie, url: QtCore.QUrl) -> bool: ...
    def allCookies(self) -> typing.List[QNetworkCookie]: ...
    def setAllCookies(self, cookieList: typing.Iterable[QNetworkCookie]) -> None: ...
    def deleteCookie(self, cookie: QNetworkCookie) -> bool: ...
    def updateCookie(self, cookie: QNetworkCookie) -> bool: ...
    def insertCookie(self, cookie: QNetworkCookie) -> bool: ...
    def setCookiesFromUrl(self, cookieList: typing.Iterable[QNetworkCookie], url: QtCore.QUrl) -> bool: ...
    def cookiesForUrl(self, url: QtCore.QUrl) -> typing.List[QNetworkCookie]: ...


class QNetworkDatagram(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray], destinationAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress] = ..., port: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkDatagram') -> None: ...

    def makeReply(self, payload: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> 'QNetworkDatagram': ...
    def setData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def data(self) -> QtCore.QByteArray: ...
    def setHopLimit(self, count: int) -> None: ...
    def hopLimit(self) -> int: ...
    def setDestination(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int) -> None: ...
    def setSender(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int = ...) -> None: ...
    def destinationPort(self) -> int: ...
    def senderPort(self) -> int: ...
    def destinationAddress(self) -> QHostAddress: ...
    def senderAddress(self) -> QHostAddress: ...
    def setInterfaceIndex(self, index: int) -> None: ...
    def interfaceIndex(self) -> int: ...
    def isNull(self) -> bool: ...
    def isValid(self) -> bool: ...
    def clear(self) -> None: ...
    def swap(self, other: 'QNetworkDatagram') -> None: ...


class QNetworkDiskCache(QAbstractNetworkCache):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def expire(self) -> int: ...
    def clear(self) -> None: ...
    def fileMetaData(self, fileName: typing.Optional[str]) -> QNetworkCacheMetaData: ...
    def insert(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def prepare(self, metaData: QNetworkCacheMetaData) -> typing.Optional[QtCore.QIODevice]: ...
    def remove(self, url: QtCore.QUrl) -> bool: ...
    def data(self, url: QtCore.QUrl) -> typing.Optional[QtCore.QIODevice]: ...
    def updateMetaData(self, metaData: QNetworkCacheMetaData) -> None: ...
    def metaData(self, url: QtCore.QUrl) -> QNetworkCacheMetaData: ...
    def cacheSize(self) -> int: ...
    def setMaximumCacheSize(self, size: int) -> None: ...
    def maximumCacheSize(self) -> int: ...
    def setCacheDirectory(self, cacheDir: typing.Optional[str]) -> None: ...
    def cacheDirectory(self) -> str: ...


class QNetworkAddressEntry(PyQt5.sipsimplewrapper):

    class DnsEligibilityStatus(int):
        DnsEligibilityUnknown = ... # type: QNetworkAddressEntry.DnsEligibilityStatus
        DnsIneligible = ... # type: QNetworkAddressEntry.DnsEligibilityStatus
        DnsEligible = ... # type: QNetworkAddressEntry.DnsEligibilityStatus

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkAddressEntry') -> None: ...

    def isTemporary(self) -> bool: ...
    def isPermanent(self) -> bool: ...
    def clearAddressLifetime(self) -> None: ...
    def setAddressLifetime(self, preferred: QtCore.QDeadlineTimer, validity: QtCore.QDeadlineTimer) -> None: ...
    def validityLifetime(self) -> QtCore.QDeadlineTimer: ...
    def preferredLifetime(self) -> QtCore.QDeadlineTimer: ...
    def isLifetimeKnown(self) -> bool: ...
    def setDnsEligibility(self, status: 'QNetworkAddressEntry.DnsEligibilityStatus') -> None: ...
    def dnsEligibility(self) -> 'QNetworkAddressEntry.DnsEligibilityStatus': ...
    def swap(self, other: 'QNetworkAddressEntry') -> None: ...
    def setPrefixLength(self, length: int) -> None: ...
    def prefixLength(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setBroadcast(self, newBroadcast: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def broadcast(self) -> QHostAddress: ...
    def setNetmask(self, newNetmask: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def netmask(self) -> QHostAddress: ...
    def setIp(self, newIp: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def ip(self) -> QHostAddress: ...


class QNetworkInterface(PyQt5.sipsimplewrapper):

    class InterfaceType(int):
        Unknown = ... # type: QNetworkInterface.InterfaceType
        Loopback = ... # type: QNetworkInterface.InterfaceType
        Virtual = ... # type: QNetworkInterface.InterfaceType
        Ethernet = ... # type: QNetworkInterface.InterfaceType
        Slip = ... # type: QNetworkInterface.InterfaceType
        CanBus = ... # type: QNetworkInterface.InterfaceType
        Ppp = ... # type: QNetworkInterface.InterfaceType
        Fddi = ... # type: QNetworkInterface.InterfaceType
        Wifi = ... # type: QNetworkInterface.InterfaceType
        Ieee80211 = ... # type: QNetworkInterface.InterfaceType
        Phonet = ... # type: QNetworkInterface.InterfaceType
        Ieee802154 = ... # type: QNetworkInterface.InterfaceType
        SixLoWPAN = ... # type: QNetworkInterface.InterfaceType
        Ieee80216 = ... # type: QNetworkInterface.InterfaceType
        Ieee1394 = ... # type: QNetworkInterface.InterfaceType

    class InterfaceFlag(int):
        IsUp = ... # type: QNetworkInterface.InterfaceFlag
        IsRunning = ... # type: QNetworkInterface.InterfaceFlag
        CanBroadcast = ... # type: QNetworkInterface.InterfaceFlag
        IsLoopBack = ... # type: QNetworkInterface.InterfaceFlag
        IsPointToPoint = ... # type: QNetworkInterface.InterfaceFlag
        CanMulticast = ... # type: QNetworkInterface.InterfaceFlag

    class InterfaceFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __xor__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __ior__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __or__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __iand__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __and__(self, f: typing.Union['QNetworkInterface.InterfaceFlags', 'QNetworkInterface.InterfaceFlag']) -> 'QNetworkInterface.InterfaceFlags': ...
        def __invert__(self) -> 'QNetworkInterface.InterfaceFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkInterface') -> None: ...

    def maximumTransmissionUnit(self) -> int: ...
    def type(self) -> 'QNetworkInterface.InterfaceType': ...
    @staticmethod
    def interfaceNameFromIndex(index: int) -> str: ...
    @staticmethod
    def interfaceIndexFromName(name: typing.Optional[str]) -> int: ...
    def swap(self, other: 'QNetworkInterface') -> None: ...
    def humanReadableName(self) -> str: ...
    def index(self) -> int: ...
    @staticmethod
    def allAddresses() -> typing.List[QHostAddress]: ...
    @staticmethod
    def allInterfaces() -> typing.List['QNetworkInterface']: ...
    @staticmethod
    def interfaceFromIndex(index: int) -> 'QNetworkInterface': ...
    @staticmethod
    def interfaceFromName(name: typing.Optional[str]) -> 'QNetworkInterface': ...
    def addressEntries(self) -> typing.List[QNetworkAddressEntry]: ...
    def hardwareAddress(self) -> str: ...
    def flags(self) -> 'QNetworkInterface.InterfaceFlags': ...
    def name(self) -> str: ...
    def isValid(self) -> bool: ...


class QNetworkProxy(PyQt5.sipsimplewrapper):

    class Capability(int):
        TunnelingCapability = ... # type: QNetworkProxy.Capability
        ListeningCapability = ... # type: QNetworkProxy.Capability
        UdpTunnelingCapability = ... # type: QNetworkProxy.Capability
        CachingCapability = ... # type: QNetworkProxy.Capability
        HostNameLookupCapability = ... # type: QNetworkProxy.Capability
        SctpTunnelingCapability = ... # type: QNetworkProxy.Capability
        SctpListeningCapability = ... # type: QNetworkProxy.Capability

    class ProxyType(int):
        DefaultProxy = ... # type: QNetworkProxy.ProxyType
        Socks5Proxy = ... # type: QNetworkProxy.ProxyType
        NoProxy = ... # type: QNetworkProxy.ProxyType
        HttpProxy = ... # type: QNetworkProxy.ProxyType
        HttpCachingProxy = ... # type: QNetworkProxy.ProxyType
        FtpCachingProxy = ... # type: QNetworkProxy.ProxyType

    class Capabilities(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __xor__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __ior__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __or__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __iand__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __and__(self, f: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> 'QNetworkProxy.Capabilities': ...
        def __invert__(self) -> 'QNetworkProxy.Capabilities': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: 'QNetworkProxy.ProxyType', hostName: typing.Optional[str] = ..., port: int = ..., user: typing.Optional[str] = ..., password: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkProxy') -> None: ...

    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray], value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def rawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def swap(self, other: 'QNetworkProxy') -> None: ...
    def capabilities(self) -> 'QNetworkProxy.Capabilities': ...
    def setCapabilities(self, capab: typing.Union['QNetworkProxy.Capabilities', 'QNetworkProxy.Capability']) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isTransparentProxy(self) -> bool: ...
    def isCachingProxy(self) -> bool: ...
    @staticmethod
    def applicationProxy() -> 'QNetworkProxy': ...
    @staticmethod
    def setApplicationProxy(proxy: 'QNetworkProxy') -> None: ...
    def port(self) -> int: ...
    def setPort(self, port: int) -> None: ...
    def hostName(self) -> str: ...
    def setHostName(self, hostName: typing.Optional[str]) -> None: ...
    def password(self) -> str: ...
    def setPassword(self, password: typing.Optional[str]) -> None: ...
    def user(self) -> str: ...
    def setUser(self, userName: typing.Optional[str]) -> None: ...
    def type(self) -> 'QNetworkProxy.ProxyType': ...
    def setType(self, type: 'QNetworkProxy.ProxyType') -> None: ...


class QNetworkProxyQuery(PyQt5.sipsimplewrapper):

    class QueryType(int):
        TcpSocket = ... # type: QNetworkProxyQuery.QueryType
        UdpSocket = ... # type: QNetworkProxyQuery.QueryType
        TcpServer = ... # type: QNetworkProxyQuery.QueryType
        UrlRequest = ... # type: QNetworkProxyQuery.QueryType
        SctpSocket = ... # type: QNetworkProxyQuery.QueryType
        SctpServer = ... # type: QNetworkProxyQuery.QueryType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, requestUrl: QtCore.QUrl, type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, hostname: typing.Optional[str], port: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, bindPort: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, networkConfiguration: QNetworkConfiguration, requestUrl: QtCore.QUrl, queryType: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, networkConfiguration: QNetworkConfiguration, hostname: typing.Optional[str], port: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, networkConfiguration: QNetworkConfiguration, bindPort: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkProxyQuery') -> None: ...

    def swap(self, other: 'QNetworkProxyQuery') -> None: ...
    def setNetworkConfiguration(self, networkConfiguration: QNetworkConfiguration) -> None: ...
    def networkConfiguration(self) -> QNetworkConfiguration: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def setProtocolTag(self, protocolTag: typing.Optional[str]) -> None: ...
    def protocolTag(self) -> str: ...
    def setLocalPort(self, port: int) -> None: ...
    def localPort(self) -> int: ...
    def setPeerHostName(self, hostname: typing.Optional[str]) -> None: ...
    def peerHostName(self) -> str: ...
    def setPeerPort(self, port: int) -> None: ...
    def peerPort(self) -> int: ...
    def setQueryType(self, type: 'QNetworkProxyQuery.QueryType') -> None: ...
    def queryType(self) -> 'QNetworkProxyQuery.QueryType': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QNetworkProxyFactory(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QNetworkProxyFactory') -> None: ...

    @staticmethod
    def usesSystemConfiguration() -> bool: ...
    @staticmethod
    def setUseSystemConfiguration(enable: bool) -> None: ...
    @staticmethod
    def systemProxyForQuery(query: QNetworkProxyQuery = ...) -> typing.List[QNetworkProxy]: ...
    @staticmethod
    def proxyForQuery(query: QNetworkProxyQuery) -> typing.List[QNetworkProxy]: ...
    @staticmethod
    def setApplicationProxyFactory(factory: typing.Optional['QNetworkProxyFactory']) -> None: ...
    def queryProxy(self, query: QNetworkProxyQuery = ...) -> typing.List[QNetworkProxy]: ...


class QNetworkReply(QtCore.QIODevice):

    class NetworkError(int):
        NoError = ... # type: QNetworkReply.NetworkError
        ConnectionRefusedError = ... # type: QNetworkReply.NetworkError
        RemoteHostClosedError = ... # type: QNetworkReply.NetworkError
        HostNotFoundError = ... # type: QNetworkReply.NetworkError
        TimeoutError = ... # type: QNetworkReply.NetworkError
        OperationCanceledError = ... # type: QNetworkReply.NetworkError
        SslHandshakeFailedError = ... # type: QNetworkReply.NetworkError
        UnknownNetworkError = ... # type: QNetworkReply.NetworkError
        ProxyConnectionRefusedError = ... # type: QNetworkReply.NetworkError
        ProxyConnectionClosedError = ... # type: QNetworkReply.NetworkError
        ProxyNotFoundError = ... # type: QNetworkReply.NetworkError
        ProxyTimeoutError = ... # type: QNetworkReply.NetworkError
        ProxyAuthenticationRequiredError = ... # type: QNetworkReply.NetworkError
        UnknownProxyError = ... # type: QNetworkReply.NetworkError
        ContentAccessDenied = ... # type: QNetworkReply.NetworkError
        ContentOperationNotPermittedError = ... # type: QNetworkReply.NetworkError
        ContentNotFoundError = ... # type: QNetworkReply.NetworkError
        AuthenticationRequiredError = ... # type: QNetworkReply.NetworkError
        UnknownContentError = ... # type: QNetworkReply.NetworkError
        ProtocolUnknownError = ... # type: QNetworkReply.NetworkError
        ProtocolInvalidOperationError = ... # type: QNetworkReply.NetworkError
        ProtocolFailure = ... # type: QNetworkReply.NetworkError
        ContentReSendError = ... # type: QNetworkReply.NetworkError
        TemporaryNetworkFailureError = ... # type: QNetworkReply.NetworkError
        NetworkSessionFailedError = ... # type: QNetworkReply.NetworkError
        BackgroundRequestNotAllowedError = ... # type: QNetworkReply.NetworkError
        ContentConflictError = ... # type: QNetworkReply.NetworkError
        ContentGoneError = ... # type: QNetworkReply.NetworkError
        InternalServerError = ... # type: QNetworkReply.NetworkError
        OperationNotImplementedError = ... # type: QNetworkReply.NetworkError
        ServiceUnavailableError = ... # type: QNetworkReply.NetworkError
        UnknownServerError = ... # type: QNetworkReply.NetworkError
        TooManyRedirectsError = ... # type: QNetworkReply.NetworkError
        InsecureRedirectError = ... # type: QNetworkReply.NetworkError

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def ignoreSslErrorsImplementation(self, a0: typing.Iterable['QSslError']) -> None: ...
    def setSslConfigurationImplementation(self, a0: 'QSslConfiguration') -> None: ...
    def sslConfigurationImplementation(self, a0: 'QSslConfiguration') -> None: ...
    def rawHeaderPairs(self) -> typing.List[typing.Tuple[QtCore.QByteArray, QtCore.QByteArray]]: ...
    def isRunning(self) -> bool: ...
    def isFinished(self) -> bool: ...
    def setFinished(self, finished: bool) -> None: ...
    def setAttribute(self, code: 'QNetworkRequest.Attribute', value: typing.Any) -> None: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray], value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def setError(self, errorCode: 'QNetworkReply.NetworkError', errorString: typing.Optional[str]) -> None: ...
    def setRequest(self, request: 'QNetworkRequest') -> None: ...
    def setOperation(self, operation: QNetworkAccessManager.Operation) -> None: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    redirectAllowed: typing.ClassVar[QtCore.pyqtSignal]
    redirected: typing.ClassVar[QtCore.pyqtSignal]
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    downloadProgress: typing.ClassVar[QtCore.pyqtSignal]
    uploadProgress: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def ignoreSslErrors(self) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: typing.Iterable['QSslError']) -> None: ...
    def setSslConfiguration(self, configuration: 'QSslConfiguration') -> None: ...
    def sslConfiguration(self) -> 'QSslConfiguration': ...
    def attribute(self, code: 'QNetworkRequest.Attribute') -> typing.Any: ...
    def rawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def url(self) -> QtCore.QUrl: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def request(self) -> 'QNetworkRequest': ...
    def operation(self) -> QNetworkAccessManager.Operation: ...
    def manager(self) -> typing.Optional[QNetworkAccessManager]: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    def abort(self) -> None: ...


class QNetworkRequest(PyQt5.sipsimplewrapper):

    class TransferTimeoutConstant(int):
        DefaultTransferTimeoutConstant = ... # type: QNetworkRequest.TransferTimeoutConstant

    class RedirectPolicy(int):
        ManualRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        NoLessSafeRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        SameOriginRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        UserVerifiedRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy

    class Priority(int):
        HighPriority = ... # type: QNetworkRequest.Priority
        NormalPriority = ... # type: QNetworkRequest.Priority
        LowPriority = ... # type: QNetworkRequest.Priority

    class LoadControl(int):
        Automatic = ... # type: QNetworkRequest.LoadControl
        Manual = ... # type: QNetworkRequest.LoadControl

    class CacheLoadControl(int):
        AlwaysNetwork = ... # type: QNetworkRequest.CacheLoadControl
        PreferNetwork = ... # type: QNetworkRequest.CacheLoadControl
        PreferCache = ... # type: QNetworkRequest.CacheLoadControl
        AlwaysCache = ... # type: QNetworkRequest.CacheLoadControl

    class Attribute(int):
        HttpStatusCodeAttribute = ... # type: QNetworkRequest.Attribute
        HttpReasonPhraseAttribute = ... # type: QNetworkRequest.Attribute
        RedirectionTargetAttribute = ... # type: QNetworkRequest.Attribute
        ConnectionEncryptedAttribute = ... # type: QNetworkRequest.Attribute
        CacheLoadControlAttribute = ... # type: QNetworkRequest.Attribute
        CacheSaveControlAttribute = ... # type: QNetworkRequest.Attribute
        SourceIsFromCacheAttribute = ... # type: QNetworkRequest.Attribute
        DoNotBufferUploadDataAttribute = ... # type: QNetworkRequest.Attribute
        HttpPipeliningAllowedAttribute = ... # type: QNetworkRequest.Attribute
        HttpPipeliningWasUsedAttribute = ... # type: QNetworkRequest.Attribute
        CustomVerbAttribute = ... # type: QNetworkRequest.Attribute
        CookieLoadControlAttribute = ... # type: QNetworkRequest.Attribute
        AuthenticationReuseAttribute = ... # type: QNetworkRequest.Attribute
        CookieSaveControlAttribute = ... # type: QNetworkRequest.Attribute
        BackgroundRequestAttribute = ... # type: QNetworkRequest.Attribute
        SpdyAllowedAttribute = ... # type: QNetworkRequest.Attribute
        SpdyWasUsedAttribute = ... # type: QNetworkRequest.Attribute
        EmitAllUploadProgressSignalsAttribute = ... # type: QNetworkRequest.Attribute
        FollowRedirectsAttribute = ... # type: QNetworkRequest.Attribute
        HTTP2AllowedAttribute = ... # type: QNetworkRequest.Attribute
        Http2AllowedAttribute = ... # type: QNetworkRequest.Attribute
        HTTP2WasUsedAttribute = ... # type: QNetworkRequest.Attribute
        Http2WasUsedAttribute = ... # type: QNetworkRequest.Attribute
        OriginalContentLengthAttribute = ... # type: QNetworkRequest.Attribute
        RedirectPolicyAttribute = ... # type: QNetworkRequest.Attribute
        Http2DirectAttribute = ... # type: QNetworkRequest.Attribute
        AutoDeleteReplyOnFinishAttribute = ... # type: QNetworkRequest.Attribute
        User = ... # type: QNetworkRequest.Attribute
        UserMax = ... # type: QNetworkRequest.Attribute

    class KnownHeaders(int):
        ContentTypeHeader = ... # type: QNetworkRequest.KnownHeaders
        ContentLengthHeader = ... # type: QNetworkRequest.KnownHeaders
        LocationHeader = ... # type: QNetworkRequest.KnownHeaders
        LastModifiedHeader = ... # type: QNetworkRequest.KnownHeaders
        CookieHeader = ... # type: QNetworkRequest.KnownHeaders
        SetCookieHeader = ... # type: QNetworkRequest.KnownHeaders
        ContentDispositionHeader = ... # type: QNetworkRequest.KnownHeaders
        UserAgentHeader = ... # type: QNetworkRequest.KnownHeaders
        ServerHeader = ... # type: QNetworkRequest.KnownHeaders
        IfModifiedSinceHeader = ... # type: QNetworkRequest.KnownHeaders
        ETagHeader = ... # type: QNetworkRequest.KnownHeaders
        IfMatchHeader = ... # type: QNetworkRequest.KnownHeaders
        IfNoneMatchHeader = ... # type: QNetworkRequest.KnownHeaders

    @typing.overload
    def __init__(self, url: QtCore.QUrl = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkRequest') -> None: ...

    def setTransferTimeout(self, timeout: int = ...) -> None: ...
    def transferTimeout(self) -> int: ...
    def setHttp2Configuration(self, configuration: QHttp2Configuration) -> None: ...
    def http2Configuration(self) -> QHttp2Configuration: ...
    def setPeerVerifyName(self, peerName: typing.Optional[str]) -> None: ...
    def peerVerifyName(self) -> str: ...
    def setMaximumRedirectsAllowed(self, maximumRedirectsAllowed: int) -> None: ...
    def maximumRedirectsAllowed(self) -> int: ...
    def swap(self, other: 'QNetworkRequest') -> None: ...
    def setPriority(self, priority: 'QNetworkRequest.Priority') -> None: ...
    def priority(self) -> 'QNetworkRequest.Priority': ...
    def originatingObject(self) -> typing.Optional[QtCore.QObject]: ...
    def setOriginatingObject(self, object: typing.Optional[QtCore.QObject]) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setSslConfiguration(self, configuration: 'QSslConfiguration') -> None: ...
    def sslConfiguration(self) -> 'QSslConfiguration': ...
    def setAttribute(self, code: 'QNetworkRequest.Attribute', value: typing.Any) -> None: ...
    def attribute(self, code: 'QNetworkRequest.Attribute', defaultValue: typing.Any = ...) -> typing.Any: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray], value: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def rawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...


class QNetworkSession(QtCore.QObject):

    class UsagePolicy(int):
        NoPolicy = ... # type: QNetworkSession.UsagePolicy
        NoBackgroundTrafficPolicy = ... # type: QNetworkSession.UsagePolicy

    class SessionError(int):
        UnknownSessionError = ... # type: QNetworkSession.SessionError
        SessionAbortedError = ... # type: QNetworkSession.SessionError
        RoamingError = ... # type: QNetworkSession.SessionError
        OperationNotSupportedError = ... # type: QNetworkSession.SessionError
        InvalidConfigurationError = ... # type: QNetworkSession.SessionError

    class State(int):
        Invalid = ... # type: QNetworkSession.State
        NotAvailable = ... # type: QNetworkSession.State
        Connecting = ... # type: QNetworkSession.State
        Connected = ... # type: QNetworkSession.State
        Closing = ... # type: QNetworkSession.State
        Disconnected = ... # type: QNetworkSession.State
        Roaming = ... # type: QNetworkSession.State

    class UsagePolicies(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __xor__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __ior__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __or__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __iand__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __and__(self, f: typing.Union['QNetworkSession.UsagePolicies', 'QNetworkSession.UsagePolicy']) -> 'QNetworkSession.UsagePolicies': ...
        def __invert__(self) -> 'QNetworkSession.UsagePolicies': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, connConfig: QNetworkConfiguration, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    usagePoliciesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def usagePolicies(self) -> 'QNetworkSession.UsagePolicies': ...
    def disconnectNotify(self, signal: QtCore.QMetaMethod) -> None: ...
    def connectNotify(self, signal: QtCore.QMetaMethod) -> None: ...
    newConfigurationActivated: typing.ClassVar[QtCore.pyqtSignal]
    preferredConfigurationChanged: typing.ClassVar[QtCore.pyqtSignal]
    closed: typing.ClassVar[QtCore.pyqtSignal]
    opened: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def reject(self) -> None: ...
    def accept(self) -> None: ...
    def ignore(self) -> None: ...
    def migrate(self) -> None: ...
    def stop(self) -> None: ...
    def close(self) -> None: ...
    def open(self) -> None: ...
    def waitForOpened(self, msecs: int = ...) -> bool: ...
    def activeTime(self) -> int: ...
    def bytesReceived(self) -> int: ...
    def bytesWritten(self) -> int: ...
    def setSessionProperty(self, key: typing.Optional[str], value: typing.Any) -> None: ...
    def sessionProperty(self, key: typing.Optional[str]) -> typing.Any: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> 'QNetworkSession.State': ...
    def interface(self) -> QNetworkInterface: ...
    def configuration(self) -> QNetworkConfiguration: ...
    def isOpen(self) -> bool: ...


class QOcspResponse(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QOcspResponse') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def swap(self, other: 'QOcspResponse') -> None: ...
    def subject(self) -> 'QSslCertificate': ...
    def responder(self) -> 'QSslCertificate': ...
    def revocationReason(self) -> QOcspRevocationReason: ...
    def certificateStatus(self) -> QOcspCertificateStatus: ...


class QPasswordDigestor(PyQt5.sip.simplewrapper):

    def deriveKeyPbkdf2(self, algorithm: QtCore.QCryptographicHash.Algorithm, password: typing.Union[QtCore.QByteArray, bytes, bytearray], salt: typing.Union[QtCore.QByteArray, bytes, bytearray], iterations: int, dkLen: int) -> QtCore.QByteArray: ...
    def deriveKeyPbkdf1(self, algorithm: QtCore.QCryptographicHash.Algorithm, password: typing.Union[QtCore.QByteArray, bytes, bytearray], salt: typing.Union[QtCore.QByteArray, bytes, bytearray], iterations: int, dkLen: int) -> QtCore.QByteArray: ...


class QSsl(PyQt5.sip.simplewrapper):

    class SslOption(int):
        SslOptionDisableEmptyFragments = ... # type: QSsl.SslOption
        SslOptionDisableSessionTickets = ... # type: QSsl.SslOption
        SslOptionDisableCompression = ... # type: QSsl.SslOption
        SslOptionDisableServerNameIndication = ... # type: QSsl.SslOption
        SslOptionDisableLegacyRenegotiation = ... # type: QSsl.SslOption
        SslOptionDisableSessionSharing = ... # type: QSsl.SslOption
        SslOptionDisableSessionPersistence = ... # type: QSsl.SslOption
        SslOptionDisableServerCipherPreference = ... # type: QSsl.SslOption

    class SslProtocol(int):
        UnknownProtocol = ... # type: QSsl.SslProtocol
        SslV3 = ... # type: QSsl.SslProtocol
        SslV2 = ... # type: QSsl.SslProtocol
        TlsV1_0 = ... # type: QSsl.SslProtocol
        TlsV1_0OrLater = ... # type: QSsl.SslProtocol
        TlsV1_1 = ... # type: QSsl.SslProtocol
        TlsV1_1OrLater = ... # type: QSsl.SslProtocol
        TlsV1_2 = ... # type: QSsl.SslProtocol
        TlsV1_2OrLater = ... # type: QSsl.SslProtocol
        AnyProtocol = ... # type: QSsl.SslProtocol
        TlsV1SslV3 = ... # type: QSsl.SslProtocol
        SecureProtocols = ... # type: QSsl.SslProtocol
        DtlsV1_0 = ... # type: QSsl.SslProtocol
        DtlsV1_0OrLater = ... # type: QSsl.SslProtocol
        DtlsV1_2 = ... # type: QSsl.SslProtocol
        DtlsV1_2OrLater = ... # type: QSsl.SslProtocol
        TlsV1_3 = ... # type: QSsl.SslProtocol
        TlsV1_3OrLater = ... # type: QSsl.SslProtocol

    class AlternativeNameEntryType(int):
        EmailEntry = ... # type: QSsl.AlternativeNameEntryType
        DnsEntry = ... # type: QSsl.AlternativeNameEntryType
        IpAddressEntry = ... # type: QSsl.AlternativeNameEntryType

    class KeyAlgorithm(int):
        Opaque = ... # type: QSsl.KeyAlgorithm
        Rsa = ... # type: QSsl.KeyAlgorithm
        Dsa = ... # type: QSsl.KeyAlgorithm
        Ec = ... # type: QSsl.KeyAlgorithm
        Dh = ... # type: QSsl.KeyAlgorithm

    class EncodingFormat(int):
        Pem = ... # type: QSsl.EncodingFormat
        Der = ... # type: QSsl.EncodingFormat

    class KeyType(int):
        PrivateKey = ... # type: QSsl.KeyType
        PublicKey = ... # type: QSsl.KeyType

    class SslOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __xor__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __ior__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __or__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __iand__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __and__(self, f: typing.Union['QSsl.SslOptions', 'QSsl.SslOption']) -> 'QSsl.SslOptions': ...
        def __invert__(self) -> 'QSsl.SslOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...


class QSslCertificate(PyQt5.sipsimplewrapper):

    class PatternSyntax(int):
        RegularExpression = ... # type: QSslCertificate.PatternSyntax
        Wildcard = ... # type: QSslCertificate.PatternSyntax
        FixedString = ... # type: QSslCertificate.PatternSyntax

    class SubjectInfo(int):
        Organization = ... # type: QSslCertificate.SubjectInfo
        CommonName = ... # type: QSslCertificate.SubjectInfo
        LocalityName = ... # type: QSslCertificate.SubjectInfo
        OrganizationalUnitName = ... # type: QSslCertificate.SubjectInfo
        CountryName = ... # type: QSslCertificate.SubjectInfo
        StateOrProvinceName = ... # type: QSslCertificate.SubjectInfo
        DistinguishedNameQualifier = ... # type: QSslCertificate.SubjectInfo
        SerialNumber = ... # type: QSslCertificate.SubjectInfo
        EmailAddress = ... # type: QSslCertificate.SubjectInfo

    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray] = ..., format: QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCertificate') -> None: ...

    def subjectDisplayName(self) -> str: ...
    def issuerDisplayName(self) -> str: ...
    @staticmethod
    def importPkcs12(device: typing.Optional[QtCore.QIODevice], key: typing.Optional['QSslKey'], certificate: typing.Optional['QSslCertificate'], caCertificates: typing.Optional[typing.Iterable['QSslCertificate']] = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> bool: ...
    def __hash__(self) -> int: ...
    def isSelfSigned(self) -> bool: ...
    @staticmethod
    def verify(certificateChain: typing.Iterable['QSslCertificate'], hostName: typing.Optional[str] = ...) -> typing.List['QSslError']: ...
    def toText(self) -> str: ...
    def extensions(self) -> typing.List['QSslCertificateExtension']: ...
    def issuerInfoAttributes(self) -> typing.List[QtCore.QByteArray]: ...
    def subjectInfoAttributes(self) -> typing.List[QtCore.QByteArray]: ...
    def isBlacklisted(self) -> bool: ...
    def swap(self, other: 'QSslCertificate') -> None: ...
    def handle(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    @staticmethod
    def fromData(data: typing.Union[QtCore.QByteArray, bytes, bytearray], format: QSsl.EncodingFormat = ...) -> typing.List['QSslCertificate']: ...
    @staticmethod
    def fromDevice(device: typing.Optional[QtCore.QIODevice], format: QSsl.EncodingFormat = ...) -> typing.List['QSslCertificate']: ...
    @staticmethod
    def fromPath(path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: QtCore.QRegExp.PatternSyntax = ...) -> typing.List['QSslCertificate']: ...
    def toDer(self) -> QtCore.QByteArray: ...
    def toPem(self) -> QtCore.QByteArray: ...
    def publicKey(self) -> 'QSslKey': ...
    def expiryDate(self) -> QtCore.QDateTime: ...
    def effectiveDate(self) -> QtCore.QDateTime: ...
    def subjectAlternativeNames(self) -> typing.Dict[QSsl.AlternativeNameEntryType, typing.List[str]]: ...
    @typing.overload
    def subjectInfo(self, info: 'QSslCertificate.SubjectInfo') -> typing.List[str]: ...
    @typing.overload
    def subjectInfo(self, attribute: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.List[str]: ...
    @typing.overload
    def issuerInfo(self, info: 'QSslCertificate.SubjectInfo') -> typing.List[str]: ...
    @typing.overload
    def issuerInfo(self, attribute: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.List[str]: ...
    def digest(self, algorithm: QtCore.QCryptographicHash.Algorithm = ...) -> QtCore.QByteArray: ...
    def serialNumber(self) -> QtCore.QByteArray: ...
    def version(self) -> QtCore.QByteArray: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QSslCertificateExtension(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCertificateExtension') -> None: ...

    def isSupported(self) -> bool: ...
    def isCritical(self) -> bool: ...
    def value(self) -> typing.Any: ...
    def name(self) -> str: ...
    def oid(self) -> str: ...
    def swap(self, other: 'QSslCertificateExtension') -> None: ...


class QSslCipher(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], protocol: QSsl.SslProtocol) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCipher') -> None: ...

    def swap(self, other: 'QSslCipher') -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def protocolString(self) -> str: ...
    def encryptionMethod(self) -> str: ...
    def authenticationMethod(self) -> str: ...
    def keyExchangeMethod(self) -> str: ...
    def usedBits(self) -> int: ...
    def supportedBits(self) -> int: ...
    def name(self) -> str: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QSslConfiguration(PyQt5.sipsimplewrapper):

    class NextProtocolNegotiationStatus(int):
        NextProtocolNegotiationNone = ... # type: QSslConfiguration.NextProtocolNegotiationStatus
        NextProtocolNegotiationNegotiated = ... # type: QSslConfiguration.NextProtocolNegotiationStatus
        NextProtocolNegotiationUnsupported = ... # type: QSslConfiguration.NextProtocolNegotiationStatus

    NextProtocolHttp1_1 = ... # type: bytes
    NextProtocolSpdy3_0 = ... # type: bytes

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslConfiguration') -> None: ...

    @typing.overload
    def addCaCertificates(self, path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: QSslCertificate.PatternSyntax = ...) -> bool: ...
    @typing.overload
    def addCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def addCaCertificate(self, certificate: QSslCertificate) -> None: ...
    def ocspStaplingEnabled(self) -> bool: ...
    def setOcspStaplingEnabled(self, enable: bool) -> None: ...
    def setBackendConfiguration(self, backendConfiguration: typing.Dict[typing.Union[QtCore.QByteArray, bytes, bytearray], typing.Any] = ...) -> None: ...
    def setBackendConfigurationOption(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray], value: typing.Any) -> None: ...
    def backendConfiguration(self) -> typing.Dict[QtCore.QByteArray, typing.Any]: ...
    def setDiffieHellmanParameters(self, dhparams: 'QSslDiffieHellmanParameters') -> None: ...
    def diffieHellmanParameters(self) -> 'QSslDiffieHellmanParameters': ...
    def setPreSharedKeyIdentityHint(self, hint: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def preSharedKeyIdentityHint(self) -> QtCore.QByteArray: ...
    def ephemeralServerKey(self) -> 'QSslKey': ...
    @staticmethod
    def supportedEllipticCurves() -> typing.List['QSslEllipticCurve']: ...
    def setEllipticCurves(self, curves: typing.Iterable['QSslEllipticCurve']) -> None: ...
    def ellipticCurves(self) -> typing.List['QSslEllipticCurve']: ...
    @staticmethod
    def systemCaCertificates() -> typing.List[QSslCertificate]: ...
    @staticmethod
    def supportedCiphers() -> typing.List[QSslCipher]: ...
    def sessionProtocol(self) -> QSsl.SslProtocol: ...
    def nextProtocolNegotiationStatus(self) -> 'QSslConfiguration.NextProtocolNegotiationStatus': ...
    def nextNegotiatedProtocol(self) -> QtCore.QByteArray: ...
    def allowedNextProtocols(self) -> typing.List[QtCore.QByteArray]: ...
    def setAllowedNextProtocols(self, protocols: typing.Iterable[typing.Union[QtCore.QByteArray, bytes, bytearray]]) -> None: ...
    def sessionTicketLifeTimeHint(self) -> int: ...
    def setSessionTicket(self, sessionTicket: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def sessionTicket(self) -> QtCore.QByteArray: ...
    def setLocalCertificateChain(self, localChain: typing.Iterable[QSslCertificate]) -> None: ...
    def localCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def swap(self, other: 'QSslConfiguration') -> None: ...
    def testSslOption(self, option: QSsl.SslOption) -> bool: ...
    def setSslOption(self, option: QSsl.SslOption, on: bool) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def setDefaultConfiguration(configuration: 'QSslConfiguration') -> None: ...
    @staticmethod
    def defaultConfiguration() -> 'QSslConfiguration': ...
    def setCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def caCertificates(self) -> typing.List[QSslCertificate]: ...
    def setCiphers(self, ciphers: typing.Iterable[QSslCipher]) -> None: ...
    def ciphers(self) -> typing.List[QSslCipher]: ...
    def setPrivateKey(self, key: 'QSslKey') -> None: ...
    def privateKey(self) -> 'QSslKey': ...
    def sessionCipher(self) -> QSslCipher: ...
    def peerCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def peerCertificate(self) -> QSslCertificate: ...
    def setLocalCertificate(self, certificate: QSslCertificate) -> None: ...
    def localCertificate(self) -> QSslCertificate: ...
    def setPeerVerifyDepth(self, depth: int) -> None: ...
    def peerVerifyDepth(self) -> int: ...
    def setPeerVerifyMode(self, mode: 'QSslSocket.PeerVerifyMode') -> None: ...
    def peerVerifyMode(self) -> 'QSslSocket.PeerVerifyMode': ...
    def setProtocol(self, protocol: QSsl.SslProtocol) -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def isNull(self) -> bool: ...


class QSslDiffieHellmanParameters(PyQt5.sipsimplewrapper):

    class Error(int):
        NoError = ... # type: QSslDiffieHellmanParameters.Error
        InvalidInputDataError = ... # type: QSslDiffieHellmanParameters.Error
        UnsafeParametersError = ... # type: QSslDiffieHellmanParameters.Error

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslDiffieHellmanParameters') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QSslDiffieHellmanParameters.Error': ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    @typing.overload
    @staticmethod
    def fromEncoded(encoded: typing.Union[QtCore.QByteArray, bytes, bytearray], encoding: QSsl.EncodingFormat = ...) -> 'QSslDiffieHellmanParameters': ...
    @typing.overload
    @staticmethod
    def fromEncoded(device: typing.Optional[QtCore.QIODevice], encoding: QSsl.EncodingFormat = ...) -> 'QSslDiffieHellmanParameters': ...
    @staticmethod
    def defaultParameters() -> 'QSslDiffieHellmanParameters': ...
    def swap(self, other: 'QSslDiffieHellmanParameters') -> None: ...


class QSslEllipticCurve(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSslEllipticCurve') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def isTlsNamedCurve(self) -> bool: ...
    def isValid(self) -> bool: ...
    def longName(self) -> str: ...
    def shortName(self) -> str: ...
    @staticmethod
    def fromLongName(name: typing.Optional[str]) -> 'QSslEllipticCurve': ...
    @staticmethod
    def fromShortName(name: typing.Optional[str]) -> 'QSslEllipticCurve': ...


class QSslError(PyQt5.sipsimplewrapper):

    class SslError(int):
        UnspecifiedError = ... # type: QSslError.SslError
        NoError = ... # type: QSslError.SslError
        UnableToGetIssuerCertificate = ... # type: QSslError.SslError
        UnableToDecryptCertificateSignature = ... # type: QSslError.SslError
        UnableToDecodeIssuerPublicKey = ... # type: QSslError.SslError
        CertificateSignatureFailed = ... # type: QSslError.SslError
        CertificateNotYetValid = ... # type: QSslError.SslError
        CertificateExpired = ... # type: QSslError.SslError
        InvalidNotBeforeField = ... # type: QSslError.SslError
        InvalidNotAfterField = ... # type: QSslError.SslError
        SelfSignedCertificate = ... # type: QSslError.SslError
        SelfSignedCertificateInChain = ... # type: QSslError.SslError
        UnableToGetLocalIssuerCertificate = ... # type: QSslError.SslError
        UnableToVerifyFirstCertificate = ... # type: QSslError.SslError
        CertificateRevoked = ... # type: QSslError.SslError
        InvalidCaCertificate = ... # type: QSslError.SslError
        PathLengthExceeded = ... # type: QSslError.SslError
        InvalidPurpose = ... # type: QSslError.SslError
        CertificateUntrusted = ... # type: QSslError.SslError
        CertificateRejected = ... # type: QSslError.SslError
        SubjectIssuerMismatch = ... # type: QSslError.SslError
        AuthorityIssuerSerialNumberMismatch = ... # type: QSslError.SslError
        NoPeerCertificate = ... # type: QSslError.SslError
        HostNameMismatch = ... # type: QSslError.SslError
        NoSslSupport = ... # type: QSslError.SslError
        CertificateBlacklisted = ... # type: QSslError.SslError
        CertificateStatusUnknown = ... # type: QSslError.SslError
        OcspNoResponseFound = ... # type: QSslError.SslError
        OcspMalformedRequest = ... # type: QSslError.SslError
        OcspMalformedResponse = ... # type: QSslError.SslError
        OcspInternalError = ... # type: QSslError.SslError
        OcspTryLater = ... # type: QSslError.SslError
        OcspSigRequred = ... # type: QSslError.SslError
        OcspUnauthorized = ... # type: QSslError.SslError
        OcspResponseCannotBeTrusted = ... # type: QSslError.SslError
        OcspResponseCertIdUnknown = ... # type: QSslError.SslError
        OcspResponseExpired = ... # type: QSslError.SslError
        OcspStatusUnknown = ... # type: QSslError.SslError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, error: 'QSslError.SslError') -> None: ...
    @typing.overload
    def __init__(self, error: 'QSslError.SslError', certificate: QSslCertificate) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslError') -> None: ...

    def __hash__(self) -> int: ...
    def swap(self, other: 'QSslError') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def certificate(self) -> QSslCertificate: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QSslError.SslError': ...


class QSslKey(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, encoded: typing.Union[QtCore.QByteArray, bytes, bytearray], algorithm: QSsl.KeyAlgorithm, encoding: QSsl.EncodingFormat = ..., type: QSsl.KeyType = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], algorithm: QSsl.KeyAlgorithm, encoding: QSsl.EncodingFormat = ..., type: QSsl.KeyType = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    @typing.overload
    def __init__(self, handle: typing.Optional[PyQt5.sip.voidptr], type: QSsl.KeyType = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslKey') -> None: ...

    def swap(self, other: 'QSslKey') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def handle(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def toDer(self, passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> QtCore.QByteArray: ...
    def toPem(self, passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> QtCore.QByteArray: ...
    def algorithm(self) -> QSsl.KeyAlgorithm: ...
    def type(self) -> QSsl.KeyType: ...
    def length(self) -> int: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...


class QSslPreSharedKeyAuthenticator(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, authenticator: 'QSslPreSharedKeyAuthenticator') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def maximumPreSharedKeyLength(self) -> int: ...
    def preSharedKey(self) -> QtCore.QByteArray: ...
    def setPreSharedKey(self, preSharedKey: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def maximumIdentityLength(self) -> int: ...
    def identity(self) -> QtCore.QByteArray: ...
    def setIdentity(self, identity: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def identityHint(self) -> QtCore.QByteArray: ...
    def swap(self, authenticator: 'QSslPreSharedKeyAuthenticator') -> None: ...


class QTcpSocket(QAbstractSocket):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...


class QSslSocket(QTcpSocket):

    class PeerVerifyMode(int):
        VerifyNone = ... # type: QSslSocket.PeerVerifyMode
        QueryPeer = ... # type: QSslSocket.PeerVerifyMode
        VerifyPeer = ... # type: QSslSocket.PeerVerifyMode
        AutoVerifyPeer = ... # type: QSslSocket.PeerVerifyMode

    class SslMode(int):
        UnencryptedMode = ... # type: QSslSocket.SslMode
        SslClientMode = ... # type: QSslSocket.SslMode
        SslServerMode = ... # type: QSslSocket.SslMode

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    newSessionTicketReceived: typing.ClassVar[QtCore.pyqtSignal]
    def sslHandshakeErrors(self) -> typing.List[QSslError]: ...
    def ocspResponses(self) -> typing.List[QOcspResponse]: ...
    @staticmethod
    def sslLibraryBuildVersionString() -> str: ...
    @staticmethod
    def sslLibraryBuildVersionNumber() -> int: ...
    def sessionProtocol(self) -> QSsl.SslProtocol: ...
    def localCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def setLocalCertificateChain(self, localChain: typing.Iterable[QSslCertificate]) -> None: ...
    @staticmethod
    def sslLibraryVersionString() -> str: ...
    @staticmethod
    def sslLibraryVersionNumber() -> int: ...
    def disconnectFromHost(self) -> None: ...
    def connectToHost(self, hostName: typing.Optional[str], port: int, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    def resume(self) -> None: ...
    def setPeerVerifyName(self, hostName: typing.Optional[str]) -> None: ...
    def peerVerifyName(self) -> str: ...
    def socketOption(self, option: QAbstractSocket.SocketOption) -> typing.Any: ...
    def setSocketOption(self, option: QAbstractSocket.SocketOption, value: typing.Any) -> None: ...
    encryptedBytesWritten: typing.ClassVar[QtCore.pyqtSignal]
    peerVerifyError: typing.ClassVar[QtCore.pyqtSignal]
    def setSslConfiguration(self, config: QSslConfiguration) -> None: ...
    def sslConfiguration(self) -> QSslConfiguration: ...
    def encryptedBytesToWrite(self) -> int: ...
    def encryptedBytesAvailable(self) -> int: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def setPeerVerifyDepth(self, depth: int) -> None: ...
    def peerVerifyDepth(self) -> int: ...
    def setPeerVerifyMode(self, mode: 'QSslSocket.PeerVerifyMode') -> None: ...
    def peerVerifyMode(self) -> 'QSslSocket.PeerVerifyMode': ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    modeChanged: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def ignoreSslErrors(self) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: typing.Iterable[QSslError]) -> None: ...
    def startServerEncryption(self) -> None: ...
    def startClientEncryption(self) -> None: ...
    @staticmethod
    def supportsSsl() -> bool: ...
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForEncrypted(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    @staticmethod
    def systemCaCertificates() -> typing.List[QSslCertificate]: ...
    @staticmethod
    def defaultCaCertificates() -> typing.List[QSslCertificate]: ...
    @staticmethod
    def setDefaultCaCertificates(certificates: typing.Iterable[QSslCertificate]) -> None: ...
    @staticmethod
    def addDefaultCaCertificate(certificate: QSslCertificate) -> None: ...
    @typing.overload
    @staticmethod
    def addDefaultCaCertificates(path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: QtCore.QRegExp.PatternSyntax = ...) -> bool: ...
    @typing.overload
    @staticmethod
    def addDefaultCaCertificates(certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def caCertificates(self) -> typing.List[QSslCertificate]: ...
    def setCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def addCaCertificate(self, certificate: QSslCertificate) -> None: ...
    @typing.overload
    def addCaCertificates(self, path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: QtCore.QRegExp.PatternSyntax = ...) -> bool: ...
    @typing.overload
    def addCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    @staticmethod
    def supportedCiphers() -> typing.List[QSslCipher]: ...
    @staticmethod
    def defaultCiphers() -> typing.List[QSslCipher]: ...
    @staticmethod
    def setDefaultCiphers(ciphers: typing.Iterable[QSslCipher]) -> None: ...
    @typing.overload
    def setCiphers(self, ciphers: typing.Iterable[QSslCipher]) -> None: ...
    @typing.overload
    def setCiphers(self, ciphers: typing.Optional[str]) -> None: ...
    def ciphers(self) -> typing.List[QSslCipher]: ...
    def privateKey(self) -> QSslKey: ...
    @typing.overload
    def setPrivateKey(self, key: QSslKey) -> None: ...
    @typing.overload
    def setPrivateKey(self, fileName: typing.Optional[str], algorithm: QSsl.KeyAlgorithm = ..., format: QSsl.EncodingFormat = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    def sessionCipher(self) -> QSslCipher: ...
    def peerCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def peerCertificate(self) -> QSslCertificate: ...
    def localCertificate(self) -> QSslCertificate: ...
    @typing.overload
    def setLocalCertificate(self, certificate: QSslCertificate) -> None: ...
    @typing.overload
    def setLocalCertificate(self, path: typing.Optional[str], format: QSsl.EncodingFormat = ...) -> None: ...
    def abort(self) -> None: ...
    def flush(self) -> bool: ...
    def atEnd(self) -> bool: ...
    def close(self) -> None: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def setProtocol(self, protocol: QSsl.SslProtocol) -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def isEncrypted(self) -> bool: ...
    def mode(self) -> 'QSslSocket.SslMode': ...
    def setSocketDescriptor(self, socketDescriptor: PyQt5.sip.voidptr, state: QAbstractSocket.SocketState = ..., mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ...) -> bool: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, sslPeerName: typing.Optional[str], mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag] = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...


class QTcpServer(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    acceptError: typing.ClassVar[QtCore.pyqtSignal]
    newConnection: typing.ClassVar[QtCore.pyqtSignal]
    def addPendingConnection(self, socket: typing.Optional[QTcpSocket]) -> None: ...
    def incomingConnection(self, handle: PyQt5.sip.voidptr) -> None: ...
    def resumeAccepting(self) -> None: ...
    def pauseAccepting(self) -> None: ...
    def proxy(self) -> QNetworkProxy: ...
    def setProxy(self, networkProxy: QNetworkProxy) -> None: ...
    def errorString(self) -> str: ...
    def serverError(self) -> QAbstractSocket.SocketError: ...
    def nextPendingConnection(self) -> typing.Optional[QTcpSocket]: ...
    def hasPendingConnections(self) -> bool: ...
    def waitForNewConnection(self, msecs: int = ...) -> typing.Tuple[bool, typing.Optional[bool]]: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt5.sip.voidptr) -> bool: ...
    def socketDescriptor(self) -> PyQt5.sip.voidptr: ...
    def serverAddress(self) -> QHostAddress: ...
    def serverPort(self) -> int: ...
    def maxPendingConnections(self) -> int: ...
    def setMaxPendingConnections(self, numConnections: int) -> None: ...
    def isListening(self) -> bool: ...
    def close(self) -> None: ...
    def listen(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress] = ..., port: int = ...) -> bool: ...


class QUdpSocket(QAbstractSocket):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def receiveDatagram(self, maxSize: int = ...) -> QNetworkDatagram: ...
    def setMulticastInterface(self, iface: QNetworkInterface) -> None: ...
    def multicastInterface(self) -> QNetworkInterface: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> bool: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress], iface: QNetworkInterface) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress], iface: QNetworkInterface) -> bool: ...
    @typing.overload
    def writeDatagram(self, data: typing.Optional[PyQt5.sip.array[bytes]], host: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int) -> int: ...
    @typing.overload
    def writeDatagram(self, datagram: typing.Union[QtCore.QByteArray, bytes, bytearray], host: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int) -> int: ...
    @typing.overload
    def writeDatagram(self, datagram: QNetworkDatagram) -> int: ...
    def readDatagram(self, maxlen: int) -> typing.Tuple[bytes, typing.Optional[QHostAddress], typing.Optional[int]]: ...
    def pendingDatagramSize(self) -> int: ...
    def hasPendingDatagrams(self) -> bool: ...
