#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار خاصية الباركودات المتعددة للمنتج الواحد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("📊 اختبار خاصية الباركودات المتعددة")
    print("=" * 50)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("🔧 الخاصية الجديدة:")
    print("")
    print("   📊 باركودات متعددة لمنتج واحد:")
    print("     • الباركود الأساسي (الحقل الأصلي)")
    print("     • باركود إضافي 1")
    print("     • باركود إضافي 2")
    print("     • باركود إضافي 3")
    print("     • إجمالي: 4 باركودات لكل منتج")
    print("")
    print("   🎯 الاستخدامات:")
    print("     • منتج من موردين مختلفين")
    print("     • أحجام مختلفة لنفس المنتج")
    print("     • باركود داخلي وخارجي")
    print("     • باركود الشركة المصنعة والموزع")
    print("")
    print("   🛡️ الحماية:")
    print("     • منع تكرار الباركودات")
    print("     • التحقق من عدم وجود الباركود في منتجات أخرى")
    print("     • رسائل تحذير واضحة")
    print("")
    print("   🔍 البحث الذكي:")
    print("     • البحث بأي من الباركودات الأربعة")
    print("     • يعمل في جميع أنحاء النظام")
    print("     • متوافق مع قارئ الباركود")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار الباركودات المتعددة:")
        print("")
        print("   📦 إضافة منتج جديد:")
        print("     1️⃣ اذهب لقسم 'المخزون'")
        print("     2️⃣ اضغط 'إضافة منتج جديد'")
        print("     3️⃣ ستجد قسم 'الباركودات' مع 4 حقول:")
        print("        • الباركود الأساسي (مميز باللون الأزرق)")
        print("        • باركود إضافي 1")
        print("        • باركود إضافي 2")
        print("        • باركود إضافي 3")
        print("")
        print("   ✏️ إدخال البيانات:")
        print("     1️⃣ أدخل اسم المنتج والأسعار")
        print("     2️⃣ أدخل باركودات مختلفة في الحقول:")
        print("        مثال:")
        print("        • الأساسي: 1234567890123")
        print("        • إضافي 1: 9876543210987")
        print("        • إضافي 2: 5555666677778")
        print("        • إضافي 3: 1111222233334")
        print("     3️⃣ اضغط 'حفظ'")
        print("")
        print("   🔍 اختبار البحث:")
        print("     1️⃣ اذهب لصفحة 'فاتورة جديدة'")
        print("     2️⃣ في حقل البحث السريع، جرب:")
        print("        • البحث بالباركود الأساسي")
        print("        • البحث بأي من الباركودات الإضافية")
        print("        • استخدام قارئ الباركود")
        print("     3️⃣ يجب أن يجد المنتج بأي باركود")
        print("")
        print("   📱 اختبار قارئ الباركود:")
        print("     1️⃣ في فاتورة المبيعات، اضغط 'مسح بالكاميرا'")
        print("     2️⃣ أو اضغط 'مسح بالجهاز'")
        print("     3️⃣ أدخل أي من الباركودات الأربعة")
        print("     4️⃣ يجب أن يجد المنتج ويضيفه للفاتورة")
        print("")
        print("   ⚠️ اختبار الحماية:")
        print("     1️⃣ حاول إدخال نفس الباركود في حقلين")
        print("        → ستظهر رسالة 'الباركود مكرر'")
        print("     2️⃣ حاول إدخال باركود موجود في منتج آخر")
        print("        → ستظهر رسالة 'الباركود موجود بالفعل'")
        print("")
        print("   📊 اختبار التعديل:")
        print("     1️⃣ في المخزون، اضغط 'تعديل' لمنتج موجود")
        print("     2️⃣ ستظهر الباركودات المحفوظة")
        print("     3️⃣ يمكنك تعديل أو إضافة باركودات جديدة")
        print("     4️⃣ احفظ التغييرات")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 50)
    print("📊 ملخص خاصية الباركودات المتعددة:")
    print("")
    print("   ✅ ما تم إضافته:")
    print("     • جدول جديد: product_barcodes")
    print("     • 4 حقول باركود في نافذة إضافة المنتج")
    print("     • حماية من التكرار والتضارب")
    print("     • بحث ذكي بجميع الباركودات")
    print("     • تحديث دوال قارئ الباركود")
    print("")
    print("   🎯 الفوائد:")
    print("     • مرونة أكبر في إدارة المخزون")
    print("     • دعم موردين متعددين")
    print("     • سهولة البحث والوصول")
    print("     • تنظيم أفضل للمنتجات")
    print("")
    print("   💡 نصائح الاستخدام:")
    print("     • استخدم الباركود الأساسي للباركود الأكثر استخداماً")
    print("     • أضف وصف واضح لكل باركود إضافي")
    print("     • تأكد من عدم تكرار الباركودات")
    print("     • اختبر البحث بجميع الباركودات")
    print("")
    print("🎉 خاصية الباركودات المتعددة جاهزة!")

if __name__ == "__main__":
    main()
