#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لنظام التراخيص
License System Demo
"""

import os
import sys

def check_requirements():
    """فحص المتطلبات"""
    try:
        import cryptography
        import pyperclip
        print("✅ جميع المكتبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("📦 يرجى تسطيب المكتبات المطلوبة:")
        print("pip install cryptography pyperclip")
        return False

def demo_license_manager():
    """عرض توضيحي لمدير التراخيص"""
    print("\n" + "="*50)
    print("🔐 عرض توضيحي لمدير التراخيص")
    print("="*50)
    
    try:
        from license_manager import LicenseManager, LicenseCodeGenerator
        
        # إنشاء مدير التراخيص
        license_manager = LicenseManager()
        
        # عرض معلومات الجهاز
        customer_code = license_manager.get_customer_code()
        machine_id = license_manager.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص الحالي
        print("\n📋 فحص الترخيص الحالي:")
        status = license_manager.check_license()
        
        if status["valid"]:
            print(f"✅ الترخيص صالح")
            print(f"📅 ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"⏰ متبقي: {status['days_remaining']} يوم")
        else:
            print(f"❌ الترخيص غير صالح: {status['message']}")
            
            if status["status"] == "NO_LICENSE":
                print("\n🔄 إنشاء ترخيص تجريبي...")
                license_data = license_manager.create_initial_license(days=30)
                print("✅ تم إنشاء ترخيص تجريبي لمدة 30 يوم")
        
        # توليد كود تجديد تجريبي
        print("\n🔑 توليد كود تجديد تجريبي:")
        renewal_code = LicenseCodeGenerator.generate_renewal_code(
            customer_code, machine_id, 2025
        )
        print(f"📋 كود التجديد: {renewal_code}")
        
        # اختبار كود التجديد
        print("\n🧪 اختبار كود التجديد:")
        validation = license_manager.validate_renewal_code(renewal_code)
        
        if validation["valid"]:
            print("✅ كود التجديد صحيح")
            print(f"📅 سينتهي في: {validation['new_expiry'].strftime('%d/%m/%Y')}")
            
            # تطبيق كود التجديد
            result = license_manager.apply_renewal_code(renewal_code)
            if result["valid"]:
                print("✅ تم تطبيق كود التجديد بنجاح")
            else:
                print(f"❌ فشل في تطبيق الكود: {result['message']}")
        else:
            print(f"❌ كود التجديد غير صحيح: {validation['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        return False

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*50)
    print("🎯 نظام التراخيص - القائمة الرئيسية")
    print("="*50)
    print("1. 🔍 عرض توضيحي لمدير التراخيص")
    print("2. 🧪 تشغيل تطبيق الاختبار")
    print("3. 🔑 تشغيل مولد الأكواد (للمطور)")
    print("4. 📖 عرض الدليل")
    print("5. ❌ خروج")
    print("-" * 50)

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في نظام التراخيص")
    
    # فحص المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return
    
    while True:
        show_menu()
        choice = input("اختر رقم من القائمة: ").strip()
        
        if choice == "1":
            demo_license_manager()
            
        elif choice == "2":
            print("\n🧪 تشغيل تطبيق الاختبار...")
            try:
                os.system("python test_license_system.py test")
            except:
                print("❌ فشل في تشغيل تطبيق الاختبار")
                
        elif choice == "3":
            print("\n🔑 تشغيل مولد الأكواد...")
            try:
                os.system("python code_generator_ui.py")
            except:
                print("❌ فشل في تشغيل مولد الأكواد")
                
        elif choice == "4":
            print("\n📖 دليل الاستخدام:")
            print("📁 docs/دليل_نظام_التراخيص.md")
            print("📁 docs/استيراد_قاعدة_البيانات_الجاهزة.md")
            print("📁 docs/دليل_الأدوات_المنزلية_فقط.md")
            
        elif choice == "5":
            print("👋 شكراً لاستخدام نظام التراخيص!")
            break
            
        else:
            print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
