#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لبرنامج المحاسبة مع نظام التراخيص
Quick Start for Accounting Software with License System
"""

import os
import sys
import subprocess

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import cryptography
        import pyperclip
        from PyQt5.QtWidgets import QApplication
        print("✅ جميع المكتبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("\n📦 تسطيب المكتبات المطلوبة...")
        
        # تسطيب المكتبات المطلوبة
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "cryptography", "pyperclip"])
            print("✅ تم تسطيب المكتبات بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تسطيب المكتبات")
            return False

def check_license_files():
    """فحص ملفات نظام التراخيص"""
    print("📁 فحص ملفات نظام التراخيص...")
    
    required_files = [
        "license_manager.py",
        "license_ui.py",
        "code_generator_ui.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("✅ جميع ملفات نظام التراخيص موجودة")
        return True

def test_license_system():
    """اختبار نظام التراخيص"""
    print("🧪 اختبار نظام التراخيص...")
    
    try:
        from license_manager import LicenseManager
        
        # إنشاء مدير التراخيص
        lm = LicenseManager()
        
        # الحصول على معلومات الجهاز
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص
        status = lm.check_license()
        
        if status["valid"]:
            print(f"✅ الترخيص صالح حتى: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"⏰ متبقي: {status['days_remaining']} يوم")
        else:
            print(f"⚠️ الترخيص غير صالح: {status['message']}")
            
            if status["status"] == "NO_LICENSE":
                print("🔄 إنشاء ترخيص تجريبي...")
                lm.create_initial_license(days=30)
                print("✅ تم إنشاء ترخيص تجريبي لمدة 30 يوم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام التراخيص: {e}")
        return False

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*50)
    print("🎯 برنامج المحاسبة مع نظام التراخيص")
    print("="*50)
    print("1. 🚀 تشغيل برنامج المحاسبة")
    print("2. 🔐 عرض حالة الترخيص")
    print("3. 🔑 مولد أكواد التجديد (للمطور)")
    print("4. 🧪 اختبار النظام")
    print("5. 📖 عرض الدليل")
    print("6. ❌ خروج")
    print("-" * 50)

def run_accounting_software():
    """تشغيل برنامج المحاسبة"""
    print("🚀 تشغيل برنامج المحاسبة...")
    try:
        subprocess.run([sys.executable, "main.py"])
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")

def show_license_status():
    """عرض حالة الترخيص"""
    print("🔐 حالة الترخيص:")
    print("-" * 30)
    
    try:
        from license_manager import LicenseManager
        lm = LicenseManager()
        
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        status = lm.check_license()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        if status["valid"]:
            print(f"✅ الترخيص صالح")
            print(f"📅 ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"⏰ متبقي: {status['days_remaining']} يوم")
            
            if status['days_remaining'] <= 7:
                print("⚠️ تحذير: الترخيص سينتهي قريباً!")
                print("📞 يرجى التواصل مع المطور لتجديد الترخيص")
        else:
            print(f"❌ الترخيص غير صالح: {status['message']}")
            print("📞 يرجى التواصل مع المطور للحصول على ترخيص")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الترخيص: {e}")

def run_code_generator():
    """تشغيل مولد أكواد التجديد"""
    print("🔑 تشغيل مولد أكواد التجديد...")
    try:
        subprocess.run([sys.executable, "code_generator_ui.py"])
    except Exception as e:
        print(f"❌ خطأ في تشغيل مولد الأكواد: {e}")

def show_guide():
    """عرض الدليل"""
    print("📖 دليل الاستخدام:")
    print("-" * 30)
    print("📁 ملفات التوثيق المتوفرة:")
    print("   • LICENSE_SYSTEM_README.md - دليل شامل للنظام")
    print("   • docs/دليل_نظام_التراخيص.md - دليل مفصل")
    print("\n🎯 للعملاء:")
    print("   1. تشغيل البرنامج: اختر الخيار 1")
    print("   2. للتجديد: النظام > تجديد الترخيص")
    print("   3. أدخل كود التجديد المرسل من المطور")
    print("\n👨‍💻 للمطور:")
    print("   1. توليد أكواد: اختر الخيار 3")
    print("   2. أدخل كود العميل ورقم الجهاز")
    print("   3. أرسل الكود للعميل بعد الدفع")

def main():
    """الدالة الرئيسية"""
    print("🔐 مرحباً بك في برنامج المحاسبة مع نظام التراخيص")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return
    
    # فحص ملفات النظام
    if not check_license_files():
        print("💡 تأكد من وجود جميع ملفات نظام التراخيص")
        input("\nاضغط Enter للخروج...")
        return
    
    # اختبار النظام
    if not test_license_system():
        print("💡 تأكد من صحة ملفات نظام التراخيص")
        input("\nاضغط Enter للخروج...")
        return
    
    # القائمة الرئيسية
    while True:
        show_menu()
        choice = input("اختر رقم من القائمة: ").strip()
        
        if choice == "1":
            run_accounting_software()
            
        elif choice == "2":
            show_license_status()
            
        elif choice == "3":
            run_code_generator()
            
        elif choice == "4":
            test_license_system()
            
        elif choice == "5":
            show_guide()
            
        elif choice == "6":
            print("👋 شكراً لاستخدام برنامج المحاسبة!")
            break
            
        else:
            print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
