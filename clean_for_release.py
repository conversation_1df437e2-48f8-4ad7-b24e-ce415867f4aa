#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧹 سكريبت تنظيف البيانات للإصدار النهائي
يحذف جميع البيانات التجريبية مع الاحتفاظ بلوجو الشركة المطورة
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل التنظيف"""
    db_path = "accounting.db"
    if os.path.exists(db_path):
        backup_name = f"accounting_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(db_path, backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def clean_database():
    """تنظيف قاعدة البيانات من البيانات التجريبية"""
    db_path = "accounting.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧹 بدء تنظيف البيانات التجريبية...")
        
        # حذف البيانات التجريبية (بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية)
        tables_to_clean = [
            ("transaction_items", "عناصر الفواتير"),
            ("transactions", "الفواتير والمعاملات"),
            ("product_barcodes", "باركودات المنتجات"),
            ("products", "المنتجات"),
            ("customers", "العملاء"),
            ("suppliers", "الموردين"),
            ("audit_log", "سجل العمليات"),
            ("notifications", "الإشعارات")
        ]
        
        for table, description in tables_to_clean:
            try:
                cursor.execute(f"DELETE FROM {table}")
                deleted_count = cursor.rowcount
                print(f"   🗑️ تم حذف {deleted_count} سجل من {description}")
            except sqlite3.Error as e:
                print(f"   ⚠️ خطأ في حذف {description}: {e}")
        
        # إعادة تعيين معرفات التسلسل التلقائي
        reset_tables = [
            "products", "customers", "suppliers", "transactions", 
            "transaction_items", "product_barcodes", "audit_log", "notifications"
        ]
        
        for table in reset_tables:
            try:
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
            except sqlite3.Error:
                pass  # الجدول قد لا يحتوي على auto-increment
        
        print("   🔄 تم إعادة تعيين معرفات التسلسل")
        
        conn.commit()
        conn.close()
        
        print("✅ تم تنظيف قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف قاعدة البيانات: {e}")
        return False

def clean_company_settings():
    """حذف إعدادات الشركة التجريبية (مع الاحتفاظ بلوجو الشركة المطورة)"""
    try:
        conn = sqlite3.connect("accounting.db")
        cursor = conn.cursor()
        
        print("🏢 تنظيف إعدادات الشركة...")
        
        # حذف إعدادات الشركة التجريبية فقط (ليس لوجو الشركة المطورة)
        company_settings_to_clear = [
            'company_name',
            'owner_name', 
            'phone',
            'email',
            'address',
            'tax_number',
            'commercial_register',
            'company_logo'  # لوجو شركة العميل (ليس لوجو الشركة المطورة)
        ]
        
        for setting in company_settings_to_clear:
            cursor.execute("DELETE FROM company_settings WHERE key = ?", (setting,))
        
        deleted_count = cursor.rowcount
        print(f"   🗑️ تم حذف {deleted_count} إعداد شركة")
        
        conn.commit()
        conn.close()
        
        print("✅ تم تنظيف إعدادات الشركة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف إعدادات الشركة: {e}")
        return False

def clean_temp_files():
    """حذف الملفات المؤقتة والسجلات"""
    temp_files = [
        "user_settings.json",
        "app_settings.json", 
        "backup_settings.json",
        "temp_invoice.pdf",
        "debug.log",
        "error.log"
    ]
    
    print("🗂️ تنظيف الملفات المؤقتة...")
    
    cleaned_count = 0
    for file_name in temp_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"   🗑️ تم حذف: {file_name}")
                cleaned_count += 1
            except Exception as e:
                print(f"   ⚠️ لم يتم حذف {file_name}: {e}")
    
    # حذف مجلدات التخزين المؤقت
    temp_dirs = ["__pycache__", ".pytest_cache", "temp", "logs"]
    for dir_name in temp_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   🗑️ تم حذف مجلد: {dir_name}")
                cleaned_count += 1
            except Exception as e:
                print(f"   ⚠️ لم يتم حذف مجلد {dir_name}: {e}")
    
    print(f"✅ تم تنظيف {cleaned_count} ملف/مجلد مؤقت!")

def verify_developer_logo():
    """التحقق من وجود لوجو الشركة المطورة"""
    logo_path = os.path.join("assets", "icons.ico")
    if os.path.exists(logo_path):
        print("✅ لوجو الشركة المطورة موجود ومحفوظ")
        return True
    else:
        print("⚠️ تحذير: لوجو الشركة المطورة غير موجود!")
        return False

def main():
    """الدالة الرئيسية لتنظيف البيانات"""
    print("=" * 60)
    print("🧹 سكريبت تنظيف البيانات للإصدار النهائي")
    print("=" * 60)
    print()
    
    # تأكيد من المستخدم
    print("⚠️ تحذير: هذا السكريبت سيحذف جميع البيانات التجريبية!")
    print("📋 سيتم حذف:")
    print("   • جميع المنتجات والفواتير")
    print("   • جميع العملاء والموردين") 
    print("   • إعدادات الشركة التجريبية")
    print("   • سجل العمليات والإشعارات")
    print()
    print("✅ سيتم الاحتفاظ بـ:")
    print("   • لوجو الشركة المطورة")
    print("   • حساب المدير الافتراضي")
    print("   • إعدادات النظام الأساسية")
    print()
    
    confirm = input("هل تريد المتابعة؟ (نعم/لا): ").strip().lower()
    if confirm not in ['نعم', 'yes', 'y']:
        print("❌ تم إلغاء العملية")
        return
    
    print("\n🚀 بدء عملية التنظيف...")
    
    # إنشاء نسخة احتياطية
    backup_file = backup_database()
    
    # التحقق من لوجو الشركة المطورة
    verify_developer_logo()
    
    # تنظيف قاعدة البيانات
    if clean_database():
        # تنظيف إعدادات الشركة
        clean_company_settings()
        
        # تنظيف الملفات المؤقتة
        clean_temp_files()
        
        print("\n" + "=" * 60)
        print("🎉 تم تنظيف البرنامج بنجاح للإصدار النهائي!")
        print("=" * 60)
        print("✅ البرنامج جاهز للتوزيع على العملاء")
        print("✅ لوجو الشركة المطورة محفوظ")
        print("✅ حساب المدير الافتراضي متاح")
        print(f"💾 النسخة الاحتياطية: {backup_file}")
        print()
        print("🎯 العميل يمكنه الآن:")
        print("   • إدخال بيانات شركته")
        print("   • إضافة منتجاته")
        print("   • إضافة عملائه وموردينه")
        print("   • البدء في استخدام النظام")
        
    else:
        print("\n❌ فشل في تنظيف البيانات!")
        if backup_file:
            print(f"💾 يمكن استرجاع النسخة الاحتياطية: {backup_file}")

if __name__ == "__main__":
    main()
