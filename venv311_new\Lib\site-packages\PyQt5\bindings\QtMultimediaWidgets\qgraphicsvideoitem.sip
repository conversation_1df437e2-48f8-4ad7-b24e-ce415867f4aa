// qgraphicsvideoitem.sip generated by MetaSIP
//
// This file is part of the QtMultimediaWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsVideoItem : QGraphicsObject, QMediaBindableInterface
{
%TypeHeaderCode
#include <qgraphicsvideoitem.h>
%End

public:
%If (Qt_5_6_1 -)
    explicit QGraphicsVideoItem(QGraphicsItem *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QGraphicsVideoItem(QGraphicsItem *parent /TransferThis/ = 0);
%End
    virtual ~QGraphicsVideoItem();
    virtual QMediaObject *mediaObject() const;
    Qt::AspectRatioMode aspectRatioMode() const;
    void setAspectRatioMode(Qt::AspectRatioMode mode);
    QPointF offset() const;
    void setOffset(const QPointF &offset);
    QSizeF size() const;
    void setSize(const QSizeF &size);
    QSizeF nativeSize() const;
    virtual QRectF boundingRect() const;
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);

signals:
    void nativeSizeChanged(const QSizeF &size);

protected:
    virtual void timerEvent(QTimerEvent *event);
    virtual QVariant itemChange(QGraphicsItem::GraphicsItemChange change, const QVariant &value);
    virtual bool setMediaObject(QMediaObject *object);

public:
%If (Qt_5_15_0 -)
    QAbstractVideoSurface *videoSurface() const;
%End
};

%ModuleCode
// This is needed by the %ConvertToSubClassCode.
#include <QGraphicsVideoItem>
%End
