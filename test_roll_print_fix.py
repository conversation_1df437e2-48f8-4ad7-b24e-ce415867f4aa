#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة الطباعة للرول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🔧 اختبار إصلاح مشكلة الطباعة للرول...")
    print("=" * 60)
    print("❌ المشكلة السابقة:")
    print("   • خطأ في setPaperSize للرول")
    print("   • arguments did not match any overloaded call")
    print("   • setPaperSize(self, a0: QPagedPaintDevice.PageSize)")
    print("   • too many arguments")
    print("   • argument 1 has unexpected type 'int'")
    print("")
    print("🔧 الإصلاح المطبق:")
    print("   ✅ استخدام QSizeF لتحديد حجم الورق")
    print("   ✅ تمرير المعاملات بالطريقة الصحيحة:")
    print("      • paper_size = QSizeF(80, 200)  # عرض × طول")
    print("      • printer.setPaperSize(paper_size, QPrinter.Millimeter)")
    print("   ✅ إصلاح كل من دالة الحفظ والطباعة")
    print("   ✅ الحفاظ على نفس الأبعاد (80mm × 200mm)")
    print("=" * 60)
    print("📋 التفاصيل التقنية:")
    print("   قبل الإصلاح:")
    print("     ❌ printer.setPaperSize(80, 200, QPrinter.Millimeter)")
    print("     ❌ تمرير 3 معاملات منفصلة")
    print("     ❌ النوع الأول int غير متوقع")
    print("")
    print("   بعد الإصلاح:")
    print("     ✅ paper_size = QSizeF(80, 200)")
    print("     ✅ printer.setPaperSize(paper_size, QPrinter.Millimeter)")
    print("     ✅ تمرير معاملين: QSizeF + وحدة القياس")
    print("     ✅ النوع الأول QSizeF كما هو متوقع")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع الإصلاح...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 اختبر الآن:")
        print("   1️⃣ اختر نوع الطباعة: 'رول'")
        print("   2️⃣ اضغط 'حفظ PDF' - يجب أن يعمل بدون خطأ")
        print("   3️⃣ اضغط 'طباعة' - يجب أن يعمل بدون خطأ")
        print("   4️⃣ تحقق من عدم ظهور رسالة الخطأ")
        print("   5️⃣ تأكد من جودة الطباعة والحفظ")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة الوضع:")
    print("   قبل الإصلاح:")
    print("     ❌ خطأ عند اختيار الرول")
    print("     ❌ لا يمكن الحفظ أو الطباعة")
    print("     ❌ رسالة خطأ تقنية معقدة")
    print("     ❌ تجربة مستخدم سيئة")
    print("")
    print("   بعد الإصلاح:")
    print("     ✅ الرول يعمل بدون مشاكل")
    print("     ✅ الحفظ والطباعة يعملان")
    print("     ✅ لا توجد رسائل خطأ")
    print("     ✅ تجربة مستخدم سلسة")
    print("")
    print("🎯 الفوائد:")
    print("   ✅ إصلاح مشكلة تقنية مهمة")
    print("   ✅ استقرار النظام")
    print("   ✅ موثوقية الطباعة")
    print("   ✅ سهولة الاستخدام")
    print("   ✅ عدم انقطاع العمل")

if __name__ == "__main__":
    main()
