// qwebengineurlrequestjob.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_6_0 -)

class QWebEngineUrlRequestJob : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebengineurlrequestjob.h>
%End

public:
    virtual ~QWebEngineUrlRequestJob();

    enum Error
    {
        NoError,
        UrlNotFound,
        UrlInvalid,
        RequestAborted,
        RequestDenied,
        RequestFailed,
    };

    QUrl requestUrl() const;
    QByteArray requestMethod() const;
    void reply(const QByteArray &contentType, QIODevice *device);
    void fail(QWebEngineUrlRequestJob::Error error);
    void redirect(const QUrl &url);
%If (QtWebEngine_5_11_0 -)
    QUrl initiator() const;
%End
%If (QtWebEngine_5_13_0 -)
    QMap<QByteArray, QByteArray> requestHeaders() const;
%End
};

%End
