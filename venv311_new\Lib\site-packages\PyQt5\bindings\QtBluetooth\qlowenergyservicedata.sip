// qlowenergyservicedata.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_7_0 -)

class QLowEnergyServiceData
{
%TypeHeaderCode
#include <qlowenergyservicedata.h>
%End

public:
    QLowEnergyServiceData();
    QLowEnergyServiceData(const QLowEnergyServiceData &other);
    ~QLowEnergyServiceData();

    enum ServiceType
    {
        ServiceTypePrimary,
        ServiceTypeSecondary,
    };

    QLowEnergyServiceData::ServiceType type() const;
    void setType(QLowEnergyServiceData::ServiceType type);
    QBluetoothUuid uuid() const;
    void setUuid(const QBluetoothUuid &uuid);
    QList<QLowEnergyService *> includedServices() const;
    void setIncludedServices(const QList<QLowEnergyService *> &services);
    void addIncludedService(QLowEnergyService *service);
    QList<QLowEnergyCharacteristicData> characteristics() const;
    void setCharacteristics(const QList<QLowEnergyCharacteristicData> &characteristics);
    void addCharacteristic(const QLowEnergyCharacteristicData &characteristic);
    bool isValid() const;
    void swap(QLowEnergyServiceData &other);
};

%End
%If (Qt_5_7_0 -)
bool operator==(const QLowEnergyServiceData &sd1, const QLowEnergyServiceData &sd2);
%End
%If (Qt_5_7_0 -)
bool operator!=(const QLowEnergyServiceData &sd1, const QLowEnergyServiceData &sd2);
%End
