#!/usr/bin/env python3
"""
اختبار تحسينات طباعة الرول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار تحسينات طباعة الرول...")
    print("=" * 60)
    print("🚀 التحسينات المطبقة على الرول:")
    print("   • ترويسة محسنة بارتفاع 120px")
    print("   • لوجو مخصص بحجم 60x60 (مناسب للرول)")
    print("   • اسم الشركة كامل مع تقسيم ذكي")
    print("   • عنوان مختصر (32 حرف + ...)")
    print("   • رقم الهاتف كامل")
    print("   • بريد إلكتروني مختصر (17 حرف + ...)")
    print("   • رقم ضريبي مختصر (9 أرقام + ...)")
    print("   • خطوط مناسبة للرول:")
    print("     - اسم الشركة: 11px غامق")
    print("     - التفاصيل: 8px")
    print("   • تدرج لوني أخضر جميل")
    print("   • محاذاة مناسبة للمساحة الضيقة")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع طباعة الرول المحسنة...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من طباعة الرول:")
        print("   • اختر نوع الطابعة: 'رول'")
        print("   • لاحظ الترويسة المحسنة")
        print("   • تحقق من وجود جميع التفاصيل:")
        print("     ✓ لوجو الشركة")
        print("     ✓ اسم الشركة كامل")
        print("     ✓ عنوان مختصر")
        print("     ✓ رقم الهاتف")
        print("     ✓ بريد إلكتروني")
        print("     ✓ رقم ضريبي")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 مقارنة الرول قبل وبعد:")
    print("   قبل التحسين:")
    print("     ❌ ترويسة بسيطة 40px")
    print("     ❌ اسم الشركة فقط")
    print("     ❌ لا يوجد عنوان أو تفاصيل")
    print("     ❌ لا يوجد رقم ضريبي")
    print("     ❌ لا يوجد لوجو مخصص")
    print("")
    print("   بعد التحسين:")
    print("     ✅ ترويسة احترافية 120px")
    print("     ✅ لوجو مخصص 60x60")
    print("     ✅ اسم الشركة كامل")
    print("     ✅ عنوان مختصر")
    print("     ✅ رقم الهاتف")
    print("     ✅ بريد إلكتروني")
    print("     ✅ رقم ضريبي")
    print("     ✅ تدرج لوني جميل")
    print("     ✅ مناسب للمساحة الضيقة")

if __name__ == "__main__":
    main()
