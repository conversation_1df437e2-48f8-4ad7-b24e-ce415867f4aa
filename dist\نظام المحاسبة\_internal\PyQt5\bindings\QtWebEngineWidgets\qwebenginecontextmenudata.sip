// qwebenginecontextmenudata.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_7_0 -)

class QWebEngineContextMenuData
{
%TypeHeaderCode
#include <qwebenginecontextmenudata.h>
%End

public:
    QWebEngineContextMenuData();
    QWebEngineContextMenuData(const QWebEngineContextMenuData &other);
    ~QWebEngineContextMenuData();

    enum MediaType
    {
        MediaTypeNone,
        MediaTypeImage,
        MediaTypeVideo,
        MediaTypeAudio,
        MediaTypeCanvas,
        MediaTypeFile,
        MediaTypePlugin,
    };

    bool isValid() const;
    QPoint position() const;
    QString selectedText() const;
    QString linkText() const;
    QUrl linkUrl() const;
    QUrl mediaUrl() const;
    QWebEngineContextMenuData::MediaType mediaType() const;
    bool isContentEditable() const;
%If (QtWebEngine_5_8_0 -)
    QString misspelledWord() const;
%End
%If (QtWebEngine_5_8_0 -)
    QStringList spellCheckerSuggestions() const;
%End
%If (QtWebEngine_5_11_0 -)

    enum MediaFlag
    {
        MediaInError,
        MediaPaused,
        MediaMuted,
        MediaLoop,
        MediaCanSave,
        MediaHasAudio,
        MediaCanToggleControls,
        MediaControls,
        MediaCanPrint,
        MediaCanRotate,
    };

%End
%If (QtWebEngine_5_11_0 -)
    typedef QFlags<QWebEngineContextMenuData::MediaFlag> MediaFlags;
%End
%If (QtWebEngine_5_11_0 -)

    enum EditFlag
    {
        CanUndo,
        CanRedo,
        CanCut,
        CanCopy,
        CanPaste,
        CanDelete,
        CanSelectAll,
        CanTranslate,
        CanEditRichly,
    };

%End
%If (QtWebEngine_5_11_0 -)
    typedef QFlags<QWebEngineContextMenuData::EditFlag> EditFlags;
%End
%If (QtWebEngine_5_11_0 -)
    QWebEngineContextMenuData::MediaFlags mediaFlags() const;
%End
%If (QtWebEngine_5_11_0 -)
    QWebEngineContextMenuData::EditFlags editFlags() const;
%End
};

%End
%If (QtWebEngine_5_11_0 -)
QFlags<QWebEngineContextMenuData::MediaFlag> operator|(QWebEngineContextMenuData::MediaFlag f1, QFlags<QWebEngineContextMenuData::MediaFlag> f2);
%End
%If (QtWebEngine_5_11_0 -)
QFlags<QWebEngineContextMenuData::EditFlag> operator|(QWebEngineContextMenuData::EditFlag f1, QFlags<QWebEngineContextMenuData::EditFlag> f2);
%End
