#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التراخيص
Test License System
"""

import tkinter as tk
from tkinter import messagebox
from license_ui import check_license_and_show_dialog
from license_manager import LicenseManager

class TestApp:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("🧪 اختبار نظام التراخيص")
        self.window.geometry("500x400")
        self.window.configure(bg='#f8f9fa')
        
        self.license_manager = LicenseManager()
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(
            self.window,
            text="🧪 اختبار نظام التراخيص",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # معلومات الجهاز
        info_frame = tk.LabelFrame(
            self.window,
            text="معلومات الجهاز",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#34495e',
            padx=20,
            pady=15
        )
        info_frame.pack(pady=20, padx=30, fill='x')
        
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        tk.Label(
            info_frame,
            text=f"🔑 كود العميل: {customer_code}",
            font=("Arial", 11),
            bg='#f8f9fa',
            fg='#2c3e50'
        ).pack(anchor='w', pady=5)
        
        tk.Label(
            info_frame,
            text=f"💻 رقم الجهاز: {machine_id}",
            font=("Arial", 11),
            bg='#f8f9fa',
            fg='#2c3e50'
        ).pack(anchor='w', pady=5)
        
        # حالة الترخيص
        status_frame = tk.LabelFrame(
            self.window,
            text="حالة الترخيص",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#34495e',
            padx=20,
            pady=15
        )
        status_frame.pack(pady=20, padx=30, fill='x')
        
        self.status_label = tk.Label(
            status_frame,
            text="جاري فحص الترخيص...",
            font=("Arial", 11),
            bg='#f8f9fa',
            fg='#7f8c8d',
            wraplength=400,
            justify='right'
        )
        self.status_label.pack(pady=10)
        
        # الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        # زر فحص الترخيص
        check_btn = tk.Button(
            buttons_frame,
            text="🔍 فحص الترخيص",
            font=("Arial", 12, "bold"),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=8,
            command=self.check_license
        )
        check_btn.pack(side='left', padx=10)
        
        # زر تجديد الترخيص
        renew_btn = tk.Button(
            buttons_frame,
            text="🔄 تجديد الترخيص",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=20,
            pady=8,
            command=self.renew_license
        )
        renew_btn.pack(side='left', padx=10)
        
        # زر حذف الترخيص (للاختبار)
        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف الترخيص",
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=8,
            command=self.delete_license
        )
        delete_btn.pack(side='left', padx=10)
        
        # زر إنشاء ترخيص تجريبي
        trial_btn = tk.Button(
            buttons_frame,
            text="⏰ ترخيص تجريبي",
            font=("Arial", 12, "bold"),
            bg='#f39c12',
            fg='white',
            padx=20,
            pady=8,
            command=self.create_trial
        )
        trial_btn.pack(side='left', padx=10)
        
        # فحص أولي للترخيص
        self.check_license()
    
    def check_license(self):
        """فحص حالة الترخيص"""
        status = self.license_manager.check_license()
        
        if status["valid"]:
            expiry_date = status["expiry_date"].strftime("%d/%m/%Y")
            days_remaining = status["days_remaining"]
            
            if days_remaining > 30:
                color = "#27ae60"  # أخضر
                icon = "✅"
            elif days_remaining > 7:
                color = "#f39c12"  # برتقالي
                icon = "⚠️"
            else:
                color = "#e74c3c"  # أحمر
                icon = "🚨"
            
            status_text = f"{icon} الترخيص صالح\n📅 ينتهي في: {expiry_date}\n⏰ متبقي: {days_remaining} يوم"
            
        else:
            color = "#e74c3c"
            if status["status"] == "NO_LICENSE":
                status_text = "❌ لا يوجد ترخيص\n💡 يمكنك إنشاء ترخيص تجريبي"
            elif status["status"] == "EXPIRED":
                expiry_date = status.get("expiry_date")
                if expiry_date:
                    expiry_str = expiry_date.strftime("%d/%m/%Y")
                    status_text = f"⏰ انتهت الصلاحية\n📅 انتهى في: {expiry_str}\n🔄 يحتاج تجديد"
                else:
                    status_text = "⏰ انتهت الصلاحية\n🔄 يحتاج تجديد"
            elif status["status"] == "MACHINE_MISMATCH":
                status_text = "🚫 الترخيص غير صالح لهذا الجهاز"
            else:
                status_text = f"❌ خطأ في الترخيص\n{status['message']}"
        
        self.status_label.config(text=status_text, fg=color)
    
    def renew_license(self):
        """تجديد الترخيص"""
        if check_license_and_show_dialog():
            self.check_license()
            messagebox.showinfo("نجح التجديد", "تم تجديد الترخيص بنجاح!")
        else:
            messagebox.showwarning("فشل التجديد", "لم يتم تجديد الترخيص")
    
    def delete_license(self):
        """حذف الترخيص (للاختبار فقط)"""
        import os
        
        response = messagebox.askyesno(
            "تأكيد الحذف",
            "هل أنت متأكد من حذف الترخيص؟\n(هذا للاختبار فقط)"
        )
        
        if response:
            try:
                if os.path.exists(self.license_manager.license_file):
                    os.remove(self.license_manager.license_file)
                    messagebox.showinfo("تم الحذف", "تم حذف الترخيص")
                    self.check_license()
                else:
                    messagebox.showwarning("تحذير", "لا يوجد ترخيص للحذف")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الترخيص: {e}")
    
    def create_trial(self):
        """إنشاء ترخيص تجريبي"""
        try:
            # حذف الترخيص الحالي إن وجد
            import os
            if os.path.exists(self.license_manager.license_file):
                os.remove(self.license_manager.license_file)
            
            # إنشاء ترخيص تجريبي لمدة 30 يوم
            license_data = self.license_manager.create_initial_license(days=30)
            
            messagebox.showinfo(
                "ترخيص تجريبي",
                "تم إنشاء ترخيص تجريبي لمدة 30 يوم"
            )
            
            self.check_license()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء الترخيص التجريبي: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        self.window.mainloop()

# مثال على تطبيق رئيسي يستخدم نظام التراخيص
class MainApp:
    def __init__(self):
        # فحص الترخيص أولاً
        if not check_license_and_show_dialog():
            print("لم يتم تفعيل الترخيص - إغلاق البرنامج")
            return
        
        # إذا كان الترخيص صالح، تشغيل البرنامج الرئيسي
        self.window = tk.Tk()
        self.window.title("💼 البرنامج الرئيسي")
        self.window.geometry("400x300")
        self.window.configure(bg='#ecf0f1')
        
        # العنوان
        title_label = tk.Label(
            self.window,
            text="🎉 مرحباً بك في البرنامج!",
            font=("Arial", 16, "bold"),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        title_label.pack(pady=50)
        
        # رسالة النجاح
        success_label = tk.Label(
            self.window,
            text="✅ تم التحقق من الترخيص بنجاح\n🚀 البرنامج جاهز للاستخدام",
            font=("Arial", 12),
            bg='#ecf0f1',
            fg='#27ae60',
            justify='center'
        )
        success_label.pack(pady=20)
        
        # معلومات الترخيص
        license_manager = LicenseManager()
        status = license_manager.check_license()
        
        if status["valid"]:
            expiry_date = status["expiry_date"].strftime("%d/%m/%Y")
            days_remaining = status["days_remaining"]
            
            info_text = f"📅 الترخيص صالح حتى: {expiry_date}\n⏰ متبقي: {days_remaining} يوم"
            
            info_label = tk.Label(
                self.window,
                text=info_text,
                font=("Arial", 10),
                bg='#ecf0f1',
                fg='#7f8c8d',
                justify='center'
            )
            info_label.pack(pady=20)
        
        # زر إغلاق
        close_btn = tk.Button(
            self.window,
            text="إغلاق",
            font=("Arial", 12),
            bg='#95a5a6',
            fg='white',
            padx=30,
            pady=10,
            command=self.window.quit
        )
        close_btn.pack(pady=30)
    
    def run(self):
        """تشغيل التطبيق"""
        if hasattr(self, 'window'):
            self.window.mainloop()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # تشغيل تطبيق الاختبار
        test_app = TestApp()
        test_app.run()
    else:
        # تشغيل التطبيق الرئيسي مع فحص الترخيص
        main_app = MainApp()
        main_app.run()
