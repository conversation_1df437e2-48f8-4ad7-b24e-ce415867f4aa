from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFrame,
                           QLabel, QPushButton, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPainter, QColor, QFont
from PyQt5.QtChart import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta
from .reports import FinancialAnalysisDialog
from database.models import Transaction, Product, Customer, Supplier, TransactionType, TransactionItem
from utils.theme_manager import theme_manager

class DashboardWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.start_auto_refresh()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # العنوان
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2C3E50, stop:1 #3498DB);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        title_layout = QHBoxLayout()
        title_frame.setLayout(title_layout)

        welcome_label = QLabel("لوحة المعلومات")
        welcome_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        date_label = QLabel(datetime.now().strftime("%Y-%m-%d"))
        date_label.setStyleSheet("color: white; font-size: 16px;")

        title_layout.addWidget(welcome_label)
        title_layout.addStretch()
        title_layout.addWidget(date_label)

        layout.addWidget(title_frame)

        # إحصائيات سريعة
        stats_layout = QGridLayout()
        self.add_stat_card("المبيعات اليوم", "sales_today", "#2ECC71", stats_layout, 0, 0)
        self.add_stat_card("المشتريات اليوم", "purchases_today", "#E74C3C", stats_layout, 0, 1)
        self.add_stat_card("العملاء النشطين", "active_customers", "#3498DB", stats_layout, 0, 2)
        self.add_stat_card("المنتجات منخفضة المخزون", "low_stock", "#F1C40F", stats_layout, 0, 3)
        layout.addLayout(stats_layout)

        # الرسوم البيانية
        charts_layout = QHBoxLayout()
        
        # مخطط المبيعات والمشتريات
        self.sales_chart = self.create_sales_chart()
        charts_layout.addWidget(self.sales_chart)
        
        # مخطط المنتجات الأكثر مبيعاً
        self.products_chart = self.create_top_products_chart()
        charts_layout.addWidget(self.products_chart)
        
        layout.addLayout(charts_layout)

        # التنبيهات والإشعارات
        self.alerts_frame = QFrame()
        self.alerts_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        alerts_layout = QVBoxLayout()
        self.alerts_frame.setLayout(alerts_layout)
        
        alerts_title = QLabel("التنبيهات والإشعارات")
        alerts_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        alerts_layout.addWidget(alerts_title)
        
        self.alerts_list = QVBoxLayout()
        alerts_layout.addLayout(self.alerts_list)
        
        layout.addWidget(self.alerts_frame)

    def add_stat_card(self, title, stat_type, color, layout, row, col):
        card = QFrame()
        card.setProperty("class", "stat_card")
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 15px;
                padding: 25px;
                min-height: 150px;
            }}
        """)
        card_layout = QVBoxLayout()
        card.setLayout(card_layout)

        title_label = QLabel(title)
        title_label.setProperty("class", "card_title")
        title_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")

        value_label = QLabel()
        value_label.setProperty("class", "card_value")
        value_label.setStyleSheet("color: white; font-size: 36px; font-weight: bold;")
        
        if stat_type == "sales_today":
            value = self.get_today_sales()
        elif stat_type == "purchases_today":
            value = self.get_today_purchases()
        elif stat_type == "active_customers":
            value = self.get_active_customers()
        else:
            value = self.get_low_stock_count()
            
        value_label.setText(str(value))
        
        card_layout.addWidget(title_label)
        card_layout.addWidget(value_label)
        layout.addWidget(card, row, col)

    def create_sales_chart(self):
        chart = QChart()
        chart.setTitle("المبيعات والمشتريات آخر 7 أيام")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        sales_set = QBarSet("المبيعات")
        purchases_set = QBarSet("المشتريات")
        
        # جمع البيانات
        with Session(self.engine) as session:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            sales_data = session.query(
                func.date(Transaction.date),
                func.sum(Transaction.total_amount)
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).group_by(func.date(Transaction.date)).all()
            
            purchases_data = session.query(
                func.date(Transaction.date),
                func.sum(Transaction.total_amount)
            ).filter(
                Transaction.type == TransactionType.PURCHASE,
                Transaction.date.between(start_date, end_date)
            ).group_by(func.date(Transaction.date)).all()
            
            dates = [(end_date - timedelta(days=i)).strftime("%m-%d") for i in range(7)]
            sales_values = [0] * 7
            purchases_values = [0] * 7
            
            for date, amount in sales_data:
                day_diff = (end_date.date() - date).days
                if 0 <= day_diff < 7:
                    sales_values[day_diff] = float(amount)
                    
            for date, amount in purchases_data:
                day_diff = (end_date.date() - date).days
                if 0 <= day_diff < 7:
                    purchases_values[day_diff] = float(amount)
            
            sales_set.append(sales_values)
            purchases_set.append(purchases_values)

        series = QBarSeries()
        series.append(sales_set)
        series.append(purchases_set)
        chart.addSeries(series)

        axis_x = QBarCategoryAxis()
        axis_x.append(dates)
        chart.addAxis(axis_x, Qt.AlignBottom)
        series.attachAxis(axis_x)

        axis_y = QValueAxis()
        chart.addAxis(axis_y, Qt.AlignLeft)
        series.attachAxis(axis_y)

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def create_top_products_chart(self):
        chart = QChart()
        chart.setTitle("المنتجات الأكثر مبيعاً")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        series = QPieSeries()
        
        with Session(self.engine) as session:
            top_products = session.query(
                Product.name,
                func.sum(TransactionItem.quantity).label('total_quantity')
            ).join(TransactionItem).join(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).group_by(Product.name).order_by(
                func.sum(TransactionItem.quantity).desc()
            ).limit(5).all()
            
            for product_name, quantity in top_products:
                series.append(product_name, quantity)

        chart.addSeries(series)
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def get_today_sales(self):
        with Session(self.engine) as session:
            today = datetime.now().date()
            total = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE,
                func.date(Transaction.date) == today
            ).scalar()
            return total or 0

    def get_today_purchases(self):
        with Session(self.engine) as session:
            today = datetime.now().date()
            total = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.PURCHASE,
                func.date(Transaction.date) == today
            ).scalar()
            return total or 0

    def get_active_customers(self):
        with Session(self.engine) as session:
            past_month = datetime.now() - timedelta(days=30)
            count = session.query(func.count(func.distinct(Transaction.customer_id))).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date >= past_month
            ).scalar()
            return count or 0

    def get_low_stock_count(self):
        with Session(self.engine) as session:
            count = session.query(func.count(Product.id)).filter(
                Product.quantity <= Product.min_quantity
            ).scalar()
            return count or 0

    def check_alerts(self):
        alerts = []
        
        # التحقق من المنتجات منخفضة المخزون
        with Session(self.engine) as session:
            low_stock = session.query(Product).filter(
                Product.quantity <= Product.min_quantity,
                Product.min_quantity > 0  # فقط المنتجات التي لها حد أدنى محدد
            ).all()

            for product in low_stock:
                alerts.append({
                    'type': 'warning',
                    'message': f"المنتج {product.name} منخفض المخزون ({product.quantity} متبقي من أصل {product.min_quantity})"
                })
            
            # التحقق من الفواتير المستحقة
            due_invoices = session.query(Transaction).filter(
                Transaction.total_amount > Transaction.paid_amount,
                Transaction.date <= datetime.now() - timedelta(days=30)
            ).all()
            
            for invoice in due_invoices:
                if invoice.customer:
                    alerts.append({
                        'type': 'danger',
                        'message': f"فاتورة مستحقة للعميل {invoice.customer.name} - المبلغ: {invoice.total_amount - invoice.paid_amount:,.2f}"
                    })
        
        # تحديث قائمة التنبيهات
        self.update_alerts_list(alerts)

    def update_alerts_list(self, alerts):
        # مسح التنبيهات القديمة
        for i in reversed(range(self.alerts_list.count())):
            self.alerts_list.itemAt(i).widget().deleteLater()
        
        # إضافة التنبيهات الجديدة
        for alert in alerts:
            alert_frame = QFrame()
            alert_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {'#FFE5E5' if alert['type'] == 'danger' else '#FFF3CD'};
                    border-radius: 5px;
                    padding: 10px;
                    margin-bottom: 5px;
                }}
            """)
            
            alert_layout = QHBoxLayout()
            alert_frame.setLayout(alert_layout)
            
            icon = QLabel("⚠️" if alert['type'] == 'warning' else "⛔")
            message = QLabel(alert['message'])
            
            alert_layout.addWidget(icon)
            alert_layout.addWidget(message)
            alert_layout.addStretch()
            
            self.alerts_list.addWidget(alert_frame)

    def start_auto_refresh(self):
        # تحديث البيانات كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)
        self.timer.start(300000)  # 5 minutes
        
        # التحقق من التنبيهات عند بدء التشغيل
        self.check_alerts()

    def refresh_data(self):
        # تحديث الإحصائيات
        with Session(self.engine) as session:
            # تحديث المخططات
            self.sales_chart.chart().removeAllSeries()
            self.products_chart.chart().removeAllSeries()
            
            # إعادة إنشاء المخططات مع البيانات الجديدة
            self.create_sales_chart()
            self.create_top_products_chart()
            
            # تحديث التنبيهات
            self.check_alerts()

    def update_theme(self):
        """تحديث ألوان الثيم"""
        colors = theme_manager.get_colors()

        # تحديث الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث إطار العنوان
        if hasattr(self, 'title_frame'):
            title_bg = colors['primary'] if theme_manager.is_dark_mode() else "#2C3E50"
            self.title_frame.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {title_bg}, stop:1 {colors['primary']});
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 20px;
                }}
            """)

        # تحديث إطار التنبيهات
        if hasattr(self, 'alerts_frame'):
            self.alerts_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {colors['card_bg']};
                    border: 1px solid {colors['border_color']};
                    border-radius: 10px;
                    padding: 15px;
                }}
            """)

        # تحديث عنوان التنبيهات
        alerts_title = self.alerts_frame.findChild(QLabel)
        if alerts_title:
            alerts_title.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
                color: {colors['primary_text']};
            """)

        # إعادة تحميل البيانات لتطبيق الألوان الجديدة
        self.refresh_data()