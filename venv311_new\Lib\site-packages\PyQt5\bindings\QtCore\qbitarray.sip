// qbitarray.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBitArray
{
%TypeHeaderCode
#include <qbitarray.h>
%End

%TypeCode
// This is needed by __hash__().
#include <qhash.h>
%End

public:
    QBitArray();
    QBitArray(int size, bool value = false);
    QBitArray(const QBitArray &other);
    int size() const;
    int count() const /__len__/;
    int count(bool on) const;
    bool isEmpty() const;
    bool isNull() const;
    void resize(int size);
    void detach();
    bool isDetached() const;
    void clear();
    QBitArray &operator&=(const QBitArray &);
    QBitArray &operator|=(const QBitArray &);
    QBitArray &operator^=(const QBitArray &);
    QBitArray operator~() const;
    bool operator==(const QBitArray &a) const;
    bool operator!=(const QBitArray &a) const;
    void fill(bool val, int first, int last);
    void truncate(int pos);
    bool fill(bool value, int size = -1);
    bool testBit(int i) const;
    void setBit(int i);
    void clearBit(int i);
    void setBit(int i, bool val);
    bool toggleBit(int i);
    bool operator[](int i) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = sipCpp->operator[]((int)idx);
%End

    bool at(int i) const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    void swap(QBitArray &other /Constrained/);
%If (Qt_5_11_0 -)
    SIP_PYOBJECT bits() const /TypeHint="bytes"/;
%MethodCode
        return PyBytes_FromStringAndSize(sipCpp->bits(), (sipCpp->size() + 7) / 8);
%End

%End
%If (Qt_5_11_0 -)
    static QBitArray fromBits(const char *data /Encoding="None"/, Py_ssize_t len) [QBitArray (const char *data, qsizetype len)];
%End
};

QBitArray operator&(const QBitArray &, const QBitArray &);
QBitArray operator|(const QBitArray &, const QBitArray &);
QBitArray operator^(const QBitArray &, const QBitArray &);
QDataStream &operator<<(QDataStream &, const QBitArray & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QBitArray & /Constrained/) /ReleaseGIL/;
