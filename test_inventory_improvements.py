#!/usr/bin/env python3
"""
اختبار تحسينات قسم المخزون
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("📦 اختبار تحسينات قسم المخزون...")
    print("=" * 80)
    print("🔧 التحسينات المطبقة:")
    print("")
    print("   ✅ 1. عمود المكسب الجديد:")
    print("     • إضافة عمود 'المكسب' في جداول المخزون")
    print("     • حساب المكسب = الكمية × سعر البيع")
    print("     • تلوين العمود بلون أخضر فاتح للتمييز")
    print("     • عرض القيم بتنسيق مالي واضح")
    print("")
    print("   ✅ 2. التحديث التلقائي للقيم:")
    print("     • تحديث القيمة الإجمالية عند تغيير الكمية أو سعر الشراء")
    print("     • تحديث المكسب عند تغيير الكمية أو سعر البيع")
    print("     • حساب فوري بدون الحاجة لحفظ")
    print("     • عرض القيم بتنسيق جميل ومميز")
    print("")
    print("   ✅ 3. واجهة محسنة لإضافة/تعديل المنتجات:")
    print("     • حقول عرض للقيمة الإجمالية والمكسب")
    print("     • تحديث فوري عند تغيير أي قيمة")
    print("     • تصميم جميل مع ألوان مميزة")
    print("     • وضوح أكبر في المعلومات المالية")
    print("")
    print("   ✅ 4. تحسينات في الجداول:")
    print("     • إضافة عمود المكسب لجدول المخزون")
    print("     • إضافة عمود المكسب لجدول إدارة الأصناف")
    print("     • تنسيق أفضل للأعمدة")
    print("     • ألوان مميزة للقيم المالية")
    print("=" * 80)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 تشغيل النظام...")
        
        # اختبار الواجهة الرئيسية
        print("🖥️ تشغيل الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.engine = engine
        main_window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار تحسينات المخزون:")
        print("")
        print("   📦 عرض المخزون المحسن:")
        print("     1️⃣ اذهب لقائمة 'المخزون'")
        print("     2️⃣ لاحظ العمود الجديد 'المكسب' في الجدول")
        print("     3️⃣ لاحظ اللون الأخضر الفاتح لعمود المكسب")
        print("     4️⃣ قارن بين 'القيمة' (الكمية × سعر الشراء)")
        print("        و 'المكسب' (الكمية × سعر البيع)")
        print("")
        print("   ➕ إضافة منتج جديد:")
        print("     1️⃣ اضغط '➕ إضافة منتج جديد'")
        print("     2️⃣ أدخل اسم المنتج")
        print("     3️⃣ أدخل سعر الشراء (مثل: 100)")
        print("     4️⃣ أدخل سعر البيع (مثل: 150)")
        print("     5️⃣ أدخل الكمية (مثل: 10)")
        print("     6️⃣ لاحظ التحديث الفوري:")
        print("        • القيمة الإجمالية: 10 × 100 = 1,000.00 ريال")
        print("        • المكسب المتوقع: 10 × 150 = 1,500.00 ريال")
        print("     7️⃣ جرب تغيير الكمية أو الأسعار ولاحظ التحديث الفوري")
        print("")
        print("   ✏️ تعديل منتج موجود:")
        print("     1️⃣ اذهب لتبويب 'إدارة الأصناف'")
        print("     2️⃣ اضغط 'تعديل' لأي منتج")
        print("     3️⃣ لاحظ عرض القيم الحالية:")
        print("        • القيمة الإجمالية محسوبة")
        print("        • المكسب المتوقع محسوب")
        print("     4️⃣ غير الكمية أو الأسعار")
        print("     5️⃣ لاحظ التحديث الفوري للقيم")
        print("")
        print("   🔍 ما تبحث عنه:")
        print("     ✅ في جداول المخزون:")
        print("        • عمود 'المكسب' جديد")
        print("        • لون أخضر فاتح للمكسب")
        print("        • قيم محسوبة بدقة")
        print("        • تنسيق مالي واضح")
        print("     ✅ في نوافذ الإضافة/التعديل:")
        print("        • حقول عرض للقيم المحسوبة")
        print("        • تحديث فوري عند التغيير")
        print("        • ألوان مميزة (أزرق للقيمة، أخضر للمكسب)")
        print("        • تنسيق جميل ومنظم")
        print("")
        print("   💡 أمثلة للاختبار:")
        print("     📝 منتج 1:")
        print("        • الكمية: 50")
        print("        • سعر الشراء: 20.00")
        print("        • سعر البيع: 35.00")
        print("        • القيمة: 50 × 20 = 1,000.00 ريال")
        print("        • المكسب: 50 × 35 = 1,750.00 ريال")
        print("     📝 منتج 2:")
        print("        • الكمية: 100")
        print("        • سعر الشراء: 5.50")
        print("        • سعر البيع: 8.75")
        print("        • القيمة: 100 × 5.50 = 550.00 ريال")
        print("        • المكسب: 100 × 8.75 = 875.00 ريال")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 80)
    print("📊 ملخص التحسينات:")
    print("")
    print("   🔧 ما تم إضافته:")
    print("     ✅ عمود المكسب في جميع جداول المخزون")
    print("     ✅ حساب تلقائي للقيم المالية")
    print("     ✅ تحديث فوري عند تغيير البيانات")
    print("     ✅ واجهة محسنة مع ألوان مميزة")
    print("")
    print("   🎨 النتيجة النهائية:")
    print("     • وضوح أكبر في المعلومات المالية")
    print("     • سهولة في متابعة الأرباح المتوقعة")
    print("     • تحديث فوري للحسابات")
    print("     • تصميم جميل ومنظم")
    print("")
    print("   📋 الفوائد:")
    print("     💰 متابعة أفضل للأرباح")
    print("     📊 حسابات دقيقة وفورية")
    print("     🎯 قرارات مالية مدروسة")
    print("     ⚡ سرعة في العمل")
    print("")
    print("🎉 تم تطبيق جميع التحسينات بنجاح!")
    print("   📱 جرب النظام الآن ولاحظ التحسينات")
    print("   💰 متابعة شاملة للقيم والأرباح")
    print("   🎯 واجهة محسنة وسهلة الاستخدام")

if __name__ == "__main__":
    main()
