#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث معلومات التواصل
"""

import os

def test_contact_info():
    """اختبار معلومات التواصل في الملفات"""
    print("🧪 اختبار معلومات التواصل...")
    print("=" * 50)
    
    # الإيميل المطلوب
    target_email = "<EMAIL>"
    
    # الملفات المراد فحصها
    files_to_check = [
        "license_ui.py",
        "gui/main_window.py", 
        "main.py",
        "quick_start.py",
        "run_license_demo.py"
    ]
    
    found_files = []
    missing_files = []
    
    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if target_email in content:
                        found_files.append(filename)
                        print(f"✅ {filename} - يحتوي على {target_email}")
                    else:
                        missing_files.append(filename)
                        print(f"⚠️ {filename} - لا يحتوي على الإيميل الجديد")
            except Exception as e:
                print(f"❌ خطأ في قراءة {filename}: {e}")
        else:
            print(f"❌ الملف غير موجود: {filename}")
    
    print("\n" + "=" * 50)
    print("📊 النتائج:")
    print(f"✅ ملفات محدثة: {len(found_files)}")
    print(f"⚠️ ملفات تحتاج تحديث: {len(missing_files)}")
    
    if found_files:
        print(f"\n✅ الملفات المحدثة:")
        for f in found_files:
            print(f"   • {f}")
    
    if missing_files:
        print(f"\n⚠️ الملفات التي تحتاج تحديث:")
        for f in missing_files:
            print(f"   • {f}")
    
    # فحص محتوى license_ui.py بالتفصيل
    print("\n🔍 فحص تفصيلي لـ license_ui.py:")
    if os.path.exists("license_ui.py"):
        with open("license_ui.py", 'r', encoding='utf-8') as f:
            content = f.read()
            
        # البحث عن أجزاء معينة
        if "<EMAIL>" in content:
            print("   ✅ يحتوي على الإيميل الجديد")
        
        if "mailto:<EMAIL>" in content:
            print("   ✅ يحتوي على رابط الإيميل")
        
        if "أرسل إيميل يحتوي على" in content:
            print("   ✅ يحتوي على تعليمات الإرسال")
        
        if "كود العميل ورقم الجهاز" in content:
            print("   ✅ يحتوي على تعليمات البيانات المطلوبة")
    
    return len(found_files) > 0

def show_contact_summary():
    """عرض ملخص معلومات التواصل"""
    print("\n" + "=" * 60)
    print("📧 ملخص معلومات التواصل المحدثة")
    print("=" * 60)
    
    print("📧 الإيميل: <EMAIL>")
    print("\n📋 ما يرسله العميل:")
    print("   • كود العميل")
    print("   • رقم الجهاز") 
    print("   • إثبات الدفع")
    print("   • رقم الهاتف للتواصل")
    
    print("\n🔄 سير العمل:")
    print("   1. العميل يرسل إيميل مع البيانات المطلوبة")
    print("   2. المطور يتحقق من الدفع")
    print("   3. المطور يولد كود التجديد")
    print("   4. المطور يرسل الكود للعميل")
    print("   5. العميل يدخل الكود في البرنامج")
    
    print("\n💡 نصائح للمطور:")
    print("   • رد سريع على الإيميلات")
    print("   • تأكد من صحة البيانات المرسلة")
    print("   • احفظ سجل بجميع العملاء والأكواد")
    print("   • قدم دعم فني ممتاز")

def main():
    """الدالة الرئيسية"""
    print("📧 اختبار تحديث معلومات التواصل")
    print("=" * 60)
    
    # اختبار معلومات التواصل
    success = test_contact_info()
    
    # عرض الملخص
    show_contact_summary()
    
    if success:
        print("\n🎉 تم تحديث معلومات التواصل بنجاح!")
        print("📧 الإيميل الجديد: <EMAIL>")
    else:
        print("\n⚠️ تحتاج بعض الملفات لتحديث معلومات التواصل")

if __name__ == "__main__":
    main()
