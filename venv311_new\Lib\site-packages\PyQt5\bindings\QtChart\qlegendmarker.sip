// qlegendmarker.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_2_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qlegendmarker.h>
%End

    class QLegendMarker : public QObject /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qlegendmarker.h>
%End

    public:
        enum LegendMarkerType
        {
            LegendMarkerTypeArea,
            LegendMarkerTypeBar,
            LegendMarkerTypePie,
            LegendMarkerTypeXY,
%If (QtChart_1_3_0 -)
            LegendMarkerTypeBoxPlot,
%End
%If (QtChart_5_8_0 -)
            LegendMarkerTypeCandlestick,
%End
        };

        virtual ~QLegendMarker();
        virtual QtCharts::QLegendMarker::LegendMarkerType type() = 0;
        QString label() const;
        void setLabel(const QString &label);
        QBrush labelBrush() const;
        void setLabelBrush(const QBrush &brush);
        QFont font() const;
        void setFont(const QFont &font);
        QPen pen() const;
        void setPen(const QPen &pen);
        QBrush brush() const;
        void setBrush(const QBrush &brush);
        bool isVisible() const;
        void setVisible(bool visible);
        virtual QtCharts::QAbstractSeries *series() = 0;

    signals:
        void clicked();
        void hovered(bool status);
        void labelChanged();
        void labelBrushChanged();
        void fontChanged();
        void penChanged();
        void brushChanged();
        void visibleChanged();

    public:
%If (QtChart_5_9_0 -)
        QtCharts::QLegend::MarkerShape shape() const;
%End
%If (QtChart_5_9_0 -)
        void setShape(QtCharts::QLegend::MarkerShape shape);
%End

    signals:
%If (QtChart_5_9_0 -)
        void shapeChanged();
%End
    };
};

%End
