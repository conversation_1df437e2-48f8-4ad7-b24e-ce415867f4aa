#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام توزيع الخصم النسبي على المرتجعات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine

def main():
    print("🧾 اختبار نظام توزيع الخصم النسبي على المرتجعات")
    print("=" * 70)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    print("🔧 التحسينات الجديدة:")
    print("")
    print("   ✅ توزيع الخصم النسبي:")
    print("     • حساب نسبة الخصم من الفاتورة الأصلية")
    print("     • تطبيق نفس النسبة على كل منتج مرتجع")
    print("     • عرض تفاصيل الخصم في ملخص المرتجع")
    print("     • حفظ معلومات الخصم في فاتورة المرتجع")
    print("")
    print("   📊 مثال توضيحي:")
    print("     الفاتورة الأصلية:")
    print("     • منتج أ: 800 ريال")
    print("     • منتج ب: 200 ريال")
    print("     • المجموع: 1000 ريال")
    print("     • خصم: 100 ريال (10%)")
    print("     • النهائي: 900 ريال")
    print("")
    print("     عند مرتجع منتج أ فقط:")
    print("     • سعر منتج أ: 800 ريال")
    print("     • خصم منتج أ: 800 × 10% = 80 ريال")
    print("     • المبلغ المسترد: 800 - 80 = 720 ريال ✅")
    print("")
    print("   🎯 المميزات:")
    print("     ✅ عدالة في توزيع الخصم")
    print("     ✅ دقة محاسبية عالية")
    print("     ✅ شفافية في العرض")
    print("     ✅ يعمل مع مرتجع المبيعات والمشتريات")
    print("")
    
    try:
        print("🚀 فتح النافذة الرئيسية...")
        
        # استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine)
        window.show()
        
        print("✅ تم فتح النافذة الرئيسية!")
        print("")
        print("🎯 لاختبار النظام الجديد:")
        print("")
        print("   📋 إنشاء فاتورة بخصم:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'فاتورة جديدة'")
        print("     2️⃣ أضف منتجات متعددة بأسعار مختلفة")
        print("     3️⃣ أضف خصم (مبلغ أو نسبة)")
        print("     4️⃣ احفظ الفاتورة")
        print("")
        print("   🔄 اختبار المرتجع:")
        print("     1️⃣ اذهب لقائمة 'المبيعات' ← 'مرتجع المبيعات'")
        print("     2️⃣ ابحث عن الفاتورة التي أنشأتها")
        print("     3️⃣ اختر منتجات للمرتجع")
        print("     4️⃣ لاحظ حساب الخصم النسبي:")
        print("        • المبلغ قبل الخصم")
        print("        • مبلغ الخصم المحسوب")
        print("        • المبلغ النهائي للاسترداد")
        print("")
        print("   🖨️ طباعة المرتجع:")
        print("     1️⃣ احفظ المرتجع")
        print("     2️⃣ اطبع فاتورة المرتجع")
        print("     3️⃣ لاحظ عرض معلومات الخصم في الطباعة")
        print("")
        print("   📊 مقارنة النتائج:")
        print("     • قارن المبلغ المسترد مع الحساب اليدوي")
        print("     • تأكد من صحة النسبة المطبقة")
        print("     • تحقق من عدالة التوزيع")
        print("")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 70)
    print("📊 ملخص النظام الجديد:")
    print("")
    print("   🔧 ما تم تطبيقه:")
    print("     ✅ حساب الخصم النسبي للمرتجعات")
    print("     ✅ عرض تفاصيل الخصم في الواجهة")
    print("     ✅ حفظ معلومات الخصم في قاعدة البيانات")
    print("     ✅ تطبيق النظام على مرتجع المبيعات والمشتريات")
    print("")
    print("   💡 الفوائد:")
    print("     • عدالة في حساب المرتجعات")
    print("     • دقة محاسبية عالية")
    print("     • شفافية كاملة للعميل")
    print("     • سهولة في المراجعة والتدقيق")
    print("")
    print("🎉 النظام جاهز للاستخدام!")

if __name__ == "__main__":
    main()
