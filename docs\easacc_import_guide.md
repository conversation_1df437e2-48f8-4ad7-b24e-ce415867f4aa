# دليل استيراد البيانات من برنامج EasAcc

## 🎯 نظرة عامة

أداة استيراد البيانات من برنامج EasAcc تتيح لك نقل بياناتك من النظام القديم إلى النظام الجديد بسهولة وأمان.

## 🔧 المتطلبات

### للاستيراد من قواعد بيانات Access:
```bash
pip install pyodbc
```

### للاستيراد من Excel/CSV:
- لا توجد متطلبات إضافية (مدعوم افتراضياً)

## 📂 أنواع الملفات المدعومة

| النوع | الامتداد | الوصف |
|-------|----------|-------|
| Microsoft Access | .mdb, .accdb | قاعدة بيانات EasAcc الأصلية |
| Excel | .xlsx, .xls | ملفات Excel المصدرة |
| CSV | .csv | ملفات نصية مفصولة بفواصل |

## 🚀 خطوات الاستيراد

### 1. فتح أداة الاستيراد
- اذهب إلى قائمة **"ملف"**
- اختر **"📥 استيراد من برنامج EasAcc"**

### 2. اختيار نوع قاعدة البيانات
- حدد نوع الملف من القائمة المنسدلة
- اختر المسار للملف

### 3. الاتصال بقاعدة البيانات
- اضغط **"🔗 الاتصال بقاعدة البيانات"**
- انتظر تحميل البيانات

### 4. مراجعة البيانات
- راجع البيانات في التبويبات المختلفة:
  - 👥 العملاء
  - 🏢 الموردين  
  - 📦 المنتجات

### 5. تحديد البيانات للاستيراد
- حدد/ألغ تحديد البيانات المطلوبة
- استخدم "تحديد جميع..." لتحديد الكل

### 6. بدء الاستيراد
- اضغط **"📥 استيراد البيانات المحددة"**
- راقب التقدم في سجل الأحداث

## 🔍 البحث التلقائي عن الجداول

الأداة تبحث تلقائياً عن الجداول التالية في قاعدة بيانات EasAcc:

### العملاء:
- `Customers`, `Customer`
- `Clients`, `Client`
- `العملاء`

### الموردين:
- `Suppliers`, `Supplier`
- `Vendors`, `Vendor`
- `الموردين`

### المنتجات:
- `Products`, `Product`
- `Items`, `Item`
- `الاصناف`, `المنتجات`

## 📊 تحويل الحقول

### العملاء:
| EasAcc | النظام الجديد |
|--------|---------------|
| Name, CustomerName | name |
| Phone, Tel, Mobile | phone |
| Address | address |
| Email, E_Mail | email |
| Balance | balance |
| Credit_Limit | credit_limit |

### الموردين:
| EasAcc | النظام الجديد |
|--------|---------------|
| Name, SupplierName | name |
| Phone, Tel, Mobile | phone |
| Address | address |
| Email, E_Mail | email |
| Balance | balance |

### المنتجات:
| EasAcc | النظام الجديد |
|--------|---------------|
| Name, ProductName | name |
| Code, ProductCode | code |
| Description | description |
| Category | category |
| Purchase_Price, Cost | purchase_price |
| Sale_Price, Price | sale_price |
| Quantity, Stock | quantity |
| Unit | unit |
| Barcode | barcode |

## ⚠️ تنبيهات مهمة

### 1. النسخ الاحتياطي
- **اعمل نسخة احتياطية** قبل الاستيراد
- يمكن التراجع في حالة حدوث مشاكل

### 2. التحقق من التكرار
- الأداة تتحقق من وجود بيانات مكررة
- لن يتم استيراد البيانات المكررة

### 3. الجداول غير المدعومة
- سيتم تجاهل الجداول غير المدعومة
- لن تؤثر على عملية الاستيراد

## 🛠️ حل المشاكل الشائعة

### مشكلة: "No module named 'pyodbc'"
**الحل:**
```bash
pip install pyodbc
```
أو استخدم Excel/CSV كبديل

### مشكلة: "فشل الاتصال بقاعدة البيانات"
**الحلول:**
1. تأكد من تثبيت Microsoft Access Database Engine
2. تحقق من صحة مسار الملف
3. أغلق ملف EasAcc إذا كان مفتوحاً

### مشكلة: "لم يتم العثور على جدول..."
**الحل:**
- هذا طبيعي إذا لم يكن الجدول موجوداً
- الأداة ستتجاهله وتكمل

## 📈 نصائح للحصول على أفضل النتائج

### 1. تحضير البيانات
- تأكد من سلامة البيانات في EasAcc
- أغلق جميع البرامج المتصلة بقاعدة البيانات

### 2. استخدام Excel كبديل
إذا واجهت مشاكل مع Access:
1. صدّر البيانات من EasAcc إلى Excel
2. استخدم أداة الاستيراد مع ملفات Excel

### 3. التحقق من النتائج
- راجع البيانات المستوردة
- تأكد من صحة الأرصدة والأسعار

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع سجل الأحداث في الأداة
2. تأكد من اتباع الخطوات بالترتيب
3. جرب استخدام Excel/CSV كبديل

---

**ملاحظة:** هذه الأداة مصممة خصيصاً لبرنامج EasAcc وتدعم معظم إصداراته الشائعة.
