# The PEP 484 type hints stub file for the QtGui module.
#
# Generated by SIP 6.7.7
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., None], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_SHADER_ATTRIBUTE_ARRAY = typing.Union[typing.Sequence['QVector2D'],
        typing.Sequence['QVector3D'], typing.Sequence['QVector4D'],
        typing.Sequence[typing.Sequence[float]]]
PYQT_SHADER_UNIFORM_VALUE_ARRAY = typing.Union[typing.Sequence['QVector2D'],
        typing.Sequence['QVector3D'], typing.Sequence['QVector4D'],
        typing.Sequence['QMatrix2x2'], typing.Sequence['QMatrix2x3'],
        typing.Sequence['QMatrix2x4'], typing.Sequence['QMatrix3x2'],
        typing.Sequence['QMatrix3x3'], typing.Sequence['QMatrix3x4'],
        typing.Sequence['QMatrix4x2'], typing.Sequence['QMatrix4x3'],
        typing.Sequence['QMatrix4x4'], typing.Sequence[typing.Sequence[float]]]


class QAbstractTextDocumentLayout(QtCore.QObject):

    class Selection(PyQt5.sipsimplewrapper):

        cursor = ... # type: 'QTextCursor'
        format = ... # type: 'QTextCharFormat'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QAbstractTextDocumentLayout.Selection') -> None: ...

    class PaintContext(PyQt5.sipsimplewrapper):

        clip = ... # type: QtCore.QRectF
        cursorPosition = ... # type: int
        palette = ... # type: 'QPalette'
        selections = ... # type: typing.Iterable['QAbstractTextDocumentLayout.Selection']

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QAbstractTextDocumentLayout.PaintContext') -> None: ...

    def __init__(self, doc: 'QTextDocument') -> None: ...

    def blockWithMarkerAt(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QTextBlock': ...
    def formatAt(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QTextFormat': ...
    def imageAt(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> str: ...
    def format(self, pos: int) -> 'QTextCharFormat': ...
    def drawInlineObject(self, painter: 'QPainter', rect: QtCore.QRectF, object: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def positionInlineObject(self, item: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def resizeInlineObject(self, item: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def documentChanged(self, from_: int, charsRemoved: int, charsAdded: int) -> None: ...
    updateBlock: typing.ClassVar[QtCore.pyqtSignal]
    pageCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    documentSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    update: typing.ClassVar[QtCore.pyqtSignal]
    def handlerForObject(self, objectType: int) -> 'QTextObjectInterface': ...
    def unregisterHandler(self, objectType: int, component: typing.Optional[QtCore.QObject] = ...) -> None: ...
    def registerHandler(self, objectType: int, component: QtCore.QObject) -> None: ...
    def document(self) -> 'QTextDocument': ...
    def paintDevice(self) -> 'QPaintDevice': ...
    def setPaintDevice(self, device: 'QPaintDevice') -> None: ...
    def blockBoundingRect(self, block: 'QTextBlock') -> QtCore.QRectF: ...
    def frameBoundingRect(self, frame: 'QTextFrame') -> QtCore.QRectF: ...
    def documentSize(self) -> QtCore.QSizeF: ...
    def pageCount(self) -> int: ...
    def anchorAt(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> str: ...
    def hitTest(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], accuracy: QtCore.Qt.HitTestAccuracy) -> int: ...
    def draw(self, painter: 'QPainter', context: 'QAbstractTextDocumentLayout.PaintContext') -> None: ...


class QTextObjectInterface(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextObjectInterface') -> None: ...

    def drawObject(self, painter: 'QPainter', rect: QtCore.QRectF, doc: 'QTextDocument', posInDocument: int, format: 'QTextFormat') -> None: ...
    def intrinsicSize(self, doc: 'QTextDocument', posInDocument: int, format: 'QTextFormat') -> QtCore.QSizeF: ...


class QBackingStore(PyQt5.sipsimplewrapper):

    def __init__(self, window: 'QWindow') -> None: ...

    def hasStaticContents(self) -> bool: ...
    def staticContents(self) -> 'QRegion': ...
    def setStaticContents(self, region: 'QRegion') -> None: ...
    def endPaint(self) -> None: ...
    def beginPaint(self, a0: 'QRegion') -> None: ...
    def scroll(self, area: 'QRegion', dx: int, dy: int) -> bool: ...
    def size(self) -> QtCore.QSize: ...
    def resize(self, size: QtCore.QSize) -> None: ...
    def flush(self, region: 'QRegion', window: typing.Optional['QWindow'] = ..., offset: QtCore.QPoint = ...) -> None: ...
    def paintDevice(self) -> 'QPaintDevice': ...
    def window(self) -> 'QWindow': ...


class QPaintDevice(PyQt5.sipsimplewrapper):

    class PaintDeviceMetric(int):
        PdmWidth = ... # type: QPaintDevice.PaintDeviceMetric
        PdmHeight = ... # type: QPaintDevice.PaintDeviceMetric
        PdmWidthMM = ... # type: QPaintDevice.PaintDeviceMetric
        PdmHeightMM = ... # type: QPaintDevice.PaintDeviceMetric
        PdmNumColors = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDepth = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDpiX = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDpiY = ... # type: QPaintDevice.PaintDeviceMetric
        PdmPhysicalDpiX = ... # type: QPaintDevice.PaintDeviceMetric
        PdmPhysicalDpiY = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatio = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatioScaled = ... # type: QPaintDevice.PaintDeviceMetric

    def __init__(self) -> None: ...

    @staticmethod
    def devicePixelRatioFScale() -> float: ...
    def devicePixelRatioF(self) -> float: ...
    def metric(self, metric: 'QPaintDevice.PaintDeviceMetric') -> int: ...
    def devicePixelRatio(self) -> int: ...
    def colorCount(self) -> int: ...
    def paintingActive(self) -> bool: ...
    def depth(self) -> int: ...
    def physicalDpiY(self) -> int: ...
    def physicalDpiX(self) -> int: ...
    def logicalDpiY(self) -> int: ...
    def logicalDpiX(self) -> int: ...
    def heightMM(self) -> int: ...
    def widthMM(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def paintEngine(self) -> 'QPaintEngine': ...


class QPixmap(QPaintDevice):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, w: int, h: int) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Optional[str] = ..., flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def __init__(self, xpm: typing.List[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixmap') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def setDevicePixelRatio(self, scaleFactor: float) -> None: ...
    def devicePixelRatio(self) -> float: ...
    def swap(self, other: 'QPixmap') -> None: ...
    @typing.overload
    def scroll(self, dx: int, dy: int, rect: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def scroll(self, dx: int, dy: int, x: int, y: int, width: int, height: int) -> 'QRegion': ...
    def cacheKey(self) -> int: ...
    @staticmethod
    def trueMatrix(m: 'QTransform', w: int, h: int) -> 'QTransform': ...
    def transformed(self, transform: 'QTransform', mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def metric(self, a0: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> 'QPaintEngine': ...
    def isQBitmap(self) -> bool: ...
    def detach(self) -> None: ...
    @typing.overload
    def copy(self, rect: QtCore.QRect = ...) -> 'QPixmap': ...
    @typing.overload
    def copy(self, ax: int, ay: int, awidth: int, aheight: int) -> 'QPixmap': ...
    @typing.overload
    def save(self, fileName: str, format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def save(self, device: QtCore.QIODevice, format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, buf: PyQt5.sip.array[bytes], format: typing.Optional[str] = ..., flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, buf: typing.Union[QtCore.QByteArray, bytes, bytearray], format: typing.Optional[str] = ..., flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> bool: ...
    def load(self, fileName: str, format: typing.Optional[str] = ..., flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> bool: ...
    def convertFromImage(self, img: 'QImage', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> bool: ...
    @staticmethod
    def fromImageReader(imageReader: 'QImageReader', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QPixmap': ...
    @staticmethod
    def fromImage(image: 'QImage', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QPixmap': ...
    def toImage(self) -> 'QImage': ...
    def scaledToHeight(self, height: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def scaledToWidth(self, width: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    @typing.overload
    def scaled(self, width: int, height: int, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    @typing.overload
    def scaled(self, size: QtCore.QSize, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def createMaskFromColor(self, maskColor: typing.Union['QColor', QtCore.Qt.GlobalColor], mode: QtCore.Qt.MaskMode = ...) -> 'QBitmap': ...
    def createHeuristicMask(self, clipTight: bool = ...) -> 'QBitmap': ...
    def hasAlphaChannel(self) -> bool: ...
    def hasAlpha(self) -> bool: ...
    def setMask(self, a0: 'QBitmap') -> None: ...
    def mask(self) -> 'QBitmap': ...
    def fill(self, color: typing.Union['QColor', QtCore.Qt.GlobalColor] = ...) -> None: ...
    @staticmethod
    def defaultDepth() -> int: ...
    def depth(self) -> int: ...
    def rect(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def devType(self) -> int: ...
    def isNull(self) -> bool: ...


class QBitmap(QPixmap):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QBitmap') -> None: ...
    @typing.overload
    def __init__(self, a0: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, w: int, h: int) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QBitmap') -> None: ...
    def transformed(self, matrix: 'QTransform') -> 'QBitmap': ...
    @staticmethod
    def fromData(size: QtCore.QSize, bits: bytes, format: 'QImage.Format' = ...) -> 'QBitmap': ...
    @staticmethod
    def fromImage(image: 'QImage', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QBitmap': ...
    def clear(self) -> None: ...


class QColor(PyQt5.sipsimplewrapper):

    class NameFormat(int):
        HexRgb = ... # type: QColor.NameFormat
        HexArgb = ... # type: QColor.NameFormat

    class Spec(int):
        Invalid = ... # type: QColor.Spec
        Rgb = ... # type: QColor.Spec
        Hsv = ... # type: QColor.Spec
        Cmyk = ... # type: QColor.Spec
        Hsl = ... # type: QColor.Spec
        ExtendedRgb = ... # type: QColor.Spec

    @typing.overload
    def __init__(self, color: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def __init__(self, rgb: int) -> None: ...
    @typing.overload
    def __init__(self, rgba64: 'QRgba64') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, r: int, g: int, b: int, alpha: int = ...) -> None: ...
    @typing.overload
    def __init__(self, aname: str) -> None: ...
    @typing.overload
    def __init__(self, acolor: typing.Union['QColor', QtCore.Qt.GlobalColor]) -> None: ...

    def toExtendedRgb(self) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgba64(r: int, g: int, b: int, alpha: int = ...) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgba64(rgba: 'QRgba64') -> 'QColor': ...
    def setRgba64(self, rgba: 'QRgba64') -> None: ...
    def rgba64(self) -> 'QRgba64': ...
    @staticmethod
    def isValidColor(name: str) -> bool: ...
    @staticmethod
    def fromHslF(h: float, s: float, l: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromHsl(h: int, s: int, l: int, alpha: int = ...) -> 'QColor': ...
    def toHsl(self) -> 'QColor': ...
    def setHslF(self, h: float, s: float, l: float, alpha: float = ...) -> None: ...
    def getHslF(self) -> typing.Tuple[float, float, float, float]: ...
    def setHsl(self, h: int, s: int, l: int, alpha: int = ...) -> None: ...
    def getHsl(self) -> typing.Tuple[int, int, int, int]: ...
    def lightnessF(self) -> float: ...
    def hslSaturationF(self) -> float: ...
    def hslHueF(self) -> float: ...
    def lightness(self) -> int: ...
    def hslSaturation(self) -> int: ...
    def hslHue(self) -> int: ...
    def hsvSaturationF(self) -> float: ...
    def hsvHueF(self) -> float: ...
    def hsvSaturation(self) -> int: ...
    def hsvHue(self) -> int: ...
    def darker(self, factor: int = ...) -> 'QColor': ...
    def lighter(self, factor: int = ...) -> 'QColor': ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def fromCmykF(c: float, m: float, y: float, k: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromCmyk(c: int, m: int, y: int, k: int, alpha: int = ...) -> 'QColor': ...
    @staticmethod
    def fromHsvF(h: float, s: float, v: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromHsv(h: int, s: int, v: int, alpha: int = ...) -> 'QColor': ...
    @staticmethod
    def fromRgbF(r: float, g: float, b: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromRgba(rgba: int) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgb(rgb: int) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgb(r: int, g: int, b: int, alpha: int = ...) -> 'QColor': ...
    def convertTo(self, colorSpec: 'QColor.Spec') -> 'QColor': ...
    def toCmyk(self) -> 'QColor': ...
    def toHsv(self) -> 'QColor': ...
    def toRgb(self) -> 'QColor': ...
    def setCmykF(self, c: float, m: float, y: float, k: float, alpha: float = ...) -> None: ...
    def getCmykF(self) -> typing.Tuple[float, float, float, float, float]: ...
    def setCmyk(self, c: int, m: int, y: int, k: int, alpha: int = ...) -> None: ...
    def getCmyk(self) -> typing.Tuple[int, int, int, int, int]: ...
    def blackF(self) -> float: ...
    def yellowF(self) -> float: ...
    def magentaF(self) -> float: ...
    def cyanF(self) -> float: ...
    def black(self) -> int: ...
    def yellow(self) -> int: ...
    def magenta(self) -> int: ...
    def cyan(self) -> int: ...
    def setHsvF(self, h: float, s: float, v: float, alpha: float = ...) -> None: ...
    def getHsvF(self) -> typing.Tuple[float, float, float, float]: ...
    def setHsv(self, h: int, s: int, v: int, alpha: int = ...) -> None: ...
    def getHsv(self) -> typing.Tuple[int, int, int, int]: ...
    def valueF(self) -> float: ...
    def saturationF(self) -> float: ...
    def hueF(self) -> float: ...
    def value(self) -> int: ...
    def saturation(self) -> int: ...
    def hue(self) -> int: ...
    def rgb(self) -> int: ...
    def setRgba(self, rgba: int) -> None: ...
    def rgba(self) -> int: ...
    def setRgbF(self, r: float, g: float, b: float, alpha: float = ...) -> None: ...
    def getRgbF(self) -> typing.Tuple[float, float, float, float]: ...
    @typing.overload
    def setRgb(self, r: int, g: int, b: int, alpha: int = ...) -> None: ...
    @typing.overload
    def setRgb(self, rgb: int) -> None: ...
    def getRgb(self) -> typing.Tuple[int, int, int, int]: ...
    def setBlueF(self, blue: float) -> None: ...
    def setGreenF(self, green: float) -> None: ...
    def setRedF(self, red: float) -> None: ...
    def blueF(self) -> float: ...
    def greenF(self) -> float: ...
    def redF(self) -> float: ...
    def setBlue(self, blue: int) -> None: ...
    def setGreen(self, green: int) -> None: ...
    def setRed(self, red: int) -> None: ...
    def blue(self) -> int: ...
    def green(self) -> int: ...
    def red(self) -> int: ...
    def setAlphaF(self, alpha: float) -> None: ...
    def alphaF(self) -> float: ...
    def setAlpha(self, alpha: int) -> None: ...
    def alpha(self) -> int: ...
    def spec(self) -> 'QColor.Spec': ...
    @staticmethod
    def colorNames() -> typing.List[str]: ...
    def setNamedColor(self, name: str) -> None: ...
    @typing.overload
    def name(self) -> str: ...
    @typing.overload
    def name(self, format: 'QColor.NameFormat') -> str: ...


class QColorConstants(PyQt5.sip.simplewrapper):

    class Svg(PyQt5.sip.simplewrapper):

        aliceblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        antiquewhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        aqua = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        aquamarine = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        azure = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        beige = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        bisque = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        black = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        blanchedalmond = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        blue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        blueviolet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        brown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        burlywood = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        cadetblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        chartreuse = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        chocolate = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        coral = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        cornflowerblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        cornsilk = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        crimson = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        cyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkcyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkgoldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkkhaki = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkmagenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkolivegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkorange = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkorchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darksalmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkslateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkslategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkslategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        darkviolet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        deeppink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        deepskyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        dimgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        dimgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        dodgerblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        firebrick = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        floralwhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        forestgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        fuchsia = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        gainsboro = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        ghostwhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        gold = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        goldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        gray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        green = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        greenyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        grey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        honeydew = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        hotpink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        indianred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        indigo = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        ivory = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        khaki = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lavender = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lavenderblush = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lawngreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lemonchiffon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightcoral = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightcyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightgoldenrodyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightpink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightsalmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightskyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightslategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightslategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightsteelblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lightyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        lime = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        limegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        linen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        magenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        maroon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumaquamarine = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumorchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumpurple = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumslateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumspringgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mediumvioletred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        midnightblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mintcream = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        mistyrose = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        moccasin = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        navajowhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        navy = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        oldlace = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        olive = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        olivedrab = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        orange = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        orangered = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        orchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        palegoldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        palegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        paleturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        palevioletred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        papayawhip = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        peachpuff = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        peru = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        pink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        plum = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        powderblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        purple = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        red = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        rosybrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        royalblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        saddlebrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        salmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        sandybrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        seagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        seashell = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        sienna = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        silver = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        skyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        slateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        slategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        slategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        snow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        springgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        steelblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        tan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        teal = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        thistle = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        tomato = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        turquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        violet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        wheat = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        white = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        whitesmoke = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        yellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
        yellowgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]

    Black = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Blue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Color0 = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Color1 = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Cyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkBlue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkCyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkGray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkGreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkMagenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkRed = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    DarkYellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Gray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Green = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    LightGray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Magenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Red = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Transparent = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    White = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]
    Yellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor]


class QBrush(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, bs: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def __init__(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor], style: QtCore.Qt.BrushStyle = ...) -> None: ...
    @typing.overload
    def __init__(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor], pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, image: 'QImage') -> None: ...
    @typing.overload
    def __init__(self, brush: typing.Union['QBrush', QColor, QtCore.Qt.GlobalColor, 'QGradient']) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QBrush') -> None: ...
    def transform(self) -> 'QTransform': ...
    def setTransform(self, a0: 'QTransform') -> None: ...
    def textureImage(self) -> 'QImage': ...
    def setTextureImage(self, image: 'QImage') -> None: ...
    def color(self) -> QColor: ...
    def style(self) -> QtCore.Qt.BrushStyle: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isOpaque(self) -> bool: ...
    def gradient(self) -> 'QGradient': ...
    @typing.overload
    def setColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setColor(self, acolor: QtCore.Qt.GlobalColor) -> None: ...
    def setTexture(self, pixmap: QPixmap) -> None: ...
    def texture(self) -> QPixmap: ...
    def setStyle(self, a0: QtCore.Qt.BrushStyle) -> None: ...


class QGradient(PyQt5.sipsimplewrapper):

    class Preset(int):
        WarmFlame = ... # type: QGradient.Preset
        NightFade = ... # type: QGradient.Preset
        SpringWarmth = ... # type: QGradient.Preset
        JuicyPeach = ... # type: QGradient.Preset
        YoungPassion = ... # type: QGradient.Preset
        LadyLips = ... # type: QGradient.Preset
        SunnyMorning = ... # type: QGradient.Preset
        RainyAshville = ... # type: QGradient.Preset
        FrozenDreams = ... # type: QGradient.Preset
        WinterNeva = ... # type: QGradient.Preset
        DustyGrass = ... # type: QGradient.Preset
        TemptingAzure = ... # type: QGradient.Preset
        HeavyRain = ... # type: QGradient.Preset
        AmyCrisp = ... # type: QGradient.Preset
        MeanFruit = ... # type: QGradient.Preset
        DeepBlue = ... # type: QGradient.Preset
        RipeMalinka = ... # type: QGradient.Preset
        CloudyKnoxville = ... # type: QGradient.Preset
        MalibuBeach = ... # type: QGradient.Preset
        NewLife = ... # type: QGradient.Preset
        TrueSunset = ... # type: QGradient.Preset
        MorpheusDen = ... # type: QGradient.Preset
        RareWind = ... # type: QGradient.Preset
        NearMoon = ... # type: QGradient.Preset
        WildApple = ... # type: QGradient.Preset
        SaintPetersburg = ... # type: QGradient.Preset
        PlumPlate = ... # type: QGradient.Preset
        EverlastingSky = ... # type: QGradient.Preset
        HappyFisher = ... # type: QGradient.Preset
        Blessing = ... # type: QGradient.Preset
        SharpeyeEagle = ... # type: QGradient.Preset
        LadogaBottom = ... # type: QGradient.Preset
        LemonGate = ... # type: QGradient.Preset
        ItmeoBranding = ... # type: QGradient.Preset
        ZeusMiracle = ... # type: QGradient.Preset
        OldHat = ... # type: QGradient.Preset
        StarWine = ... # type: QGradient.Preset
        HappyAcid = ... # type: QGradient.Preset
        AwesomePine = ... # type: QGradient.Preset
        NewYork = ... # type: QGradient.Preset
        ShyRainbow = ... # type: QGradient.Preset
        MixedHopes = ... # type: QGradient.Preset
        FlyHigh = ... # type: QGradient.Preset
        StrongBliss = ... # type: QGradient.Preset
        FreshMilk = ... # type: QGradient.Preset
        SnowAgain = ... # type: QGradient.Preset
        FebruaryInk = ... # type: QGradient.Preset
        KindSteel = ... # type: QGradient.Preset
        SoftGrass = ... # type: QGradient.Preset
        GrownEarly = ... # type: QGradient.Preset
        SharpBlues = ... # type: QGradient.Preset
        ShadyWater = ... # type: QGradient.Preset
        DirtyBeauty = ... # type: QGradient.Preset
        GreatWhale = ... # type: QGradient.Preset
        TeenNotebook = ... # type: QGradient.Preset
        PoliteRumors = ... # type: QGradient.Preset
        SweetPeriod = ... # type: QGradient.Preset
        WideMatrix = ... # type: QGradient.Preset
        SoftCherish = ... # type: QGradient.Preset
        RedSalvation = ... # type: QGradient.Preset
        BurningSpring = ... # type: QGradient.Preset
        NightParty = ... # type: QGradient.Preset
        SkyGlider = ... # type: QGradient.Preset
        HeavenPeach = ... # type: QGradient.Preset
        PurpleDivision = ... # type: QGradient.Preset
        AquaSplash = ... # type: QGradient.Preset
        SpikyNaga = ... # type: QGradient.Preset
        LoveKiss = ... # type: QGradient.Preset
        CleanMirror = ... # type: QGradient.Preset
        PremiumDark = ... # type: QGradient.Preset
        ColdEvening = ... # type: QGradient.Preset
        CochitiLake = ... # type: QGradient.Preset
        SummerGames = ... # type: QGradient.Preset
        PassionateBed = ... # type: QGradient.Preset
        MountainRock = ... # type: QGradient.Preset
        DesertHump = ... # type: QGradient.Preset
        JungleDay = ... # type: QGradient.Preset
        PhoenixStart = ... # type: QGradient.Preset
        OctoberSilence = ... # type: QGradient.Preset
        FarawayRiver = ... # type: QGradient.Preset
        AlchemistLab = ... # type: QGradient.Preset
        OverSun = ... # type: QGradient.Preset
        PremiumWhite = ... # type: QGradient.Preset
        MarsParty = ... # type: QGradient.Preset
        EternalConstance = ... # type: QGradient.Preset
        JapanBlush = ... # type: QGradient.Preset
        SmilingRain = ... # type: QGradient.Preset
        CloudyApple = ... # type: QGradient.Preset
        BigMango = ... # type: QGradient.Preset
        HealthyWater = ... # type: QGradient.Preset
        AmourAmour = ... # type: QGradient.Preset
        RiskyConcrete = ... # type: QGradient.Preset
        StrongStick = ... # type: QGradient.Preset
        ViciousStance = ... # type: QGradient.Preset
        PaloAlto = ... # type: QGradient.Preset
        HappyMemories = ... # type: QGradient.Preset
        MidnightBloom = ... # type: QGradient.Preset
        Crystalline = ... # type: QGradient.Preset
        PartyBliss = ... # type: QGradient.Preset
        ConfidentCloud = ... # type: QGradient.Preset
        LeCocktail = ... # type: QGradient.Preset
        RiverCity = ... # type: QGradient.Preset
        FrozenBerry = ... # type: QGradient.Preset
        ChildCare = ... # type: QGradient.Preset
        FlyingLemon = ... # type: QGradient.Preset
        NewRetrowave = ... # type: QGradient.Preset
        HiddenJaguar = ... # type: QGradient.Preset
        AboveTheSky = ... # type: QGradient.Preset
        Nega = ... # type: QGradient.Preset
        DenseWater = ... # type: QGradient.Preset
        Seashore = ... # type: QGradient.Preset
        MarbleWall = ... # type: QGradient.Preset
        CheerfulCaramel = ... # type: QGradient.Preset
        NightSky = ... # type: QGradient.Preset
        MagicLake = ... # type: QGradient.Preset
        YoungGrass = ... # type: QGradient.Preset
        ColorfulPeach = ... # type: QGradient.Preset
        GentleCare = ... # type: QGradient.Preset
        PlumBath = ... # type: QGradient.Preset
        HappyUnicorn = ... # type: QGradient.Preset
        AfricanField = ... # type: QGradient.Preset
        SolidStone = ... # type: QGradient.Preset
        OrangeJuice = ... # type: QGradient.Preset
        GlassWater = ... # type: QGradient.Preset
        NorthMiracle = ... # type: QGradient.Preset
        FruitBlend = ... # type: QGradient.Preset
        MillenniumPine = ... # type: QGradient.Preset
        HighFlight = ... # type: QGradient.Preset
        MoleHall = ... # type: QGradient.Preset
        SpaceShift = ... # type: QGradient.Preset
        ForestInei = ... # type: QGradient.Preset
        RoyalGarden = ... # type: QGradient.Preset
        RichMetal = ... # type: QGradient.Preset
        JuicyCake = ... # type: QGradient.Preset
        SmartIndigo = ... # type: QGradient.Preset
        SandStrike = ... # type: QGradient.Preset
        NorseBeauty = ... # type: QGradient.Preset
        AquaGuidance = ... # type: QGradient.Preset
        SunVeggie = ... # type: QGradient.Preset
        SeaLord = ... # type: QGradient.Preset
        BlackSea = ... # type: QGradient.Preset
        GrassShampoo = ... # type: QGradient.Preset
        LandingAircraft = ... # type: QGradient.Preset
        WitchDance = ... # type: QGradient.Preset
        SleeplessNight = ... # type: QGradient.Preset
        AngelCare = ... # type: QGradient.Preset
        CrystalRiver = ... # type: QGradient.Preset
        SoftLipstick = ... # type: QGradient.Preset
        SaltMountain = ... # type: QGradient.Preset
        PerfectWhite = ... # type: QGradient.Preset
        FreshOasis = ... # type: QGradient.Preset
        StrictNovember = ... # type: QGradient.Preset
        MorningSalad = ... # type: QGradient.Preset
        DeepRelief = ... # type: QGradient.Preset
        SeaStrike = ... # type: QGradient.Preset
        NightCall = ... # type: QGradient.Preset
        SupremeSky = ... # type: QGradient.Preset
        LightBlue = ... # type: QGradient.Preset
        MindCrawl = ... # type: QGradient.Preset
        LilyMeadow = ... # type: QGradient.Preset
        SugarLollipop = ... # type: QGradient.Preset
        SweetDessert = ... # type: QGradient.Preset
        MagicRay = ... # type: QGradient.Preset
        TeenParty = ... # type: QGradient.Preset
        FrozenHeat = ... # type: QGradient.Preset
        GagarinView = ... # type: QGradient.Preset
        FabledSunset = ... # type: QGradient.Preset
        PerfectBlue = ... # type: QGradient.Preset
        NumPresets = ... # type: QGradient.Preset

    class Spread(int):
        PadSpread = ... # type: QGradient.Spread
        ReflectSpread = ... # type: QGradient.Spread
        RepeatSpread = ... # type: QGradient.Spread

    class Type(int):
        LinearGradient = ... # type: QGradient.Type
        RadialGradient = ... # type: QGradient.Type
        ConicalGradient = ... # type: QGradient.Type
        NoGradient = ... # type: QGradient.Type

    class CoordinateMode(int):
        LogicalMode = ... # type: QGradient.CoordinateMode
        StretchToDeviceMode = ... # type: QGradient.CoordinateMode
        ObjectBoundingMode = ... # type: QGradient.CoordinateMode
        ObjectMode = ... # type: QGradient.CoordinateMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QGradient.Preset') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QGradient') -> None: ...

    def setCoordinateMode(self, mode: 'QGradient.CoordinateMode') -> None: ...
    def coordinateMode(self) -> 'QGradient.CoordinateMode': ...
    def setSpread(self, aspread: 'QGradient.Spread') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def stops(self) -> typing.List[typing.Tuple[float, QColor]]: ...
    def setStops(self, stops: typing.Iterable[typing.Tuple[float, typing.Union[QColor, QtCore.Qt.GlobalColor]]]) -> None: ...
    def setColorAt(self, pos: float, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def spread(self) -> 'QGradient.Spread': ...
    def type(self) -> 'QGradient.Type': ...


class QLinearGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, start: typing.Union[QtCore.QPointF, QtCore.QPoint], finalStop: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, xStart: float, yStart: float, xFinalStop: float, yFinalStop: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QLinearGradient') -> None: ...

    @typing.overload
    def setFinalStop(self, stop: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setFinalStop(self, x: float, y: float) -> None: ...
    @typing.overload
    def setStart(self, start: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setStart(self, x: float, y: float) -> None: ...
    def finalStop(self) -> QtCore.QPointF: ...
    def start(self) -> QtCore.QPointF: ...


class QRadialGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], radius: float, focalPoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], centerRadius: float, focalPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], focalRadius: float) -> None: ...
    @typing.overload
    def __init__(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], radius: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, radius: float, fx: float, fy: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, centerRadius: float, fx: float, fy: float, focalRadius: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, radius: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRadialGradient') -> None: ...

    def setFocalRadius(self, radius: float) -> None: ...
    def focalRadius(self) -> float: ...
    def setCenterRadius(self, radius: float) -> None: ...
    def centerRadius(self) -> float: ...
    def setRadius(self, radius: float) -> None: ...
    @typing.overload
    def setFocalPoint(self, focalPoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setFocalPoint(self, x: float, y: float) -> None: ...
    @typing.overload
    def setCenter(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setCenter(self, x: float, y: float) -> None: ...
    def radius(self) -> float: ...
    def focalPoint(self) -> QtCore.QPointF: ...
    def center(self) -> QtCore.QPointF: ...


class QConicalGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], startAngle: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, startAngle: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QConicalGradient') -> None: ...

    def setAngle(self, angle: float) -> None: ...
    @typing.overload
    def setCenter(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setCenter(self, x: float, y: float) -> None: ...
    def angle(self) -> float: ...
    def center(self) -> QtCore.QPointF: ...


class QClipboard(QtCore.QObject):

    class Mode(int):
        Clipboard = ... # type: QClipboard.Mode
        Selection = ... # type: QClipboard.Mode
        FindBuffer = ... # type: QClipboard.Mode

    selectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    findBufferChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataChanged: typing.ClassVar[QtCore.pyqtSignal]
    changed: typing.ClassVar[QtCore.pyqtSignal]
    def setPixmap(self, a0: QPixmap, mode: 'QClipboard.Mode' = ...) -> None: ...
    def setImage(self, a0: 'QImage', mode: 'QClipboard.Mode' = ...) -> None: ...
    def pixmap(self, mode: 'QClipboard.Mode' = ...) -> QPixmap: ...
    def image(self, mode: 'QClipboard.Mode' = ...) -> 'QImage': ...
    def setMimeData(self, data: QtCore.QMimeData, mode: 'QClipboard.Mode' = ...) -> None: ...
    def mimeData(self, mode: 'QClipboard.Mode' = ...) -> QtCore.QMimeData: ...
    def setText(self, a0: str, mode: 'QClipboard.Mode' = ...) -> None: ...
    @typing.overload
    def text(self, mode: 'QClipboard.Mode' = ...) -> str: ...
    @typing.overload
    def text(self, subtype: str, mode: 'QClipboard.Mode' = ...) -> typing.Tuple[str, str]: ...
    def ownsSelection(self) -> bool: ...
    def ownsFindBuffer(self) -> bool: ...
    def ownsClipboard(self) -> bool: ...
    def supportsSelection(self) -> bool: ...
    def supportsFindBuffer(self) -> bool: ...
    def clear(self, mode: 'QClipboard.Mode' = ...) -> None: ...


class QColorSpace(PyQt5.sipsimplewrapper):

    class TransferFunction(int):
        Custom = ... # type: QColorSpace.TransferFunction
        Linear = ... # type: QColorSpace.TransferFunction
        Gamma = ... # type: QColorSpace.TransferFunction
        SRgb = ... # type: QColorSpace.TransferFunction
        ProPhotoRgb = ... # type: QColorSpace.TransferFunction

    class Primaries(int):
        Custom = ... # type: QColorSpace.Primaries
        SRgb = ... # type: QColorSpace.Primaries
        AdobeRgb = ... # type: QColorSpace.Primaries
        DciP3D65 = ... # type: QColorSpace.Primaries
        ProPhotoRgb = ... # type: QColorSpace.Primaries

    class NamedColorSpace(int):
        SRgb = ... # type: QColorSpace.NamedColorSpace
        SRgbLinear = ... # type: QColorSpace.NamedColorSpace
        AdobeRgb = ... # type: QColorSpace.NamedColorSpace
        DisplayP3 = ... # type: QColorSpace.NamedColorSpace
        ProPhotoRgb = ... # type: QColorSpace.NamedColorSpace

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, namedColorSpace: 'QColorSpace.NamedColorSpace') -> None: ...
    @typing.overload
    def __init__(self, primaries: 'QColorSpace.Primaries', fun: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def __init__(self, primaries: 'QColorSpace.Primaries', gamma: float) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: typing.Union[QtCore.QPointF, QtCore.QPoint], redPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], greenPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], bluePoint: typing.Union[QtCore.QPointF, QtCore.QPoint], fun: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def __init__(self, colorSpace: 'QColorSpace') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def transformationToColorSpace(self, colorspace: 'QColorSpace') -> 'QColorTransform': ...
    def iccProfile(self) -> QtCore.QByteArray: ...
    @staticmethod
    def fromIccProfile(iccProfile: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> 'QColorSpace': ...
    def isValid(self) -> bool: ...
    @typing.overload
    def setPrimaries(self, primariesId: 'QColorSpace.Primaries') -> None: ...
    @typing.overload
    def setPrimaries(self, whitePoint: typing.Union[QtCore.QPointF, QtCore.QPoint], redPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], greenPoint: typing.Union[QtCore.QPointF, QtCore.QPoint], bluePoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def withTransferFunction(self, transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> 'QColorSpace': ...
    def setTransferFunction(self, transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    def gamma(self) -> float: ...
    def transferFunction(self) -> 'QColorSpace.TransferFunction': ...
    def primaries(self) -> 'QColorSpace.Primaries': ...
    def swap(self, colorSpace: 'QColorSpace') -> None: ...


class QColorTransform(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, colorTransform: 'QColorTransform') -> None: ...

    @typing.overload
    def map(self, argb: int) -> int: ...
    @typing.overload
    def map(self, rgba64: 'QRgba64') -> 'QRgba64': ...
    @typing.overload
    def map(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> QColor: ...
    def swap(self, other: 'QColorTransform') -> None: ...


class QCursor(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, bitmap: QBitmap, mask: QBitmap, hotX: int = ..., hotY: int = ...) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap, hotX: int = ..., hotY: int = ...) -> None: ...
    @typing.overload
    def __init__(self, cursor: typing.Union['QCursor', QtCore.Qt.CursorShape]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: typing.Union['QCursor', QtCore.Qt.CursorShape]) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(x: int, y: int) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(p: QtCore.QPoint) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(screen: 'QScreen', x: int, y: int) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(screen: 'QScreen', p: QtCore.QPoint) -> None: ...
    @typing.overload
    @staticmethod
    def pos() -> QtCore.QPoint: ...
    @typing.overload
    @staticmethod
    def pos(screen: 'QScreen') -> QtCore.QPoint: ...
    def hotSpot(self) -> QtCore.QPoint: ...
    def pixmap(self) -> QPixmap: ...
    def mask(self) -> QBitmap: ...
    def bitmap(self) -> QBitmap: ...
    def setShape(self, newShape: QtCore.Qt.CursorShape) -> None: ...
    def shape(self) -> QtCore.Qt.CursorShape: ...


class QDesktopServices(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDesktopServices') -> None: ...

    @staticmethod
    def unsetUrlHandler(scheme: str) -> None: ...
    @typing.overload
    @staticmethod
    def setUrlHandler(scheme: str, receiver: QtCore.QObject, method: str) -> None: ...
    @typing.overload
    @staticmethod
    def setUrlHandler(scheme: str, method: typing.Callable[[QtCore.QUrl], None]) -> None: ...
    @staticmethod
    def openUrl(url: QtCore.QUrl) -> bool: ...


class QDrag(QtCore.QObject):

    def __init__(self, dragSource: QtCore.QObject) -> None: ...

    @staticmethod
    def cancel() -> None: ...
    def defaultAction(self) -> QtCore.Qt.DropAction: ...
    def supportedActions(self) -> QtCore.Qt.DropActions: ...
    def dragCursor(self, action: QtCore.Qt.DropAction) -> QPixmap: ...
    targetChanged: typing.ClassVar[QtCore.pyqtSignal]
    actionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setDragCursor(self, cursor: QPixmap, action: QtCore.Qt.DropAction) -> None: ...
    def target(self) -> QtCore.QObject: ...
    def source(self) -> QtCore.QObject: ...
    def hotSpot(self) -> QtCore.QPoint: ...
    def setHotSpot(self, hotspot: QtCore.QPoint) -> None: ...
    def pixmap(self) -> QPixmap: ...
    def setPixmap(self, a0: QPixmap) -> None: ...
    def mimeData(self) -> QtCore.QMimeData: ...
    def setMimeData(self, data: QtCore.QMimeData) -> None: ...
    @typing.overload
    def exec(self, supportedActions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction] = ...) -> QtCore.Qt.DropAction: ...
    @typing.overload
    def exec(self, supportedActions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction], defaultDropAction: QtCore.Qt.DropAction) -> QtCore.Qt.DropAction: ...
    @typing.overload
    def exec_(self, supportedActions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction] = ...) -> QtCore.Qt.DropAction: ...
    @typing.overload
    def exec_(self, supportedActions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction], defaultDropAction: QtCore.Qt.DropAction) -> QtCore.Qt.DropAction: ...


class QInputEvent(QtCore.QEvent):

    def setTimestamp(self, atimestamp: int) -> None: ...
    def timestamp(self) -> int: ...
    def modifiers(self) -> QtCore.Qt.KeyboardModifiers: ...


class QMouseEvent(QInputEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], button: QtCore.Qt.MouseButton, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], button: QtCore.Qt.MouseButton, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], windowPos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], button: QtCore.Qt.MouseButton, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, localPos: typing.Union[QtCore.QPointF, QtCore.QPoint], windowPos: typing.Union[QtCore.QPointF, QtCore.QPoint], screenPos: typing.Union[QtCore.QPointF, QtCore.QPoint], button: QtCore.Qt.MouseButton, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], source: QtCore.Qt.MouseEventSource) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMouseEvent') -> None: ...

    def flags(self) -> QtCore.Qt.MouseEventFlags: ...
    def source(self) -> QtCore.Qt.MouseEventSource: ...
    def screenPos(self) -> QtCore.QPointF: ...
    def windowPos(self) -> QtCore.QPointF: ...
    def localPos(self) -> QtCore.QPointF: ...
    def buttons(self) -> QtCore.Qt.MouseButtons: ...
    def button(self) -> QtCore.Qt.MouseButton: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QHoverEvent(QInputEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], oldPos: typing.Union[QtCore.QPointF, QtCore.QPoint], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QHoverEvent') -> None: ...

    def oldPosF(self) -> QtCore.QPointF: ...
    def posF(self) -> QtCore.QPointF: ...
    def oldPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QWheelEvent(QInputEvent):

    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, qt4Delta: int, qt4Orientation: QtCore.Qt.Orientation, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, qt4Delta: int, qt4Orientation: QtCore.Qt.Orientation, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], phase: QtCore.Qt.ScrollPhase) -> None: ...
    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, qt4Delta: int, qt4Orientation: QtCore.Qt.Orientation, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], phase: QtCore.Qt.ScrollPhase, source: QtCore.Qt.MouseEventSource) -> None: ...
    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, qt4Delta: int, qt4Orientation: QtCore.Qt.Orientation, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], phase: QtCore.Qt.ScrollPhase, source: QtCore.Qt.MouseEventSource, inverted: bool) -> None: ...
    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], phase: QtCore.Qt.ScrollPhase, inverted: bool, source: QtCore.Qt.MouseEventSource = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QWheelEvent') -> None: ...

    def globalPosition(self) -> QtCore.QPointF: ...
    def position(self) -> QtCore.QPointF: ...
    def inverted(self) -> bool: ...
    def source(self) -> QtCore.Qt.MouseEventSource: ...
    def phase(self) -> QtCore.Qt.ScrollPhase: ...
    def globalPosF(self) -> QtCore.QPointF: ...
    def posF(self) -> QtCore.QPointF: ...
    def angleDelta(self) -> QtCore.QPoint: ...
    def pixelDelta(self) -> QtCore.QPoint: ...
    def buttons(self) -> QtCore.Qt.MouseButtons: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QTabletEvent(QInputEvent):

    class PointerType(int):
        UnknownPointer = ... # type: QTabletEvent.PointerType
        Pen = ... # type: QTabletEvent.PointerType
        Cursor = ... # type: QTabletEvent.PointerType
        Eraser = ... # type: QTabletEvent.PointerType

    class TabletDevice(int):
        NoDevice = ... # type: QTabletEvent.TabletDevice
        Puck = ... # type: QTabletEvent.TabletDevice
        Stylus = ... # type: QTabletEvent.TabletDevice
        Airbrush = ... # type: QTabletEvent.TabletDevice
        FourDMouse = ... # type: QTabletEvent.TabletDevice
        XFreeEraser = ... # type: QTabletEvent.TabletDevice
        RotationStylus = ... # type: QTabletEvent.TabletDevice

    @typing.overload
    def __init__(self, t: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], device: int, pointerType: int, pressure: float, xTilt: int, yTilt: int, tangentialPressure: float, rotation: float, z: int, keyState: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], uniqueID: int, button: QtCore.Qt.MouseButton, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton]) -> None: ...
    @typing.overload
    def __init__(self, t: QtCore.QEvent.Type, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], globalPos: typing.Union[QtCore.QPointF, QtCore.QPoint], device: int, pointerType: int, pressure: float, xTilt: int, yTilt: int, tangentialPressure: float, rotation: float, z: int, keyState: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], uniqueID: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTabletEvent') -> None: ...

    def deviceType(self) -> 'QTabletEvent.TabletDevice': ...
    def buttons(self) -> QtCore.Qt.MouseButtons: ...
    def button(self) -> QtCore.Qt.MouseButton: ...
    def globalPosF(self) -> QtCore.QPointF: ...
    def posF(self) -> QtCore.QPointF: ...
    def yTilt(self) -> int: ...
    def xTilt(self) -> int: ...
    def rotation(self) -> float: ...
    def tangentialPressure(self) -> float: ...
    def z(self) -> int: ...
    def pressure(self) -> float: ...
    def uniqueId(self) -> int: ...
    def pointerType(self) -> 'QTabletEvent.PointerType': ...
    def device(self) -> 'QTabletEvent.TabletDevice': ...
    def hiResGlobalY(self) -> float: ...
    def hiResGlobalX(self) -> float: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QKeyEvent(QInputEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, key: int, modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], nativeScanCode: int, nativeVirtualKey: int, nativeModifiers: int, text: str = ..., autorep: bool = ..., count: int = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, key: int, modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], text: str = ..., autorep: bool = ..., count: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QKeyEvent') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def nativeVirtualKey(self) -> int: ...
    def nativeScanCode(self) -> int: ...
    def nativeModifiers(self) -> int: ...
    def matches(self, key: 'QKeySequence.StandardKey') -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def isAutoRepeat(self) -> bool: ...
    def text(self) -> str: ...
    def modifiers(self) -> QtCore.Qt.KeyboardModifiers: ...
    def key(self) -> int: ...


class QFocusEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, reason: QtCore.Qt.FocusReason = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFocusEvent') -> None: ...

    def reason(self) -> QtCore.Qt.FocusReason: ...
    def lostFocus(self) -> bool: ...
    def gotFocus(self) -> bool: ...


class QPaintEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, paintRegion: 'QRegion') -> None: ...
    @typing.overload
    def __init__(self, paintRect: QtCore.QRect) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPaintEvent') -> None: ...

    def region(self) -> 'QRegion': ...
    def rect(self) -> QtCore.QRect: ...


class QMoveEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, pos: QtCore.QPoint, oldPos: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMoveEvent') -> None: ...

    def oldPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QResizeEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, size: QtCore.QSize, oldSize: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QResizeEvent') -> None: ...

    def oldSize(self) -> QtCore.QSize: ...
    def size(self) -> QtCore.QSize: ...


class QCloseEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QCloseEvent') -> None: ...


class QIconDragEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QIconDragEvent') -> None: ...


class QShowEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QShowEvent') -> None: ...


class QHideEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QHideEvent') -> None: ...


class QContextMenuEvent(QInputEvent):

    class Reason(int):
        Mouse = ... # type: QContextMenuEvent.Reason
        Keyboard = ... # type: QContextMenuEvent.Reason
        Other = ... # type: QContextMenuEvent.Reason

    @typing.overload
    def __init__(self, reason: 'QContextMenuEvent.Reason', pos: QtCore.QPoint, globalPos: QtCore.QPoint, modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, reason: 'QContextMenuEvent.Reason', pos: QtCore.QPoint, globalPos: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, reason: 'QContextMenuEvent.Reason', pos: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QContextMenuEvent') -> None: ...

    def reason(self) -> 'QContextMenuEvent.Reason': ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...


class QInputMethodEvent(QtCore.QEvent):

    class AttributeType(int):
        TextFormat = ... # type: QInputMethodEvent.AttributeType
        Cursor = ... # type: QInputMethodEvent.AttributeType
        Language = ... # type: QInputMethodEvent.AttributeType
        Ruby = ... # type: QInputMethodEvent.AttributeType
        Selection = ... # type: QInputMethodEvent.AttributeType

    class Attribute(PyQt5.sipsimplewrapper):

        length = ... # type: int
        start = ... # type: int
        type = ... # type: 'QInputMethodEvent.AttributeType'
        value = ... # type: typing.Any

        @typing.overload
        def __init__(self, t: 'QInputMethodEvent.AttributeType', s: int, l: int, val: typing.Any) -> None: ...
        @typing.overload
        def __init__(self, typ: 'QInputMethodEvent.AttributeType', s: int, l: int) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QInputMethodEvent.Attribute') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, preeditText: str, attributes: typing.Iterable['QInputMethodEvent.Attribute']) -> None: ...
    @typing.overload
    def __init__(self, other: 'QInputMethodEvent') -> None: ...

    def replacementLength(self) -> int: ...
    def replacementStart(self) -> int: ...
    def commitString(self) -> str: ...
    def preeditString(self) -> str: ...
    def attributes(self) -> typing.List['QInputMethodEvent.Attribute']: ...
    def setCommitString(self, commitString: str, from_: int = ..., length: int = ...) -> None: ...


class QInputMethodQueryEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, queries: typing.Union[QtCore.Qt.InputMethodQueries, QtCore.Qt.InputMethodQuery]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QInputMethodQueryEvent') -> None: ...

    def value(self, query: QtCore.Qt.InputMethodQuery) -> typing.Any: ...
    def setValue(self, query: QtCore.Qt.InputMethodQuery, value: typing.Any) -> None: ...
    def queries(self) -> QtCore.Qt.InputMethodQueries: ...


class QDropEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], actions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction], data: QtCore.QMimeData, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], type: QtCore.QEvent.Type = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDropEvent') -> None: ...

    def mimeData(self) -> QtCore.QMimeData: ...
    def source(self) -> QtCore.QObject: ...
    def setDropAction(self, action: QtCore.Qt.DropAction) -> None: ...
    def dropAction(self) -> QtCore.Qt.DropAction: ...
    def acceptProposedAction(self) -> None: ...
    def proposedAction(self) -> QtCore.Qt.DropAction: ...
    def possibleActions(self) -> QtCore.Qt.DropActions: ...
    def keyboardModifiers(self) -> QtCore.Qt.KeyboardModifiers: ...
    def mouseButtons(self) -> QtCore.Qt.MouseButtons: ...
    def posF(self) -> QtCore.QPointF: ...
    def pos(self) -> QtCore.QPoint: ...


class QDragMoveEvent(QDropEvent):

    @typing.overload
    def __init__(self, pos: QtCore.QPoint, actions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction], data: QtCore.QMimeData, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier], type: QtCore.QEvent.Type = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDragMoveEvent') -> None: ...

    @typing.overload
    def ignore(self) -> None: ...
    @typing.overload
    def ignore(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def accept(self) -> None: ...
    @typing.overload
    def accept(self, r: QtCore.QRect) -> None: ...
    def answerRect(self) -> QtCore.QRect: ...


class QDragEnterEvent(QDragMoveEvent):

    @typing.overload
    def __init__(self, pos: QtCore.QPoint, actions: typing.Union[QtCore.Qt.DropActions, QtCore.Qt.DropAction], data: QtCore.QMimeData, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton], modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDragEnterEvent') -> None: ...


class QDragLeaveEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDragLeaveEvent') -> None: ...


class QHelpEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: QtCore.QPoint, globalPos: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QHelpEvent') -> None: ...

    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...


class QStatusTipEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, tip: str) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QStatusTipEvent') -> None: ...

    def tip(self) -> str: ...


class QWhatsThisClickedEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, href: str) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QWhatsThisClickedEvent') -> None: ...

    def href(self) -> str: ...


class QActionEvent(QtCore.QEvent):

    from PyQt5.QtWidgets import QAction

    @typing.overload
    def __init__(self, type: int, action: QAction, before: typing.Optional[QAction] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QActionEvent') -> None: ...

    def before(self) -> QAction: ...
    def action(self) -> QAction: ...


class QFileOpenEvent(QtCore.QEvent):

    def openFile(self, file: QtCore.QFile, flags: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag]) -> bool: ...
    def url(self) -> QtCore.QUrl: ...
    def file(self) -> str: ...


class QShortcutEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, key: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int], id: int, ambiguous: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QShortcutEvent') -> None: ...

    def shortcutId(self) -> int: ...
    def key(self) -> 'QKeySequence': ...
    def isAmbiguous(self) -> bool: ...


class QWindowStateChangeEvent(QtCore.QEvent):

    def oldState(self) -> QtCore.Qt.WindowStates: ...


class QTouchEvent(QInputEvent):

    class TouchPoint(PyQt5.sipsimplewrapper):

        class InfoFlag(int):
            Pen = ... # type: QTouchEvent.TouchPoint.InfoFlag
            Token = ... # type: QTouchEvent.TouchPoint.InfoFlag

        class InfoFlags(PyQt5.sipsimplewrapper):

            @typing.overload
            def __init__(self) -> None: ...
            @typing.overload
            def __init__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> None: ...

            def __hash__(self) -> int: ...
            def __bool__(self) -> int: ...
            def __ne__(self, other: object): ...
            def __eq__(self, other: object): ...
            def __ixor__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __xor__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __ior__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __or__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __iand__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __and__(self, f: typing.Union['QTouchEvent.TouchPoint.InfoFlags', 'QTouchEvent.TouchPoint.InfoFlag']) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __invert__(self) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
            def __index__(self) -> int: ...
            def __int__(self) -> int: ...

        def ellipseDiameters(self) -> QtCore.QSizeF: ...
        def rotation(self) -> float: ...
        def uniqueId(self) -> 'QPointingDeviceUniqueId': ...
        def rawScreenPositions(self) -> typing.List[QtCore.QPointF]: ...
        def flags(self) -> 'QTouchEvent.TouchPoint.InfoFlags': ...
        def velocity(self) -> 'QVector2D': ...
        def pressure(self) -> float: ...
        def screenRect(self) -> QtCore.QRectF: ...
        def sceneRect(self) -> QtCore.QRectF: ...
        def rect(self) -> QtCore.QRectF: ...
        def lastNormalizedPos(self) -> QtCore.QPointF: ...
        def startNormalizedPos(self) -> QtCore.QPointF: ...
        def normalizedPos(self) -> QtCore.QPointF: ...
        def lastScreenPos(self) -> QtCore.QPointF: ...
        def startScreenPos(self) -> QtCore.QPointF: ...
        def screenPos(self) -> QtCore.QPointF: ...
        def lastScenePos(self) -> QtCore.QPointF: ...
        def startScenePos(self) -> QtCore.QPointF: ...
        def scenePos(self) -> QtCore.QPointF: ...
        def lastPos(self) -> QtCore.QPointF: ...
        def startPos(self) -> QtCore.QPointF: ...
        def pos(self) -> QtCore.QPointF: ...
        def state(self) -> QtCore.Qt.TouchPointState: ...
        def id(self) -> int: ...

    @typing.overload
    def __init__(self, eventType: QtCore.QEvent.Type, device: typing.Optional['QTouchDevice'] = ..., modifiers: typing.Union[QtCore.Qt.KeyboardModifiers, QtCore.Qt.KeyboardModifier] = ..., touchPointStates: typing.Union[QtCore.Qt.TouchPointStates, QtCore.Qt.TouchPointState] = ..., touchPoints: typing.Iterable['QTouchEvent.TouchPoint'] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTouchEvent') -> None: ...

    def setDevice(self, adevice: 'QTouchDevice') -> None: ...
    def device(self) -> 'QTouchDevice': ...
    def window(self) -> 'QWindow': ...
    def touchPoints(self) -> typing.List['QTouchEvent.TouchPoint']: ...
    def touchPointStates(self) -> QtCore.Qt.TouchPointStates: ...
    def target(self) -> QtCore.QObject: ...


class QExposeEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, rgn: 'QRegion') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QExposeEvent') -> None: ...

    def region(self) -> 'QRegion': ...


class QScrollPrepareEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, startPos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QScrollPrepareEvent') -> None: ...

    def setContentPos(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def setContentPosRange(self, rect: QtCore.QRectF) -> None: ...
    def setViewportSize(self, size: QtCore.QSizeF) -> None: ...
    def contentPos(self) -> QtCore.QPointF: ...
    def contentPosRange(self) -> QtCore.QRectF: ...
    def viewportSize(self) -> QtCore.QSizeF: ...
    def startPos(self) -> QtCore.QPointF: ...


class QScrollEvent(QtCore.QEvent):

    class ScrollState(int):
        ScrollStarted = ... # type: QScrollEvent.ScrollState
        ScrollUpdated = ... # type: QScrollEvent.ScrollState
        ScrollFinished = ... # type: QScrollEvent.ScrollState

    @typing.overload
    def __init__(self, contentPos: typing.Union[QtCore.QPointF, QtCore.QPoint], overshoot: typing.Union[QtCore.QPointF, QtCore.QPoint], scrollState: 'QScrollEvent.ScrollState') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QScrollEvent') -> None: ...

    def scrollState(self) -> 'QScrollEvent.ScrollState': ...
    def overshootDistance(self) -> QtCore.QPointF: ...
    def contentPos(self) -> QtCore.QPointF: ...


class QEnterEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, localPos: typing.Union[QtCore.QPointF, QtCore.QPoint], windowPos: typing.Union[QtCore.QPointF, QtCore.QPoint], screenPos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QEnterEvent') -> None: ...

    def screenPos(self) -> QtCore.QPointF: ...
    def windowPos(self) -> QtCore.QPointF: ...
    def localPos(self) -> QtCore.QPointF: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QNativeGestureEvent(QInputEvent):

    @typing.overload
    def __init__(self, type: QtCore.Qt.NativeGestureType, localPos: typing.Union[QtCore.QPointF, QtCore.QPoint], windowPos: typing.Union[QtCore.QPointF, QtCore.QPoint], screenPos: typing.Union[QtCore.QPointF, QtCore.QPoint], value: float, sequenceId: int, intArgument: int) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.Qt.NativeGestureType, dev: 'QTouchDevice', localPos: typing.Union[QtCore.QPointF, QtCore.QPoint], windowPos: typing.Union[QtCore.QPointF, QtCore.QPoint], screenPos: typing.Union[QtCore.QPointF, QtCore.QPoint], value: float, sequenceId: int, intArgument: int) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QNativeGestureEvent') -> None: ...

    def device(self) -> 'QTouchDevice': ...
    def screenPos(self) -> QtCore.QPointF: ...
    def windowPos(self) -> QtCore.QPointF: ...
    def localPos(self) -> QtCore.QPointF: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...
    def value(self) -> float: ...
    def gestureType(self) -> QtCore.Qt.NativeGestureType: ...


class QPlatformSurfaceEvent(QtCore.QEvent):

    class SurfaceEventType(int):
        SurfaceCreated = ... # type: QPlatformSurfaceEvent.SurfaceEventType
        SurfaceAboutToBeDestroyed = ... # type: QPlatformSurfaceEvent.SurfaceEventType

    @typing.overload
    def __init__(self, surfaceEventType: 'QPlatformSurfaceEvent.SurfaceEventType') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlatformSurfaceEvent') -> None: ...

    def surfaceEventType(self) -> 'QPlatformSurfaceEvent.SurfaceEventType': ...


class QPointingDeviceUniqueId(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPointingDeviceUniqueId') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def numericId(self) -> int: ...
    def isValid(self) -> bool: ...
    @staticmethod
    def fromNumericId(id: int) -> 'QPointingDeviceUniqueId': ...


class QFont(PyQt5.sipsimplewrapper):

    class HintingPreference(int):
        PreferDefaultHinting = ... # type: QFont.HintingPreference
        PreferNoHinting = ... # type: QFont.HintingPreference
        PreferVerticalHinting = ... # type: QFont.HintingPreference
        PreferFullHinting = ... # type: QFont.HintingPreference

    class SpacingType(int):
        PercentageSpacing = ... # type: QFont.SpacingType
        AbsoluteSpacing = ... # type: QFont.SpacingType

    class Capitalization(int):
        MixedCase = ... # type: QFont.Capitalization
        AllUppercase = ... # type: QFont.Capitalization
        AllLowercase = ... # type: QFont.Capitalization
        SmallCaps = ... # type: QFont.Capitalization
        Capitalize = ... # type: QFont.Capitalization

    class Stretch(int):
        AnyStretch = ... # type: QFont.Stretch
        UltraCondensed = ... # type: QFont.Stretch
        ExtraCondensed = ... # type: QFont.Stretch
        Condensed = ... # type: QFont.Stretch
        SemiCondensed = ... # type: QFont.Stretch
        Unstretched = ... # type: QFont.Stretch
        SemiExpanded = ... # type: QFont.Stretch
        Expanded = ... # type: QFont.Stretch
        ExtraExpanded = ... # type: QFont.Stretch
        UltraExpanded = ... # type: QFont.Stretch

    class Style(int):
        StyleNormal = ... # type: QFont.Style
        StyleItalic = ... # type: QFont.Style
        StyleOblique = ... # type: QFont.Style

    class Weight(int):
        Thin = ... # type: QFont.Weight
        ExtraLight = ... # type: QFont.Weight
        Light = ... # type: QFont.Weight
        Normal = ... # type: QFont.Weight
        Medium = ... # type: QFont.Weight
        DemiBold = ... # type: QFont.Weight
        Bold = ... # type: QFont.Weight
        ExtraBold = ... # type: QFont.Weight
        Black = ... # type: QFont.Weight

    class StyleStrategy(int):
        PreferDefault = ... # type: QFont.StyleStrategy
        PreferBitmap = ... # type: QFont.StyleStrategy
        PreferDevice = ... # type: QFont.StyleStrategy
        PreferOutline = ... # type: QFont.StyleStrategy
        ForceOutline = ... # type: QFont.StyleStrategy
        PreferMatch = ... # type: QFont.StyleStrategy
        PreferQuality = ... # type: QFont.StyleStrategy
        PreferAntialias = ... # type: QFont.StyleStrategy
        NoAntialias = ... # type: QFont.StyleStrategy
        NoSubpixelAntialias = ... # type: QFont.StyleStrategy
        OpenGLCompatible = ... # type: QFont.StyleStrategy
        NoFontMerging = ... # type: QFont.StyleStrategy
        ForceIntegerMetrics = ... # type: QFont.StyleStrategy
        PreferNoShaping = ... # type: QFont.StyleStrategy

    class StyleHint(int):
        Helvetica = ... # type: QFont.StyleHint
        SansSerif = ... # type: QFont.StyleHint
        Times = ... # type: QFont.StyleHint
        Serif = ... # type: QFont.StyleHint
        Courier = ... # type: QFont.StyleHint
        TypeWriter = ... # type: QFont.StyleHint
        OldEnglish = ... # type: QFont.StyleHint
        Decorative = ... # type: QFont.StyleHint
        System = ... # type: QFont.StyleHint
        AnyStyle = ... # type: QFont.StyleHint
        Cursive = ... # type: QFont.StyleHint
        Monospace = ... # type: QFont.StyleHint
        Fantasy = ... # type: QFont.StyleHint

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, family: str, pointSize: int = ..., weight: int = ..., italic: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFont', pd: QPaintDevice) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFont') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __ge__(self, a0: 'QFont') -> bool: ...
    def setFamilies(self, a0: typing.Iterable[str]) -> None: ...
    def families(self) -> typing.List[str]: ...
    def __hash__(self) -> int: ...
    def swap(self, other: 'QFont') -> None: ...
    def hintingPreference(self) -> 'QFont.HintingPreference': ...
    def setHintingPreference(self, hintingPreference: 'QFont.HintingPreference') -> None: ...
    def setStyleName(self, styleName: str) -> None: ...
    def styleName(self) -> str: ...
    def capitalization(self) -> 'QFont.Capitalization': ...
    def setCapitalization(self, a0: 'QFont.Capitalization') -> None: ...
    def setWordSpacing(self, spacing: float) -> None: ...
    def wordSpacing(self) -> float: ...
    def setLetterSpacing(self, type: 'QFont.SpacingType', spacing: float) -> None: ...
    def letterSpacingType(self) -> 'QFont.SpacingType': ...
    def letterSpacing(self) -> float: ...
    def setItalic(self, b: bool) -> None: ...
    def italic(self) -> bool: ...
    def setBold(self, enable: bool) -> None: ...
    def bold(self) -> bool: ...
    def resolve(self, a0: 'QFont') -> 'QFont': ...
    def lastResortFont(self) -> str: ...
    def lastResortFamily(self) -> str: ...
    def defaultFamily(self) -> str: ...
    @staticmethod
    def cacheStatistics() -> None: ...
    @staticmethod
    def cleanup() -> None: ...
    @staticmethod
    def initialize() -> None: ...
    @staticmethod
    def removeSubstitutions(a0: str) -> None: ...
    @staticmethod
    def insertSubstitutions(a0: str, a1: typing.Iterable[str]) -> None: ...
    @staticmethod
    def insertSubstitution(a0: str, a1: str) -> None: ...
    @staticmethod
    def substitutions() -> typing.List[str]: ...
    @staticmethod
    def substitutes(a0: str) -> typing.List[str]: ...
    @staticmethod
    def substitute(a0: str) -> str: ...
    def fromString(self, a0: str) -> bool: ...
    def toString(self) -> str: ...
    def key(self) -> str: ...
    def rawName(self) -> str: ...
    def setRawName(self, a0: str) -> None: ...
    def isCopyOf(self, a0: 'QFont') -> bool: ...
    def __lt__(self, a0: 'QFont') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def exactMatch(self) -> bool: ...
    def setRawMode(self, a0: bool) -> None: ...
    def rawMode(self) -> bool: ...
    def setStretch(self, a0: int) -> None: ...
    def stretch(self) -> int: ...
    def setStyleStrategy(self, s: 'QFont.StyleStrategy') -> None: ...
    def setStyleHint(self, hint: 'QFont.StyleHint', strategy: 'QFont.StyleStrategy' = ...) -> None: ...
    def styleStrategy(self) -> 'QFont.StyleStrategy': ...
    def styleHint(self) -> 'QFont.StyleHint': ...
    def setKerning(self, a0: bool) -> None: ...
    def kerning(self) -> bool: ...
    def setFixedPitch(self, a0: bool) -> None: ...
    def fixedPitch(self) -> bool: ...
    def setStrikeOut(self, a0: bool) -> None: ...
    def strikeOut(self) -> bool: ...
    def setOverline(self, a0: bool) -> None: ...
    def overline(self) -> bool: ...
    def setUnderline(self, a0: bool) -> None: ...
    def underline(self) -> bool: ...
    def style(self) -> 'QFont.Style': ...
    def setStyle(self, style: 'QFont.Style') -> None: ...
    def setWeight(self, a0: int) -> None: ...
    def weight(self) -> int: ...
    def setPixelSize(self, a0: int) -> None: ...
    def pixelSize(self) -> int: ...
    def setPointSizeF(self, a0: float) -> None: ...
    def pointSizeF(self) -> float: ...
    def setPointSize(self, a0: int) -> None: ...
    def pointSize(self) -> int: ...
    def setFamily(self, a0: str) -> None: ...
    def family(self) -> str: ...


class QFontDatabase(PyQt5.sipsimplewrapper):

    class SystemFont(int):
        GeneralFont = ... # type: QFontDatabase.SystemFont
        FixedFont = ... # type: QFontDatabase.SystemFont
        TitleFont = ... # type: QFontDatabase.SystemFont
        SmallestReadableFont = ... # type: QFontDatabase.SystemFont

    class WritingSystem(int):
        Any = ... # type: QFontDatabase.WritingSystem
        Latin = ... # type: QFontDatabase.WritingSystem
        Greek = ... # type: QFontDatabase.WritingSystem
        Cyrillic = ... # type: QFontDatabase.WritingSystem
        Armenian = ... # type: QFontDatabase.WritingSystem
        Hebrew = ... # type: QFontDatabase.WritingSystem
        Arabic = ... # type: QFontDatabase.WritingSystem
        Syriac = ... # type: QFontDatabase.WritingSystem
        Thaana = ... # type: QFontDatabase.WritingSystem
        Devanagari = ... # type: QFontDatabase.WritingSystem
        Bengali = ... # type: QFontDatabase.WritingSystem
        Gurmukhi = ... # type: QFontDatabase.WritingSystem
        Gujarati = ... # type: QFontDatabase.WritingSystem
        Oriya = ... # type: QFontDatabase.WritingSystem
        Tamil = ... # type: QFontDatabase.WritingSystem
        Telugu = ... # type: QFontDatabase.WritingSystem
        Kannada = ... # type: QFontDatabase.WritingSystem
        Malayalam = ... # type: QFontDatabase.WritingSystem
        Sinhala = ... # type: QFontDatabase.WritingSystem
        Thai = ... # type: QFontDatabase.WritingSystem
        Lao = ... # type: QFontDatabase.WritingSystem
        Tibetan = ... # type: QFontDatabase.WritingSystem
        Myanmar = ... # type: QFontDatabase.WritingSystem
        Georgian = ... # type: QFontDatabase.WritingSystem
        Khmer = ... # type: QFontDatabase.WritingSystem
        SimplifiedChinese = ... # type: QFontDatabase.WritingSystem
        TraditionalChinese = ... # type: QFontDatabase.WritingSystem
        Japanese = ... # type: QFontDatabase.WritingSystem
        Korean = ... # type: QFontDatabase.WritingSystem
        Vietnamese = ... # type: QFontDatabase.WritingSystem
        Other = ... # type: QFontDatabase.WritingSystem
        Symbol = ... # type: QFontDatabase.WritingSystem
        Ogham = ... # type: QFontDatabase.WritingSystem
        Runic = ... # type: QFontDatabase.WritingSystem
        Nko = ... # type: QFontDatabase.WritingSystem

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontDatabase') -> None: ...

    def isPrivateFamily(self, family: str) -> bool: ...
    @staticmethod
    def systemFont(type: 'QFontDatabase.SystemFont') -> QFont: ...
    @staticmethod
    def supportsThreadedFontRendering() -> bool: ...
    @staticmethod
    def removeAllApplicationFonts() -> bool: ...
    @staticmethod
    def removeApplicationFont(id: int) -> bool: ...
    @staticmethod
    def applicationFontFamilies(id: int) -> typing.List[str]: ...
    @staticmethod
    def addApplicationFontFromData(fontData: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> int: ...
    @staticmethod
    def addApplicationFont(fileName: str) -> int: ...
    @staticmethod
    def writingSystemSample(writingSystem: 'QFontDatabase.WritingSystem') -> str: ...
    @staticmethod
    def writingSystemName(writingSystem: 'QFontDatabase.WritingSystem') -> str: ...
    def weight(self, family: str, style: str) -> int: ...
    def bold(self, family: str, style: str) -> bool: ...
    def italic(self, family: str, style: str) -> bool: ...
    def isFixedPitch(self, family: str, style: str = ...) -> bool: ...
    def isScalable(self, family: str, style: str = ...) -> bool: ...
    def isSmoothlyScalable(self, family: str, style: str = ...) -> bool: ...
    def isBitmapScalable(self, family: str, style: str = ...) -> bool: ...
    def font(self, family: str, style: str, pointSize: int) -> QFont: ...
    @typing.overload
    def styleString(self, font: QFont) -> str: ...
    @typing.overload
    def styleString(self, fontInfo: 'QFontInfo') -> str: ...
    def smoothSizes(self, family: str, style: str) -> typing.List[int]: ...
    def pointSizes(self, family: str, style: str = ...) -> typing.List[int]: ...
    def styles(self, family: str) -> typing.List[str]: ...
    def families(self, writingSystem: 'QFontDatabase.WritingSystem' = ...) -> typing.List[str]: ...
    @typing.overload
    def writingSystems(self) -> typing.List['QFontDatabase.WritingSystem']: ...
    @typing.overload
    def writingSystems(self, family: str) -> typing.List['QFontDatabase.WritingSystem']: ...
    @staticmethod
    def standardSizes() -> typing.List[int]: ...


class QFontInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontInfo') -> None: ...

    def swap(self, other: 'QFontInfo') -> None: ...
    def styleName(self) -> str: ...
    def exactMatch(self) -> bool: ...
    def rawMode(self) -> bool: ...
    def styleHint(self) -> QFont.StyleHint: ...
    def fixedPitch(self) -> bool: ...
    def bold(self) -> bool: ...
    def weight(self) -> int: ...
    def style(self) -> QFont.Style: ...
    def italic(self) -> bool: ...
    def pointSizeF(self) -> float: ...
    def pointSize(self) -> int: ...
    def pixelSize(self) -> int: ...
    def family(self) -> str: ...


class QFontMetrics(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, a0: QFont, pd: QPaintDevice) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontMetrics') -> None: ...

    def fontDpi(self) -> float: ...
    def horizontalAdvance(self, a0: str, length: int = ...) -> int: ...
    def capHeight(self) -> int: ...
    def swap(self, other: 'QFontMetrics') -> None: ...
    def inFontUcs4(self, character: int) -> bool: ...
    def tightBoundingRect(self, text: str) -> QtCore.QRect: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def elidedText(self, text: str, mode: QtCore.Qt.TextElideMode, width: int, flags: int = ...) -> str: ...
    def averageCharWidth(self) -> int: ...
    def lineWidth(self) -> int: ...
    def strikeOutPos(self) -> int: ...
    def overlinePos(self) -> int: ...
    def underlinePos(self) -> int: ...
    def size(self, flags: int, text: str, tabStops: int = ..., tabArray: typing.Optional[typing.Optional[typing.List[int]]] = ...) -> QtCore.QSize: ...
    @typing.overload
    def boundingRect(self, text: str) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRect, flags: int, text: str, tabStops: int = ..., tabArray: typing.Optional[typing.Optional[typing.List[int]]] = ...) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, x: int, y: int, width: int, height: int, flags: int, text: str, tabStops: int = ..., tabArray: typing.Optional[typing.Optional[typing.List[int]]] = ...) -> QtCore.QRect: ...
    def boundingRectChar(self, a0: str) -> QtCore.QRect: ...
    def width(self, text: str, length: int = ...) -> int: ...
    def widthChar(self, a0: str) -> int: ...
    def rightBearing(self, a0: str) -> int: ...
    def leftBearing(self, a0: str) -> int: ...
    def inFont(self, a0: str) -> bool: ...
    def xHeight(self) -> int: ...
    def maxWidth(self) -> int: ...
    def minRightBearing(self) -> int: ...
    def minLeftBearing(self) -> int: ...
    def lineSpacing(self) -> int: ...
    def leading(self) -> int: ...
    def height(self) -> int: ...
    def descent(self) -> int: ...
    def ascent(self) -> int: ...


class QFontMetricsF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, a0: QFont, pd: QPaintDevice) -> None: ...
    @typing.overload
    def __init__(self, a0: QFontMetrics) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontMetricsF') -> None: ...

    def fontDpi(self) -> float: ...
    def horizontalAdvance(self, string: str, length: int = ...) -> float: ...
    def capHeight(self) -> float: ...
    def swap(self, other: 'QFontMetricsF') -> None: ...
    def inFontUcs4(self, character: int) -> bool: ...
    def tightBoundingRect(self, text: str) -> QtCore.QRectF: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def elidedText(self, text: str, mode: QtCore.Qt.TextElideMode, width: float, flags: int = ...) -> str: ...
    def averageCharWidth(self) -> float: ...
    def lineWidth(self) -> float: ...
    def strikeOutPos(self) -> float: ...
    def overlinePos(self) -> float: ...
    def underlinePos(self) -> float: ...
    def size(self, flags: int, text: str, tabStops: int = ..., tabArray: typing.Optional[typing.Optional[typing.List[int]]] = ...) -> QtCore.QSizeF: ...
    @typing.overload
    def boundingRect(self, string: str) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRectF, flags: int, text: str, tabStops: int = ..., tabArray: typing.Optional[typing.Optional[typing.List[int]]] = ...) -> QtCore.QRectF: ...
    def boundingRectChar(self, a0: str) -> QtCore.QRectF: ...
    def width(self, string: str) -> float: ...
    def widthChar(self, a0: str) -> float: ...
    def rightBearing(self, a0: str) -> float: ...
    def leftBearing(self, a0: str) -> float: ...
    def inFont(self, a0: str) -> bool: ...
    def xHeight(self) -> float: ...
    def maxWidth(self) -> float: ...
    def minRightBearing(self) -> float: ...
    def minLeftBearing(self) -> float: ...
    def lineSpacing(self) -> float: ...
    def leading(self) -> float: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...


class QMatrix4x3(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix4x3') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix4x3': ...
    def __imul__(self, a0: float) -> 'QMatrix4x3': ...
    def __isub__(self, a0: 'QMatrix4x3') -> 'QMatrix4x3': ...
    def __iadd__(self, a0: 'QMatrix4x3') -> 'QMatrix4x3': ...
    def transposed(self) -> 'QMatrix3x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix4x2(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix4x2') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix4x2': ...
    def __imul__(self, a0: float) -> 'QMatrix4x2': ...
    def __isub__(self, a0: 'QMatrix4x2') -> 'QMatrix4x2': ...
    def __iadd__(self, a0: 'QMatrix4x2') -> 'QMatrix4x2': ...
    def transposed(self) -> 'QMatrix2x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x4(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x4') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x4': ...
    def __imul__(self, a0: float) -> 'QMatrix3x4': ...
    def __isub__(self, a0: 'QMatrix3x4') -> 'QMatrix3x4': ...
    def __iadd__(self, a0: 'QMatrix3x4') -> 'QMatrix3x4': ...
    def transposed(self) -> QMatrix4x3: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x3(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x3') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x3': ...
    def __imul__(self, a0: float) -> 'QMatrix3x3': ...
    def __isub__(self, a0: 'QMatrix3x3') -> 'QMatrix3x3': ...
    def __iadd__(self, a0: 'QMatrix3x3') -> 'QMatrix3x3': ...
    def transposed(self) -> 'QMatrix3x3': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x2(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x2') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x2': ...
    def __imul__(self, a0: float) -> 'QMatrix3x2': ...
    def __isub__(self, a0: 'QMatrix3x2') -> 'QMatrix3x2': ...
    def __iadd__(self, a0: 'QMatrix3x2') -> 'QMatrix3x2': ...
    def transposed(self) -> 'QMatrix2x3': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x4(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x4') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x4': ...
    def __imul__(self, a0: float) -> 'QMatrix2x4': ...
    def __isub__(self, a0: 'QMatrix2x4') -> 'QMatrix2x4': ...
    def __iadd__(self, a0: 'QMatrix2x4') -> 'QMatrix2x4': ...
    def transposed(self) -> QMatrix4x2: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x3(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x3') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x3': ...
    def __imul__(self, a0: float) -> 'QMatrix2x3': ...
    def __isub__(self, a0: 'QMatrix2x3') -> 'QMatrix2x3': ...
    def __iadd__(self, a0: 'QMatrix2x3') -> 'QMatrix2x3': ...
    def transposed(self) -> QMatrix3x2: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x2(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x2') -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x2': ...
    def __imul__(self, a0: float) -> 'QMatrix2x2': ...
    def __isub__(self, a0: 'QMatrix2x2') -> 'QMatrix2x2': ...
    def __iadd__(self, a0: 'QMatrix2x2') -> 'QMatrix2x2': ...
    def transposed(self) -> 'QMatrix2x2': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> typing.List[float]: ...
    def data(self) -> typing.List[float]: ...
    def __repr__(self) -> str: ...


class QGlyphRun(PyQt5.sipsimplewrapper):

    class GlyphRunFlag(int):
        Overline = ... # type: QGlyphRun.GlyphRunFlag
        Underline = ... # type: QGlyphRun.GlyphRunFlag
        StrikeOut = ... # type: QGlyphRun.GlyphRunFlag
        RightToLeft = ... # type: QGlyphRun.GlyphRunFlag
        SplitLigature = ... # type: QGlyphRun.GlyphRunFlag

    class GlyphRunFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __xor__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __ior__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __or__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __iand__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __and__(self, f: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> 'QGlyphRun.GlyphRunFlags': ...
        def __invert__(self) -> 'QGlyphRun.GlyphRunFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGlyphRun') -> None: ...

    def swap(self, other: 'QGlyphRun') -> None: ...
    def isEmpty(self) -> bool: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def setBoundingRect(self, boundingRect: QtCore.QRectF) -> None: ...
    def flags(self) -> 'QGlyphRun.GlyphRunFlags': ...
    def setFlags(self, flags: typing.Union['QGlyphRun.GlyphRunFlags', 'QGlyphRun.GlyphRunFlag']) -> None: ...
    def setFlag(self, flag: 'QGlyphRun.GlyphRunFlag', enabled: bool = ...) -> None: ...
    def isRightToLeft(self) -> bool: ...
    def setRightToLeft(self, on: bool) -> None: ...
    def strikeOut(self) -> bool: ...
    def setStrikeOut(self, strikeOut: bool) -> None: ...
    def underline(self) -> bool: ...
    def setUnderline(self, underline: bool) -> None: ...
    def overline(self) -> bool: ...
    def setOverline(self, overline: bool) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def clear(self) -> None: ...
    def setPositions(self, positions: typing.Iterable[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    def positions(self) -> typing.List[QtCore.QPointF]: ...
    def setGlyphIndexes(self, glyphIndexes: typing.Iterable[int]) -> None: ...
    def glyphIndexes(self) -> typing.List[int]: ...
    def setRawFont(self, rawFont: 'QRawFont') -> None: ...
    def rawFont(self) -> 'QRawFont': ...


class QGuiApplication(QtCore.QCoreApplication):

    def __init__(self, argv: typing.List[str]) -> None: ...

    @staticmethod
    def highDpiScaleFactorRoundingPolicy() -> QtCore.Qt.HighDpiScaleFactorRoundingPolicy: ...
    @staticmethod
    def setHighDpiScaleFactorRoundingPolicy(policy: QtCore.Qt.HighDpiScaleFactorRoundingPolicy) -> None: ...
    fontChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def screenAt(point: QtCore.QPoint) -> 'QScreen': ...
    @staticmethod
    def desktopFileName() -> str: ...
    @staticmethod
    def setDesktopFileName(name: str) -> None: ...
    primaryScreenChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def setFallbackSessionManagementEnabled(a0: bool) -> None: ...
    @staticmethod
    def isFallbackSessionManagementEnabled() -> bool: ...
    paletteChanged: typing.ClassVar[QtCore.pyqtSignal]
    layoutDirectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    screenRemoved: typing.ClassVar[QtCore.pyqtSignal]
    def event(self, a0: QtCore.QEvent) -> bool: ...
    @staticmethod
    def windowIcon() -> 'QIcon': ...
    @staticmethod
    def setWindowIcon(icon: 'QIcon') -> None: ...
    @staticmethod
    def sync() -> None: ...
    @staticmethod
    def applicationState() -> QtCore.Qt.ApplicationState: ...
    def isSavingSession(self) -> bool: ...
    def sessionKey(self) -> str: ...
    def sessionId(self) -> str: ...
    def isSessionRestored(self) -> bool: ...
    def devicePixelRatio(self) -> float: ...
    @staticmethod
    def inputMethod() -> 'QInputMethod': ...
    @staticmethod
    def styleHints() -> 'QStyleHints': ...
    @staticmethod
    def modalWindow() -> 'QWindow': ...
    @staticmethod
    def applicationDisplayName() -> str: ...
    @staticmethod
    def setApplicationDisplayName(name: str) -> None: ...
    applicationDisplayNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    applicationStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusWindowChanged: typing.ClassVar[QtCore.pyqtSignal]
    saveStateRequest: typing.ClassVar[QtCore.pyqtSignal]
    commitDataRequest: typing.ClassVar[QtCore.pyqtSignal]
    focusObjectChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastWindowClosed: typing.ClassVar[QtCore.pyqtSignal]
    screenAdded: typing.ClassVar[QtCore.pyqtSignal]
    fontDatabaseChanged: typing.ClassVar[QtCore.pyqtSignal]
    def notify(self, a0: QtCore.QObject, a1: QtCore.QEvent) -> bool: ...
    @staticmethod
    def exec() -> int: ...
    @staticmethod
    def exec_() -> int: ...
    @staticmethod
    def quitOnLastWindowClosed() -> bool: ...
    @staticmethod
    def setQuitOnLastWindowClosed(quit: bool) -> None: ...
    @staticmethod
    def desktopSettingsAware() -> bool: ...
    @staticmethod
    def setDesktopSettingsAware(on: bool) -> None: ...
    @staticmethod
    def isLeftToRight() -> bool: ...
    @staticmethod
    def isRightToLeft() -> bool: ...
    @staticmethod
    def layoutDirection() -> QtCore.Qt.LayoutDirection: ...
    @staticmethod
    def setLayoutDirection(direction: QtCore.Qt.LayoutDirection) -> None: ...
    @staticmethod
    def mouseButtons() -> QtCore.Qt.MouseButtons: ...
    @staticmethod
    def queryKeyboardModifiers() -> QtCore.Qt.KeyboardModifiers: ...
    @staticmethod
    def keyboardModifiers() -> QtCore.Qt.KeyboardModifiers: ...
    @staticmethod
    def setPalette(pal: 'QPalette') -> None: ...
    @staticmethod
    def palette() -> 'QPalette': ...
    @staticmethod
    def clipboard() -> QClipboard: ...
    @staticmethod
    def setFont(a0: QFont) -> None: ...
    @staticmethod
    def font() -> QFont: ...
    @staticmethod
    def restoreOverrideCursor() -> None: ...
    @staticmethod
    def changeOverrideCursor(a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    @staticmethod
    def setOverrideCursor(a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    @staticmethod
    def overrideCursor() -> QCursor: ...
    @staticmethod
    def screens() -> typing.List['QScreen']: ...
    @staticmethod
    def primaryScreen() -> 'QScreen': ...
    @staticmethod
    def focusObject() -> QtCore.QObject: ...
    @staticmethod
    def focusWindow() -> 'QWindow': ...
    @staticmethod
    def platformName() -> str: ...
    @staticmethod
    def topLevelAt(pos: QtCore.QPoint) -> 'QWindow': ...
    @staticmethod
    def topLevelWindows() -> typing.List['QWindow']: ...
    @staticmethod
    def allWindows() -> typing.List['QWindow']: ...


class QIcon(PyQt5.sip.wrapper):

    class State(int):
        On = ... # type: QIcon.State
        Off = ... # type: QIcon.State

    class Mode(int):
        Normal = ... # type: QIcon.Mode
        Disabled = ... # type: QIcon.Mode
        Active = ... # type: QIcon.Mode
        Selected = ... # type: QIcon.Mode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, other: 'QIcon') -> None: ...
    @typing.overload
    def __init__(self, fileName: str) -> None: ...
    @typing.overload
    def __init__(self, engine: 'QIconEngine') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    @staticmethod
    def setFallbackThemeName(name: str) -> None: ...
    @staticmethod
    def fallbackThemeName() -> str: ...
    @staticmethod
    def setFallbackSearchPaths(paths: typing.Iterable[str]) -> None: ...
    @staticmethod
    def fallbackSearchPaths() -> typing.List[str]: ...
    def isMask(self) -> bool: ...
    def setIsMask(self, isMask: bool) -> None: ...
    def swap(self, other: 'QIcon') -> None: ...
    def name(self) -> str: ...
    @staticmethod
    def setThemeName(path: str) -> None: ...
    @staticmethod
    def themeName() -> str: ...
    @staticmethod
    def setThemeSearchPaths(searchpath: typing.Iterable[str]) -> None: ...
    @staticmethod
    def themeSearchPaths() -> typing.List[str]: ...
    @staticmethod
    def hasThemeIcon(name: str) -> bool: ...
    @typing.overload
    @staticmethod
    def fromTheme(name: str) -> 'QIcon': ...
    @typing.overload
    @staticmethod
    def fromTheme(name: str, fallback: 'QIcon') -> 'QIcon': ...
    def cacheKey(self) -> int: ...
    def addFile(self, fileName: str, size: QtCore.QSize = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def addPixmap(self, pixmap: QPixmap, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def isDetached(self) -> bool: ...
    def isNull(self) -> bool: ...
    @typing.overload
    def paint(self, painter: 'QPainter', rect: QtCore.QRect, alignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag] = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    @typing.overload
    def paint(self, painter: 'QPainter', x: int, y: int, w: int, h: int, alignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag] = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def availableSizes(self, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> typing.List[QtCore.QSize]: ...
    @typing.overload
    def actualSize(self, size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QtCore.QSize: ...
    @typing.overload
    def actualSize(self, window: 'QWindow', size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QtCore.QSize: ...
    @typing.overload
    def pixmap(self, size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, w: int, h: int, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, extent: int, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, window: 'QWindow', size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...


class QIconEngine(PyQt5.sip.wrapper):

    class IconEngineHook(int):
        AvailableSizesHook = ... # type: QIconEngine.IconEngineHook
        IconNameHook = ... # type: QIconEngine.IconEngineHook
        IsNullHook = ... # type: QIconEngine.IconEngineHook
        ScaledPixmapHook = ... # type: QIconEngine.IconEngineHook

    class AvailableSizesArgument(PyQt5.sipsimplewrapper):

        mode = ... # type: QIcon.Mode
        sizes = ... # type: typing.Iterable[QtCore.QSize]
        state = ... # type: QIcon.State

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QIconEngine.AvailableSizesArgument') -> None: ...

    class ScaledPixmapArgument(PyQt5.sipsimplewrapper):

        mode = ... # type: QIcon.Mode
        pixmap = ... # type: QPixmap
        scale = ... # type: float
        size = ... # type: QtCore.QSize
        state = ... # type: QIcon.State

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QIconEngine.ScaledPixmapArgument') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QIconEngine') -> None: ...

    def scaledPixmap(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State, scale: float) -> QPixmap: ...
    def isNull(self) -> bool: ...
    def iconName(self) -> str: ...
    def availableSizes(self, mode: QIcon.Mode = ..., state: QIcon.State = ...) -> typing.List[QtCore.QSize]: ...
    def write(self, out: QtCore.QDataStream) -> bool: ...
    def read(self, in_: QtCore.QDataStream) -> bool: ...
    def clone(self) -> 'QIconEngine': ...
    def key(self) -> str: ...
    def addFile(self, fileName: str, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> None: ...
    def addPixmap(self, pixmap: QPixmap, mode: QIcon.Mode, state: QIcon.State) -> None: ...
    def pixmap(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> QPixmap: ...
    def actualSize(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> QtCore.QSize: ...
    def paint(self, painter: 'QPainter', rect: QtCore.QRect, mode: QIcon.Mode, state: QIcon.State) -> None: ...


class QImage(QPaintDevice):

    class Format(int):
        Format_Invalid = ... # type: QImage.Format
        Format_Mono = ... # type: QImage.Format
        Format_MonoLSB = ... # type: QImage.Format
        Format_Indexed8 = ... # type: QImage.Format
        Format_RGB32 = ... # type: QImage.Format
        Format_ARGB32 = ... # type: QImage.Format
        Format_ARGB32_Premultiplied = ... # type: QImage.Format
        Format_RGB16 = ... # type: QImage.Format
        Format_ARGB8565_Premultiplied = ... # type: QImage.Format
        Format_RGB666 = ... # type: QImage.Format
        Format_ARGB6666_Premultiplied = ... # type: QImage.Format
        Format_RGB555 = ... # type: QImage.Format
        Format_ARGB8555_Premultiplied = ... # type: QImage.Format
        Format_RGB888 = ... # type: QImage.Format
        Format_RGB444 = ... # type: QImage.Format
        Format_ARGB4444_Premultiplied = ... # type: QImage.Format
        Format_RGBX8888 = ... # type: QImage.Format
        Format_RGBA8888 = ... # type: QImage.Format
        Format_RGBA8888_Premultiplied = ... # type: QImage.Format
        Format_BGR30 = ... # type: QImage.Format
        Format_A2BGR30_Premultiplied = ... # type: QImage.Format
        Format_RGB30 = ... # type: QImage.Format
        Format_A2RGB30_Premultiplied = ... # type: QImage.Format
        Format_Alpha8 = ... # type: QImage.Format
        Format_Grayscale8 = ... # type: QImage.Format
        Format_RGBX64 = ... # type: QImage.Format
        Format_RGBA64 = ... # type: QImage.Format
        Format_RGBA64_Premultiplied = ... # type: QImage.Format
        Format_Grayscale16 = ... # type: QImage.Format
        Format_BGR888 = ... # type: QImage.Format

    class InvertMode(int):
        InvertRgb = ... # type: QImage.InvertMode
        InvertRgba = ... # type: QImage.InvertMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, data: bytes, width: int, height: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, data: PyQt5.sip.voidptr, width: int, height: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, data: bytes, width: int, height: int, bytesPerLine: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, data: PyQt5.sip.voidptr, width: int, height: int, bytesPerLine: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, xpm: typing.List[str]) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QImage') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def applyColorTransform(self, transform: QColorTransform) -> None: ...
    def setColorSpace(self, a0: QColorSpace) -> None: ...
    def convertToColorSpace(self, a0: QColorSpace) -> None: ...
    def convertedToColorSpace(self, a0: QColorSpace) -> 'QImage': ...
    def colorSpace(self) -> QColorSpace: ...
    def convertTo(self, f: 'QImage.Format', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    def sizeInBytes(self) -> int: ...
    def reinterpretAsFormat(self, f: 'QImage.Format') -> bool: ...
    @typing.overload
    def setPixelColor(self, x: int, y: int, c: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setPixelColor(self, pt: QtCore.QPoint, c: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def pixelColor(self, x: int, y: int) -> QColor: ...
    @typing.overload
    def pixelColor(self, pt: QtCore.QPoint) -> QColor: ...
    @staticmethod
    def toImageFormat(format: 'QPixelFormat') -> 'QImage.Format': ...
    @staticmethod
    def toPixelFormat(format: 'QImage.Format') -> 'QPixelFormat': ...
    def pixelFormat(self) -> 'QPixelFormat': ...
    def setDevicePixelRatio(self, scaleFactor: float) -> None: ...
    def devicePixelRatio(self) -> float: ...
    def swap(self, other: 'QImage') -> None: ...
    def bitPlaneCount(self) -> int: ...
    def byteCount(self) -> int: ...
    def setColorCount(self, a0: int) -> None: ...
    def colorCount(self) -> int: ...
    def cacheKey(self) -> int: ...
    @staticmethod
    def trueMatrix(a0: 'QTransform', w: int, h: int) -> 'QTransform': ...
    def transformed(self, matrix: 'QTransform', mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def createMaskFromColor(self, color: int, mode: QtCore.Qt.MaskMode = ...) -> 'QImage': ...
    def smoothScaled(self, w: int, h: int) -> 'QImage': ...
    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...
    def setText(self, key: str, value: str) -> None: ...
    def text(self, key: str = ...) -> str: ...
    def textKeys(self) -> typing.List[str]: ...
    def setOffset(self, a0: QtCore.QPoint) -> None: ...
    def offset(self) -> QtCore.QPoint: ...
    def setDotsPerMeterY(self, a0: int) -> None: ...
    def setDotsPerMeterX(self, a0: int) -> None: ...
    def dotsPerMeterY(self) -> int: ...
    def dotsPerMeterX(self) -> int: ...
    def paintEngine(self) -> 'QPaintEngine': ...
    @typing.overload
    @staticmethod
    def fromData(data: PyQt5.sip.array[bytes], format: typing.Optional[str] = ...) -> 'QImage': ...
    @typing.overload
    @staticmethod
    def fromData(data: typing.Union[QtCore.QByteArray, bytes, bytearray], format: typing.Optional[str] = ...) -> 'QImage': ...
    @typing.overload
    def save(self, fileName: str, format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def save(self, device: QtCore.QIODevice, format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, data: PyQt5.sip.array[bytes], format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray], format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, device: QtCore.QIODevice, format: str) -> bool: ...
    @typing.overload
    def load(self, fileName: str, format: typing.Optional[str] = ...) -> bool: ...
    def invertPixels(self, mode: 'QImage.InvertMode' = ...) -> None: ...
    def rgbSwapped(self) -> 'QImage': ...
    def mirrored(self, horizontal: bool = ..., vertical: bool = ...) -> 'QImage': ...
    def scaledToHeight(self, height: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def scaledToWidth(self, width: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    @typing.overload
    def scaled(self, width: int, height: int, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    @typing.overload
    def scaled(self, size: QtCore.QSize, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def createHeuristicMask(self, clipTight: bool = ...) -> 'QImage': ...
    def createAlphaMask(self, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QImage': ...
    def setAlphaChannel(self, alphaChannel: 'QImage') -> None: ...
    def hasAlphaChannel(self) -> bool: ...
    @typing.overload
    def fill(self, color: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fill(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def fill(self, pixel: int) -> None: ...
    def setColorTable(self, colors: typing.Iterable[int]) -> None: ...
    def colorTable(self) -> typing.List[int]: ...
    @typing.overload
    def setPixel(self, pt: QtCore.QPoint, index_or_rgb: int) -> None: ...
    @typing.overload
    def setPixel(self, x: int, y: int, index_or_rgb: int) -> None: ...
    @typing.overload
    def pixel(self, pt: QtCore.QPoint) -> int: ...
    @typing.overload
    def pixel(self, x: int, y: int) -> int: ...
    @typing.overload
    def pixelIndex(self, pt: QtCore.QPoint) -> int: ...
    @typing.overload
    def pixelIndex(self, x: int, y: int) -> int: ...
    @typing.overload
    def valid(self, pt: QtCore.QPoint) -> bool: ...
    @typing.overload
    def valid(self, x: int, y: int) -> bool: ...
    def bytesPerLine(self) -> int: ...
    def constScanLine(self, a0: int) -> PyQt5.sip.voidptr: ...
    def scanLine(self, a0: int) -> PyQt5.sip.voidptr: ...
    def constBits(self) -> PyQt5.sip.voidptr: ...
    def bits(self) -> PyQt5.sip.voidptr: ...
    def isGrayscale(self) -> bool: ...
    def allGray(self) -> bool: ...
    def setColor(self, i: int, c: int) -> None: ...
    def color(self, i: int) -> int: ...
    def depth(self) -> int: ...
    def rect(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    @typing.overload
    def convertToFormat(self, f: 'QImage.Format', flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QImage': ...
    @typing.overload
    def convertToFormat(self, f: 'QImage.Format', colorTable: typing.Iterable[int], flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> 'QImage': ...
    def format(self) -> 'QImage.Format': ...
    @typing.overload
    def copy(self, rect: QtCore.QRect = ...) -> 'QImage': ...
    @typing.overload
    def copy(self, x: int, y: int, w: int, h: int) -> 'QImage': ...
    def isDetached(self) -> bool: ...
    def detach(self) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def devType(self) -> int: ...
    def isNull(self) -> bool: ...


class QImageIOHandler(PyQt5.sipsimplewrapper):

    class Transformation(int):
        TransformationNone = ... # type: QImageIOHandler.Transformation
        TransformationMirror = ... # type: QImageIOHandler.Transformation
        TransformationFlip = ... # type: QImageIOHandler.Transformation
        TransformationRotate180 = ... # type: QImageIOHandler.Transformation
        TransformationRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationMirrorAndRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationFlipAndRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationRotate270 = ... # type: QImageIOHandler.Transformation

    class ImageOption(int):
        Size = ... # type: QImageIOHandler.ImageOption
        ClipRect = ... # type: QImageIOHandler.ImageOption
        Description = ... # type: QImageIOHandler.ImageOption
        ScaledClipRect = ... # type: QImageIOHandler.ImageOption
        ScaledSize = ... # type: QImageIOHandler.ImageOption
        CompressionRatio = ... # type: QImageIOHandler.ImageOption
        Gamma = ... # type: QImageIOHandler.ImageOption
        Quality = ... # type: QImageIOHandler.ImageOption
        Name = ... # type: QImageIOHandler.ImageOption
        SubType = ... # type: QImageIOHandler.ImageOption
        IncrementalReading = ... # type: QImageIOHandler.ImageOption
        Endianness = ... # type: QImageIOHandler.ImageOption
        Animation = ... # type: QImageIOHandler.ImageOption
        BackgroundColor = ... # type: QImageIOHandler.ImageOption
        SupportedSubTypes = ... # type: QImageIOHandler.ImageOption
        OptimizedWrite = ... # type: QImageIOHandler.ImageOption
        ProgressiveScanWrite = ... # type: QImageIOHandler.ImageOption
        ImageTransformation = ... # type: QImageIOHandler.ImageOption
        TransformedByDefault = ... # type: QImageIOHandler.ImageOption

    class Transformations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __xor__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __ior__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __or__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __iand__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __and__(self, f: typing.Union['QImageIOHandler.Transformations', 'QImageIOHandler.Transformation']) -> 'QImageIOHandler.Transformations': ...
        def __invert__(self) -> 'QImageIOHandler.Transformations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self) -> None: ...

    def currentImageRect(self) -> QtCore.QRect: ...
    def currentImageNumber(self) -> int: ...
    def nextImageDelay(self) -> int: ...
    def imageCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToImage(self, imageNumber: int) -> bool: ...
    def jumpToNextImage(self) -> bool: ...
    def supportsOption(self, option: 'QImageIOHandler.ImageOption') -> bool: ...
    def setOption(self, option: 'QImageIOHandler.ImageOption', value: typing.Any) -> None: ...
    def option(self, option: 'QImageIOHandler.ImageOption') -> typing.Any: ...
    def write(self, image: QImage) -> bool: ...
    def read(self, image: QImage) -> bool: ...
    def canRead(self) -> bool: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def device(self) -> QtCore.QIODevice: ...
    def setDevice(self, device: QtCore.QIODevice) -> None: ...


class QImageReader(PyQt5.sipsimplewrapper):

    class ImageReaderError(int):
        UnknownError = ... # type: QImageReader.ImageReaderError
        FileNotFoundError = ... # type: QImageReader.ImageReaderError
        DeviceError = ... # type: QImageReader.ImageReaderError
        UnsupportedFormatError = ... # type: QImageReader.ImageReaderError
        InvalidDataError = ... # type: QImageReader.ImageReaderError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: QtCore.QIODevice, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...

    @staticmethod
    def imageFormatsForMimeType(mimeType: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.List[QtCore.QByteArray]: ...
    def gamma(self) -> float: ...
    def setGamma(self, gamma: float) -> None: ...
    def autoTransform(self) -> bool: ...
    def setAutoTransform(self, enabled: bool) -> None: ...
    def transformation(self) -> QImageIOHandler.Transformations: ...
    def supportedSubTypes(self) -> typing.List[QtCore.QByteArray]: ...
    def subType(self) -> QtCore.QByteArray: ...
    @staticmethod
    def supportedMimeTypes() -> typing.List[QtCore.QByteArray]: ...
    def decideFormatFromContent(self) -> bool: ...
    def setDecideFormatFromContent(self, ignored: bool) -> None: ...
    def autoDetectImageFormat(self) -> bool: ...
    def setAutoDetectImageFormat(self, enabled: bool) -> None: ...
    def supportsOption(self, option: QImageIOHandler.ImageOption) -> bool: ...
    def quality(self) -> int: ...
    def setQuality(self, quality: int) -> None: ...
    def supportsAnimation(self) -> bool: ...
    def backgroundColor(self) -> QColor: ...
    def setBackgroundColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def text(self, key: str) -> str: ...
    def textKeys(self) -> typing.List[str]: ...
    @staticmethod
    def supportedImageFormats() -> typing.List[QtCore.QByteArray]: ...
    @typing.overload
    @staticmethod
    def imageFormat(fileName: str) -> QtCore.QByteArray: ...
    @typing.overload
    @staticmethod
    def imageFormat(device: QtCore.QIODevice) -> QtCore.QByteArray: ...
    @typing.overload
    def imageFormat(self) -> QImage.Format: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QImageReader.ImageReaderError': ...
    def currentImageRect(self) -> QtCore.QRect: ...
    def currentImageNumber(self) -> int: ...
    def nextImageDelay(self) -> int: ...
    def imageCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToImage(self, imageNumber: int) -> bool: ...
    def jumpToNextImage(self) -> bool: ...
    @typing.overload
    def read(self) -> QImage: ...
    @typing.overload
    def read(self, image: QImage) -> bool: ...
    def canRead(self) -> bool: ...
    def scaledClipRect(self) -> QtCore.QRect: ...
    def setScaledClipRect(self, rect: QtCore.QRect) -> None: ...
    def scaledSize(self) -> QtCore.QSize: ...
    def setScaledSize(self, size: QtCore.QSize) -> None: ...
    def clipRect(self) -> QtCore.QRect: ...
    def setClipRect(self, rect: QtCore.QRect) -> None: ...
    def size(self) -> QtCore.QSize: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: str) -> None: ...
    def device(self) -> QtCore.QIODevice: ...
    def setDevice(self, device: QtCore.QIODevice) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...


class QImageWriter(PyQt5.sipsimplewrapper):

    class ImageWriterError(int):
        UnknownError = ... # type: QImageWriter.ImageWriterError
        DeviceError = ... # type: QImageWriter.ImageWriterError
        UnsupportedFormatError = ... # type: QImageWriter.ImageWriterError
        InvalidImageError = ... # type: QImageWriter.ImageWriterError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: QtCore.QIODevice, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...

    @staticmethod
    def imageFormatsForMimeType(mimeType: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> typing.List[QtCore.QByteArray]: ...
    def setTransformation(self, orientation: typing.Union[QImageIOHandler.Transformations, QImageIOHandler.Transformation]) -> None: ...
    def transformation(self) -> QImageIOHandler.Transformations: ...
    def progressiveScanWrite(self) -> bool: ...
    def setProgressiveScanWrite(self, progressive: bool) -> None: ...
    def optimizedWrite(self) -> bool: ...
    def setOptimizedWrite(self, optimize: bool) -> None: ...
    def supportedSubTypes(self) -> typing.List[QtCore.QByteArray]: ...
    def subType(self) -> QtCore.QByteArray: ...
    def setSubType(self, type: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    @staticmethod
    def supportedMimeTypes() -> typing.List[QtCore.QByteArray]: ...
    def compression(self) -> int: ...
    def setCompression(self, compression: int) -> None: ...
    def supportsOption(self, option: QImageIOHandler.ImageOption) -> bool: ...
    def setText(self, key: str, text: str) -> None: ...
    @staticmethod
    def supportedImageFormats() -> typing.List[QtCore.QByteArray]: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QImageWriter.ImageWriterError': ...
    def write(self, image: QImage) -> bool: ...
    def canWrite(self) -> bool: ...
    def gamma(self) -> float: ...
    def setGamma(self, gamma: float) -> None: ...
    def quality(self) -> int: ...
    def setQuality(self, quality: int) -> None: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: str) -> None: ...
    def device(self) -> QtCore.QIODevice: ...
    def setDevice(self, device: QtCore.QIODevice) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...


class QInputMethod(QtCore.QObject):

    class Action(int):
        Click = ... # type: QInputMethod.Action
        ContextMenu = ... # type: QInputMethod.Action

    inputItemClipRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    anchorRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def inputItemClipRectangle(self) -> QtCore.QRectF: ...
    def anchorRectangle(self) -> QtCore.QRectF: ...
    inputDirectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    localeChanged: typing.ClassVar[QtCore.pyqtSignal]
    animatingChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    keyboardRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def invokeAction(self, a: 'QInputMethod.Action', cursorPosition: int) -> None: ...
    def commit(self) -> None: ...
    def reset(self) -> None: ...
    def update(self, queries: typing.Union[QtCore.Qt.InputMethodQueries, QtCore.Qt.InputMethodQuery]) -> None: ...
    def hide(self) -> None: ...
    def show(self) -> None: ...
    @staticmethod
    def queryFocusObject(query: QtCore.Qt.InputMethodQuery, argument: typing.Any) -> typing.Any: ...
    def setInputItemRectangle(self, rect: QtCore.QRectF) -> None: ...
    def inputItemRectangle(self) -> QtCore.QRectF: ...
    def inputDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def locale(self) -> QtCore.QLocale: ...
    def isAnimating(self) -> bool: ...
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def keyboardRectangle(self) -> QtCore.QRectF: ...
    def cursorRectangle(self) -> QtCore.QRectF: ...
    def setInputItemTransform(self, transform: 'QTransform') -> None: ...
    def inputItemTransform(self) -> 'QTransform': ...


class QKeySequence(PyQt5.sipsimplewrapper):

    class StandardKey(int):
        UnknownKey = ... # type: QKeySequence.StandardKey
        HelpContents = ... # type: QKeySequence.StandardKey
        WhatsThis = ... # type: QKeySequence.StandardKey
        Open = ... # type: QKeySequence.StandardKey
        Close = ... # type: QKeySequence.StandardKey
        Save = ... # type: QKeySequence.StandardKey
        New = ... # type: QKeySequence.StandardKey
        Delete = ... # type: QKeySequence.StandardKey
        Cut = ... # type: QKeySequence.StandardKey
        Copy = ... # type: QKeySequence.StandardKey
        Paste = ... # type: QKeySequence.StandardKey
        Undo = ... # type: QKeySequence.StandardKey
        Redo = ... # type: QKeySequence.StandardKey
        Back = ... # type: QKeySequence.StandardKey
        Forward = ... # type: QKeySequence.StandardKey
        Refresh = ... # type: QKeySequence.StandardKey
        ZoomIn = ... # type: QKeySequence.StandardKey
        ZoomOut = ... # type: QKeySequence.StandardKey
        Print = ... # type: QKeySequence.StandardKey
        AddTab = ... # type: QKeySequence.StandardKey
        NextChild = ... # type: QKeySequence.StandardKey
        PreviousChild = ... # type: QKeySequence.StandardKey
        Find = ... # type: QKeySequence.StandardKey
        FindNext = ... # type: QKeySequence.StandardKey
        FindPrevious = ... # type: QKeySequence.StandardKey
        Replace = ... # type: QKeySequence.StandardKey
        SelectAll = ... # type: QKeySequence.StandardKey
        Bold = ... # type: QKeySequence.StandardKey
        Italic = ... # type: QKeySequence.StandardKey
        Underline = ... # type: QKeySequence.StandardKey
        MoveToNextChar = ... # type: QKeySequence.StandardKey
        MoveToPreviousChar = ... # type: QKeySequence.StandardKey
        MoveToNextWord = ... # type: QKeySequence.StandardKey
        MoveToPreviousWord = ... # type: QKeySequence.StandardKey
        MoveToNextLine = ... # type: QKeySequence.StandardKey
        MoveToPreviousLine = ... # type: QKeySequence.StandardKey
        MoveToNextPage = ... # type: QKeySequence.StandardKey
        MoveToPreviousPage = ... # type: QKeySequence.StandardKey
        MoveToStartOfLine = ... # type: QKeySequence.StandardKey
        MoveToEndOfLine = ... # type: QKeySequence.StandardKey
        MoveToStartOfBlock = ... # type: QKeySequence.StandardKey
        MoveToEndOfBlock = ... # type: QKeySequence.StandardKey
        MoveToStartOfDocument = ... # type: QKeySequence.StandardKey
        MoveToEndOfDocument = ... # type: QKeySequence.StandardKey
        SelectNextChar = ... # type: QKeySequence.StandardKey
        SelectPreviousChar = ... # type: QKeySequence.StandardKey
        SelectNextWord = ... # type: QKeySequence.StandardKey
        SelectPreviousWord = ... # type: QKeySequence.StandardKey
        SelectNextLine = ... # type: QKeySequence.StandardKey
        SelectPreviousLine = ... # type: QKeySequence.StandardKey
        SelectNextPage = ... # type: QKeySequence.StandardKey
        SelectPreviousPage = ... # type: QKeySequence.StandardKey
        SelectStartOfLine = ... # type: QKeySequence.StandardKey
        SelectEndOfLine = ... # type: QKeySequence.StandardKey
        SelectStartOfBlock = ... # type: QKeySequence.StandardKey
        SelectEndOfBlock = ... # type: QKeySequence.StandardKey
        SelectStartOfDocument = ... # type: QKeySequence.StandardKey
        SelectEndOfDocument = ... # type: QKeySequence.StandardKey
        DeleteStartOfWord = ... # type: QKeySequence.StandardKey
        DeleteEndOfWord = ... # type: QKeySequence.StandardKey
        DeleteEndOfLine = ... # type: QKeySequence.StandardKey
        InsertParagraphSeparator = ... # type: QKeySequence.StandardKey
        InsertLineSeparator = ... # type: QKeySequence.StandardKey
        SaveAs = ... # type: QKeySequence.StandardKey
        Preferences = ... # type: QKeySequence.StandardKey
        Quit = ... # type: QKeySequence.StandardKey
        FullScreen = ... # type: QKeySequence.StandardKey
        Deselect = ... # type: QKeySequence.StandardKey
        DeleteCompleteLine = ... # type: QKeySequence.StandardKey
        Backspace = ... # type: QKeySequence.StandardKey
        Cancel = ... # type: QKeySequence.StandardKey

    class SequenceMatch(int):
        NoMatch = ... # type: QKeySequence.SequenceMatch
        PartialMatch = ... # type: QKeySequence.SequenceMatch
        ExactMatch = ... # type: QKeySequence.SequenceMatch

    class SequenceFormat(int):
        NativeText = ... # type: QKeySequence.SequenceFormat
        PortableText = ... # type: QKeySequence.SequenceFormat

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, ks: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> None: ...
    @typing.overload
    def __init__(self, key: str, format: 'QKeySequence.SequenceFormat' = ...) -> None: ...
    @typing.overload
    def __init__(self, k1: int, key2: int = ..., key3: int = ..., key4: int = ...) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __hash__(self) -> int: ...
    @staticmethod
    def listToString(list: typing.Iterable[typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]], format: 'QKeySequence.SequenceFormat' = ...) -> str: ...
    @staticmethod
    def listFromString(str: str, format: 'QKeySequence.SequenceFormat' = ...) -> typing.List['QKeySequence']: ...
    @staticmethod
    def keyBindings(key: 'QKeySequence.StandardKey') -> typing.List['QKeySequence']: ...
    @staticmethod
    def fromString(str: str, format: 'QKeySequence.SequenceFormat' = ...) -> 'QKeySequence': ...
    def toString(self, format: 'QKeySequence.SequenceFormat' = ...) -> str: ...
    def swap(self, other: 'QKeySequence') -> None: ...
    def isDetached(self) -> bool: ...
    def __ge__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> bool: ...
    def __le__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> bool: ...
    def __gt__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> bool: ...
    def __lt__(self, ks: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __getitem__(self, i: int) -> int: ...
    @staticmethod
    def mnemonic(text: str) -> 'QKeySequence': ...
    def matches(self, seq: typing.Union['QKeySequence', 'QKeySequence.StandardKey', str, int]) -> 'QKeySequence.SequenceMatch': ...
    def isEmpty(self) -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...


class QMatrix4x4(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, values: typing.Sequence[float]) -> None: ...
    @typing.overload
    def __init__(self, m11: float, m12: float, m13: float, m14: float, m21: float, m22: float, m23: float, m24: float, m31: float, m32: float, m33: float, m34: float, m41: float, m42: float, m43: float, m44: float) -> None: ...
    @typing.overload
    def __init__(self, transform: 'QTransform') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMatrix4x4') -> None: ...

    def __truediv__(self, matrix: 'QMatrix4x4', divisor: float) -> 'QMatrix4x4': ...
    def __add__(self, m1: 'QMatrix4x4', m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __sub__(self, m1: 'QMatrix4x4', m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, m1: 'QMatrix4x4', m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, matrix: 'QMatrix4x4', vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, matrix: 'QMatrix4x4', vector: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, matrix: 'QMatrix4x4', point: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def __mul__(self, matrix: 'QMatrix4x4', point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    @typing.overload
    def __mul__(self, factor: float, matrix: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, matrix: 'QMatrix4x4', factor: float) -> 'QMatrix4x4': ...
    def __matmul__(self, m1: 'QMatrix4x4', m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __neg__(self) -> 'QMatrix4x4': ...
    def isAffine(self) -> bool: ...
    @typing.overload
    def viewport(self, left: float, bottom: float, width: float, height: float, nearPlane: float = ..., farPlane: float = ...) -> None: ...
    @typing.overload
    def viewport(self, rect: QtCore.QRectF) -> None: ...
    def mapVector(self, vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def map(self, point: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def map(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    @typing.overload
    def map(self, point: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def map(self, point: 'QVector4D') -> 'QVector4D': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, divisor: float) -> 'QMatrix4x4': ...
    def __imatmul__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __imul__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QMatrix4x4': ...
    def __isub__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __iadd__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def setRow(self, index: int, value: 'QVector4D') -> None: ...
    def row(self, index: int) -> 'QVector4D': ...
    def setColumn(self, index: int, value: 'QVector4D') -> None: ...
    def column(self, index: int) -> 'QVector4D': ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def optimize(self) -> None: ...
    def data(self) -> typing.List[float]: ...
    @typing.overload
    def mapRect(self, rect: QtCore.QRect) -> QtCore.QRect: ...
    @typing.overload
    def mapRect(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    @typing.overload
    def toTransform(self) -> 'QTransform': ...
    @typing.overload
    def toTransform(self, distanceToPlane: float) -> 'QTransform': ...
    def copyDataTo(self) -> typing.List[float]: ...
    def lookAt(self, eye: 'QVector3D', center: 'QVector3D', up: 'QVector3D') -> None: ...
    def perspective(self, angle: float, aspect: float, nearPlane: float, farPlane: float) -> None: ...
    def frustum(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
    @typing.overload
    def ortho(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def ortho(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def ortho(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
    @typing.overload
    def rotate(self, angle: float, vector: 'QVector3D') -> None: ...
    @typing.overload
    def rotate(self, angle: float, x: float, y: float, z: float = ...) -> None: ...
    @typing.overload
    def rotate(self, quaternion: 'QQuaternion') -> None: ...
    @typing.overload
    def translate(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def translate(self, x: float, y: float) -> None: ...
    @typing.overload
    def translate(self, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def scale(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def scale(self, x: float, y: float) -> None: ...
    @typing.overload
    def scale(self, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def scale(self, factor: float) -> None: ...
    def normalMatrix(self) -> QMatrix3x3: ...
    def transposed(self) -> 'QMatrix4x4': ...
    def inverted(self) -> typing.Tuple['QMatrix4x4', bool]: ...
    def determinant(self) -> float: ...
    def __repr__(self) -> str: ...


class QMovie(QtCore.QObject):

    class CacheMode(int):
        CacheNone = ... # type: QMovie.CacheMode
        CacheAll = ... # type: QMovie.CacheMode

    class MovieState(int):
        NotRunning = ... # type: QMovie.MovieState
        Paused = ... # type: QMovie.MovieState
        Running = ... # type: QMovie.MovieState

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, device: QtCore.QIODevice, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def lastErrorString(self) -> str: ...
    def lastError(self) -> QImageReader.ImageReaderError: ...
    def stop(self) -> None: ...
    def setPaused(self, paused: bool) -> None: ...
    def jumpToNextFrame(self) -> bool: ...
    def start(self) -> None: ...
    frameChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    updated: typing.ClassVar[QtCore.pyqtSignal]
    resized: typing.ClassVar[QtCore.pyqtSignal]
    started: typing.ClassVar[QtCore.pyqtSignal]
    def setCacheMode(self, mode: 'QMovie.CacheMode') -> None: ...
    def cacheMode(self) -> 'QMovie.CacheMode': ...
    def setScaledSize(self, size: QtCore.QSize) -> None: ...
    def scaledSize(self) -> QtCore.QSize: ...
    def speed(self) -> int: ...
    def setSpeed(self, percentSpeed: int) -> None: ...
    def currentFrameNumber(self) -> int: ...
    def nextFrameDelay(self) -> int: ...
    def frameCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToFrame(self, frameNumber: int) -> bool: ...
    def isValid(self) -> bool: ...
    def currentPixmap(self) -> QPixmap: ...
    def currentImage(self) -> QImage: ...
    def frameRect(self) -> QtCore.QRect: ...
    def state(self) -> 'QMovie.MovieState': ...
    def backgroundColor(self) -> QColor: ...
    def setBackgroundColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: str) -> None: ...
    def device(self) -> QtCore.QIODevice: ...
    def setDevice(self, device: QtCore.QIODevice) -> None: ...
    @staticmethod
    def supportedFormats() -> typing.List[QtCore.QByteArray]: ...


class QSurface(PyQt5.sipsimplewrapper):

    class SurfaceType(int):
        RasterSurface = ... # type: QSurface.SurfaceType
        OpenGLSurface = ... # type: QSurface.SurfaceType
        RasterGLSurface = ... # type: QSurface.SurfaceType
        OpenVGSurface = ... # type: QSurface.SurfaceType
        VulkanSurface = ... # type: QSurface.SurfaceType
        MetalSurface = ... # type: QSurface.SurfaceType

    class SurfaceClass(int):
        Window = ... # type: QSurface.SurfaceClass
        Offscreen = ... # type: QSurface.SurfaceClass

    @typing.overload
    def __init__(self, type: 'QSurface.SurfaceClass') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSurface') -> None: ...

    def supportsOpenGL(self) -> bool: ...
    def size(self) -> QtCore.QSize: ...
    def surfaceType(self) -> 'QSurface.SurfaceType': ...
    def format(self) -> 'QSurfaceFormat': ...
    def surfaceClass(self) -> 'QSurface.SurfaceClass': ...


class QOffscreenSurface(QtCore.QObject, QSurface):

    @typing.overload
    def __init__(self, screen: typing.Optional['QScreen'] = ...) -> None: ...
    @typing.overload
    def __init__(self, screen: 'QScreen', parent: QtCore.QObject) -> None: ...

    def setNativeHandle(self, handle: PyQt5.sip.voidptr) -> None: ...
    def nativeHandle(self) -> PyQt5.sip.voidptr: ...
    screenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setScreen(self, screen: 'QScreen') -> None: ...
    def screen(self) -> 'QScreen': ...
    def size(self) -> QtCore.QSize: ...
    def requestedFormat(self) -> 'QSurfaceFormat': ...
    def format(self) -> 'QSurfaceFormat': ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...
    def isValid(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> None: ...
    def surfaceType(self) -> QSurface.SurfaceType: ...


class QOpenGLBuffer(PyQt5.sipsimplewrapper):

    class RangeAccessFlag(int):
        RangeRead = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeWrite = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeInvalidate = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeInvalidateBuffer = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeFlushExplicit = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeUnsynchronized = ... # type: QOpenGLBuffer.RangeAccessFlag

    class Access(int):
        ReadOnly = ... # type: QOpenGLBuffer.Access
        WriteOnly = ... # type: QOpenGLBuffer.Access
        ReadWrite = ... # type: QOpenGLBuffer.Access

    class UsagePattern(int):
        StreamDraw = ... # type: QOpenGLBuffer.UsagePattern
        StreamRead = ... # type: QOpenGLBuffer.UsagePattern
        StreamCopy = ... # type: QOpenGLBuffer.UsagePattern
        StaticDraw = ... # type: QOpenGLBuffer.UsagePattern
        StaticRead = ... # type: QOpenGLBuffer.UsagePattern
        StaticCopy = ... # type: QOpenGLBuffer.UsagePattern
        DynamicDraw = ... # type: QOpenGLBuffer.UsagePattern
        DynamicRead = ... # type: QOpenGLBuffer.UsagePattern
        DynamicCopy = ... # type: QOpenGLBuffer.UsagePattern

    class Type(int):
        VertexBuffer = ... # type: QOpenGLBuffer.Type
        IndexBuffer = ... # type: QOpenGLBuffer.Type
        PixelPackBuffer = ... # type: QOpenGLBuffer.Type
        PixelUnpackBuffer = ... # type: QOpenGLBuffer.Type

    class RangeAccessFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __xor__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __ior__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __or__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __iand__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __and__(self, f: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __invert__(self) -> 'QOpenGLBuffer.RangeAccessFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: 'QOpenGLBuffer.Type') -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLBuffer') -> None: ...

    def mapRange(self, offset: int, count: int, access: typing.Union['QOpenGLBuffer.RangeAccessFlags', 'QOpenGLBuffer.RangeAccessFlag']) -> PyQt5.sip.voidptr: ...
    def unmap(self) -> bool: ...
    def map(self, access: 'QOpenGLBuffer.Access') -> PyQt5.sip.voidptr: ...
    @typing.overload
    def allocate(self, data: PyQt5.sip.voidptr, count: int) -> None: ...
    @typing.overload
    def allocate(self, count: int) -> None: ...
    def write(self, offset: int, data: PyQt5.sip.voidptr, count: int) -> None: ...
    def read(self, offset: int, data: PyQt5.sip.voidptr, count: int) -> bool: ...
    def __len__(self) -> int: ...
    def size(self) -> int: ...
    def bufferId(self) -> int: ...
    @typing.overload
    def release(self) -> None: ...
    @typing.overload
    @staticmethod
    def release(type: 'QOpenGLBuffer.Type') -> None: ...
    def bind(self) -> bool: ...
    def destroy(self) -> None: ...
    def isCreated(self) -> bool: ...
    def create(self) -> bool: ...
    def setUsagePattern(self, value: 'QOpenGLBuffer.UsagePattern') -> None: ...
    def usagePattern(self) -> 'QOpenGLBuffer.UsagePattern': ...
    def type(self) -> 'QOpenGLBuffer.Type': ...


class QOpenGLContextGroup(QtCore.QObject):

    @staticmethod
    def currentContextGroup() -> 'QOpenGLContextGroup': ...
    def shares(self) -> typing.List['QOpenGLContext']: ...


class QOpenGLContext(QtCore.QObject):

    class OpenGLModuleType(int):
        LibGL = ... # type: QOpenGLContext.OpenGLModuleType
        LibGLES = ... # type: QOpenGLContext.OpenGLModuleType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def globalShareContext() -> 'QOpenGLContext': ...
    @staticmethod
    def supportsThreadedOpenGL() -> bool: ...
    def nativeHandle(self) -> typing.Any: ...
    def setNativeHandle(self, handle: typing.Any) -> None: ...
    def isOpenGLES(self) -> bool: ...
    @staticmethod
    def openGLModuleType() -> 'QOpenGLContext.OpenGLModuleType': ...
    @staticmethod
    def openGLModuleHandle() -> PyQt5.sip.voidptr: ...
    def versionFunctions(self, versionProfile: typing.Optional['QOpenGLVersionProfile'] = ...) -> typing.Any: ...
    aboutToBeDestroyed: typing.ClassVar[QtCore.pyqtSignal]
    def hasExtension(self, extension: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    def extensions(self) -> typing.Set[QtCore.QByteArray]: ...
    @staticmethod
    def areSharing(first: 'QOpenGLContext', second: 'QOpenGLContext') -> bool: ...
    @staticmethod
    def currentContext() -> 'QOpenGLContext': ...
    def surface(self) -> QSurface: ...
    def getProcAddress(self, procName: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> PyQt5.sip.voidptr: ...
    def swapBuffers(self, surface: QSurface) -> None: ...
    def doneCurrent(self) -> None: ...
    def makeCurrent(self, surface: QSurface) -> bool: ...
    def defaultFramebufferObject(self) -> int: ...
    def screen(self) -> 'QScreen': ...
    def shareGroup(self) -> QOpenGLContextGroup: ...
    def shareContext(self) -> 'QOpenGLContext': ...
    def format(self) -> 'QSurfaceFormat': ...
    def isValid(self) -> bool: ...
    def create(self) -> bool: ...
    def setScreen(self, screen: 'QScreen') -> None: ...
    def setShareContext(self, shareContext: 'QOpenGLContext') -> None: ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...


class QOpenGLVersionProfile(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, format: 'QSurfaceFormat') -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLVersionProfile') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def isValid(self) -> bool: ...
    def isLegacyVersion(self) -> bool: ...
    def hasProfiles(self) -> bool: ...
    def setProfile(self, profile: 'QSurfaceFormat.OpenGLContextProfile') -> None: ...
    def profile(self) -> 'QSurfaceFormat.OpenGLContextProfile': ...
    def setVersion(self, majorVersion: int, minorVersion: int) -> None: ...
    def version(self) -> typing.Tuple[int, int]: ...


class QOpenGLDebugMessage(PyQt5.sipsimplewrapper):

    class Severity(int):
        InvalidSeverity = ... # type: QOpenGLDebugMessage.Severity
        HighSeverity = ... # type: QOpenGLDebugMessage.Severity
        MediumSeverity = ... # type: QOpenGLDebugMessage.Severity
        LowSeverity = ... # type: QOpenGLDebugMessage.Severity
        NotificationSeverity = ... # type: QOpenGLDebugMessage.Severity
        AnySeverity = ... # type: QOpenGLDebugMessage.Severity

    class Type(int):
        InvalidType = ... # type: QOpenGLDebugMessage.Type
        ErrorType = ... # type: QOpenGLDebugMessage.Type
        DeprecatedBehaviorType = ... # type: QOpenGLDebugMessage.Type
        UndefinedBehaviorType = ... # type: QOpenGLDebugMessage.Type
        PortabilityType = ... # type: QOpenGLDebugMessage.Type
        PerformanceType = ... # type: QOpenGLDebugMessage.Type
        OtherType = ... # type: QOpenGLDebugMessage.Type
        MarkerType = ... # type: QOpenGLDebugMessage.Type
        GroupPushType = ... # type: QOpenGLDebugMessage.Type
        GroupPopType = ... # type: QOpenGLDebugMessage.Type
        AnyType = ... # type: QOpenGLDebugMessage.Type

    class Source(int):
        InvalidSource = ... # type: QOpenGLDebugMessage.Source
        APISource = ... # type: QOpenGLDebugMessage.Source
        WindowSystemSource = ... # type: QOpenGLDebugMessage.Source
        ShaderCompilerSource = ... # type: QOpenGLDebugMessage.Source
        ThirdPartySource = ... # type: QOpenGLDebugMessage.Source
        ApplicationSource = ... # type: QOpenGLDebugMessage.Source
        OtherSource = ... # type: QOpenGLDebugMessage.Source
        AnySource = ... # type: QOpenGLDebugMessage.Source

    class Sources(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __xor__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __ior__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __or__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __iand__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __and__(self, f: typing.Union['QOpenGLDebugMessage.Sources', 'QOpenGLDebugMessage.Source']) -> 'QOpenGLDebugMessage.Sources': ...
        def __invert__(self) -> 'QOpenGLDebugMessage.Sources': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Types(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __xor__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __ior__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __or__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __iand__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __and__(self, f: typing.Union['QOpenGLDebugMessage.Types', 'QOpenGLDebugMessage.Type']) -> 'QOpenGLDebugMessage.Types': ...
        def __invert__(self) -> 'QOpenGLDebugMessage.Types': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Severities(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __xor__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __ior__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __or__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __iand__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __and__(self, f: typing.Union['QOpenGLDebugMessage.Severities', 'QOpenGLDebugMessage.Severity']) -> 'QOpenGLDebugMessage.Severities': ...
        def __invert__(self) -> 'QOpenGLDebugMessage.Severities': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, debugMessage: 'QOpenGLDebugMessage') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def createThirdPartyMessage(text: str, id: int = ..., severity: 'QOpenGLDebugMessage.Severity' = ..., type: 'QOpenGLDebugMessage.Type' = ...) -> 'QOpenGLDebugMessage': ...
    @staticmethod
    def createApplicationMessage(text: str, id: int = ..., severity: 'QOpenGLDebugMessage.Severity' = ..., type: 'QOpenGLDebugMessage.Type' = ...) -> 'QOpenGLDebugMessage': ...
    def message(self) -> str: ...
    def id(self) -> int: ...
    def severity(self) -> 'QOpenGLDebugMessage.Severity': ...
    def type(self) -> 'QOpenGLDebugMessage.Type': ...
    def source(self) -> 'QOpenGLDebugMessage.Source': ...
    def swap(self, debugMessage: 'QOpenGLDebugMessage') -> None: ...


class QOpenGLDebugLogger(QtCore.QObject):

    class LoggingMode(int):
        AsynchronousLogging = ... # type: QOpenGLDebugLogger.LoggingMode
        SynchronousLogging = ... # type: QOpenGLDebugLogger.LoggingMode

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    messageLogged: typing.ClassVar[QtCore.pyqtSignal]
    def stopLogging(self) -> None: ...
    def startLogging(self, loggingMode: 'QOpenGLDebugLogger.LoggingMode' = ...) -> None: ...
    def logMessage(self, debugMessage: QOpenGLDebugMessage) -> None: ...
    def loggedMessages(self) -> typing.List[QOpenGLDebugMessage]: ...
    @typing.overload
    def disableMessages(self, sources: typing.Union[QOpenGLDebugMessage.Sources, QOpenGLDebugMessage.Source] = ..., types: typing.Union[QOpenGLDebugMessage.Types, QOpenGLDebugMessage.Type] = ..., severities: typing.Union[QOpenGLDebugMessage.Severities, QOpenGLDebugMessage.Severity] = ...) -> None: ...
    @typing.overload
    def disableMessages(self, ids: typing.Iterable[int], sources: typing.Union[QOpenGLDebugMessage.Sources, QOpenGLDebugMessage.Source] = ..., types: typing.Union[QOpenGLDebugMessage.Types, QOpenGLDebugMessage.Type] = ...) -> None: ...
    @typing.overload
    def enableMessages(self, sources: typing.Union[QOpenGLDebugMessage.Sources, QOpenGLDebugMessage.Source] = ..., types: typing.Union[QOpenGLDebugMessage.Types, QOpenGLDebugMessage.Type] = ..., severities: typing.Union[QOpenGLDebugMessage.Severities, QOpenGLDebugMessage.Severity] = ...) -> None: ...
    @typing.overload
    def enableMessages(self, ids: typing.Iterable[int], sources: typing.Union[QOpenGLDebugMessage.Sources, QOpenGLDebugMessage.Source] = ..., types: typing.Union[QOpenGLDebugMessage.Types, QOpenGLDebugMessage.Type] = ...) -> None: ...
    def popGroup(self) -> None: ...
    def pushGroup(self, name: str, id: int = ..., source: QOpenGLDebugMessage.Source = ...) -> None: ...
    def maximumMessageLength(self) -> int: ...
    def loggingMode(self) -> 'QOpenGLDebugLogger.LoggingMode': ...
    def isLogging(self) -> bool: ...
    def initialize(self) -> bool: ...


class QOpenGLFramebufferObject(PyQt5.sipsimplewrapper):

    class FramebufferRestorePolicy(int):
        DontRestoreFramebufferBinding = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy
        RestoreFramebufferBindingToDefault = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy
        RestoreFrameBufferBinding = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy

    class Attachment(int):
        NoAttachment = ... # type: QOpenGLFramebufferObject.Attachment
        CombinedDepthStencil = ... # type: QOpenGLFramebufferObject.Attachment
        Depth = ... # type: QOpenGLFramebufferObject.Attachment

    @typing.overload
    def __init__(self, size: QtCore.QSize, target: int = ...) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, target: int = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, attachment: 'QOpenGLFramebufferObject.Attachment', target: int = ..., internal_format: int = ...) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, attachment: 'QOpenGLFramebufferObject.Attachment', target: int = ..., internal_format: int = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, format: 'QOpenGLFramebufferObjectFormat') -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, format: 'QOpenGLFramebufferObjectFormat') -> None: ...

    def sizes(self) -> typing.List[QtCore.QSize]: ...
    @typing.overload
    def addColorAttachment(self, size: QtCore.QSize, internal_format: int = ...) -> None: ...
    @typing.overload
    def addColorAttachment(self, width: int, height: int, internal_format: int = ...) -> None: ...
    @typing.overload
    def takeTexture(self) -> int: ...
    @typing.overload
    def takeTexture(self, colorAttachmentIndex: int) -> int: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: 'QOpenGLFramebufferObject', targetRect: QtCore.QRect, source: 'QOpenGLFramebufferObject', sourceRect: QtCore.QRect, buffers: int = ..., filter: int = ...) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: 'QOpenGLFramebufferObject', source: 'QOpenGLFramebufferObject', buffers: int = ..., filter: int = ...) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: 'QOpenGLFramebufferObject', targetRect: QtCore.QRect, source: 'QOpenGLFramebufferObject', sourceRect: QtCore.QRect, buffers: int, filter: int, readColorAttachmentIndex: int, drawColorAttachmentIndex: int) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: 'QOpenGLFramebufferObject', targetRect: QtCore.QRect, source: 'QOpenGLFramebufferObject', sourceRect: QtCore.QRect, buffers: int, filter: int, readColorAttachmentIndex: int, drawColorAttachmentIndex: int, restorePolicy: 'QOpenGLFramebufferObject.FramebufferRestorePolicy') -> None: ...
    @staticmethod
    def hasOpenGLFramebufferBlit() -> bool: ...
    @staticmethod
    def hasOpenGLFramebufferObjects() -> bool: ...
    @staticmethod
    def bindDefault() -> bool: ...
    def handle(self) -> int: ...
    def setAttachment(self, attachment: 'QOpenGLFramebufferObject.Attachment') -> None: ...
    def attachment(self) -> 'QOpenGLFramebufferObject.Attachment': ...
    @typing.overload
    def toImage(self) -> QImage: ...
    @typing.overload
    def toImage(self, flipped: bool) -> QImage: ...
    @typing.overload
    def toImage(self, flipped: bool, colorAttachmentIndex: int) -> QImage: ...
    def size(self) -> QtCore.QSize: ...
    def textures(self) -> typing.List[int]: ...
    def texture(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def release(self) -> bool: ...
    def bind(self) -> bool: ...
    def isBound(self) -> bool: ...
    def isValid(self) -> bool: ...
    def format(self) -> 'QOpenGLFramebufferObjectFormat': ...


class QOpenGLFramebufferObjectFormat(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLFramebufferObjectFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def internalTextureFormat(self) -> int: ...
    def setInternalTextureFormat(self, internalTextureFormat: int) -> None: ...
    def textureTarget(self) -> int: ...
    def setTextureTarget(self, target: int) -> None: ...
    def attachment(self) -> QOpenGLFramebufferObject.Attachment: ...
    def setAttachment(self, attachment: QOpenGLFramebufferObject.Attachment) -> None: ...
    def mipmap(self) -> bool: ...
    def setMipmap(self, enabled: bool) -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, samples: int) -> None: ...


class QOpenGLPaintDevice(QPaintDevice):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int) -> None: ...

    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...
    def setDevicePixelRatio(self, devicePixelRatio: float) -> None: ...
    def ensureActiveTarget(self) -> None: ...
    def paintFlipped(self) -> bool: ...
    def setPaintFlipped(self, flipped: bool) -> None: ...
    def setDotsPerMeterY(self, a0: float) -> None: ...
    def setDotsPerMeterX(self, a0: float) -> None: ...
    def dotsPerMeterY(self) -> float: ...
    def dotsPerMeterX(self) -> float: ...
    def setSize(self, size: QtCore.QSize) -> None: ...
    def size(self) -> QtCore.QSize: ...
    def context(self) -> QOpenGLContext: ...
    def paintEngine(self) -> 'QPaintEngine': ...


class QOpenGLPixelTransferOptions(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QOpenGLPixelTransferOptions') -> None: ...

    def isSwapBytesEnabled(self) -> bool: ...
    def setSwapBytesEnabled(self, swapBytes: bool) -> None: ...
    def isLeastSignificantBitFirst(self) -> bool: ...
    def setLeastSignificantByteFirst(self, lsbFirst: bool) -> None: ...
    def rowLength(self) -> int: ...
    def setRowLength(self, rowLength: int) -> None: ...
    def imageHeight(self) -> int: ...
    def setImageHeight(self, imageHeight: int) -> None: ...
    def skipPixels(self) -> int: ...
    def setSkipPixels(self, skipPixels: int) -> None: ...
    def skipRows(self) -> int: ...
    def setSkipRows(self, skipRows: int) -> None: ...
    def skipImages(self) -> int: ...
    def setSkipImages(self, skipImages: int) -> None: ...
    def alignment(self) -> int: ...
    def setAlignment(self, alignment: int) -> None: ...
    def swap(self, other: 'QOpenGLPixelTransferOptions') -> None: ...


class QOpenGLShader(QtCore.QObject):

    class ShaderTypeBit(int):
        Vertex = ... # type: QOpenGLShader.ShaderTypeBit
        Fragment = ... # type: QOpenGLShader.ShaderTypeBit
        Geometry = ... # type: QOpenGLShader.ShaderTypeBit
        TessellationControl = ... # type: QOpenGLShader.ShaderTypeBit
        TessellationEvaluation = ... # type: QOpenGLShader.ShaderTypeBit
        Compute = ... # type: QOpenGLShader.ShaderTypeBit

    class ShaderType(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __xor__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __ior__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __or__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __iand__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __and__(self, f: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit']) -> 'QOpenGLShader.ShaderType': ...
        def __invert__(self) -> 'QOpenGLShader.ShaderType': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, type: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def hasOpenGLShaders(type: typing.Union['QOpenGLShader.ShaderType', 'QOpenGLShader.ShaderTypeBit'], context: typing.Optional[QOpenGLContext] = ...) -> bool: ...
    def shaderId(self) -> int: ...
    def log(self) -> str: ...
    def isCompiled(self) -> bool: ...
    def sourceCode(self) -> QtCore.QByteArray: ...
    def compileSourceFile(self, fileName: str) -> bool: ...
    @typing.overload
    def compileSourceCode(self, source: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    @typing.overload
    def compileSourceCode(self, source: str) -> bool: ...
    def shaderType(self) -> 'QOpenGLShader.ShaderType': ...


class QOpenGLShaderProgram(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def addCacheableShaderFromSourceFile(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], fileName: str) -> bool: ...
    @typing.overload
    def addCacheableShaderFromSourceCode(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], source: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    @typing.overload
    def addCacheableShaderFromSourceCode(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], source: str) -> bool: ...
    def create(self) -> bool: ...
    def defaultInnerTessellationLevels(self) -> typing.List[float]: ...
    def setDefaultInnerTessellationLevels(self, levels: typing.Iterable[float]) -> None: ...
    def defaultOuterTessellationLevels(self) -> typing.List[float]: ...
    def setDefaultOuterTessellationLevels(self, levels: typing.Iterable[float]) -> None: ...
    def patchVertexCount(self) -> int: ...
    def setPatchVertexCount(self, count: int) -> None: ...
    def maxGeometryOutputVertices(self) -> int: ...
    @staticmethod
    def hasOpenGLShaderPrograms(context: typing.Optional[QOpenGLContext] = ...) -> bool: ...
    @typing.overload
    def setUniformValueArray(self, location: int, values: PYQT_SHADER_UNIFORM_VALUE_ARRAY) -> None: ...
    @typing.overload
    def setUniformValueArray(self, name: str, values: PYQT_SHADER_UNIFORM_VALUE_ARRAY) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: int) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: 'QVector2D') -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: 'QVector3D') -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: 'QVector4D') -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, size: QtCore.QSize) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, size: QtCore.QSizeF) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix2x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix2x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix2x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix3x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix3x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix3x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix4x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix4x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QMatrix4x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: 'QTransform') -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: int) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, x: float, y: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: 'QVector2D') -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: 'QVector3D') -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: 'QVector4D') -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, size: QtCore.QSize) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, size: QtCore.QSizeF) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix2x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix2x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix2x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix3x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix3x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix3x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix4x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix4x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: QMatrix4x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: str, value: 'QTransform') -> None: ...
    @typing.overload
    def uniformLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> int: ...
    @typing.overload
    def uniformLocation(self, name: str) -> int: ...
    @typing.overload
    def disableAttributeArray(self, location: int) -> None: ...
    @typing.overload
    def disableAttributeArray(self, name: str) -> None: ...
    @typing.overload
    def enableAttributeArray(self, location: int) -> None: ...
    @typing.overload
    def enableAttributeArray(self, name: str) -> None: ...
    @typing.overload
    def setAttributeBuffer(self, location: int, type: int, offset: int, tupleSize: int, stride: int = ...) -> None: ...
    @typing.overload
    def setAttributeBuffer(self, name: str, type: int, offset: int, tupleSize: int, stride: int = ...) -> None: ...
    @typing.overload
    def setAttributeArray(self, location: int, values: PYQT_SHADER_ATTRIBUTE_ARRAY) -> None: ...
    @typing.overload
    def setAttributeArray(self, name: str, values: PYQT_SHADER_ATTRIBUTE_ARRAY) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: 'QVector2D') -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: 'QVector3D') -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: 'QVector4D') -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, value: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, x: float, y: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, value: 'QVector2D') -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, value: 'QVector3D') -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, value: 'QVector4D') -> None: ...
    @typing.overload
    def setAttributeValue(self, name: str, value: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def attributeLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> int: ...
    @typing.overload
    def attributeLocation(self, name: str) -> int: ...
    @typing.overload
    def bindAttributeLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray], location: int) -> None: ...
    @typing.overload
    def bindAttributeLocation(self, name: str, location: int) -> None: ...
    def programId(self) -> int: ...
    def release(self) -> None: ...
    def bind(self) -> bool: ...
    def log(self) -> str: ...
    def isLinked(self) -> bool: ...
    def link(self) -> bool: ...
    def removeAllShaders(self) -> None: ...
    def addShaderFromSourceFile(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], fileName: str) -> bool: ...
    @typing.overload
    def addShaderFromSourceCode(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], source: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> bool: ...
    @typing.overload
    def addShaderFromSourceCode(self, type: typing.Union[QOpenGLShader.ShaderType, QOpenGLShader.ShaderTypeBit], source: str) -> bool: ...
    def shaders(self) -> typing.List[QOpenGLShader]: ...
    def removeShader(self, shader: QOpenGLShader) -> None: ...
    def addShader(self, shader: QOpenGLShader) -> bool: ...


class QOpenGLTexture(PyQt5.sipsimplewrapper):

    class ComparisonMode(int):
        CompareRefToTexture = ... # type: QOpenGLTexture.ComparisonMode
        CompareNone = ... # type: QOpenGLTexture.ComparisonMode

    class ComparisonFunction(int):
        CompareLessEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareGreaterEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareLess = ... # type: QOpenGLTexture.ComparisonFunction
        CompareGreater = ... # type: QOpenGLTexture.ComparisonFunction
        CompareEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CommpareNotEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareAlways = ... # type: QOpenGLTexture.ComparisonFunction
        CompareNever = ... # type: QOpenGLTexture.ComparisonFunction

    class CoordinateDirection(int):
        DirectionS = ... # type: QOpenGLTexture.CoordinateDirection
        DirectionT = ... # type: QOpenGLTexture.CoordinateDirection
        DirectionR = ... # type: QOpenGLTexture.CoordinateDirection

    class WrapMode(int):
        Repeat = ... # type: QOpenGLTexture.WrapMode
        MirroredRepeat = ... # type: QOpenGLTexture.WrapMode
        ClampToEdge = ... # type: QOpenGLTexture.WrapMode
        ClampToBorder = ... # type: QOpenGLTexture.WrapMode

    class Filter(int):
        Nearest = ... # type: QOpenGLTexture.Filter
        Linear = ... # type: QOpenGLTexture.Filter
        NearestMipMapNearest = ... # type: QOpenGLTexture.Filter
        NearestMipMapLinear = ... # type: QOpenGLTexture.Filter
        LinearMipMapNearest = ... # type: QOpenGLTexture.Filter
        LinearMipMapLinear = ... # type: QOpenGLTexture.Filter

    class DepthStencilMode(int):
        DepthMode = ... # type: QOpenGLTexture.DepthStencilMode
        StencilMode = ... # type: QOpenGLTexture.DepthStencilMode

    class SwizzleValue(int):
        RedValue = ... # type: QOpenGLTexture.SwizzleValue
        GreenValue = ... # type: QOpenGLTexture.SwizzleValue
        BlueValue = ... # type: QOpenGLTexture.SwizzleValue
        AlphaValue = ... # type: QOpenGLTexture.SwizzleValue
        ZeroValue = ... # type: QOpenGLTexture.SwizzleValue
        OneValue = ... # type: QOpenGLTexture.SwizzleValue

    class SwizzleComponent(int):
        SwizzleRed = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleGreen = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleBlue = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleAlpha = ... # type: QOpenGLTexture.SwizzleComponent

    class Feature(int):
        ImmutableStorage = ... # type: QOpenGLTexture.Feature
        ImmutableMultisampleStorage = ... # type: QOpenGLTexture.Feature
        TextureRectangle = ... # type: QOpenGLTexture.Feature
        TextureArrays = ... # type: QOpenGLTexture.Feature
        Texture3D = ... # type: QOpenGLTexture.Feature
        TextureMultisample = ... # type: QOpenGLTexture.Feature
        TextureBuffer = ... # type: QOpenGLTexture.Feature
        TextureCubeMapArrays = ... # type: QOpenGLTexture.Feature
        Swizzle = ... # type: QOpenGLTexture.Feature
        StencilTexturing = ... # type: QOpenGLTexture.Feature
        AnisotropicFiltering = ... # type: QOpenGLTexture.Feature
        NPOTTextures = ... # type: QOpenGLTexture.Feature
        NPOTTextureRepeat = ... # type: QOpenGLTexture.Feature
        Texture1D = ... # type: QOpenGLTexture.Feature
        TextureComparisonOperators = ... # type: QOpenGLTexture.Feature
        TextureMipMapLevel = ... # type: QOpenGLTexture.Feature

    class PixelType(int):
        NoPixelType = ... # type: QOpenGLTexture.PixelType
        Int8 = ... # type: QOpenGLTexture.PixelType
        UInt8 = ... # type: QOpenGLTexture.PixelType
        Int16 = ... # type: QOpenGLTexture.PixelType
        UInt16 = ... # type: QOpenGLTexture.PixelType
        Int32 = ... # type: QOpenGLTexture.PixelType
        UInt32 = ... # type: QOpenGLTexture.PixelType
        Float16 = ... # type: QOpenGLTexture.PixelType
        Float16OES = ... # type: QOpenGLTexture.PixelType
        Float32 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB9_E5 = ... # type: QOpenGLTexture.PixelType
        UInt32_RG11B10F = ... # type: QOpenGLTexture.PixelType
        UInt8_RG3B2 = ... # type: QOpenGLTexture.PixelType
        UInt8_RG3B2_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_RGB5A1 = ... # type: QOpenGLTexture.PixelType
        UInt16_RGB5A1_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_R5G6B5 = ... # type: QOpenGLTexture.PixelType
        UInt16_R5G6B5_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_RGBA4 = ... # type: QOpenGLTexture.PixelType
        UInt16_RGBA4_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB10A2 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB10A2_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_RGBA8 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGBA8_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_D24S8 = ... # type: QOpenGLTexture.PixelType
        Float32_D32_UInt32_S8_X24 = ... # type: QOpenGLTexture.PixelType

    class PixelFormat(int):
        NoSourceFormat = ... # type: QOpenGLTexture.PixelFormat
        Red = ... # type: QOpenGLTexture.PixelFormat
        RG = ... # type: QOpenGLTexture.PixelFormat
        RGB = ... # type: QOpenGLTexture.PixelFormat
        BGR = ... # type: QOpenGLTexture.PixelFormat
        RGBA = ... # type: QOpenGLTexture.PixelFormat
        BGRA = ... # type: QOpenGLTexture.PixelFormat
        Red_Integer = ... # type: QOpenGLTexture.PixelFormat
        RG_Integer = ... # type: QOpenGLTexture.PixelFormat
        RGB_Integer = ... # type: QOpenGLTexture.PixelFormat
        BGR_Integer = ... # type: QOpenGLTexture.PixelFormat
        RGBA_Integer = ... # type: QOpenGLTexture.PixelFormat
        BGRA_Integer = ... # type: QOpenGLTexture.PixelFormat
        Depth = ... # type: QOpenGLTexture.PixelFormat
        DepthStencil = ... # type: QOpenGLTexture.PixelFormat
        Alpha = ... # type: QOpenGLTexture.PixelFormat
        Luminance = ... # type: QOpenGLTexture.PixelFormat
        LuminanceAlpha = ... # type: QOpenGLTexture.PixelFormat
        Stencil = ... # type: QOpenGLTexture.PixelFormat

    class CubeMapFace(int):
        CubeMapPositiveX = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeX = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapPositiveY = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeY = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapPositiveZ = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeZ = ... # type: QOpenGLTexture.CubeMapFace

    class TextureFormat(int):
        NoFormat = ... # type: QOpenGLTexture.TextureFormat
        R8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        R16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        R8U = ... # type: QOpenGLTexture.TextureFormat
        RG8U = ... # type: QOpenGLTexture.TextureFormat
        RGB8U = ... # type: QOpenGLTexture.TextureFormat
        RGBA8U = ... # type: QOpenGLTexture.TextureFormat
        R16U = ... # type: QOpenGLTexture.TextureFormat
        RG16U = ... # type: QOpenGLTexture.TextureFormat
        RGB16U = ... # type: QOpenGLTexture.TextureFormat
        RGBA16U = ... # type: QOpenGLTexture.TextureFormat
        R32U = ... # type: QOpenGLTexture.TextureFormat
        RG32U = ... # type: QOpenGLTexture.TextureFormat
        RGB32U = ... # type: QOpenGLTexture.TextureFormat
        RGBA32U = ... # type: QOpenGLTexture.TextureFormat
        R8I = ... # type: QOpenGLTexture.TextureFormat
        RG8I = ... # type: QOpenGLTexture.TextureFormat
        RGB8I = ... # type: QOpenGLTexture.TextureFormat
        RGBA8I = ... # type: QOpenGLTexture.TextureFormat
        R16I = ... # type: QOpenGLTexture.TextureFormat
        RG16I = ... # type: QOpenGLTexture.TextureFormat
        RGB16I = ... # type: QOpenGLTexture.TextureFormat
        RGBA16I = ... # type: QOpenGLTexture.TextureFormat
        R32I = ... # type: QOpenGLTexture.TextureFormat
        RG32I = ... # type: QOpenGLTexture.TextureFormat
        RGB32I = ... # type: QOpenGLTexture.TextureFormat
        RGBA32I = ... # type: QOpenGLTexture.TextureFormat
        R16F = ... # type: QOpenGLTexture.TextureFormat
        RG16F = ... # type: QOpenGLTexture.TextureFormat
        RGB16F = ... # type: QOpenGLTexture.TextureFormat
        RGBA16F = ... # type: QOpenGLTexture.TextureFormat
        R32F = ... # type: QOpenGLTexture.TextureFormat
        RG32F = ... # type: QOpenGLTexture.TextureFormat
        RGB32F = ... # type: QOpenGLTexture.TextureFormat
        RGBA32F = ... # type: QOpenGLTexture.TextureFormat
        RGB9E5 = ... # type: QOpenGLTexture.TextureFormat
        RG11B10F = ... # type: QOpenGLTexture.TextureFormat
        RG3B2 = ... # type: QOpenGLTexture.TextureFormat
        R5G6B5 = ... # type: QOpenGLTexture.TextureFormat
        RGB5A1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA4 = ... # type: QOpenGLTexture.TextureFormat
        RGB10A2 = ... # type: QOpenGLTexture.TextureFormat
        D16 = ... # type: QOpenGLTexture.TextureFormat
        D24 = ... # type: QOpenGLTexture.TextureFormat
        D24S8 = ... # type: QOpenGLTexture.TextureFormat
        D32 = ... # type: QOpenGLTexture.TextureFormat
        D32F = ... # type: QOpenGLTexture.TextureFormat
        D32FS8X24 = ... # type: QOpenGLTexture.TextureFormat
        RGB_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT3 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT5 = ... # type: QOpenGLTexture.TextureFormat
        R_ATI1N_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R_ATI1N_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG_ATI2N_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG_ATI2N_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_UNSIGNED_FLOAT = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_SIGNED_FLOAT = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_UNorm = ... # type: QOpenGLTexture.TextureFormat
        SRGB8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT3 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_BP_UNorm = ... # type: QOpenGLTexture.TextureFormat
        DepthFormat = ... # type: QOpenGLTexture.TextureFormat
        AlphaFormat = ... # type: QOpenGLTexture.TextureFormat
        RGBFormat = ... # type: QOpenGLTexture.TextureFormat
        RGBAFormat = ... # type: QOpenGLTexture.TextureFormat
        LuminanceFormat = ... # type: QOpenGLTexture.TextureFormat
        LuminanceAlphaFormat = ... # type: QOpenGLTexture.TextureFormat
        S8 = ... # type: QOpenGLTexture.TextureFormat
        R11_EAC_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R11_EAC_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG11_EAC_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG11_EAC_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        RGB8_PunchThrough_Alpha1_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_PunchThrough_Alpha1_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_ETC2_EAC = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ETC2_EAC = ... # type: QOpenGLTexture.TextureFormat
        RGB8_ETC1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_4x4 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_5x4 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_5x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_6x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_6x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x8 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x8 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x10 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_12x10 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_12x12 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_4x4 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_5x4 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_5x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_6x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_6x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x10 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_12x10 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_12x12 = ... # type: QOpenGLTexture.TextureFormat

    class TextureUnitReset(int):
        ResetTextureUnit = ... # type: QOpenGLTexture.TextureUnitReset
        DontResetTextureUnit = ... # type: QOpenGLTexture.TextureUnitReset

    class MipMapGeneration(int):
        GenerateMipMaps = ... # type: QOpenGLTexture.MipMapGeneration
        DontGenerateMipMaps = ... # type: QOpenGLTexture.MipMapGeneration

    class BindingTarget(int):
        BindingTarget1D = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget1DArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2D = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget3D = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetCubeMap = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetCubeMapArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DMultisample = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DMultisampleArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetRectangle = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetBuffer = ... # type: QOpenGLTexture.BindingTarget

    class Target(int):
        Target1D = ... # type: QOpenGLTexture.Target
        Target1DArray = ... # type: QOpenGLTexture.Target
        Target2D = ... # type: QOpenGLTexture.Target
        Target2DArray = ... # type: QOpenGLTexture.Target
        Target3D = ... # type: QOpenGLTexture.Target
        TargetCubeMap = ... # type: QOpenGLTexture.Target
        TargetCubeMapArray = ... # type: QOpenGLTexture.Target
        Target2DMultisample = ... # type: QOpenGLTexture.Target
        Target2DMultisampleArray = ... # type: QOpenGLTexture.Target
        TargetRectangle = ... # type: QOpenGLTexture.Target
        TargetBuffer = ... # type: QOpenGLTexture.Target

    class Features(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __xor__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __ior__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __or__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __iand__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __and__(self, f: typing.Union['QOpenGLTexture.Features', 'QOpenGLTexture.Feature']) -> 'QOpenGLTexture.Features': ...
        def __invert__(self) -> 'QOpenGLTexture.Features': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, target: 'QOpenGLTexture.Target') -> None: ...
    @typing.overload
    def __init__(self, image: QImage, genMipMaps: 'QOpenGLTexture.MipMapGeneration' = ...) -> None: ...

    def comparisonMode(self) -> 'QOpenGLTexture.ComparisonMode': ...
    def setComparisonMode(self, mode: 'QOpenGLTexture.ComparisonMode') -> None: ...
    def comparisonFunction(self) -> 'QOpenGLTexture.ComparisonFunction': ...
    def setComparisonFunction(self, function: 'QOpenGLTexture.ComparisonFunction') -> None: ...
    def isFixedSamplePositions(self) -> bool: ...
    def setFixedSamplePositions(self, fixed: bool) -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, samples: int) -> None: ...
    def target(self) -> 'QOpenGLTexture.Target': ...
    def levelofDetailBias(self) -> float: ...
    def setLevelofDetailBias(self, bias: float) -> None: ...
    def levelOfDetailRange(self) -> typing.Tuple[float, float]: ...
    def setLevelOfDetailRange(self, min: float, max: float) -> None: ...
    def maximumLevelOfDetail(self) -> float: ...
    def setMaximumLevelOfDetail(self, value: float) -> None: ...
    def minimumLevelOfDetail(self) -> float: ...
    def setMinimumLevelOfDetail(self, value: float) -> None: ...
    def borderColor(self) -> QColor: ...
    def setBorderColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def wrapMode(self, direction: 'QOpenGLTexture.CoordinateDirection') -> 'QOpenGLTexture.WrapMode': ...
    @typing.overload
    def setWrapMode(self, mode: 'QOpenGLTexture.WrapMode') -> None: ...
    @typing.overload
    def setWrapMode(self, direction: 'QOpenGLTexture.CoordinateDirection', mode: 'QOpenGLTexture.WrapMode') -> None: ...
    def maximumAnisotropy(self) -> float: ...
    def setMaximumAnisotropy(self, anisotropy: float) -> None: ...
    def minMagFilters(self) -> typing.Tuple['QOpenGLTexture.Filter', 'QOpenGLTexture.Filter']: ...
    def setMinMagFilters(self, minificationFilter: 'QOpenGLTexture.Filter', magnificationFilter: 'QOpenGLTexture.Filter') -> None: ...
    def magnificationFilter(self) -> 'QOpenGLTexture.Filter': ...
    def setMagnificationFilter(self, filter: 'QOpenGLTexture.Filter') -> None: ...
    def minificationFilter(self) -> 'QOpenGLTexture.Filter': ...
    def setMinificationFilter(self, filter: 'QOpenGLTexture.Filter') -> None: ...
    def depthStencilMode(self) -> 'QOpenGLTexture.DepthStencilMode': ...
    def setDepthStencilMode(self, mode: 'QOpenGLTexture.DepthStencilMode') -> None: ...
    def swizzleMask(self, component: 'QOpenGLTexture.SwizzleComponent') -> 'QOpenGLTexture.SwizzleValue': ...
    @typing.overload
    def setSwizzleMask(self, component: 'QOpenGLTexture.SwizzleComponent', value: 'QOpenGLTexture.SwizzleValue') -> None: ...
    @typing.overload
    def setSwizzleMask(self, r: 'QOpenGLTexture.SwizzleValue', g: 'QOpenGLTexture.SwizzleValue', b: 'QOpenGLTexture.SwizzleValue', a: 'QOpenGLTexture.SwizzleValue') -> None: ...
    @typing.overload
    def generateMipMaps(self) -> None: ...
    @typing.overload
    def generateMipMaps(self, baseLevel: int, resetBaseLevel: bool = ...) -> None: ...
    def isAutoMipMapGenerationEnabled(self) -> bool: ...
    def setAutoMipMapGenerationEnabled(self, enabled: bool) -> None: ...
    def mipLevelRange(self) -> typing.Tuple[int, int]: ...
    def setMipLevelRange(self, baseLevel: int, maxLevel: int) -> None: ...
    def mipMaxLevel(self) -> int: ...
    def setMipMaxLevel(self, maxLevel: int) -> None: ...
    def mipBaseLevel(self) -> int: ...
    def setMipBaseLevel(self, baseLevel: int) -> None: ...
    @staticmethod
    def hasFeature(feature: 'QOpenGLTexture.Feature') -> bool: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', dataSize: int, data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, dataSize: int, data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, dataSize: int, data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, dataSize: int, data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, layerCount: int, cubeFace: 'QOpenGLTexture.CubeMapFace', dataSize: int, data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, image: QImage, genMipMaps: 'QOpenGLTexture.MipMapGeneration' = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, layerCount: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', layerCount: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: PyQt5.sip.voidptr, options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    def isTextureView(self) -> bool: ...
    def createTextureView(self, target: 'QOpenGLTexture.Target', viewFormat: 'QOpenGLTexture.TextureFormat', minimumMipmapLevel: int, maximumMipmapLevel: int, minimumLayer: int, maximumLayer: int) -> 'QOpenGLTexture': ...
    def isStorageAllocated(self) -> bool: ...
    @typing.overload
    def allocateStorage(self) -> None: ...
    @typing.overload
    def allocateStorage(self, pixelFormat: 'QOpenGLTexture.PixelFormat', pixelType: 'QOpenGLTexture.PixelType') -> None: ...
    def faces(self) -> int: ...
    def layers(self) -> int: ...
    def setLayers(self, layers: int) -> None: ...
    def maximumMipLevels(self) -> int: ...
    def mipLevels(self) -> int: ...
    def setMipLevels(self, levels: int) -> None: ...
    def depth(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def setSize(self, width: int, height: int = ..., depth: int = ...) -> None: ...
    def format(self) -> 'QOpenGLTexture.TextureFormat': ...
    def setFormat(self, format: 'QOpenGLTexture.TextureFormat') -> None: ...
    @typing.overload
    @staticmethod
    def boundTextureId(target: 'QOpenGLTexture.BindingTarget') -> int: ...
    @typing.overload
    @staticmethod
    def boundTextureId(unit: int, target: 'QOpenGLTexture.BindingTarget') -> int: ...
    @typing.overload
    def isBound(self) -> bool: ...
    @typing.overload
    def isBound(self, unit: int) -> bool: ...
    @typing.overload
    def release(self) -> None: ...
    @typing.overload
    def release(self, unit: int, reset: 'QOpenGLTexture.TextureUnitReset' = ...) -> None: ...
    @typing.overload
    def bind(self) -> None: ...
    @typing.overload
    def bind(self, unit: int, reset: 'QOpenGLTexture.TextureUnitReset' = ...) -> None: ...
    def textureId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QOpenGLTextureBlitter(PyQt5.sipsimplewrapper):

    class Origin(int):
        OriginBottomLeft = ... # type: QOpenGLTextureBlitter.Origin
        OriginTopLeft = ... # type: QOpenGLTextureBlitter.Origin

    def __init__(self) -> None: ...

    @staticmethod
    def sourceTransform(subTexture: QtCore.QRectF, textureSize: QtCore.QSize, origin: 'QOpenGLTextureBlitter.Origin') -> QMatrix3x3: ...
    @staticmethod
    def targetTransform(target: QtCore.QRectF, viewport: QtCore.QRect) -> QMatrix4x4: ...
    @typing.overload
    def blit(self, texture: int, targetTransform: QMatrix4x4, sourceOrigin: 'QOpenGLTextureBlitter.Origin') -> None: ...
    @typing.overload
    def blit(self, texture: int, targetTransform: QMatrix4x4, sourceTransform: QMatrix3x3) -> None: ...
    def setOpacity(self, opacity: float) -> None: ...
    def setRedBlueSwizzle(self, swizzle: bool) -> None: ...
    def release(self) -> None: ...
    def bind(self, target: int = ...) -> None: ...
    def supportsExternalOESTarget(self) -> bool: ...
    def destroy(self) -> None: ...
    def isCreated(self) -> bool: ...
    def create(self) -> bool: ...


class QOpenGLTimerQuery(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def waitForResult(self) -> int: ...
    def isResultAvailable(self) -> bool: ...
    def recordTimestamp(self) -> None: ...
    def waitForTimestamp(self) -> int: ...
    def end(self) -> None: ...
    def begin(self) -> None: ...
    def objectId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QOpenGLTimeMonitor(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def reset(self) -> None: ...
    def waitForIntervals(self) -> typing.List[int]: ...
    def waitForSamples(self) -> typing.List[int]: ...
    def isResultAvailable(self) -> bool: ...
    def recordSample(self) -> int: ...
    def objectIds(self) -> typing.List[int]: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...
    def sampleCount(self) -> int: ...
    def setSampleCount(self, sampleCount: int) -> None: ...


class QAbstractOpenGLFunctions(PyQt5.sip.wrapper): ...


class QOpenGLVertexArrayObject(QtCore.QObject):

    class Binder(PyQt5.sipsimplewrapper):

        def __init__(self, v: 'QOpenGLVertexArrayObject') -> None: ...

        def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
        def __enter__(self) -> typing.Any: ...
        def rebind(self) -> None: ...
        def release(self) -> None: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def release(self) -> None: ...
    def bind(self) -> None: ...
    def objectId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QWindow(QtCore.QObject, QSurface):

    class Visibility(int):
        Hidden = ... # type: QWindow.Visibility
        AutomaticVisibility = ... # type: QWindow.Visibility
        Windowed = ... # type: QWindow.Visibility
        Minimized = ... # type: QWindow.Visibility
        Maximized = ... # type: QWindow.Visibility
        FullScreen = ... # type: QWindow.Visibility

    class AncestorMode(int):
        ExcludeTransients = ... # type: QWindow.AncestorMode
        IncludeTransients = ... # type: QWindow.AncestorMode

    @typing.overload
    def __init__(self, screen: typing.Optional['QScreen'] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: 'QWindow') -> None: ...

    def startSystemMove(self) -> bool: ...
    def startSystemResize(self, edges: typing.Union[QtCore.Qt.Edges, QtCore.Qt.Edge]) -> bool: ...
    def setWindowStates(self, states: typing.Union[QtCore.Qt.WindowStates, QtCore.Qt.WindowState]) -> None: ...
    def windowStates(self) -> QtCore.Qt.WindowStates: ...
    def setFlag(self, a0: QtCore.Qt.WindowType, on: bool = ...) -> None: ...
    opacityChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def fromWinId(id: PyQt5.sip.voidptr) -> 'QWindow': ...
    def mask(self) -> 'QRegion': ...
    def setMask(self, region: 'QRegion') -> None: ...
    def opacity(self) -> float: ...
    def setVisibility(self, v: 'QWindow.Visibility') -> None: ...
    def visibility(self) -> 'QWindow.Visibility': ...
    def tabletEvent(self, a0: QTabletEvent) -> None: ...
    def touchEvent(self, a0: QTouchEvent) -> None: ...
    def wheelEvent(self, a0: QWheelEvent) -> None: ...
    def mouseMoveEvent(self, a0: QMouseEvent) -> None: ...
    def mouseDoubleClickEvent(self, a0: QMouseEvent) -> None: ...
    def mouseReleaseEvent(self, a0: QMouseEvent) -> None: ...
    def mousePressEvent(self, a0: QMouseEvent) -> None: ...
    def keyReleaseEvent(self, a0: QKeyEvent) -> None: ...
    def keyPressEvent(self, a0: QKeyEvent) -> None: ...
    def event(self, a0: QtCore.QEvent) -> bool: ...
    def hideEvent(self, a0: QHideEvent) -> None: ...
    def showEvent(self, a0: QShowEvent) -> None: ...
    def focusOutEvent(self, a0: QFocusEvent) -> None: ...
    def focusInEvent(self, a0: QFocusEvent) -> None: ...
    def moveEvent(self, a0: QMoveEvent) -> None: ...
    def resizeEvent(self, a0: QResizeEvent) -> None: ...
    def exposeEvent(self, a0: QExposeEvent) -> None: ...
    windowTitleChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusObjectChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentOrientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumHeightChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumHeightChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    heightChanged: typing.ClassVar[QtCore.pyqtSignal]
    widthChanged: typing.ClassVar[QtCore.pyqtSignal]
    yChanged: typing.ClassVar[QtCore.pyqtSignal]
    xChanged: typing.ClassVar[QtCore.pyqtSignal]
    windowStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    modalityChanged: typing.ClassVar[QtCore.pyqtSignal]
    screenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def requestUpdate(self) -> None: ...
    def alert(self, msec: int) -> None: ...
    def setMaximumHeight(self, h: int) -> None: ...
    def setMaximumWidth(self, w: int) -> None: ...
    def setMinimumHeight(self, h: int) -> None: ...
    def setMinimumWidth(self, w: int) -> None: ...
    def setHeight(self, arg: int) -> None: ...
    def setWidth(self, arg: int) -> None: ...
    def setY(self, arg: int) -> None: ...
    def setX(self, arg: int) -> None: ...
    def setTitle(self, a0: str) -> None: ...
    def lower(self) -> None: ...
    def raise_(self) -> None: ...
    def close(self) -> bool: ...
    def showNormal(self) -> None: ...
    def showFullScreen(self) -> None: ...
    def showMaximized(self) -> None: ...
    def showMinimized(self) -> None: ...
    def hide(self) -> None: ...
    def show(self) -> None: ...
    def setVisible(self, visible: bool) -> None: ...
    def unsetCursor(self) -> None: ...
    def setCursor(self, a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    def cursor(self) -> QCursor: ...
    def mapFromGlobal(self, pos: QtCore.QPoint) -> QtCore.QPoint: ...
    def mapToGlobal(self, pos: QtCore.QPoint) -> QtCore.QPoint: ...
    def focusObject(self) -> QtCore.QObject: ...
    def setScreen(self, screen: 'QScreen') -> None: ...
    def screen(self) -> 'QScreen': ...
    def setMouseGrabEnabled(self, grab: bool) -> bool: ...
    def setKeyboardGrabEnabled(self, grab: bool) -> bool: ...
    def destroy(self) -> None: ...
    def icon(self) -> QIcon: ...
    def setIcon(self, icon: QIcon) -> None: ...
    def filePath(self) -> str: ...
    def setFilePath(self, filePath: str) -> None: ...
    @typing.overload
    def resize(self, newSize: QtCore.QSize) -> None: ...
    @typing.overload
    def resize(self, w: int, h: int) -> None: ...
    @typing.overload
    def setPosition(self, pt: QtCore.QPoint) -> None: ...
    @typing.overload
    def setPosition(self, posx: int, posy: int) -> None: ...
    def position(self) -> QtCore.QPoint: ...
    def size(self) -> QtCore.QSize: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def setFramePosition(self, point: QtCore.QPoint) -> None: ...
    def framePosition(self) -> QtCore.QPoint: ...
    def frameGeometry(self) -> QtCore.QRect: ...
    def frameMargins(self) -> QtCore.QMargins: ...
    def geometry(self) -> QtCore.QRect: ...
    @typing.overload
    def setGeometry(self, posx: int, posy: int, w: int, h: int) -> None: ...
    @typing.overload
    def setGeometry(self, rect: QtCore.QRect) -> None: ...
    def setSizeIncrement(self, size: QtCore.QSize) -> None: ...
    def setBaseSize(self, size: QtCore.QSize) -> None: ...
    def setMaximumSize(self, size: QtCore.QSize) -> None: ...
    def setMinimumSize(self, size: QtCore.QSize) -> None: ...
    def sizeIncrement(self) -> QtCore.QSize: ...
    def baseSize(self) -> QtCore.QSize: ...
    def maximumSize(self) -> QtCore.QSize: ...
    def minimumSize(self) -> QtCore.QSize: ...
    def maximumHeight(self) -> int: ...
    def maximumWidth(self) -> int: ...
    def minimumHeight(self) -> int: ...
    def minimumWidth(self) -> int: ...
    def isExposed(self) -> bool: ...
    def isAncestorOf(self, child: 'QWindow', mode: 'QWindow.AncestorMode' = ...) -> bool: ...
    def transientParent(self) -> 'QWindow': ...
    def setTransientParent(self, parent: 'QWindow') -> None: ...
    def setWindowState(self, state: QtCore.Qt.WindowState) -> None: ...
    def windowState(self) -> QtCore.Qt.WindowState: ...
    def devicePixelRatio(self) -> float: ...
    def contentOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def reportContentOrientationChange(self, orientation: QtCore.Qt.ScreenOrientation) -> None: ...
    def isActive(self) -> bool: ...
    def requestActivate(self) -> None: ...
    def setOpacity(self, level: float) -> None: ...
    def title(self) -> str: ...
    def type(self) -> QtCore.Qt.WindowType: ...
    def flags(self) -> QtCore.Qt.WindowFlags: ...
    def setFlags(self, flags: typing.Union[QtCore.Qt.WindowFlags, QtCore.Qt.WindowType]) -> None: ...
    def requestedFormat(self) -> 'QSurfaceFormat': ...
    def format(self) -> 'QSurfaceFormat': ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...
    def setModality(self, modality: QtCore.Qt.WindowModality) -> None: ...
    def modality(self) -> QtCore.Qt.WindowModality: ...
    def isModal(self) -> bool: ...
    def isTopLevel(self) -> bool: ...
    def setParent(self, parent: 'QWindow') -> None: ...
    @typing.overload
    def parent(self) -> 'QWindow': ...
    @typing.overload
    def parent(self, mode: 'QWindow.AncestorMode') -> 'QWindow': ...
    def winId(self) -> PyQt5.sip.voidptr: ...
    def create(self) -> None: ...
    def isVisible(self) -> bool: ...
    def surfaceType(self) -> QSurface.SurfaceType: ...
    def setSurfaceType(self, surfaceType: QSurface.SurfaceType) -> None: ...


class QPaintDeviceWindow(QWindow, QPaintDevice):

    def event(self, event: QtCore.QEvent) -> bool: ...
    def exposeEvent(self, a0: QExposeEvent) -> None: ...
    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEvent(self, event: QPaintEvent) -> None: ...
    @typing.overload
    def update(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def update(self, region: 'QRegion') -> None: ...
    @typing.overload
    def update(self) -> None: ...


class QOpenGLWindow(QPaintDeviceWindow):

    class UpdateBehavior(int):
        NoPartialUpdate = ... # type: QOpenGLWindow.UpdateBehavior
        PartialUpdateBlit = ... # type: QOpenGLWindow.UpdateBehavior
        PartialUpdateBlend = ... # type: QOpenGLWindow.UpdateBehavior

    @typing.overload
    def __init__(self, updateBehavior: 'QOpenGLWindow.UpdateBehavior' = ..., parent: typing.Optional[QWindow] = ...) -> None: ...
    @typing.overload
    def __init__(self, shareContext: QOpenGLContext, updateBehavior: 'QOpenGLWindow.UpdateBehavior' = ..., parent: typing.Optional[QWindow] = ...) -> None: ...

    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...
    def resizeEvent(self, event: QResizeEvent) -> None: ...
    def paintEvent(self, event: QPaintEvent) -> None: ...
    def paintOverGL(self) -> None: ...
    def paintUnderGL(self) -> None: ...
    def paintGL(self) -> None: ...
    def resizeGL(self, w: int, h: int) -> None: ...
    def initializeGL(self) -> None: ...
    frameSwapped: typing.ClassVar[QtCore.pyqtSignal]
    def shareContext(self) -> QOpenGLContext: ...
    def grabFramebuffer(self) -> QImage: ...
    def defaultFramebufferObject(self) -> int: ...
    def context(self) -> QOpenGLContext: ...
    def doneCurrent(self) -> None: ...
    def makeCurrent(self) -> None: ...
    def isValid(self) -> bool: ...
    def updateBehavior(self) -> 'QOpenGLWindow.UpdateBehavior': ...


class QPagedPaintDevice(QPaintDevice):

    class PdfVersion(int):
        PdfVersion_1_4 = ... # type: QPagedPaintDevice.PdfVersion
        PdfVersion_A1b = ... # type: QPagedPaintDevice.PdfVersion
        PdfVersion_1_6 = ... # type: QPagedPaintDevice.PdfVersion

    class PageSize(int):
        A4 = ... # type: QPagedPaintDevice.PageSize
        B5 = ... # type: QPagedPaintDevice.PageSize
        Letter = ... # type: QPagedPaintDevice.PageSize
        Legal = ... # type: QPagedPaintDevice.PageSize
        Executive = ... # type: QPagedPaintDevice.PageSize
        A0 = ... # type: QPagedPaintDevice.PageSize
        A1 = ... # type: QPagedPaintDevice.PageSize
        A2 = ... # type: QPagedPaintDevice.PageSize
        A3 = ... # type: QPagedPaintDevice.PageSize
        A5 = ... # type: QPagedPaintDevice.PageSize
        A6 = ... # type: QPagedPaintDevice.PageSize
        A7 = ... # type: QPagedPaintDevice.PageSize
        A8 = ... # type: QPagedPaintDevice.PageSize
        A9 = ... # type: QPagedPaintDevice.PageSize
        B0 = ... # type: QPagedPaintDevice.PageSize
        B1 = ... # type: QPagedPaintDevice.PageSize
        B10 = ... # type: QPagedPaintDevice.PageSize
        B2 = ... # type: QPagedPaintDevice.PageSize
        B3 = ... # type: QPagedPaintDevice.PageSize
        B4 = ... # type: QPagedPaintDevice.PageSize
        B6 = ... # type: QPagedPaintDevice.PageSize
        B7 = ... # type: QPagedPaintDevice.PageSize
        B8 = ... # type: QPagedPaintDevice.PageSize
        B9 = ... # type: QPagedPaintDevice.PageSize
        C5E = ... # type: QPagedPaintDevice.PageSize
        Comm10E = ... # type: QPagedPaintDevice.PageSize
        DLE = ... # type: QPagedPaintDevice.PageSize
        Folio = ... # type: QPagedPaintDevice.PageSize
        Ledger = ... # type: QPagedPaintDevice.PageSize
        Tabloid = ... # type: QPagedPaintDevice.PageSize
        Custom = ... # type: QPagedPaintDevice.PageSize
        A10 = ... # type: QPagedPaintDevice.PageSize
        A3Extra = ... # type: QPagedPaintDevice.PageSize
        A4Extra = ... # type: QPagedPaintDevice.PageSize
        A4Plus = ... # type: QPagedPaintDevice.PageSize
        A4Small = ... # type: QPagedPaintDevice.PageSize
        A5Extra = ... # type: QPagedPaintDevice.PageSize
        B5Extra = ... # type: QPagedPaintDevice.PageSize
        JisB0 = ... # type: QPagedPaintDevice.PageSize
        JisB1 = ... # type: QPagedPaintDevice.PageSize
        JisB2 = ... # type: QPagedPaintDevice.PageSize
        JisB3 = ... # type: QPagedPaintDevice.PageSize
        JisB4 = ... # type: QPagedPaintDevice.PageSize
        JisB5 = ... # type: QPagedPaintDevice.PageSize
        JisB6 = ... # type: QPagedPaintDevice.PageSize
        JisB7 = ... # type: QPagedPaintDevice.PageSize
        JisB8 = ... # type: QPagedPaintDevice.PageSize
        JisB9 = ... # type: QPagedPaintDevice.PageSize
        JisB10 = ... # type: QPagedPaintDevice.PageSize
        AnsiC = ... # type: QPagedPaintDevice.PageSize
        AnsiD = ... # type: QPagedPaintDevice.PageSize
        AnsiE = ... # type: QPagedPaintDevice.PageSize
        LegalExtra = ... # type: QPagedPaintDevice.PageSize
        LetterExtra = ... # type: QPagedPaintDevice.PageSize
        LetterPlus = ... # type: QPagedPaintDevice.PageSize
        LetterSmall = ... # type: QPagedPaintDevice.PageSize
        TabloidExtra = ... # type: QPagedPaintDevice.PageSize
        ArchA = ... # type: QPagedPaintDevice.PageSize
        ArchB = ... # type: QPagedPaintDevice.PageSize
        ArchC = ... # type: QPagedPaintDevice.PageSize
        ArchD = ... # type: QPagedPaintDevice.PageSize
        ArchE = ... # type: QPagedPaintDevice.PageSize
        Imperial7x9 = ... # type: QPagedPaintDevice.PageSize
        Imperial8x10 = ... # type: QPagedPaintDevice.PageSize
        Imperial9x11 = ... # type: QPagedPaintDevice.PageSize
        Imperial9x12 = ... # type: QPagedPaintDevice.PageSize
        Imperial10x11 = ... # type: QPagedPaintDevice.PageSize
        Imperial10x13 = ... # type: QPagedPaintDevice.PageSize
        Imperial10x14 = ... # type: QPagedPaintDevice.PageSize
        Imperial12x11 = ... # type: QPagedPaintDevice.PageSize
        Imperial15x11 = ... # type: QPagedPaintDevice.PageSize
        ExecutiveStandard = ... # type: QPagedPaintDevice.PageSize
        Note = ... # type: QPagedPaintDevice.PageSize
        Quarto = ... # type: QPagedPaintDevice.PageSize
        Statement = ... # type: QPagedPaintDevice.PageSize
        SuperA = ... # type: QPagedPaintDevice.PageSize
        SuperB = ... # type: QPagedPaintDevice.PageSize
        Postcard = ... # type: QPagedPaintDevice.PageSize
        DoublePostcard = ... # type: QPagedPaintDevice.PageSize
        Prc16K = ... # type: QPagedPaintDevice.PageSize
        Prc32K = ... # type: QPagedPaintDevice.PageSize
        Prc32KBig = ... # type: QPagedPaintDevice.PageSize
        FanFoldUS = ... # type: QPagedPaintDevice.PageSize
        FanFoldGerman = ... # type: QPagedPaintDevice.PageSize
        FanFoldGermanLegal = ... # type: QPagedPaintDevice.PageSize
        EnvelopeB4 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeB5 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeB6 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC0 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC1 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC2 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC3 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC4 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC6 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC65 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC7 = ... # type: QPagedPaintDevice.PageSize
        Envelope9 = ... # type: QPagedPaintDevice.PageSize
        Envelope11 = ... # type: QPagedPaintDevice.PageSize
        Envelope12 = ... # type: QPagedPaintDevice.PageSize
        Envelope14 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeMonarch = ... # type: QPagedPaintDevice.PageSize
        EnvelopePersonal = ... # type: QPagedPaintDevice.PageSize
        EnvelopeChou3 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeChou4 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeInvite = ... # type: QPagedPaintDevice.PageSize
        EnvelopeItalian = ... # type: QPagedPaintDevice.PageSize
        EnvelopeKaku2 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeKaku3 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc1 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc2 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc3 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc4 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc5 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc6 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc7 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc8 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc9 = ... # type: QPagedPaintDevice.PageSize
        EnvelopePrc10 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeYou4 = ... # type: QPagedPaintDevice.PageSize
        NPaperSize = ... # type: QPagedPaintDevice.PageSize
        AnsiA = ... # type: QPagedPaintDevice.PageSize
        AnsiB = ... # type: QPagedPaintDevice.PageSize
        EnvelopeC5 = ... # type: QPagedPaintDevice.PageSize
        EnvelopeDL = ... # type: QPagedPaintDevice.PageSize
        Envelope10 = ... # type: QPagedPaintDevice.PageSize
        LastPageSize = ... # type: QPagedPaintDevice.PageSize

    class Margins(PyQt5.sipsimplewrapper):

        bottom = ... # type: float
        left = ... # type: float
        right = ... # type: float
        top = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPagedPaintDevice.Margins') -> None: ...

    def __init__(self) -> None: ...

    def pageLayout(self) -> 'QPageLayout': ...
    @typing.overload
    def setPageMargins(self, margins: QtCore.QMarginsF) -> bool: ...
    @typing.overload
    def setPageMargins(self, margins: QtCore.QMarginsF, units: 'QPageLayout.Unit') -> bool: ...
    def setPageOrientation(self, orientation: 'QPageLayout.Orientation') -> bool: ...
    def setPageLayout(self, pageLayout: 'QPageLayout') -> bool: ...
    def margins(self) -> 'QPagedPaintDevice.Margins': ...
    def setMargins(self, margins: 'QPagedPaintDevice.Margins') -> None: ...
    def pageSizeMM(self) -> QtCore.QSizeF: ...
    def setPageSizeMM(self, size: QtCore.QSizeF) -> None: ...
    def pageSize(self) -> 'QPagedPaintDevice.PageSize': ...
    @typing.overload
    def setPageSize(self, size: 'QPagedPaintDevice.PageSize') -> None: ...
    @typing.overload
    def setPageSize(self, pageSize: 'QPageSize') -> bool: ...
    def newPage(self) -> bool: ...


class QPageLayout(PyQt5.sipsimplewrapper):

    class Mode(int):
        StandardMode = ... # type: QPageLayout.Mode
        FullPageMode = ... # type: QPageLayout.Mode

    class Orientation(int):
        Portrait = ... # type: QPageLayout.Orientation
        Landscape = ... # type: QPageLayout.Orientation

    class Unit(int):
        Millimeter = ... # type: QPageLayout.Unit
        Point = ... # type: QPageLayout.Unit
        Inch = ... # type: QPageLayout.Unit
        Pica = ... # type: QPageLayout.Unit
        Didot = ... # type: QPageLayout.Unit
        Cicero = ... # type: QPageLayout.Unit

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pageSize: 'QPageSize', orientation: 'QPageLayout.Orientation', margins: QtCore.QMarginsF, units: 'QPageLayout.Unit' = ..., minMargins: QtCore.QMarginsF = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPageLayout') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def paintRectPixels(self, resolution: int) -> QtCore.QRect: ...
    def paintRectPoints(self) -> QtCore.QRect: ...
    @typing.overload
    def paintRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def paintRect(self, units: 'QPageLayout.Unit') -> QtCore.QRectF: ...
    def fullRectPixels(self, resolution: int) -> QtCore.QRect: ...
    def fullRectPoints(self) -> QtCore.QRect: ...
    @typing.overload
    def fullRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def fullRect(self, units: 'QPageLayout.Unit') -> QtCore.QRectF: ...
    def maximumMargins(self) -> QtCore.QMarginsF: ...
    def minimumMargins(self) -> QtCore.QMarginsF: ...
    def setMinimumMargins(self, minMargins: QtCore.QMarginsF) -> None: ...
    def marginsPixels(self, resolution: int) -> QtCore.QMargins: ...
    def marginsPoints(self) -> QtCore.QMargins: ...
    @typing.overload
    def margins(self) -> QtCore.QMarginsF: ...
    @typing.overload
    def margins(self, units: 'QPageLayout.Unit') -> QtCore.QMarginsF: ...
    def setBottomMargin(self, bottomMargin: float) -> bool: ...
    def setTopMargin(self, topMargin: float) -> bool: ...
    def setRightMargin(self, rightMargin: float) -> bool: ...
    def setLeftMargin(self, leftMargin: float) -> bool: ...
    def setMargins(self, margins: QtCore.QMarginsF) -> bool: ...
    def units(self) -> 'QPageLayout.Unit': ...
    def setUnits(self, units: 'QPageLayout.Unit') -> None: ...
    def orientation(self) -> 'QPageLayout.Orientation': ...
    def setOrientation(self, orientation: 'QPageLayout.Orientation') -> None: ...
    def pageSize(self) -> 'QPageSize': ...
    def setPageSize(self, pageSize: 'QPageSize', minMargins: QtCore.QMarginsF = ...) -> None: ...
    def mode(self) -> 'QPageLayout.Mode': ...
    def setMode(self, mode: 'QPageLayout.Mode') -> None: ...
    def isValid(self) -> bool: ...
    def isEquivalentTo(self, other: 'QPageLayout') -> bool: ...
    def swap(self, other: 'QPageLayout') -> None: ...


class QPageSize(PyQt5.sipsimplewrapper):

    class SizeMatchPolicy(int):
        FuzzyMatch = ... # type: QPageSize.SizeMatchPolicy
        FuzzyOrientationMatch = ... # type: QPageSize.SizeMatchPolicy
        ExactMatch = ... # type: QPageSize.SizeMatchPolicy

    class Unit(int):
        Millimeter = ... # type: QPageSize.Unit
        Point = ... # type: QPageSize.Unit
        Inch = ... # type: QPageSize.Unit
        Pica = ... # type: QPageSize.Unit
        Didot = ... # type: QPageSize.Unit
        Cicero = ... # type: QPageSize.Unit

    class PageSizeId(int):
        A4 = ... # type: QPageSize.PageSizeId
        B5 = ... # type: QPageSize.PageSizeId
        Letter = ... # type: QPageSize.PageSizeId
        Legal = ... # type: QPageSize.PageSizeId
        Executive = ... # type: QPageSize.PageSizeId
        A0 = ... # type: QPageSize.PageSizeId
        A1 = ... # type: QPageSize.PageSizeId
        A2 = ... # type: QPageSize.PageSizeId
        A3 = ... # type: QPageSize.PageSizeId
        A5 = ... # type: QPageSize.PageSizeId
        A6 = ... # type: QPageSize.PageSizeId
        A7 = ... # type: QPageSize.PageSizeId
        A8 = ... # type: QPageSize.PageSizeId
        A9 = ... # type: QPageSize.PageSizeId
        B0 = ... # type: QPageSize.PageSizeId
        B1 = ... # type: QPageSize.PageSizeId
        B10 = ... # type: QPageSize.PageSizeId
        B2 = ... # type: QPageSize.PageSizeId
        B3 = ... # type: QPageSize.PageSizeId
        B4 = ... # type: QPageSize.PageSizeId
        B6 = ... # type: QPageSize.PageSizeId
        B7 = ... # type: QPageSize.PageSizeId
        B8 = ... # type: QPageSize.PageSizeId
        B9 = ... # type: QPageSize.PageSizeId
        C5E = ... # type: QPageSize.PageSizeId
        Comm10E = ... # type: QPageSize.PageSizeId
        DLE = ... # type: QPageSize.PageSizeId
        Folio = ... # type: QPageSize.PageSizeId
        Ledger = ... # type: QPageSize.PageSizeId
        Tabloid = ... # type: QPageSize.PageSizeId
        Custom = ... # type: QPageSize.PageSizeId
        A10 = ... # type: QPageSize.PageSizeId
        A3Extra = ... # type: QPageSize.PageSizeId
        A4Extra = ... # type: QPageSize.PageSizeId
        A4Plus = ... # type: QPageSize.PageSizeId
        A4Small = ... # type: QPageSize.PageSizeId
        A5Extra = ... # type: QPageSize.PageSizeId
        B5Extra = ... # type: QPageSize.PageSizeId
        JisB0 = ... # type: QPageSize.PageSizeId
        JisB1 = ... # type: QPageSize.PageSizeId
        JisB2 = ... # type: QPageSize.PageSizeId
        JisB3 = ... # type: QPageSize.PageSizeId
        JisB4 = ... # type: QPageSize.PageSizeId
        JisB5 = ... # type: QPageSize.PageSizeId
        JisB6 = ... # type: QPageSize.PageSizeId
        JisB7 = ... # type: QPageSize.PageSizeId
        JisB8 = ... # type: QPageSize.PageSizeId
        JisB9 = ... # type: QPageSize.PageSizeId
        JisB10 = ... # type: QPageSize.PageSizeId
        AnsiC = ... # type: QPageSize.PageSizeId
        AnsiD = ... # type: QPageSize.PageSizeId
        AnsiE = ... # type: QPageSize.PageSizeId
        LegalExtra = ... # type: QPageSize.PageSizeId
        LetterExtra = ... # type: QPageSize.PageSizeId
        LetterPlus = ... # type: QPageSize.PageSizeId
        LetterSmall = ... # type: QPageSize.PageSizeId
        TabloidExtra = ... # type: QPageSize.PageSizeId
        ArchA = ... # type: QPageSize.PageSizeId
        ArchB = ... # type: QPageSize.PageSizeId
        ArchC = ... # type: QPageSize.PageSizeId
        ArchD = ... # type: QPageSize.PageSizeId
        ArchE = ... # type: QPageSize.PageSizeId
        Imperial7x9 = ... # type: QPageSize.PageSizeId
        Imperial8x10 = ... # type: QPageSize.PageSizeId
        Imperial9x11 = ... # type: QPageSize.PageSizeId
        Imperial9x12 = ... # type: QPageSize.PageSizeId
        Imperial10x11 = ... # type: QPageSize.PageSizeId
        Imperial10x13 = ... # type: QPageSize.PageSizeId
        Imperial10x14 = ... # type: QPageSize.PageSizeId
        Imperial12x11 = ... # type: QPageSize.PageSizeId
        Imperial15x11 = ... # type: QPageSize.PageSizeId
        ExecutiveStandard = ... # type: QPageSize.PageSizeId
        Note = ... # type: QPageSize.PageSizeId
        Quarto = ... # type: QPageSize.PageSizeId
        Statement = ... # type: QPageSize.PageSizeId
        SuperA = ... # type: QPageSize.PageSizeId
        SuperB = ... # type: QPageSize.PageSizeId
        Postcard = ... # type: QPageSize.PageSizeId
        DoublePostcard = ... # type: QPageSize.PageSizeId
        Prc16K = ... # type: QPageSize.PageSizeId
        Prc32K = ... # type: QPageSize.PageSizeId
        Prc32KBig = ... # type: QPageSize.PageSizeId
        FanFoldUS = ... # type: QPageSize.PageSizeId
        FanFoldGerman = ... # type: QPageSize.PageSizeId
        FanFoldGermanLegal = ... # type: QPageSize.PageSizeId
        EnvelopeB4 = ... # type: QPageSize.PageSizeId
        EnvelopeB5 = ... # type: QPageSize.PageSizeId
        EnvelopeB6 = ... # type: QPageSize.PageSizeId
        EnvelopeC0 = ... # type: QPageSize.PageSizeId
        EnvelopeC1 = ... # type: QPageSize.PageSizeId
        EnvelopeC2 = ... # type: QPageSize.PageSizeId
        EnvelopeC3 = ... # type: QPageSize.PageSizeId
        EnvelopeC4 = ... # type: QPageSize.PageSizeId
        EnvelopeC6 = ... # type: QPageSize.PageSizeId
        EnvelopeC65 = ... # type: QPageSize.PageSizeId
        EnvelopeC7 = ... # type: QPageSize.PageSizeId
        Envelope9 = ... # type: QPageSize.PageSizeId
        Envelope11 = ... # type: QPageSize.PageSizeId
        Envelope12 = ... # type: QPageSize.PageSizeId
        Envelope14 = ... # type: QPageSize.PageSizeId
        EnvelopeMonarch = ... # type: QPageSize.PageSizeId
        EnvelopePersonal = ... # type: QPageSize.PageSizeId
        EnvelopeChou3 = ... # type: QPageSize.PageSizeId
        EnvelopeChou4 = ... # type: QPageSize.PageSizeId
        EnvelopeInvite = ... # type: QPageSize.PageSizeId
        EnvelopeItalian = ... # type: QPageSize.PageSizeId
        EnvelopeKaku2 = ... # type: QPageSize.PageSizeId
        EnvelopeKaku3 = ... # type: QPageSize.PageSizeId
        EnvelopePrc1 = ... # type: QPageSize.PageSizeId
        EnvelopePrc2 = ... # type: QPageSize.PageSizeId
        EnvelopePrc3 = ... # type: QPageSize.PageSizeId
        EnvelopePrc4 = ... # type: QPageSize.PageSizeId
        EnvelopePrc5 = ... # type: QPageSize.PageSizeId
        EnvelopePrc6 = ... # type: QPageSize.PageSizeId
        EnvelopePrc7 = ... # type: QPageSize.PageSizeId
        EnvelopePrc8 = ... # type: QPageSize.PageSizeId
        EnvelopePrc9 = ... # type: QPageSize.PageSizeId
        EnvelopePrc10 = ... # type: QPageSize.PageSizeId
        EnvelopeYou4 = ... # type: QPageSize.PageSizeId
        NPageSize = ... # type: QPageSize.PageSizeId
        NPaperSize = ... # type: QPageSize.PageSizeId
        AnsiA = ... # type: QPageSize.PageSizeId
        AnsiB = ... # type: QPageSize.PageSizeId
        EnvelopeC5 = ... # type: QPageSize.PageSizeId
        EnvelopeDL = ... # type: QPageSize.PageSizeId
        Envelope10 = ... # type: QPageSize.PageSizeId
        LastPageSize = ... # type: QPageSize.PageSizeId

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pageSizeId: 'QPageSize.PageSizeId') -> None: ...
    @typing.overload
    def __init__(self, pointSize: QtCore.QSize, name: str = ..., matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSizeF, units: 'QPageSize.Unit', name: str = ..., matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPageSize') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def rectPixels(self, resolution: int) -> QtCore.QRect: ...
    def rectPoints(self) -> QtCore.QRect: ...
    def rect(self, units: 'QPageSize.Unit') -> QtCore.QRectF: ...
    @typing.overload
    def sizePixels(self, resolution: int) -> QtCore.QSize: ...
    @typing.overload
    @staticmethod
    def sizePixels(pageSizeId: 'QPageSize.PageSizeId', resolution: int) -> QtCore.QSize: ...
    @typing.overload
    def sizePoints(self) -> QtCore.QSize: ...
    @typing.overload
    @staticmethod
    def sizePoints(pageSizeId: 'QPageSize.PageSizeId') -> QtCore.QSize: ...
    @typing.overload
    def size(self, units: 'QPageSize.Unit') -> QtCore.QSizeF: ...
    @typing.overload
    @staticmethod
    def size(pageSizeId: 'QPageSize.PageSizeId', units: 'QPageSize.Unit') -> QtCore.QSizeF: ...
    @typing.overload
    def definitionUnits(self) -> 'QPageSize.Unit': ...
    @typing.overload
    @staticmethod
    def definitionUnits(pageSizeId: 'QPageSize.PageSizeId') -> 'QPageSize.Unit': ...
    @typing.overload
    def definitionSize(self) -> QtCore.QSizeF: ...
    @typing.overload
    @staticmethod
    def definitionSize(pageSizeId: 'QPageSize.PageSizeId') -> QtCore.QSizeF: ...
    @typing.overload
    def windowsId(self) -> int: ...
    @typing.overload
    @staticmethod
    def windowsId(pageSizeId: 'QPageSize.PageSizeId') -> int: ...
    @typing.overload
    def id(self) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(pointSize: QtCore.QSize, matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(size: QtCore.QSizeF, units: 'QPageSize.Unit', matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(windowsId: int) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    def name(self) -> str: ...
    @typing.overload
    @staticmethod
    def name(pageSizeId: 'QPageSize.PageSizeId') -> str: ...
    @typing.overload
    def key(self) -> str: ...
    @typing.overload
    @staticmethod
    def key(pageSizeId: 'QPageSize.PageSizeId') -> str: ...
    def isValid(self) -> bool: ...
    def isEquivalentTo(self, other: 'QPageSize') -> bool: ...
    def swap(self, other: 'QPageSize') -> None: ...


class QPainter(PyQt5.sipsimplewrapper):

    class PixmapFragmentHint(int):
        OpaqueHint = ... # type: QPainter.PixmapFragmentHint

    class CompositionMode(int):
        CompositionMode_SourceOver = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationOver = ... # type: QPainter.CompositionMode
        CompositionMode_Clear = ... # type: QPainter.CompositionMode
        CompositionMode_Source = ... # type: QPainter.CompositionMode
        CompositionMode_Destination = ... # type: QPainter.CompositionMode
        CompositionMode_SourceIn = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationIn = ... # type: QPainter.CompositionMode
        CompositionMode_SourceOut = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationOut = ... # type: QPainter.CompositionMode
        CompositionMode_SourceAtop = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationAtop = ... # type: QPainter.CompositionMode
        CompositionMode_Xor = ... # type: QPainter.CompositionMode
        CompositionMode_Plus = ... # type: QPainter.CompositionMode
        CompositionMode_Multiply = ... # type: QPainter.CompositionMode
        CompositionMode_Screen = ... # type: QPainter.CompositionMode
        CompositionMode_Overlay = ... # type: QPainter.CompositionMode
        CompositionMode_Darken = ... # type: QPainter.CompositionMode
        CompositionMode_Lighten = ... # type: QPainter.CompositionMode
        CompositionMode_ColorDodge = ... # type: QPainter.CompositionMode
        CompositionMode_ColorBurn = ... # type: QPainter.CompositionMode
        CompositionMode_HardLight = ... # type: QPainter.CompositionMode
        CompositionMode_SoftLight = ... # type: QPainter.CompositionMode
        CompositionMode_Difference = ... # type: QPainter.CompositionMode
        CompositionMode_Exclusion = ... # type: QPainter.CompositionMode
        RasterOp_SourceOrDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceAndDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceXorDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceAndNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceOrNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceXorDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSource = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceAndDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceAndNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceOrDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceOrNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_ClearDestination = ... # type: QPainter.CompositionMode
        RasterOp_SetDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotDestination = ... # type: QPainter.CompositionMode

    class RenderHint(int):
        Antialiasing = ... # type: QPainter.RenderHint
        TextAntialiasing = ... # type: QPainter.RenderHint
        SmoothPixmapTransform = ... # type: QPainter.RenderHint
        HighQualityAntialiasing = ... # type: QPainter.RenderHint
        NonCosmeticDefaultPen = ... # type: QPainter.RenderHint
        Qt4CompatiblePainting = ... # type: QPainter.RenderHint
        LosslessImageRendering = ... # type: QPainter.RenderHint

    class RenderHints(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __xor__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __ior__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __or__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __iand__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __and__(self, f: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint']) -> 'QPainter.RenderHints': ...
        def __invert__(self) -> 'QPainter.RenderHints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class PixmapFragment(PyQt5.sipsimplewrapper):

        height = ... # type: float
        opacity = ... # type: float
        rotation = ... # type: float
        scaleX = ... # type: float
        scaleY = ... # type: float
        sourceLeft = ... # type: float
        sourceTop = ... # type: float
        width = ... # type: float
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPainter.PixmapFragment') -> None: ...

        @staticmethod
        def create(pos: typing.Union[QtCore.QPointF, QtCore.QPoint], sourceRect: QtCore.QRectF, scaleX: float = ..., scaleY: float = ..., rotation: float = ..., opacity: float = ...) -> 'QPainter.PixmapFragment': ...

    class PixmapFragmentHints(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __xor__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __ior__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __or__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __iand__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __and__(self, f: typing.Union['QPainter.PixmapFragmentHints', 'QPainter.PixmapFragmentHint']) -> 'QPainter.PixmapFragmentHints': ...
        def __invert__(self) -> 'QPainter.PixmapFragmentHints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: QPaintDevice) -> None: ...

    def drawGlyphRun(self, position: typing.Union[QtCore.QPointF, QtCore.QPoint], glyphRun: QGlyphRun) -> None: ...
    def clipBoundingRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def drawStaticText(self, topLeftPosition: typing.Union[QtCore.QPointF, QtCore.QPoint], staticText: 'QStaticText') -> None: ...
    @typing.overload
    def drawStaticText(self, p: QtCore.QPoint, staticText: 'QStaticText') -> None: ...
    @typing.overload
    def drawStaticText(self, x: int, y: int, staticText: 'QStaticText') -> None: ...
    def drawPixmapFragments(self, fragments: PyQt5.sip.array['QPainter.PixmapFragment'], pixmap: QPixmap, hints: 'QPainter.PixmapFragmentHints' = ...) -> None: ...
    def endNativePainting(self) -> None: ...
    def beginNativePainting(self) -> None: ...
    @typing.overload
    def drawRoundedRect(self, rect: QtCore.QRectF, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def drawRoundedRect(self, x: int, y: int, w: int, h: int, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def drawRoundedRect(self, rect: QtCore.QRect, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    def testRenderHint(self, hint: 'QPainter.RenderHint') -> bool: ...
    def combinedTransform(self) -> 'QTransform': ...
    def worldTransform(self) -> 'QTransform': ...
    def setWorldTransform(self, matrix: 'QTransform', combine: bool = ...) -> None: ...
    def resetTransform(self) -> None: ...
    def deviceTransform(self) -> 'QTransform': ...
    def transform(self) -> 'QTransform': ...
    def setTransform(self, transform: 'QTransform', combine: bool = ...) -> None: ...
    def setWorldMatrixEnabled(self, enabled: bool) -> None: ...
    def worldMatrixEnabled(self) -> bool: ...
    def setOpacity(self, opacity: float) -> None: ...
    def opacity(self) -> float: ...
    @typing.overload
    def drawImage(self, r: QtCore.QRectF, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, r: QtCore.QRect, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPoint, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, x: int, y: int, image: QImage, sx: int = ..., sy: int = ..., sw: int = ..., sh: int = ..., flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def drawImage(self, targetRect: QtCore.QRectF, image: QImage, sourceRect: QtCore.QRectF, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def drawImage(self, targetRect: QtCore.QRect, image: QImage, sourceRect: QtCore.QRect, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def drawImage(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], image: QImage, sr: QtCore.QRectF, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPoint, image: QImage, sr: QtCore.QRect, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    @typing.overload
    def drawPoint(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def drawPoint(self, x: int, y: int) -> None: ...
    @typing.overload
    def drawPoint(self, p: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawRect(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def drawRect(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawLine(self, l: QtCore.QLineF) -> None: ...
    @typing.overload
    def drawLine(self, line: QtCore.QLine) -> None: ...
    @typing.overload
    def drawLine(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    @typing.overload
    def drawLine(self, p1: QtCore.QPoint, p2: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawLine(self, p1: typing.Union[QtCore.QPointF, QtCore.QPoint], p2: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def paintEngine(self) -> 'QPaintEngine': ...
    def setRenderHints(self, hints: typing.Union['QPainter.RenderHints', 'QPainter.RenderHint'], on: bool = ...) -> None: ...
    def renderHints(self) -> 'QPainter.RenderHints': ...
    def setRenderHint(self, hint: 'QPainter.RenderHint', on: bool = ...) -> None: ...
    @typing.overload
    def eraseRect(self, a0: QtCore.QRectF) -> None: ...
    @typing.overload
    def eraseRect(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def eraseRect(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRectF, a1: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRect, a1: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, b: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRectF, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRect, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, b: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRectF, flags: int, text: str) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRect, flags: int, text: str) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, rectangle: QtCore.QRectF, text: str, option: 'QTextOption' = ...) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, x: int, y: int, w: int, h: int, flags: int, text: str) -> QtCore.QRect: ...
    @typing.overload
    def drawText(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], s: str) -> None: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRectF, flags: int, text: str) -> QtCore.QRectF: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRect, flags: int, text: str) -> QtCore.QRect: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRectF, text: str, option: 'QTextOption' = ...) -> None: ...
    @typing.overload
    def drawText(self, p: QtCore.QPoint, s: str) -> None: ...
    @typing.overload
    def drawText(self, x: int, y: int, width: int, height: int, flags: int, text: str) -> QtCore.QRect: ...
    @typing.overload
    def drawText(self, x: int, y: int, s: str) -> None: ...
    def layoutDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setLayoutDirection(self, direction: QtCore.Qt.LayoutDirection) -> None: ...
    @typing.overload
    def drawPixmap(self, targetRect: QtCore.QRectF, pixmap: QPixmap, sourceRect: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPixmap(self, targetRect: QtCore.QRect, pixmap: QPixmap, sourceRect: QtCore.QRect) -> None: ...
    @typing.overload
    def drawPixmap(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPoint, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, r: QtCore.QRect, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int) -> None: ...
    @typing.overload
    def drawPixmap(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], pm: QPixmap, sr: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPoint, pm: QPixmap, sr: QtCore.QRect) -> None: ...
    @typing.overload
    def drawPicture(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], picture: 'QPicture') -> None: ...
    @typing.overload
    def drawPicture(self, x: int, y: int, p: 'QPicture') -> None: ...
    @typing.overload
    def drawPicture(self, pt: QtCore.QPoint, p: 'QPicture') -> None: ...
    @typing.overload
    def drawTiledPixmap(self, rectangle: QtCore.QRectF, pixmap: QPixmap, pos: typing.Union[QtCore.QPointF, QtCore.QPoint] = ...) -> None: ...
    @typing.overload
    def drawTiledPixmap(self, rectangle: QtCore.QRect, pixmap: QPixmap, pos: QtCore.QPoint = ...) -> None: ...
    @typing.overload
    def drawTiledPixmap(self, x: int, y: int, width: int, height: int, pixmap: QPixmap, sx: int = ..., sy: int = ...) -> None: ...
    @typing.overload
    def drawChord(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawChord(self, rect: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawChord(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, rect: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, r: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, poly: 'QPolygonF') -> None: ...
    @typing.overload
    def drawConvexPolygon(self, poly: 'QPolygon') -> None: ...
    @typing.overload
    def drawConvexPolygon(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], *a1) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, points: PyQt5.sip.array[QtCore.QPoint]) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, point: QtCore.QPoint, *a1) -> None: ...
    @typing.overload
    def drawPolygon(self, points: 'QPolygonF', fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, points: 'QPolygon', fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]], fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], *a1) -> None: ...
    @typing.overload
    def drawPolygon(self, points: PyQt5.sip.array[QtCore.QPoint], fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, point: QtCore.QPoint, *a1) -> None: ...
    @typing.overload
    def drawPolyline(self, polyline: 'QPolygonF') -> None: ...
    @typing.overload
    def drawPolyline(self, polyline: 'QPolygon') -> None: ...
    @typing.overload
    def drawPolyline(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawPolyline(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], *a1) -> None: ...
    @typing.overload
    def drawPolyline(self, points: PyQt5.sip.array[QtCore.QPoint]) -> None: ...
    @typing.overload
    def drawPolyline(self, point: QtCore.QPoint, *a1) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawEllipse(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def drawEllipse(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], rx: float, ry: float) -> None: ...
    @typing.overload
    def drawEllipse(self, center: QtCore.QPoint, rx: int, ry: int) -> None: ...
    @typing.overload
    def drawRects(self, rects: PyQt5.sip.array[QtCore.QRectF]) -> None: ...
    @typing.overload
    def drawRects(self, rect: QtCore.QRectF, *a1) -> None: ...
    @typing.overload
    def drawRects(self, rects: PyQt5.sip.array[QtCore.QRect]) -> None: ...
    @typing.overload
    def drawRects(self, rect: QtCore.QRect, *a1) -> None: ...
    @typing.overload
    def drawLines(self, lines: PyQt5.sip.array[QtCore.QLineF]) -> None: ...
    @typing.overload
    def drawLines(self, line: QtCore.QLineF, *a1) -> None: ...
    @typing.overload
    def drawLines(self, pointPairs: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawLines(self, pointPair: typing.Union[QtCore.QPointF, QtCore.QPoint], *a1) -> None: ...
    @typing.overload
    def drawLines(self, lines: PyQt5.sip.array[QtCore.QLine]) -> None: ...
    @typing.overload
    def drawLines(self, line: QtCore.QLine, *a1) -> None: ...
    @typing.overload
    def drawLines(self, pointPairs: PyQt5.sip.array[QtCore.QPoint]) -> None: ...
    @typing.overload
    def drawLines(self, pointPair: QtCore.QPoint, *a1) -> None: ...
    @typing.overload
    def drawPoints(self, points: 'QPolygonF') -> None: ...
    @typing.overload
    def drawPoints(self, points: 'QPolygon') -> None: ...
    @typing.overload
    def drawPoints(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawPoints(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], *a1) -> None: ...
    @typing.overload
    def drawPoints(self, points: PyQt5.sip.array[QtCore.QPoint]) -> None: ...
    @typing.overload
    def drawPoints(self, point: QtCore.QPoint, *a1) -> None: ...
    def drawPath(self, path: 'QPainterPath') -> None: ...
    def fillPath(self, path: 'QPainterPath', brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def strokePath(self, path: 'QPainterPath', pen: typing.Union['QPen', QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def viewTransformEnabled(self) -> bool: ...
    def setViewTransformEnabled(self, enable: bool) -> None: ...
    @typing.overload
    def setViewport(self, viewport: QtCore.QRect) -> None: ...
    @typing.overload
    def setViewport(self, x: int, y: int, w: int, h: int) -> None: ...
    def viewport(self) -> QtCore.QRect: ...
    @typing.overload
    def setWindow(self, window: QtCore.QRect) -> None: ...
    @typing.overload
    def setWindow(self, x: int, y: int, w: int, h: int) -> None: ...
    def window(self) -> QtCore.QRect: ...
    @typing.overload
    def translate(self, offset: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPoint) -> None: ...
    def rotate(self, a: float) -> None: ...
    def shear(self, sh: float, sv: float) -> None: ...
    def scale(self, sx: float, sy: float) -> None: ...
    def restore(self) -> None: ...
    def save(self) -> None: ...
    def hasClipping(self) -> bool: ...
    def setClipping(self, enable: bool) -> None: ...
    def setClipPath(self, path: 'QPainterPath', operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    def setClipRegion(self, region: 'QRegion', operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, rectangle: QtCore.QRectF, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, x: int, y: int, width: int, height: int, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, rectangle: QtCore.QRect, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    def clipPath(self) -> 'QPainterPath': ...
    def clipRegion(self) -> 'QRegion': ...
    def background(self) -> QBrush: ...
    def setBackground(self, bg: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def setBrushOrigin(self, a0: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setBrushOrigin(self, x: int, y: int) -> None: ...
    @typing.overload
    def setBrushOrigin(self, p: QtCore.QPoint) -> None: ...
    def brushOrigin(self) -> QtCore.QPoint: ...
    def backgroundMode(self) -> QtCore.Qt.BGMode: ...
    def setBackgroundMode(self, mode: QtCore.Qt.BGMode) -> None: ...
    def brush(self) -> QBrush: ...
    @typing.overload
    def setBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, style: QtCore.Qt.BrushStyle) -> None: ...
    def pen(self) -> 'QPen': ...
    @typing.overload
    def setPen(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setPen(self, pen: typing.Union['QPen', QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setPen(self, style: QtCore.Qt.PenStyle) -> None: ...
    def fontInfo(self) -> QFontInfo: ...
    def fontMetrics(self) -> QFontMetrics: ...
    def setFont(self, f: QFont) -> None: ...
    def font(self) -> QFont: ...
    def compositionMode(self) -> 'QPainter.CompositionMode': ...
    def setCompositionMode(self, mode: 'QPainter.CompositionMode') -> None: ...
    def isActive(self) -> bool: ...
    def end(self) -> bool: ...
    def begin(self, a0: QPaintDevice) -> bool: ...
    def device(self) -> QPaintDevice: ...
    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...


class QTextItem(PyQt5.sipsimplewrapper):

    class RenderFlag(int):
        RightToLeft = ... # type: QTextItem.RenderFlag
        Overline = ... # type: QTextItem.RenderFlag
        Underline = ... # type: QTextItem.RenderFlag
        StrikeOut = ... # type: QTextItem.RenderFlag

    class RenderFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __xor__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __ior__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __or__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __iand__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __and__(self, f: typing.Union['QTextItem.RenderFlags', 'QTextItem.RenderFlag']) -> 'QTextItem.RenderFlags': ...
        def __invert__(self) -> 'QTextItem.RenderFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextItem') -> None: ...

    def font(self) -> QFont: ...
    def text(self) -> str: ...
    def renderFlags(self) -> 'QTextItem.RenderFlags': ...
    def width(self) -> float: ...
    def ascent(self) -> float: ...
    def descent(self) -> float: ...


class QPaintEngine(PyQt5.sipsimplewrapper):

    class Type(int):
        X11 = ... # type: QPaintEngine.Type
        Windows = ... # type: QPaintEngine.Type
        QuickDraw = ... # type: QPaintEngine.Type
        CoreGraphics = ... # type: QPaintEngine.Type
        MacPrinter = ... # type: QPaintEngine.Type
        QWindowSystem = ... # type: QPaintEngine.Type
        PostScript = ... # type: QPaintEngine.Type
        OpenGL = ... # type: QPaintEngine.Type
        Picture = ... # type: QPaintEngine.Type
        SVG = ... # type: QPaintEngine.Type
        Raster = ... # type: QPaintEngine.Type
        Direct3D = ... # type: QPaintEngine.Type
        Pdf = ... # type: QPaintEngine.Type
        OpenVG = ... # type: QPaintEngine.Type
        OpenGL2 = ... # type: QPaintEngine.Type
        PaintBuffer = ... # type: QPaintEngine.Type
        Blitter = ... # type: QPaintEngine.Type
        Direct2D = ... # type: QPaintEngine.Type
        User = ... # type: QPaintEngine.Type
        MaxUser = ... # type: QPaintEngine.Type

    class PolygonDrawMode(int):
        OddEvenMode = ... # type: QPaintEngine.PolygonDrawMode
        WindingMode = ... # type: QPaintEngine.PolygonDrawMode
        ConvexMode = ... # type: QPaintEngine.PolygonDrawMode
        PolylineMode = ... # type: QPaintEngine.PolygonDrawMode

    class DirtyFlag(int):
        DirtyPen = ... # type: QPaintEngine.DirtyFlag
        DirtyBrush = ... # type: QPaintEngine.DirtyFlag
        DirtyBrushOrigin = ... # type: QPaintEngine.DirtyFlag
        DirtyFont = ... # type: QPaintEngine.DirtyFlag
        DirtyBackground = ... # type: QPaintEngine.DirtyFlag
        DirtyBackgroundMode = ... # type: QPaintEngine.DirtyFlag
        DirtyTransform = ... # type: QPaintEngine.DirtyFlag
        DirtyClipRegion = ... # type: QPaintEngine.DirtyFlag
        DirtyClipPath = ... # type: QPaintEngine.DirtyFlag
        DirtyHints = ... # type: QPaintEngine.DirtyFlag
        DirtyCompositionMode = ... # type: QPaintEngine.DirtyFlag
        DirtyClipEnabled = ... # type: QPaintEngine.DirtyFlag
        DirtyOpacity = ... # type: QPaintEngine.DirtyFlag
        AllDirty = ... # type: QPaintEngine.DirtyFlag

    class PaintEngineFeature(int):
        PrimitiveTransform = ... # type: QPaintEngine.PaintEngineFeature
        PatternTransform = ... # type: QPaintEngine.PaintEngineFeature
        PixmapTransform = ... # type: QPaintEngine.PaintEngineFeature
        PatternBrush = ... # type: QPaintEngine.PaintEngineFeature
        LinearGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        RadialGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        ConicalGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        AlphaBlend = ... # type: QPaintEngine.PaintEngineFeature
        PorterDuff = ... # type: QPaintEngine.PaintEngineFeature
        PainterPaths = ... # type: QPaintEngine.PaintEngineFeature
        Antialiasing = ... # type: QPaintEngine.PaintEngineFeature
        BrushStroke = ... # type: QPaintEngine.PaintEngineFeature
        ConstantOpacity = ... # type: QPaintEngine.PaintEngineFeature
        MaskedBrush = ... # type: QPaintEngine.PaintEngineFeature
        PaintOutsidePaintEvent = ... # type: QPaintEngine.PaintEngineFeature
        PerspectiveTransform = ... # type: QPaintEngine.PaintEngineFeature
        BlendModes = ... # type: QPaintEngine.PaintEngineFeature
        ObjectBoundingModeGradients = ... # type: QPaintEngine.PaintEngineFeature
        RasterOpModes = ... # type: QPaintEngine.PaintEngineFeature
        AllFeatures = ... # type: QPaintEngine.PaintEngineFeature

    class PaintEngineFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __xor__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __ior__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __or__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __iand__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __and__(self, f: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __invert__(self) -> 'QPaintEngine.PaintEngineFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class DirtyFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __xor__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __ior__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __or__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __iand__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __and__(self, f: typing.Union['QPaintEngine.DirtyFlags', 'QPaintEngine.DirtyFlag']) -> 'QPaintEngine.DirtyFlags': ...
        def __invert__(self) -> 'QPaintEngine.DirtyFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, features: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature'] = ...) -> None: ...

    def hasFeature(self, feature: typing.Union['QPaintEngine.PaintEngineFeatures', 'QPaintEngine.PaintEngineFeature']) -> bool: ...
    def painter(self) -> QPainter: ...
    def type(self) -> 'QPaintEngine.Type': ...
    def paintDevice(self) -> QPaintDevice: ...
    def setPaintDevice(self, device: QPaintDevice) -> None: ...
    def drawImage(self, r: QtCore.QRectF, pm: QImage, sr: QtCore.QRectF, flags: typing.Union[QtCore.Qt.ImageConversionFlags, QtCore.Qt.ImageConversionFlag] = ...) -> None: ...
    def drawTiledPixmap(self, r: QtCore.QRectF, pixmap: QPixmap, s: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def drawTextItem(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint], textItem: QTextItem) -> None: ...
    def drawPixmap(self, r: QtCore.QRectF, pm: QPixmap, sr: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPolygon(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]], mode: 'QPaintEngine.PolygonDrawMode') -> None: ...
    @typing.overload
    def drawPolygon(self, points: PyQt5.sip.array[QtCore.QPoint], mode: 'QPaintEngine.PolygonDrawMode') -> None: ...
    @typing.overload
    def drawPoints(self, points: PyQt5.sip.array[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawPoints(self, points: PyQt5.sip.array[QtCore.QPoint]) -> None: ...
    def drawPath(self, path: 'QPainterPath') -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawLines(self, lines: PyQt5.sip.array[QtCore.QLine]) -> None: ...
    @typing.overload
    def drawLines(self, lines: PyQt5.sip.array[QtCore.QLineF]) -> None: ...
    @typing.overload
    def drawRects(self, rects: PyQt5.sip.array[QtCore.QRect]) -> None: ...
    @typing.overload
    def drawRects(self, rects: PyQt5.sip.array[QtCore.QRectF]) -> None: ...
    def updateState(self, state: 'QPaintEngineState') -> None: ...
    def end(self) -> bool: ...
    def begin(self, pdev: QPaintDevice) -> bool: ...
    def setActive(self, newState: bool) -> None: ...
    def isActive(self) -> bool: ...


class QPaintEngineState(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPaintEngineState') -> None: ...

    def penNeedsResolving(self) -> bool: ...
    def brushNeedsResolving(self) -> bool: ...
    def transform(self) -> 'QTransform': ...
    def painter(self) -> QPainter: ...
    def compositionMode(self) -> QPainter.CompositionMode: ...
    def renderHints(self) -> QPainter.RenderHints: ...
    def isClipEnabled(self) -> bool: ...
    def clipPath(self) -> 'QPainterPath': ...
    def clipRegion(self) -> 'QRegion': ...
    def clipOperation(self) -> QtCore.Qt.ClipOperation: ...
    def opacity(self) -> float: ...
    def font(self) -> QFont: ...
    def backgroundMode(self) -> QtCore.Qt.BGMode: ...
    def backgroundBrush(self) -> QBrush: ...
    def brushOrigin(self) -> QtCore.QPointF: ...
    def brush(self) -> QBrush: ...
    def pen(self) -> 'QPen': ...
    def state(self) -> QPaintEngine.DirtyFlags: ...


class QPainterPath(PyQt5.sipsimplewrapper):

    class ElementType(int):
        MoveToElement = ... # type: QPainterPath.ElementType
        LineToElement = ... # type: QPainterPath.ElementType
        CurveToElement = ... # type: QPainterPath.ElementType
        CurveToDataElement = ... # type: QPainterPath.ElementType

    class Element(PyQt5.sipsimplewrapper):

        type = ... # type: 'QPainterPath.ElementType'
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPainterPath.Element') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def isCurveTo(self) -> bool: ...
        def isLineTo(self) -> bool: ...
        def isMoveTo(self) -> bool: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, startPoint: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPainterPath') -> None: ...

    def __mul__(self, p: 'QPainterPath', m: 'QTransform') -> 'QPainterPath': ...
    def capacity(self) -> int: ...
    def reserve(self, size: int) -> None: ...
    def clear(self) -> None: ...
    def swap(self, other: 'QPainterPath') -> None: ...
    @typing.overload
    def translated(self, dx: float, dy: float) -> 'QPainterPath': ...
    @typing.overload
    def translated(self, offset: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QPainterPath': ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, offset: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def __isub__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __iadd__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __ior__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __iand__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __sub__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __add__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __or__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __and__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def simplified(self) -> 'QPainterPath': ...
    @typing.overload
    def addRoundedRect(self, rect: QtCore.QRectF, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def addRoundedRect(self, x: float, y: float, w: float, h: float, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    def subtracted(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def intersected(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def united(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def slopeAtPercent(self, t: float) -> float: ...
    def angleAtPercent(self, t: float) -> float: ...
    def pointAtPercent(self, t: float) -> QtCore.QPointF: ...
    def percentAtLength(self, t: float) -> float: ...
    def length(self) -> float: ...
    def setElementPositionAt(self, i: int, x: float, y: float) -> None: ...
    def elementAt(self, i: int) -> 'QPainterPath.Element': ...
    def elementCount(self) -> int: ...
    def isEmpty(self) -> bool: ...
    @typing.overload
    def arcMoveTo(self, rect: QtCore.QRectF, angle: float) -> None: ...
    @typing.overload
    def arcMoveTo(self, x: float, y: float, w: float, h: float, angle: float) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def toFillPolygon(self) -> 'QPolygonF': ...
    @typing.overload
    def toFillPolygon(self, matrix: 'QTransform') -> 'QPolygonF': ...
    @typing.overload
    def toFillPolygons(self) -> typing.List['QPolygonF']: ...
    @typing.overload
    def toFillPolygons(self, matrix: 'QTransform') -> typing.List['QPolygonF']: ...
    @typing.overload
    def toSubpathPolygons(self) -> typing.List['QPolygonF']: ...
    @typing.overload
    def toSubpathPolygons(self, matrix: 'QTransform') -> typing.List['QPolygonF']: ...
    def toReversed(self) -> 'QPainterPath': ...
    def setFillRule(self, fillRule: QtCore.Qt.FillRule) -> None: ...
    def fillRule(self) -> QtCore.Qt.FillRule: ...
    def controlPointRect(self) -> QtCore.QRectF: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def intersects(self, rect: QtCore.QRectF) -> bool: ...
    @typing.overload
    def intersects(self, p: 'QPainterPath') -> bool: ...
    @typing.overload
    def contains(self, pt: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> bool: ...
    @typing.overload
    def contains(self, rect: QtCore.QRectF) -> bool: ...
    @typing.overload
    def contains(self, p: 'QPainterPath') -> bool: ...
    def connectPath(self, path: 'QPainterPath') -> None: ...
    def addRegion(self, region: 'QRegion') -> None: ...
    def addPath(self, path: 'QPainterPath') -> None: ...
    @typing.overload
    def addText(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint], f: QFont, text: str) -> None: ...
    @typing.overload
    def addText(self, x: float, y: float, f: QFont, text: str) -> None: ...
    def addPolygon(self, polygon: 'QPolygonF') -> None: ...
    @typing.overload
    def addEllipse(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def addEllipse(self, x: float, y: float, w: float, h: float) -> None: ...
    @typing.overload
    def addEllipse(self, center: typing.Union[QtCore.QPointF, QtCore.QPoint], rx: float, ry: float) -> None: ...
    @typing.overload
    def addRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def addRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def currentPosition(self) -> QtCore.QPointF: ...
    @typing.overload
    def quadTo(self, ctrlPt: typing.Union[QtCore.QPointF, QtCore.QPoint], endPt: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def quadTo(self, ctrlPtx: float, ctrlPty: float, endPtx: float, endPty: float) -> None: ...
    @typing.overload
    def cubicTo(self, ctrlPt1: typing.Union[QtCore.QPointF, QtCore.QPoint], ctrlPt2: typing.Union[QtCore.QPointF, QtCore.QPoint], endPt: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def cubicTo(self, ctrlPt1x: float, ctrlPt1y: float, ctrlPt2x: float, ctrlPt2y: float, endPtx: float, endPty: float) -> None: ...
    @typing.overload
    def arcTo(self, rect: QtCore.QRectF, startAngle: float, arcLength: float) -> None: ...
    @typing.overload
    def arcTo(self, x: float, y: float, w: float, h: float, startAngle: float, arcLenght: float) -> None: ...
    @typing.overload
    def lineTo(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def lineTo(self, x: float, y: float) -> None: ...
    @typing.overload
    def moveTo(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def moveTo(self, x: float, y: float) -> None: ...
    def closeSubpath(self) -> None: ...


class QPainterPathStroker(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pen: typing.Union['QPen', QColor, QtCore.Qt.GlobalColor]) -> None: ...

    def dashOffset(self) -> float: ...
    def setDashOffset(self, offset: float) -> None: ...
    def createStroke(self, path: QPainterPath) -> QPainterPath: ...
    def dashPattern(self) -> typing.List[float]: ...
    @typing.overload
    def setDashPattern(self, a0: QtCore.Qt.PenStyle) -> None: ...
    @typing.overload
    def setDashPattern(self, dashPattern: typing.Iterable[float]) -> None: ...
    def curveThreshold(self) -> float: ...
    def setCurveThreshold(self, threshold: float) -> None: ...
    def miterLimit(self) -> float: ...
    def setMiterLimit(self, length: float) -> None: ...
    def joinStyle(self) -> QtCore.Qt.PenJoinStyle: ...
    def setJoinStyle(self, style: QtCore.Qt.PenJoinStyle) -> None: ...
    def capStyle(self) -> QtCore.Qt.PenCapStyle: ...
    def setCapStyle(self, style: QtCore.Qt.PenCapStyle) -> None: ...
    def width(self) -> float: ...
    def setWidth(self, width: float) -> None: ...


class QPalette(PyQt5.sipsimplewrapper):

    class ColorRole(int):
        WindowText = ... # type: QPalette.ColorRole
        Foreground = ... # type: QPalette.ColorRole
        Button = ... # type: QPalette.ColorRole
        Light = ... # type: QPalette.ColorRole
        Midlight = ... # type: QPalette.ColorRole
        Dark = ... # type: QPalette.ColorRole
        Mid = ... # type: QPalette.ColorRole
        Text = ... # type: QPalette.ColorRole
        BrightText = ... # type: QPalette.ColorRole
        ButtonText = ... # type: QPalette.ColorRole
        Base = ... # type: QPalette.ColorRole
        Window = ... # type: QPalette.ColorRole
        Background = ... # type: QPalette.ColorRole
        Shadow = ... # type: QPalette.ColorRole
        Highlight = ... # type: QPalette.ColorRole
        HighlightedText = ... # type: QPalette.ColorRole
        Link = ... # type: QPalette.ColorRole
        LinkVisited = ... # type: QPalette.ColorRole
        AlternateBase = ... # type: QPalette.ColorRole
        ToolTipBase = ... # type: QPalette.ColorRole
        ToolTipText = ... # type: QPalette.ColorRole
        PlaceholderText = ... # type: QPalette.ColorRole
        NoRole = ... # type: QPalette.ColorRole
        NColorRoles = ... # type: QPalette.ColorRole

    class ColorGroup(int):
        Active = ... # type: QPalette.ColorGroup
        Disabled = ... # type: QPalette.ColorGroup
        Inactive = ... # type: QPalette.ColorGroup
        NColorGroups = ... # type: QPalette.ColorGroup
        Current = ... # type: QPalette.ColorGroup
        All = ... # type: QPalette.ColorGroup
        Normal = ... # type: QPalette.ColorGroup

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, button: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def __init__(self, button: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def __init__(self, button: typing.Union[QColor, QtCore.Qt.GlobalColor], background: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def __init__(self, foreground: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], button: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], light: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], dark: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], mid: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], text: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], bright_text: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], base: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], background: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def __init__(self, palette: 'QPalette') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QPalette') -> None: ...
    def cacheKey(self) -> int: ...
    def isBrushSet(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> bool: ...
    @typing.overload
    def setColor(self, acg: 'QPalette.ColorGroup', acr: 'QPalette.ColorRole', acolor: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setColor(self, acr: 'QPalette.ColorRole', acolor: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def resolve(self, a0: 'QPalette') -> 'QPalette': ...
    @typing.overload
    def resolve(self) -> int: ...
    @typing.overload
    def resolve(self, mask: int) -> None: ...
    def isCopyOf(self, p: 'QPalette') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def placeholderText(self) -> QBrush: ...
    def toolTipText(self) -> QBrush: ...
    def toolTipBase(self) -> QBrush: ...
    def linkVisited(self) -> QBrush: ...
    def link(self) -> QBrush: ...
    def highlightedText(self) -> QBrush: ...
    def highlight(self) -> QBrush: ...
    def shadow(self) -> QBrush: ...
    def buttonText(self) -> QBrush: ...
    def brightText(self) -> QBrush: ...
    def midlight(self) -> QBrush: ...
    def window(self) -> QBrush: ...
    def alternateBase(self) -> QBrush: ...
    def base(self) -> QBrush: ...
    def text(self) -> QBrush: ...
    def mid(self) -> QBrush: ...
    def dark(self) -> QBrush: ...
    def light(self) -> QBrush: ...
    def button(self) -> QBrush: ...
    def windowText(self) -> QBrush: ...
    def isEqual(self, cr1: 'QPalette.ColorGroup', cr2: 'QPalette.ColorGroup') -> bool: ...
    def setColorGroup(self, cr: 'QPalette.ColorGroup', foreground: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], button: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], light: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], dark: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], mid: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], text: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], bright_text: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], base: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], background: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole', brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, acr: 'QPalette.ColorRole', abrush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    @typing.overload
    def brush(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> QBrush: ...
    @typing.overload
    def brush(self, cr: 'QPalette.ColorRole') -> QBrush: ...
    @typing.overload
    def color(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> QColor: ...
    @typing.overload
    def color(self, cr: 'QPalette.ColorRole') -> QColor: ...
    def setCurrentColorGroup(self, cg: 'QPalette.ColorGroup') -> None: ...
    def currentColorGroup(self) -> 'QPalette.ColorGroup': ...


class QPdfWriter(QtCore.QObject, QPagedPaintDevice):

    @typing.overload
    def __init__(self, filename: str) -> None: ...
    @typing.overload
    def __init__(self, device: QtCore.QIODevice) -> None: ...

    def addFileAttachment(self, fileName: str, data: typing.Union[QtCore.QByteArray, bytes, bytearray], mimeType: str = ...) -> None: ...
    def documentXmpMetadata(self) -> QtCore.QByteArray: ...
    def setDocumentXmpMetadata(self, xmpMetadata: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    def pdfVersion(self) -> QPagedPaintDevice.PdfVersion: ...
    def setPdfVersion(self, version: QPagedPaintDevice.PdfVersion) -> None: ...
    def resolution(self) -> int: ...
    def setResolution(self, resolution: int) -> None: ...
    def metric(self, id: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> QPaintEngine: ...
    def setMargins(self, m: QPagedPaintDevice.Margins) -> None: ...
    def setPageSizeMM(self, size: QtCore.QSizeF) -> None: ...
    @typing.overload
    def setPageSize(self, size: QPagedPaintDevice.PageSize) -> None: ...
    @typing.overload
    def setPageSize(self, pageSize: QPageSize) -> bool: ...
    def newPage(self) -> bool: ...
    def setCreator(self, creator: str) -> None: ...
    def creator(self) -> str: ...
    def setTitle(self, title: str) -> None: ...
    def title(self) -> str: ...


class QPen(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.Qt.PenStyle) -> None: ...
    @typing.overload
    def __init__(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient], width: float, style: QtCore.Qt.PenStyle = ..., cap: QtCore.Qt.PenCapStyle = ..., join: QtCore.Qt.PenJoinStyle = ...) -> None: ...
    @typing.overload
    def __init__(self, pen: typing.Union['QPen', QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QPen') -> None: ...
    def setCosmetic(self, cosmetic: bool) -> None: ...
    def isCosmetic(self) -> bool: ...
    def setDashOffset(self, doffset: float) -> None: ...
    def dashOffset(self) -> float: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setMiterLimit(self, limit: float) -> None: ...
    def miterLimit(self) -> float: ...
    def setDashPattern(self, pattern: typing.Iterable[float]) -> None: ...
    def dashPattern(self) -> typing.List[float]: ...
    def setJoinStyle(self, pcs: QtCore.Qt.PenJoinStyle) -> None: ...
    def joinStyle(self) -> QtCore.Qt.PenJoinStyle: ...
    def setCapStyle(self, pcs: QtCore.Qt.PenCapStyle) -> None: ...
    def capStyle(self) -> QtCore.Qt.PenCapStyle: ...
    def isSolid(self) -> bool: ...
    def setBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def brush(self) -> QBrush: ...
    def setColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def color(self) -> QColor: ...
    def setWidth(self, width: int) -> None: ...
    def width(self) -> int: ...
    def setWidthF(self, width: float) -> None: ...
    def widthF(self) -> float: ...
    def setStyle(self, a0: QtCore.Qt.PenStyle) -> None: ...
    def style(self) -> QtCore.Qt.PenStyle: ...


class QPicture(QPaintDevice):

    @typing.overload
    def __init__(self, formatVersion: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPicture') -> None: ...

    def swap(self, other: 'QPicture') -> None: ...
    def metric(self, m: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> QPaintEngine: ...
    def isDetached(self) -> bool: ...
    def detach(self) -> None: ...
    def setBoundingRect(self, r: QtCore.QRect) -> None: ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def save(self, dev: QtCore.QIODevice, format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def save(self, fileName: str, format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, dev: QtCore.QIODevice, format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, fileName: str, format: typing.Optional[str] = ...) -> bool: ...
    def play(self, p: QPainter) -> bool: ...
    def setData(self, data: PyQt5.sip.array[bytes]) -> None: ...
    def data(self) -> bytes: ...
    def size(self) -> int: ...
    def devType(self) -> int: ...
    def isNull(self) -> bool: ...


class QPictureIO(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, ioDevice: QtCore.QIODevice, format: str) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: str) -> None: ...

    @staticmethod
    def defineIOHandler(format: str, header: str, flags: str, read_picture: typing.Optional[typing.Callable[['QPictureIO'], None]], write_picture: typing.Optional[typing.Callable[['QPictureIO'], None]]) -> None: ...
    @staticmethod
    def outputFormats() -> typing.List[QtCore.QByteArray]: ...
    @staticmethod
    def inputFormats() -> typing.List[QtCore.QByteArray]: ...
    @typing.overload
    @staticmethod
    def pictureFormat(fileName: str) -> QtCore.QByteArray: ...
    @typing.overload
    @staticmethod
    def pictureFormat(a0: QtCore.QIODevice) -> QtCore.QByteArray: ...
    def write(self) -> bool: ...
    def read(self) -> bool: ...
    def setGamma(self, a0: float) -> None: ...
    def setParameters(self, a0: str) -> None: ...
    def setDescription(self, a0: str) -> None: ...
    def setQuality(self, a0: int) -> None: ...
    def setFileName(self, a0: str) -> None: ...
    def setIODevice(self, a0: QtCore.QIODevice) -> None: ...
    def setFormat(self, a0: str) -> None: ...
    def setStatus(self, a0: int) -> None: ...
    def setPicture(self, a0: QPicture) -> None: ...
    def gamma(self) -> float: ...
    def parameters(self) -> str: ...
    def description(self) -> str: ...
    def quality(self) -> int: ...
    def fileName(self) -> str: ...
    def ioDevice(self) -> QtCore.QIODevice: ...
    def format(self) -> str: ...
    def status(self) -> int: ...
    def picture(self) -> QPicture: ...


class QPixelFormat(PyQt5.sipsimplewrapper):

    class ByteOrder(int):
        LittleEndian = ... # type: QPixelFormat.ByteOrder
        BigEndian = ... # type: QPixelFormat.ByteOrder
        CurrentSystemEndian = ... # type: QPixelFormat.ByteOrder

    class YUVLayout(int):
        YUV444 = ... # type: QPixelFormat.YUVLayout
        YUV422 = ... # type: QPixelFormat.YUVLayout
        YUV411 = ... # type: QPixelFormat.YUVLayout
        YUV420P = ... # type: QPixelFormat.YUVLayout
        YUV420SP = ... # type: QPixelFormat.YUVLayout
        YV12 = ... # type: QPixelFormat.YUVLayout
        UYVY = ... # type: QPixelFormat.YUVLayout
        YUYV = ... # type: QPixelFormat.YUVLayout
        NV12 = ... # type: QPixelFormat.YUVLayout
        NV21 = ... # type: QPixelFormat.YUVLayout
        IMC1 = ... # type: QPixelFormat.YUVLayout
        IMC2 = ... # type: QPixelFormat.YUVLayout
        IMC3 = ... # type: QPixelFormat.YUVLayout
        IMC4 = ... # type: QPixelFormat.YUVLayout
        Y8 = ... # type: QPixelFormat.YUVLayout
        Y16 = ... # type: QPixelFormat.YUVLayout

    class TypeInterpretation(int):
        UnsignedInteger = ... # type: QPixelFormat.TypeInterpretation
        UnsignedShort = ... # type: QPixelFormat.TypeInterpretation
        UnsignedByte = ... # type: QPixelFormat.TypeInterpretation
        FloatingPoint = ... # type: QPixelFormat.TypeInterpretation

    class AlphaPremultiplied(int):
        NotPremultiplied = ... # type: QPixelFormat.AlphaPremultiplied
        Premultiplied = ... # type: QPixelFormat.AlphaPremultiplied

    class AlphaPosition(int):
        AtBeginning = ... # type: QPixelFormat.AlphaPosition
        AtEnd = ... # type: QPixelFormat.AlphaPosition

    class AlphaUsage(int):
        UsesAlpha = ... # type: QPixelFormat.AlphaUsage
        IgnoresAlpha = ... # type: QPixelFormat.AlphaUsage

    class ColorModel(int):
        RGB = ... # type: QPixelFormat.ColorModel
        BGR = ... # type: QPixelFormat.ColorModel
        Indexed = ... # type: QPixelFormat.ColorModel
        Grayscale = ... # type: QPixelFormat.ColorModel
        CMYK = ... # type: QPixelFormat.ColorModel
        HSL = ... # type: QPixelFormat.ColorModel
        HSV = ... # type: QPixelFormat.ColorModel
        YUV = ... # type: QPixelFormat.ColorModel
        Alpha = ... # type: QPixelFormat.ColorModel

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, mdl: 'QPixelFormat.ColorModel', firstSize: int, secondSize: int, thirdSize: int, fourthSize: int, fifthSize: int, alfa: int, usage: 'QPixelFormat.AlphaUsage', position: 'QPixelFormat.AlphaPosition', premult: 'QPixelFormat.AlphaPremultiplied', typeInterp: 'QPixelFormat.TypeInterpretation', byteOrder: 'QPixelFormat.ByteOrder' = ..., subEnum: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixelFormat') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def subEnum(self) -> int: ...
    def yuvLayout(self) -> 'QPixelFormat.YUVLayout': ...
    def byteOrder(self) -> 'QPixelFormat.ByteOrder': ...
    def typeInterpretation(self) -> 'QPixelFormat.TypeInterpretation': ...
    def premultiplied(self) -> 'QPixelFormat.AlphaPremultiplied': ...
    def alphaPosition(self) -> 'QPixelFormat.AlphaPosition': ...
    def alphaUsage(self) -> 'QPixelFormat.AlphaUsage': ...
    def bitsPerPixel(self) -> int: ...
    def alphaSize(self) -> int: ...
    def brightnessSize(self) -> int: ...
    def lightnessSize(self) -> int: ...
    def saturationSize(self) -> int: ...
    def hueSize(self) -> int: ...
    def blackSize(self) -> int: ...
    def yellowSize(self) -> int: ...
    def magentaSize(self) -> int: ...
    def cyanSize(self) -> int: ...
    def blueSize(self) -> int: ...
    def greenSize(self) -> int: ...
    def redSize(self) -> int: ...
    def channelCount(self) -> int: ...
    def colorModel(self) -> 'QPixelFormat.ColorModel': ...


class QPixmapCache(PyQt5.sipsimplewrapper):

    class Key(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, other: 'QPixmapCache.Key') -> None: ...

        def isValid(self) -> bool: ...
        def swap(self, other: 'QPixmapCache.Key') -> None: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixmapCache') -> None: ...

    @staticmethod
    def setCacheLimit(a0: int) -> None: ...
    @staticmethod
    def replace(key: 'QPixmapCache.Key', pixmap: QPixmap) -> bool: ...
    @typing.overload
    @staticmethod
    def remove(key: str) -> None: ...
    @typing.overload
    @staticmethod
    def remove(key: 'QPixmapCache.Key') -> None: ...
    @typing.overload
    @staticmethod
    def insert(key: str, a1: QPixmap) -> bool: ...
    @typing.overload
    @staticmethod
    def insert(pixmap: QPixmap) -> 'QPixmapCache.Key': ...
    @typing.overload
    @staticmethod
    def find(key: str) -> QPixmap: ...
    @typing.overload
    @staticmethod
    def find(key: 'QPixmapCache.Key') -> QPixmap: ...
    @staticmethod
    def clear() -> None: ...
    @staticmethod
    def cacheLimit() -> int: ...


class QPolygon(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a: 'QPolygon') -> None: ...
    @typing.overload
    def __init__(self, points: typing.List[int]) -> None: ...
    @typing.overload
    def __init__(self, v: typing.Iterable[QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, rectangle: QtCore.QRect, closed: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, asize: int) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __mul__(self, a: 'QPolygon', m: 'QTransform') -> 'QPolygon': ...
    def intersects(self, r: 'QPolygon') -> bool: ...
    def swap(self, other: 'QPolygon') -> None: ...
    def __contains__(self, value: QtCore.QPoint) -> int: ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, value: QtCore.QPoint) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QPolygon') -> None: ...
    @typing.overload
    def __getitem__(self, i: int) -> QtCore.QPoint: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QPolygon': ...
    def __lshift__(self, value: QtCore.QPoint) -> typing.Any: ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, other: 'QPolygon') -> 'QPolygon': ...
    @typing.overload
    def __iadd__(self, value: QtCore.QPoint) -> 'QPolygon': ...
    def __add__(self, other: 'QPolygon') -> 'QPolygon': ...
    def __ne__(self, other: object): ...
    @typing.overload
    def value(self, i: int) -> QtCore.QPoint: ...
    @typing.overload
    def value(self, i: int, defaultValue: QtCore.QPoint) -> QtCore.QPoint: ...
    def size(self) -> int: ...
    def replace(self, i: int, value: QtCore.QPoint) -> None: ...
    @typing.overload
    def remove(self, i: int) -> None: ...
    @typing.overload
    def remove(self, i: int, count: int) -> None: ...
    def prepend(self, value: QtCore.QPoint) -> None: ...
    def mid(self, pos: int, length: int = ...) -> 'QPolygon': ...
    def lastIndexOf(self, value: QtCore.QPoint, from_: int = ...) -> int: ...
    def last(self) -> QtCore.QPoint: ...
    def isEmpty(self) -> bool: ...
    def insert(self, i: int, value: QtCore.QPoint) -> None: ...
    def indexOf(self, value: QtCore.QPoint, from_: int = ...) -> int: ...
    def first(self) -> QtCore.QPoint: ...
    def fill(self, value: QtCore.QPoint, size: int = ...) -> None: ...
    def data(self) -> PyQt5.sip.voidptr: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, value: QtCore.QPoint) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def contains(self, value: QtCore.QPoint) -> bool: ...
    def clear(self) -> None: ...
    def at(self, i: int) -> QtCore.QPoint: ...
    def append(self, value: QtCore.QPoint) -> None: ...
    @typing.overload
    def translated(self, dx: int, dy: int) -> 'QPolygon': ...
    @typing.overload
    def translated(self, offset: QtCore.QPoint) -> 'QPolygon': ...
    def subtracted(self, r: 'QPolygon') -> 'QPolygon': ...
    def intersected(self, r: 'QPolygon') -> 'QPolygon': ...
    def united(self, r: 'QPolygon') -> 'QPolygon': ...
    def containsPoint(self, pt: QtCore.QPoint, fillRule: QtCore.Qt.FillRule) -> bool: ...
    @typing.overload
    def setPoint(self, index: int, pt: QtCore.QPoint) -> None: ...
    @typing.overload
    def setPoint(self, index: int, x: int, y: int) -> None: ...
    @typing.overload
    def putPoints(self, index: int, firstx: int, firsty: int, *a3) -> None: ...
    @typing.overload
    def putPoints(self, index: int, nPoints: int, fromPolygon: 'QPolygon', from_: int = ...) -> None: ...
    @typing.overload
    def setPoints(self, points: typing.List[int]) -> None: ...
    @typing.overload
    def setPoints(self, firstx: int, firsty: int, *a2) -> None: ...
    def point(self, index: int) -> QtCore.QPoint: ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def translate(self, dx: int, dy: int) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPoint) -> None: ...


class QPolygonF(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a: 'QPolygonF') -> None: ...
    @typing.overload
    def __init__(self, v: typing.Iterable[typing.Union[QtCore.QPointF, QtCore.QPoint]]) -> None: ...
    @typing.overload
    def __init__(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def __init__(self, a: QPolygon) -> None: ...
    @typing.overload
    def __init__(self, asize: int) -> None: ...

    def __mul__(self, a: 'QPolygonF', m: 'QTransform') -> 'QPolygonF': ...
    def intersects(self, r: 'QPolygonF') -> bool: ...
    def swap(self, other: 'QPolygonF') -> None: ...
    def __contains__(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> int: ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QPolygonF') -> None: ...
    @typing.overload
    def __getitem__(self, i: int) -> QtCore.QPointF: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QPolygonF': ...
    def __lshift__(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> typing.Any: ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, other: 'QPolygonF') -> 'QPolygonF': ...
    @typing.overload
    def __iadd__(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QPolygonF': ...
    def __add__(self, other: 'QPolygonF') -> 'QPolygonF': ...
    def __ne__(self, other: object): ...
    @typing.overload
    def value(self, i: int) -> QtCore.QPointF: ...
    @typing.overload
    def value(self, i: int, defaultValue: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def size(self) -> int: ...
    def replace(self, i: int, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def remove(self, i: int) -> None: ...
    @typing.overload
    def remove(self, i: int, count: int) -> None: ...
    def prepend(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def mid(self, pos: int, length: int = ...) -> 'QPolygonF': ...
    def lastIndexOf(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint], from_: int = ...) -> int: ...
    def last(self) -> QtCore.QPointF: ...
    def isEmpty(self) -> bool: ...
    def insert(self, i: int, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def indexOf(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint], from_: int = ...) -> int: ...
    def first(self) -> QtCore.QPointF: ...
    def fill(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint], size: int = ...) -> None: ...
    def data(self) -> PyQt5.sip.voidptr: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def contains(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> bool: ...
    def clear(self) -> None: ...
    def at(self, i: int) -> QtCore.QPointF: ...
    def append(self, value: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def translated(self, offset: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> 'QPolygonF': ...
    @typing.overload
    def translated(self, dx: float, dy: float) -> 'QPolygonF': ...
    def subtracted(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def intersected(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def united(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def containsPoint(self, pt: typing.Union[QtCore.QPointF, QtCore.QPoint], fillRule: QtCore.Qt.FillRule) -> bool: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def isClosed(self) -> bool: ...
    def toPolygon(self) -> QPolygon: ...
    @typing.overload
    def translate(self, offset: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...


class QQuaternion(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, aScalar: float, xpos: float, ypos: float, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, aScalar: float, aVector: 'QVector3D') -> None: ...
    @typing.overload
    def __init__(self, aVector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQuaternion') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __truediv__(self, quaternion: 'QQuaternion', divisor: float) -> 'QQuaternion': ...
    def __add__(self, q1: 'QQuaternion', q2: 'QQuaternion') -> 'QQuaternion': ...
    def __sub__(self, q1: 'QQuaternion', q2: 'QQuaternion') -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, q1: 'QQuaternion', q2: 'QQuaternion') -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, factor: float, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, quaternion: 'QQuaternion', factor: float) -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, quaternion: 'QQuaternion', vec: 'QVector3D') -> 'QVector3D': ...
    def __neg__(self) -> 'QQuaternion': ...
    def toEulerAngles(self) -> 'QVector3D': ...
    def conjugated(self) -> 'QQuaternion': ...
    def inverted(self) -> 'QQuaternion': ...
    @staticmethod
    def dotProduct(q1: 'QQuaternion', q2: 'QQuaternion') -> float: ...
    @staticmethod
    def rotationTo(from_: 'QVector3D', to: 'QVector3D') -> 'QQuaternion': ...
    @staticmethod
    def fromDirection(direction: 'QVector3D', up: 'QVector3D') -> 'QQuaternion': ...
    @staticmethod
    def fromAxes(xAxis: 'QVector3D', yAxis: 'QVector3D', zAxis: 'QVector3D') -> 'QQuaternion': ...
    def getAxes(self) -> typing.Tuple['QVector3D', 'QVector3D', 'QVector3D']: ...
    @staticmethod
    def fromRotationMatrix(rot3x3: QMatrix3x3) -> 'QQuaternion': ...
    def toRotationMatrix(self) -> QMatrix3x3: ...
    @typing.overload
    @staticmethod
    def fromEulerAngles(pitch: float, yaw: float, roll: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromEulerAngles(eulerAngles: 'QVector3D') -> 'QQuaternion': ...
    def getEulerAngles(self) -> typing.Tuple[float, float, float]: ...
    def getAxisAndAngle(self) -> typing.Tuple['QVector3D', float]: ...
    def toVector4D(self) -> 'QVector4D': ...
    def vector(self) -> 'QVector3D': ...
    @typing.overload
    def setVector(self, aVector: 'QVector3D') -> None: ...
    @typing.overload
    def setVector(self, aX: float, aY: float, aZ: float) -> None: ...
    def __itruediv__(self, divisor: float) -> 'QQuaternion': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QQuaternion': ...
    @typing.overload
    def __imul__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def __isub__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def __iadd__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def conjugate(self) -> 'QQuaternion': ...
    def setScalar(self, aScalar: float) -> None: ...
    def setZ(self, aZ: float) -> None: ...
    def setY(self, aY: float) -> None: ...
    def setX(self, aX: float) -> None: ...
    def scalar(self) -> float: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isIdentity(self) -> bool: ...
    def isNull(self) -> bool: ...
    @staticmethod
    def nlerp(q1: 'QQuaternion', q2: 'QQuaternion', t: float) -> 'QQuaternion': ...
    @staticmethod
    def slerp(q1: 'QQuaternion', q2: 'QQuaternion', t: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromAxisAndAngle(axis: 'QVector3D', angle: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromAxisAndAngle(x: float, y: float, z: float, angle: float) -> 'QQuaternion': ...
    def rotatedVector(self, vector: 'QVector3D') -> 'QVector3D': ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QQuaternion': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __repr__(self) -> str: ...


class QRasterWindow(QPaintDeviceWindow):

    def __init__(self, parent: typing.Optional[QWindow] = ...) -> None: ...

    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...


class QRawFont(PyQt5.sipsimplewrapper):

    class LayoutFlag(int):
        SeparateAdvances = ... # type: QRawFont.LayoutFlag
        KernedAdvances = ... # type: QRawFont.LayoutFlag
        UseDesignMetrics = ... # type: QRawFont.LayoutFlag

    class AntialiasingType(int):
        PixelAntialiasing = ... # type: QRawFont.AntialiasingType
        SubPixelAntialiasing = ... # type: QRawFont.AntialiasingType

    class LayoutFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __xor__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __ior__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __or__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __iand__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __and__(self, f: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> 'QRawFont.LayoutFlags': ...
        def __invert__(self) -> 'QRawFont.LayoutFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, pixelSize: float, hintingPreference: QFont.HintingPreference = ...) -> None: ...
    @typing.overload
    def __init__(self, fontData: typing.Union[QtCore.QByteArray, bytes, bytearray], pixelSize: float, hintingPreference: QFont.HintingPreference = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QRawFont') -> None: ...

    def __hash__(self) -> int: ...
    def capHeight(self) -> float: ...
    def swap(self, other: 'QRawFont') -> None: ...
    def underlinePosition(self) -> float: ...
    def lineThickness(self) -> float: ...
    def boundingRect(self, glyphIndex: int) -> QtCore.QRectF: ...
    @staticmethod
    def fromFont(font: QFont, writingSystem: QFontDatabase.WritingSystem = ...) -> 'QRawFont': ...
    def fontTable(self, tagName: str) -> QtCore.QByteArray: ...
    def supportedWritingSystems(self) -> typing.List[QFontDatabase.WritingSystem]: ...
    @typing.overload
    def supportsCharacter(self, ucs4: int) -> bool: ...
    @typing.overload
    def supportsCharacter(self, character: str) -> bool: ...
    def loadFromData(self, fontData: typing.Union[QtCore.QByteArray, bytes, bytearray], pixelSize: float, hintingPreference: QFont.HintingPreference) -> None: ...
    def loadFromFile(self, fileName: str, pixelSize: float, hintingPreference: QFont.HintingPreference) -> None: ...
    def unitsPerEm(self) -> float: ...
    def maxCharWidth(self) -> float: ...
    def averageCharWidth(self) -> float: ...
    def xHeight(self) -> float: ...
    def leading(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def hintingPreference(self) -> QFont.HintingPreference: ...
    def pixelSize(self) -> float: ...
    def setPixelSize(self, pixelSize: float) -> None: ...
    def pathForGlyph(self, glyphIndex: int) -> QPainterPath: ...
    def alphaMapForGlyph(self, glyphIndex: int, antialiasingType: 'QRawFont.AntialiasingType' = ..., transform: 'QTransform' = ...) -> QImage: ...
    @typing.overload
    def advancesForGlyphIndexes(self, glyphIndexes: typing.Iterable[int]) -> typing.List[QtCore.QPointF]: ...
    @typing.overload
    def advancesForGlyphIndexes(self, glyphIndexes: typing.Iterable[int], layoutFlags: typing.Union['QRawFont.LayoutFlags', 'QRawFont.LayoutFlag']) -> typing.List[QtCore.QPointF]: ...
    def glyphIndexesForString(self, text: str) -> typing.List[int]: ...
    def weight(self) -> int: ...
    def style(self) -> QFont.Style: ...
    def styleName(self) -> str: ...
    def familyName(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QRegion(PyQt5.sipsimplewrapper):

    class RegionType(int):
        Rectangle = ... # type: QRegion.RegionType
        Ellipse = ... # type: QRegion.RegionType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, x: int, y: int, w: int, h: int, type: 'QRegion.RegionType' = ...) -> None: ...
    @typing.overload
    def __init__(self, r: QtCore.QRect, type: 'QRegion.RegionType' = ...) -> None: ...
    @typing.overload
    def __init__(self, a: QPolygon, fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def __init__(self, bitmap: QBitmap) -> None: ...
    @typing.overload
    def __init__(self, region: 'QRegion') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __mul__(self, r: 'QRegion', m: 'QTransform') -> 'QRegion': ...
    def isNull(self) -> bool: ...
    def swap(self, other: 'QRegion') -> None: ...
    def rectCount(self) -> int: ...
    @typing.overload
    def intersects(self, r: 'QRegion') -> bool: ...
    @typing.overload
    def intersects(self, r: QtCore.QRect) -> bool: ...
    def xored(self, r: 'QRegion') -> 'QRegion': ...
    def subtracted(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def intersected(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def intersected(self, r: QtCore.QRect) -> 'QRegion': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __ixor__(self, r: 'QRegion') -> 'QRegion': ...
    def __isub__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iand__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iand__(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def __iadd__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iadd__(self, r: QtCore.QRect) -> 'QRegion': ...
    def __ior__(self, r: 'QRegion') -> 'QRegion': ...
    def __xor__(self, r: 'QRegion') -> 'QRegion': ...
    def __sub__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __and__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __and__(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def __add__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __add__(self, r: QtCore.QRect) -> 'QRegion': ...
    def setRects(self, a0: typing.Iterable[QtCore.QRect]) -> None: ...
    def __or__(self, r: 'QRegion') -> 'QRegion': ...
    def rects(self) -> typing.List[QtCore.QRect]: ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def united(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def united(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def translated(self, dx: int, dy: int) -> 'QRegion': ...
    @typing.overload
    def translated(self, p: QtCore.QPoint) -> 'QRegion': ...
    @typing.overload
    def translate(self, dx: int, dy: int) -> None: ...
    @typing.overload
    def translate(self, p: QtCore.QPoint) -> None: ...
    @typing.overload
    def __contains__(self, p: QtCore.QPoint) -> int: ...
    @typing.overload
    def __contains__(self, r: QtCore.QRect) -> int: ...
    @typing.overload
    def contains(self, p: QtCore.QPoint) -> bool: ...
    @typing.overload
    def contains(self, r: QtCore.QRect) -> bool: ...
    def __bool__(self) -> int: ...
    def isEmpty(self) -> bool: ...


class QRgba64(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRgba64') -> None: ...

    def __int__(self) -> int: ...
    def unpremultiplied(self) -> 'QRgba64': ...
    def premultiplied(self) -> 'QRgba64': ...
    def toRgb16(self) -> int: ...
    def toArgb32(self) -> int: ...
    def alpha8(self) -> int: ...
    def blue8(self) -> int: ...
    def green8(self) -> int: ...
    def red8(self) -> int: ...
    def setAlpha(self, _alpha: int) -> None: ...
    def setBlue(self, _blue: int) -> None: ...
    def setGreen(self, _green: int) -> None: ...
    def setRed(self, _red: int) -> None: ...
    def alpha(self) -> int: ...
    def blue(self) -> int: ...
    def green(self) -> int: ...
    def red(self) -> int: ...
    def isTransparent(self) -> bool: ...
    def isOpaque(self) -> bool: ...
    @staticmethod
    def fromArgb32(rgb: int) -> 'QRgba64': ...
    @staticmethod
    def fromRgba(red: int, green: int, blue: int, alpha: int) -> 'QRgba64': ...
    @typing.overload
    @staticmethod
    def fromRgba64(c: int) -> 'QRgba64': ...
    @typing.overload
    @staticmethod
    def fromRgba64(red: int, green: int, blue: int, alpha: int) -> 'QRgba64': ...


class QScreen(QtCore.QObject):

    def virtualSiblingAt(self, point: QtCore.QPoint) -> 'QScreen': ...
    def serialNumber(self) -> str: ...
    def model(self) -> str: ...
    def manufacturer(self) -> str: ...
    availableGeometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    virtualGeometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    physicalSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    refreshRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    orientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    primaryOrientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    logicalDotsPerInchChanged: typing.ClassVar[QtCore.pyqtSignal]
    physicalDotsPerInchChanged: typing.ClassVar[QtCore.pyqtSignal]
    geometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    def devicePixelRatio(self) -> float: ...
    def refreshRate(self) -> float: ...
    def grabWindow(self, window: PyQt5.sip.voidptr, x: int = ..., y: int = ..., width: int = ..., height: int = ...) -> QPixmap: ...
    def isLandscape(self, orientation: QtCore.Qt.ScreenOrientation) -> bool: ...
    def isPortrait(self, orientation: QtCore.Qt.ScreenOrientation) -> bool: ...
    def mapBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation, rect: QtCore.QRect) -> QtCore.QRect: ...
    def transformBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation, target: QtCore.QRect) -> 'QTransform': ...
    def angleBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation) -> int: ...
    def setOrientationUpdateMask(self, mask: typing.Union[QtCore.Qt.ScreenOrientations, QtCore.Qt.ScreenOrientation]) -> None: ...
    def orientationUpdateMask(self) -> QtCore.Qt.ScreenOrientations: ...
    def orientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def primaryOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def nativeOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def availableVirtualGeometry(self) -> QtCore.QRect: ...
    def availableVirtualSize(self) -> QtCore.QSize: ...
    def virtualGeometry(self) -> QtCore.QRect: ...
    def virtualSize(self) -> QtCore.QSize: ...
    def virtualSiblings(self) -> typing.List['QScreen']: ...
    def availableGeometry(self) -> QtCore.QRect: ...
    def availableSize(self) -> QtCore.QSize: ...
    def logicalDotsPerInch(self) -> float: ...
    def logicalDotsPerInchY(self) -> float: ...
    def logicalDotsPerInchX(self) -> float: ...
    def physicalDotsPerInch(self) -> float: ...
    def physicalDotsPerInchY(self) -> float: ...
    def physicalDotsPerInchX(self) -> float: ...
    def physicalSize(self) -> QtCore.QSizeF: ...
    def geometry(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def depth(self) -> int: ...
    def name(self) -> str: ...


class QSessionManager(QtCore.QObject):

    class RestartHint(int):
        RestartIfRunning = ... # type: QSessionManager.RestartHint
        RestartAnyway = ... # type: QSessionManager.RestartHint
        RestartImmediately = ... # type: QSessionManager.RestartHint
        RestartNever = ... # type: QSessionManager.RestartHint

    def requestPhase2(self) -> None: ...
    def isPhase2(self) -> bool: ...
    @typing.overload
    def setManagerProperty(self, name: str, value: str) -> None: ...
    @typing.overload
    def setManagerProperty(self, name: str, value: typing.Iterable[str]) -> None: ...
    def discardCommand(self) -> typing.List[str]: ...
    def setDiscardCommand(self, a0: typing.Iterable[str]) -> None: ...
    def restartCommand(self) -> typing.List[str]: ...
    def setRestartCommand(self, a0: typing.Iterable[str]) -> None: ...
    def restartHint(self) -> 'QSessionManager.RestartHint': ...
    def setRestartHint(self, a0: 'QSessionManager.RestartHint') -> None: ...
    def cancel(self) -> None: ...
    def release(self) -> None: ...
    def allowsErrorInteraction(self) -> bool: ...
    def allowsInteraction(self) -> bool: ...
    def sessionKey(self) -> str: ...
    def sessionId(self) -> str: ...


class QStandardItemModel(QtCore.QAbstractItemModel):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, rows: int, columns: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def clearItemData(self, index: QtCore.QModelIndex) -> bool: ...
    itemChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setItemRoleNames(self, roleNames: typing.Dict[int, typing.Union[QtCore.QByteArray, bytes, bytearray]]) -> None: ...
    def sibling(self, row: int, column: int, idx: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    def dropMimeData(self, data: QtCore.QMimeData, action: QtCore.Qt.DropAction, row: int, column: int, parent: QtCore.QModelIndex) -> bool: ...
    def mimeData(self, indexes: typing.Iterable[QtCore.QModelIndex]) -> QtCore.QMimeData: ...
    def mimeTypes(self) -> typing.List[str]: ...
    def setSortRole(self, role: int) -> None: ...
    def sortRole(self) -> int: ...
    def findItems(self, text: str, flags: typing.Union[QtCore.Qt.MatchFlags, QtCore.Qt.MatchFlag] = ..., column: int = ...) -> typing.List['QStandardItem']: ...
    def setItemPrototype(self, item: 'QStandardItem') -> None: ...
    def itemPrototype(self) -> 'QStandardItem': ...
    def takeVerticalHeaderItem(self, row: int) -> 'QStandardItem': ...
    def takeHorizontalHeaderItem(self, column: int) -> 'QStandardItem': ...
    def takeColumn(self, column: int) -> typing.List['QStandardItem']: ...
    def takeRow(self, row: int) -> typing.List['QStandardItem']: ...
    def takeItem(self, row: int, column: int = ...) -> 'QStandardItem': ...
    @typing.overload
    def insertColumn(self, column: int, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertColumn(self, column: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    @typing.overload
    def insertRow(self, row: int, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, arow: int, aitem: 'QStandardItem') -> None: ...
    @typing.overload
    def insertRow(self, row: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def appendColumn(self, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, aitem: 'QStandardItem') -> None: ...
    def setColumnCount(self, columns: int) -> None: ...
    def setRowCount(self, rows: int) -> None: ...
    def setVerticalHeaderLabels(self, labels: typing.Iterable[str]) -> None: ...
    def setHorizontalHeaderLabels(self, labels: typing.Iterable[str]) -> None: ...
    def setVerticalHeaderItem(self, row: int, item: 'QStandardItem') -> None: ...
    def verticalHeaderItem(self, row: int) -> 'QStandardItem': ...
    def setHorizontalHeaderItem(self, column: int, item: 'QStandardItem') -> None: ...
    def horizontalHeaderItem(self, column: int) -> 'QStandardItem': ...
    def invisibleRootItem(self) -> 'QStandardItem': ...
    @typing.overload
    def setItem(self, row: int, column: int, item: 'QStandardItem') -> None: ...
    @typing.overload
    def setItem(self, arow: int, aitem: 'QStandardItem') -> None: ...
    def item(self, row: int, column: int = ...) -> 'QStandardItem': ...
    def indexFromItem(self, item: 'QStandardItem') -> QtCore.QModelIndex: ...
    def itemFromIndex(self, index: QtCore.QModelIndex) -> 'QStandardItem': ...
    def sort(self, column: int, order: QtCore.Qt.SortOrder = ...) -> None: ...
    def setItemData(self, index: QtCore.QModelIndex, roles: typing.Dict[int, typing.Any]) -> bool: ...
    def itemData(self, index: QtCore.QModelIndex) -> typing.Dict[int, typing.Any]: ...
    def supportedDropActions(self) -> QtCore.Qt.DropActions: ...
    def clear(self) -> None: ...
    def flags(self, index: QtCore.QModelIndex) -> QtCore.Qt.ItemFlags: ...
    def removeColumns(self, column: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def removeRows(self, row: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def setHeaderData(self, section: int, orientation: QtCore.Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: QtCore.Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QtCore.QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QtCore.QModelIndex, role: int = ...) -> typing.Any: ...
    def hasChildren(self, parent: QtCore.QModelIndex = ...) -> bool: ...
    def columnCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    @typing.overload
    def parent(self, child: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    @typing.overload
    def parent(self) -> QtCore.QObject: ...
    def index(self, row: int, column: int, parent: QtCore.QModelIndex = ...) -> QtCore.QModelIndex: ...


class QStandardItem(PyQt5.sip.wrapper):

    class ItemType(int):
        Type = ... # type: QStandardItem.ItemType
        UserType = ... # type: QStandardItem.ItemType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: str) -> None: ...
    @typing.overload
    def __init__(self, icon: QIcon, text: str) -> None: ...
    @typing.overload
    def __init__(self, rows: int, columns: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QStandardItem') -> None: ...

    def __ge__(self, other: 'QStandardItem') -> bool: ...
    def clearData(self) -> None: ...
    def setUserTristate(self, tristate: bool) -> None: ...
    def isUserTristate(self) -> bool: ...
    def setAutoTristate(self, tristate: bool) -> None: ...
    def isAutoTristate(self) -> bool: ...
    def emitDataChanged(self) -> None: ...
    def appendRows(self, items: typing.Iterable['QStandardItem']) -> None: ...
    def appendColumn(self, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, aitem: 'QStandardItem') -> None: ...
    def setAccessibleDescription(self, aaccessibleDescription: str) -> None: ...
    def setAccessibleText(self, aaccessibleText: str) -> None: ...
    def setCheckState(self, acheckState: QtCore.Qt.CheckState) -> None: ...
    def setForeground(self, abrush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def setBackground(self, abrush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def setTextAlignment(self, atextAlignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def setFont(self, afont: QFont) -> None: ...
    def setSizeHint(self, asizeHint: QtCore.QSize) -> None: ...
    def setWhatsThis(self, awhatsThis: str) -> None: ...
    def setStatusTip(self, astatusTip: str) -> None: ...
    def setToolTip(self, atoolTip: str) -> None: ...
    def setIcon(self, aicon: QIcon) -> None: ...
    def setText(self, atext: str) -> None: ...
    def __lt__(self, other: 'QStandardItem') -> bool: ...
    def write(self, out: QtCore.QDataStream) -> None: ...
    def read(self, in_: QtCore.QDataStream) -> None: ...
    def type(self) -> int: ...
    def clone(self) -> 'QStandardItem': ...
    def sortChildren(self, column: int, order: QtCore.Qt.SortOrder = ...) -> None: ...
    def takeColumn(self, column: int) -> typing.List['QStandardItem']: ...
    def takeRow(self, row: int) -> typing.List['QStandardItem']: ...
    def takeChild(self, row: int, column: int = ...) -> 'QStandardItem': ...
    def removeColumns(self, column: int, count: int) -> None: ...
    def removeRows(self, row: int, count: int) -> None: ...
    def removeColumn(self, column: int) -> None: ...
    def removeRow(self, row: int) -> None: ...
    def insertColumns(self, column: int, count: int) -> None: ...
    def insertColumn(self, column: int, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRows(self, row: int, count: int) -> None: ...
    @typing.overload
    def insertRows(self, row: int, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, row: int, items: typing.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, arow: int, aitem: 'QStandardItem') -> None: ...
    @typing.overload
    def setChild(self, row: int, column: int, item: 'QStandardItem') -> None: ...
    @typing.overload
    def setChild(self, arow: int, aitem: 'QStandardItem') -> None: ...
    def child(self, row: int, column: int = ...) -> 'QStandardItem': ...
    def hasChildren(self) -> bool: ...
    def setColumnCount(self, columns: int) -> None: ...
    def columnCount(self) -> int: ...
    def setRowCount(self, rows: int) -> None: ...
    def rowCount(self) -> int: ...
    def model(self) -> QStandardItemModel: ...
    def index(self) -> QtCore.QModelIndex: ...
    def column(self) -> int: ...
    def row(self) -> int: ...
    def parent(self) -> 'QStandardItem': ...
    def setDropEnabled(self, dropEnabled: bool) -> None: ...
    def isDropEnabled(self) -> bool: ...
    def setDragEnabled(self, dragEnabled: bool) -> None: ...
    def isDragEnabled(self) -> bool: ...
    def setTristate(self, tristate: bool) -> None: ...
    def isTristate(self) -> bool: ...
    def setCheckable(self, checkable: bool) -> None: ...
    def isCheckable(self) -> bool: ...
    def setSelectable(self, selectable: bool) -> None: ...
    def isSelectable(self) -> bool: ...
    def setEditable(self, editable: bool) -> None: ...
    def isEditable(self) -> bool: ...
    def setEnabled(self, enabled: bool) -> None: ...
    def isEnabled(self) -> bool: ...
    def setFlags(self, flags: typing.Union[QtCore.Qt.ItemFlags, QtCore.Qt.ItemFlag]) -> None: ...
    def flags(self) -> QtCore.Qt.ItemFlags: ...
    def accessibleDescription(self) -> str: ...
    def accessibleText(self) -> str: ...
    def checkState(self) -> QtCore.Qt.CheckState: ...
    def foreground(self) -> QBrush: ...
    def background(self) -> QBrush: ...
    def textAlignment(self) -> QtCore.Qt.Alignment: ...
    def font(self) -> QFont: ...
    def sizeHint(self) -> QtCore.QSize: ...
    def whatsThis(self) -> str: ...
    def statusTip(self) -> str: ...
    def toolTip(self) -> str: ...
    def icon(self) -> QIcon: ...
    def text(self) -> str: ...
    def setData(self, value: typing.Any, role: int = ...) -> None: ...
    def data(self, role: int = ...) -> typing.Any: ...


class QStaticText(PyQt5.sipsimplewrapper):

    class PerformanceHint(int):
        ModerateCaching = ... # type: QStaticText.PerformanceHint
        AggressiveCaching = ... # type: QStaticText.PerformanceHint

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: str) -> None: ...
    @typing.overload
    def __init__(self, other: 'QStaticText') -> None: ...

    def swap(self, other: 'QStaticText') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def performanceHint(self) -> 'QStaticText.PerformanceHint': ...
    def setPerformanceHint(self, performanceHint: 'QStaticText.PerformanceHint') -> None: ...
    def prepare(self, matrix: 'QTransform' = ..., font: QFont = ...) -> None: ...
    def size(self) -> QtCore.QSizeF: ...
    def textOption(self) -> 'QTextOption': ...
    def setTextOption(self, textOption: 'QTextOption') -> None: ...
    def textWidth(self) -> float: ...
    def setTextWidth(self, textWidth: float) -> None: ...
    def textFormat(self) -> QtCore.Qt.TextFormat: ...
    def setTextFormat(self, textFormat: QtCore.Qt.TextFormat) -> None: ...
    def text(self) -> str: ...
    def setText(self, text: str) -> None: ...


class QStyleHints(QtCore.QObject):

    def touchDoubleTapDistance(self) -> int: ...
    def mouseDoubleClickDistance(self) -> int: ...
    showShortcutsInContextMenusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setShowShortcutsInContextMenus(self, showShortcutsInContextMenus: bool) -> None: ...
    mouseQuickSelectionThresholdChanged: typing.ClassVar[QtCore.pyqtSignal]
    def mouseQuickSelectionThreshold(self) -> int: ...
    def showShortcutsInContextMenus(self) -> bool: ...
    wheelScrollLinesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def wheelScrollLines(self) -> int: ...
    useHoverEffectsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setUseHoverEffects(self, useHoverEffects: bool) -> None: ...
    def useHoverEffects(self) -> bool: ...
    def showIsMaximized(self) -> bool: ...
    tabFocusBehaviorChanged: typing.ClassVar[QtCore.pyqtSignal]
    mousePressAndHoldIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    startDragTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    startDragDistanceChanged: typing.ClassVar[QtCore.pyqtSignal]
    mouseDoubleClickIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    keyboardInputIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorFlashTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def singleClickActivation(self) -> bool: ...
    def tabFocusBehavior(self) -> QtCore.Qt.TabFocusBehavior: ...
    def mousePressAndHoldInterval(self) -> int: ...
    def setFocusOnTouchRelease(self) -> bool: ...
    def passwordMaskCharacter(self) -> str: ...
    def useRtlExtensions(self) -> bool: ...
    def fontSmoothingGamma(self) -> float: ...
    def passwordMaskDelay(self) -> int: ...
    def showIsFullScreen(self) -> bool: ...
    def cursorFlashTime(self) -> int: ...
    def keyboardAutoRepeatRate(self) -> int: ...
    def keyboardInputInterval(self) -> int: ...
    def startDragVelocity(self) -> int: ...
    def startDragTime(self) -> int: ...
    def startDragDistance(self) -> int: ...
    def mouseDoubleClickInterval(self) -> int: ...


class QSurfaceFormat(PyQt5.sipsimplewrapper):

    class ColorSpace(int):
        DefaultColorSpace = ... # type: QSurfaceFormat.ColorSpace
        sRGBColorSpace = ... # type: QSurfaceFormat.ColorSpace

    class OpenGLContextProfile(int):
        NoProfile = ... # type: QSurfaceFormat.OpenGLContextProfile
        CoreProfile = ... # type: QSurfaceFormat.OpenGLContextProfile
        CompatibilityProfile = ... # type: QSurfaceFormat.OpenGLContextProfile

    class RenderableType(int):
        DefaultRenderableType = ... # type: QSurfaceFormat.RenderableType
        OpenGL = ... # type: QSurfaceFormat.RenderableType
        OpenGLES = ... # type: QSurfaceFormat.RenderableType
        OpenVG = ... # type: QSurfaceFormat.RenderableType

    class SwapBehavior(int):
        DefaultSwapBehavior = ... # type: QSurfaceFormat.SwapBehavior
        SingleBuffer = ... # type: QSurfaceFormat.SwapBehavior
        DoubleBuffer = ... # type: QSurfaceFormat.SwapBehavior
        TripleBuffer = ... # type: QSurfaceFormat.SwapBehavior

    class FormatOption(int):
        StereoBuffers = ... # type: QSurfaceFormat.FormatOption
        DebugContext = ... # type: QSurfaceFormat.FormatOption
        DeprecatedFunctions = ... # type: QSurfaceFormat.FormatOption
        ResetNotification = ... # type: QSurfaceFormat.FormatOption

    class FormatOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __xor__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __ior__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __or__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __iand__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __and__(self, f: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> 'QSurfaceFormat.FormatOptions': ...
        def __invert__(self) -> 'QSurfaceFormat.FormatOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, options: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSurfaceFormat') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def setColorSpace(self, colorSpace: 'QSurfaceFormat.ColorSpace') -> None: ...
    def colorSpace(self) -> 'QSurfaceFormat.ColorSpace': ...
    @staticmethod
    def defaultFormat() -> 'QSurfaceFormat': ...
    @staticmethod
    def setDefaultFormat(format: 'QSurfaceFormat') -> None: ...
    def setSwapInterval(self, interval: int) -> None: ...
    def swapInterval(self) -> int: ...
    def options(self) -> 'QSurfaceFormat.FormatOptions': ...
    def setOptions(self, options: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> None: ...
    def setVersion(self, major: int, minor: int) -> None: ...
    def version(self) -> typing.Tuple[int, int]: ...
    def stereo(self) -> bool: ...
    @typing.overload
    def testOption(self, opt: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> bool: ...
    @typing.overload
    def testOption(self, option: 'QSurfaceFormat.FormatOption') -> bool: ...
    @typing.overload
    def setOption(self, opt: typing.Union['QSurfaceFormat.FormatOptions', 'QSurfaceFormat.FormatOption']) -> None: ...
    @typing.overload
    def setOption(self, option: 'QSurfaceFormat.FormatOption', on: bool = ...) -> None: ...
    def setStereo(self, enable: bool) -> None: ...
    def minorVersion(self) -> int: ...
    def setMinorVersion(self, minorVersion: int) -> None: ...
    def majorVersion(self) -> int: ...
    def setMajorVersion(self, majorVersion: int) -> None: ...
    def renderableType(self) -> 'QSurfaceFormat.RenderableType': ...
    def setRenderableType(self, type: 'QSurfaceFormat.RenderableType') -> None: ...
    def profile(self) -> 'QSurfaceFormat.OpenGLContextProfile': ...
    def setProfile(self, profile: 'QSurfaceFormat.OpenGLContextProfile') -> None: ...
    def hasAlpha(self) -> bool: ...
    def swapBehavior(self) -> 'QSurfaceFormat.SwapBehavior': ...
    def setSwapBehavior(self, behavior: 'QSurfaceFormat.SwapBehavior') -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, numSamples: int) -> None: ...
    def alphaBufferSize(self) -> int: ...
    def setAlphaBufferSize(self, size: int) -> None: ...
    def blueBufferSize(self) -> int: ...
    def setBlueBufferSize(self, size: int) -> None: ...
    def greenBufferSize(self) -> int: ...
    def setGreenBufferSize(self, size: int) -> None: ...
    def redBufferSize(self) -> int: ...
    def setRedBufferSize(self, size: int) -> None: ...
    def stencilBufferSize(self) -> int: ...
    def setStencilBufferSize(self, size: int) -> None: ...
    def depthBufferSize(self) -> int: ...
    def setDepthBufferSize(self, size: int) -> None: ...


class QSyntaxHighlighter(QtCore.QObject):

    @typing.overload
    def __init__(self, parent: 'QTextDocument') -> None: ...
    @typing.overload
    def __init__(self, parent: QtCore.QObject) -> None: ...

    def currentBlock(self) -> 'QTextBlock': ...
    def currentBlockUserData(self) -> 'QTextBlockUserData': ...
    def setCurrentBlockUserData(self, data: 'QTextBlockUserData') -> None: ...
    def setCurrentBlockState(self, newState: int) -> None: ...
    def currentBlockState(self) -> int: ...
    def previousBlockState(self) -> int: ...
    def format(self, pos: int) -> 'QTextCharFormat': ...
    @typing.overload
    def setFormat(self, start: int, count: int, format: 'QTextCharFormat') -> None: ...
    @typing.overload
    def setFormat(self, start: int, count: int, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def setFormat(self, start: int, count: int, font: QFont) -> None: ...
    def highlightBlock(self, text: str) -> None: ...
    def rehighlightBlock(self, block: 'QTextBlock') -> None: ...
    def rehighlight(self) -> None: ...
    def document(self) -> 'QTextDocument': ...
    def setDocument(self, doc: 'QTextDocument') -> None: ...


class QTextCursor(PyQt5.sipsimplewrapper):

    class SelectionType(int):
        WordUnderCursor = ... # type: QTextCursor.SelectionType
        LineUnderCursor = ... # type: QTextCursor.SelectionType
        BlockUnderCursor = ... # type: QTextCursor.SelectionType
        Document = ... # type: QTextCursor.SelectionType

    class MoveOperation(int):
        NoMove = ... # type: QTextCursor.MoveOperation
        Start = ... # type: QTextCursor.MoveOperation
        Up = ... # type: QTextCursor.MoveOperation
        StartOfLine = ... # type: QTextCursor.MoveOperation
        StartOfBlock = ... # type: QTextCursor.MoveOperation
        StartOfWord = ... # type: QTextCursor.MoveOperation
        PreviousBlock = ... # type: QTextCursor.MoveOperation
        PreviousCharacter = ... # type: QTextCursor.MoveOperation
        PreviousWord = ... # type: QTextCursor.MoveOperation
        Left = ... # type: QTextCursor.MoveOperation
        WordLeft = ... # type: QTextCursor.MoveOperation
        End = ... # type: QTextCursor.MoveOperation
        Down = ... # type: QTextCursor.MoveOperation
        EndOfLine = ... # type: QTextCursor.MoveOperation
        EndOfWord = ... # type: QTextCursor.MoveOperation
        EndOfBlock = ... # type: QTextCursor.MoveOperation
        NextBlock = ... # type: QTextCursor.MoveOperation
        NextCharacter = ... # type: QTextCursor.MoveOperation
        NextWord = ... # type: QTextCursor.MoveOperation
        Right = ... # type: QTextCursor.MoveOperation
        WordRight = ... # type: QTextCursor.MoveOperation
        NextCell = ... # type: QTextCursor.MoveOperation
        PreviousCell = ... # type: QTextCursor.MoveOperation
        NextRow = ... # type: QTextCursor.MoveOperation
        PreviousRow = ... # type: QTextCursor.MoveOperation

    class MoveMode(int):
        MoveAnchor = ... # type: QTextCursor.MoveMode
        KeepAnchor = ... # type: QTextCursor.MoveMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, document: 'QTextDocument') -> None: ...
    @typing.overload
    def __init__(self, frame: 'QTextFrame') -> None: ...
    @typing.overload
    def __init__(self, block: 'QTextBlock') -> None: ...
    @typing.overload
    def __init__(self, cursor: 'QTextCursor') -> None: ...

    def swap(self, other: 'QTextCursor') -> None: ...
    def keepPositionOnInsert(self) -> bool: ...
    def setKeepPositionOnInsert(self, b: bool) -> None: ...
    def verticalMovementX(self) -> int: ...
    def setVerticalMovementX(self, x: int) -> None: ...
    def positionInBlock(self) -> int: ...
    def document(self) -> 'QTextDocument': ...
    def setVisualNavigation(self, b: bool) -> None: ...
    def visualNavigation(self) -> bool: ...
    def isCopyOf(self, other: 'QTextCursor') -> bool: ...
    def __gt__(self, rhs: 'QTextCursor') -> bool: ...
    def __ge__(self, rhs: 'QTextCursor') -> bool: ...
    def __eq__(self, other: object): ...
    def __le__(self, rhs: 'QTextCursor') -> bool: ...
    def __lt__(self, rhs: 'QTextCursor') -> bool: ...
    def __ne__(self, other: object): ...
    def columnNumber(self) -> int: ...
    def blockNumber(self) -> int: ...
    def endEditBlock(self) -> None: ...
    def joinPreviousEditBlock(self) -> None: ...
    def beginEditBlock(self) -> None: ...
    @typing.overload
    def insertImage(self, format: 'QTextImageFormat') -> None: ...
    @typing.overload
    def insertImage(self, format: 'QTextImageFormat', alignment: 'QTextFrameFormat.Position') -> None: ...
    @typing.overload
    def insertImage(self, name: str) -> None: ...
    @typing.overload
    def insertImage(self, image: QImage, name: str = ...) -> None: ...
    def insertHtml(self, html: str) -> None: ...
    def insertFragment(self, fragment: 'QTextDocumentFragment') -> None: ...
    def currentFrame(self) -> 'QTextFrame': ...
    def insertFrame(self, format: 'QTextFrameFormat') -> 'QTextFrame': ...
    def currentTable(self) -> 'QTextTable': ...
    @typing.overload
    def insertTable(self, rows: int, cols: int, format: 'QTextTableFormat') -> 'QTextTable': ...
    @typing.overload
    def insertTable(self, rows: int, cols: int) -> 'QTextTable': ...
    def currentList(self) -> 'QTextList': ...
    @typing.overload
    def createList(self, format: 'QTextListFormat') -> 'QTextList': ...
    @typing.overload
    def createList(self, style: 'QTextListFormat.Style') -> 'QTextList': ...
    @typing.overload
    def insertList(self, format: 'QTextListFormat') -> 'QTextList': ...
    @typing.overload
    def insertList(self, style: 'QTextListFormat.Style') -> 'QTextList': ...
    @typing.overload
    def insertBlock(self) -> None: ...
    @typing.overload
    def insertBlock(self, format: 'QTextBlockFormat') -> None: ...
    @typing.overload
    def insertBlock(self, format: 'QTextBlockFormat', charFormat: 'QTextCharFormat') -> None: ...
    def atEnd(self) -> bool: ...
    def atStart(self) -> bool: ...
    def atBlockEnd(self) -> bool: ...
    def atBlockStart(self) -> bool: ...
    def mergeBlockCharFormat(self, modifier: 'QTextCharFormat') -> None: ...
    def setBlockCharFormat(self, format: 'QTextCharFormat') -> None: ...
    def blockCharFormat(self) -> 'QTextCharFormat': ...
    def mergeBlockFormat(self, modifier: 'QTextBlockFormat') -> None: ...
    def setBlockFormat(self, format: 'QTextBlockFormat') -> None: ...
    def blockFormat(self) -> 'QTextBlockFormat': ...
    def mergeCharFormat(self, modifier: 'QTextCharFormat') -> None: ...
    def setCharFormat(self, format: 'QTextCharFormat') -> None: ...
    def charFormat(self) -> 'QTextCharFormat': ...
    def block(self) -> 'QTextBlock': ...
    def selectedTableCells(self) -> typing.Tuple[int, int, int, int]: ...
    def selection(self) -> 'QTextDocumentFragment': ...
    def selectedText(self) -> str: ...
    def selectionEnd(self) -> int: ...
    def selectionStart(self) -> int: ...
    def clearSelection(self) -> None: ...
    def removeSelectedText(self) -> None: ...
    def hasComplexSelection(self) -> bool: ...
    def hasSelection(self) -> bool: ...
    def select(self, selection: 'QTextCursor.SelectionType') -> None: ...
    def deletePreviousChar(self) -> None: ...
    def deleteChar(self) -> None: ...
    def movePosition(self, op: 'QTextCursor.MoveOperation', mode: 'QTextCursor.MoveMode' = ..., n: int = ...) -> bool: ...
    @typing.overload
    def insertText(self, text: str) -> None: ...
    @typing.overload
    def insertText(self, text: str, format: 'QTextCharFormat') -> None: ...
    def anchor(self) -> int: ...
    def position(self) -> int: ...
    def setPosition(self, pos: int, mode: 'QTextCursor.MoveMode' = ...) -> None: ...
    def isNull(self) -> bool: ...


class QTextDocument(QtCore.QObject):

    class MarkdownFeature(int):
        MarkdownNoHTML = ... # type: QTextDocument.MarkdownFeature
        MarkdownDialectCommonMark = ... # type: QTextDocument.MarkdownFeature
        MarkdownDialectGitHub = ... # type: QTextDocument.MarkdownFeature

    class Stacks(int):
        UndoStack = ... # type: QTextDocument.Stacks
        RedoStack = ... # type: QTextDocument.Stacks
        UndoAndRedoStacks = ... # type: QTextDocument.Stacks

    class ResourceType(int):
        UnknownResource = ... # type: QTextDocument.ResourceType
        HtmlResource = ... # type: QTextDocument.ResourceType
        ImageResource = ... # type: QTextDocument.ResourceType
        StyleSheetResource = ... # type: QTextDocument.ResourceType
        MarkdownResource = ... # type: QTextDocument.ResourceType
        UserResource = ... # type: QTextDocument.ResourceType

    class FindFlag(int):
        FindBackward = ... # type: QTextDocument.FindFlag
        FindCaseSensitively = ... # type: QTextDocument.FindFlag
        FindWholeWords = ... # type: QTextDocument.FindFlag

    class MetaInformation(int):
        DocumentTitle = ... # type: QTextDocument.MetaInformation
        DocumentUrl = ... # type: QTextDocument.MetaInformation

    class FindFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __xor__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __ior__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __or__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __iand__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __and__(self, f: typing.Union['QTextDocument.FindFlags', 'QTextDocument.FindFlag']) -> 'QTextDocument.FindFlags': ...
        def __invert__(self) -> 'QTextDocument.FindFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MarkdownFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __xor__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __ior__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __or__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __iand__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __and__(self, f: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature']) -> 'QTextDocument.MarkdownFeatures': ...
        def __invert__(self) -> 'QTextDocument.MarkdownFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, text: str, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMarkdown(self, markdown: str, features: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature'] = ...) -> None: ...
    def toMarkdown(self, features: typing.Union['QTextDocument.MarkdownFeatures', 'QTextDocument.MarkdownFeature'] = ...) -> str: ...
    def toRawText(self) -> str: ...
    baseUrlChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setBaseUrl(self, url: QtCore.QUrl) -> None: ...
    def baseUrl(self) -> QtCore.QUrl: ...
    def setDefaultCursorMoveStyle(self, style: QtCore.Qt.CursorMoveStyle) -> None: ...
    def defaultCursorMoveStyle(self) -> QtCore.Qt.CursorMoveStyle: ...
    def clearUndoRedoStacks(self, stacks: 'QTextDocument.Stacks' = ...) -> None: ...
    def availableRedoSteps(self) -> int: ...
    def availableUndoSteps(self) -> int: ...
    def characterCount(self) -> int: ...
    def lineCount(self) -> int: ...
    def setDocumentMargin(self, margin: float) -> None: ...
    def documentMargin(self) -> float: ...
    def characterAt(self, pos: int) -> str: ...
    documentLayoutChanged: typing.ClassVar[QtCore.pyqtSignal]
    undoCommandAdded: typing.ClassVar[QtCore.pyqtSignal]
    def setIndentWidth(self, width: float) -> None: ...
    def indentWidth(self) -> float: ...
    def lastBlock(self) -> 'QTextBlock': ...
    def firstBlock(self) -> 'QTextBlock': ...
    def findBlockByLineNumber(self, blockNumber: int) -> 'QTextBlock': ...
    def findBlockByNumber(self, blockNumber: int) -> 'QTextBlock': ...
    def revision(self) -> int: ...
    def setDefaultTextOption(self, option: 'QTextOption') -> None: ...
    def defaultTextOption(self) -> 'QTextOption': ...
    def setMaximumBlockCount(self, maximum: int) -> None: ...
    def maximumBlockCount(self) -> int: ...
    def defaultStyleSheet(self) -> str: ...
    def setDefaultStyleSheet(self, sheet: str) -> None: ...
    def blockCount(self) -> int: ...
    def size(self) -> QtCore.QSizeF: ...
    def adjustSize(self) -> None: ...
    def idealWidth(self) -> float: ...
    def textWidth(self) -> float: ...
    def setTextWidth(self, width: float) -> None: ...
    def drawContents(self, p: QPainter, rect: QtCore.QRectF = ...) -> None: ...
    def loadResource(self, type: int, name: QtCore.QUrl) -> typing.Any: ...
    def createObject(self, f: 'QTextFormat') -> 'QTextObject': ...
    def setModified(self, on: bool = ...) -> None: ...
    @typing.overload
    def redo(self) -> None: ...
    @typing.overload
    def redo(self, cursor: QTextCursor) -> None: ...
    @typing.overload
    def undo(self) -> None: ...
    @typing.overload
    def undo(self, cursor: QTextCursor) -> None: ...
    undoAvailable: typing.ClassVar[QtCore.pyqtSignal]
    redoAvailable: typing.ClassVar[QtCore.pyqtSignal]
    modificationChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorPositionChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsChange: typing.ClassVar[QtCore.pyqtSignal]
    blockCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    def useDesignMetrics(self) -> bool: ...
    def setUseDesignMetrics(self, b: bool) -> None: ...
    def markContentsDirty(self, from_: int, length: int) -> None: ...
    def allFormats(self) -> typing.List['QTextFormat']: ...
    def addResource(self, type: int, name: QtCore.QUrl, resource: typing.Any) -> None: ...
    def resource(self, type: int, name: QtCore.QUrl) -> typing.Any: ...
    def print(self, printer: QPagedPaintDevice) -> None: ...
    def print_(self, printer: QPagedPaintDevice) -> None: ...
    def isModified(self) -> bool: ...
    def pageCount(self) -> int: ...
    def defaultFont(self) -> QFont: ...
    def setDefaultFont(self, font: QFont) -> None: ...
    def pageSize(self) -> QtCore.QSizeF: ...
    def setPageSize(self, size: QtCore.QSizeF) -> None: ...
    def end(self) -> 'QTextBlock': ...
    def begin(self) -> 'QTextBlock': ...
    def findBlock(self, pos: int) -> 'QTextBlock': ...
    def objectForFormat(self, a0: 'QTextFormat') -> 'QTextObject': ...
    def object(self, objectIndex: int) -> 'QTextObject': ...
    def rootFrame(self) -> 'QTextFrame': ...
    @typing.overload
    def find(self, subString: str, position: int = ..., options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, expr: QtCore.QRegExp, position: int = ..., options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, expr: QtCore.QRegularExpression, position: int = ..., options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, subString: str, cursor: QTextCursor, options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, expr: QtCore.QRegExp, cursor: QTextCursor, options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, expr: QtCore.QRegularExpression, cursor: QTextCursor, options: 'QTextDocument.FindFlags' = ...) -> QTextCursor: ...
    def setPlainText(self, text: str) -> None: ...
    def toPlainText(self) -> str: ...
    def setHtml(self, html: str) -> None: ...
    def toHtml(self, encoding: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> str: ...
    def metaInformation(self, info: 'QTextDocument.MetaInformation') -> str: ...
    def setMetaInformation(self, info: 'QTextDocument.MetaInformation', a1: str) -> None: ...
    def documentLayout(self) -> QAbstractTextDocumentLayout: ...
    def setDocumentLayout(self, layout: QAbstractTextDocumentLayout) -> None: ...
    def isRedoAvailable(self) -> bool: ...
    def isUndoAvailable(self) -> bool: ...
    def isUndoRedoEnabled(self) -> bool: ...
    def setUndoRedoEnabled(self, enable: bool) -> None: ...
    def clear(self) -> None: ...
    def isEmpty(self) -> bool: ...
    def clone(self, parent: typing.Optional[QtCore.QObject] = ...) -> 'QTextDocument': ...


class QTextDocumentFragment(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, document: QTextDocument) -> None: ...
    @typing.overload
    def __init__(self, range: QTextCursor) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QTextDocumentFragment') -> None: ...

    @typing.overload
    @staticmethod
    def fromHtml(html: str) -> 'QTextDocumentFragment': ...
    @typing.overload
    @staticmethod
    def fromHtml(html: str, resourceProvider: QTextDocument) -> 'QTextDocumentFragment': ...
    @staticmethod
    def fromPlainText(plainText: str) -> 'QTextDocumentFragment': ...
    def toHtml(self, encoding: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> str: ...
    def toPlainText(self) -> str: ...
    def isEmpty(self) -> bool: ...


class QTextDocumentWriter(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: QtCore.QIODevice, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, format: typing.Union[QtCore.QByteArray, bytes, bytearray] = ...) -> None: ...

    @staticmethod
    def supportedDocumentFormats() -> typing.List[QtCore.QByteArray]: ...
    def codec(self) -> QtCore.QTextCodec: ...
    def setCodec(self, codec: QtCore.QTextCodec) -> None: ...
    @typing.overload
    def write(self, document: QTextDocument) -> bool: ...
    @typing.overload
    def write(self, fragment: QTextDocumentFragment) -> bool: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: str) -> None: ...
    def device(self) -> QtCore.QIODevice: ...
    def setDevice(self, device: QtCore.QIODevice) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray]) -> None: ...


class QTextLength(PyQt5.sipsimplewrapper):

    class Type(int):
        VariableLength = ... # type: QTextLength.Type
        FixedLength = ... # type: QTextLength.Type
        PercentageLength = ... # type: QTextLength.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, atype: 'QTextLength.Type', avalue: float) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextLength') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def rawValue(self) -> float: ...
    def value(self, maximumLength: float) -> float: ...
    def type(self) -> 'QTextLength.Type': ...


class QTextFormat(PyQt5.sipsimplewrapper):

    class Property(int):
        ObjectIndex = ... # type: QTextFormat.Property
        CssFloat = ... # type: QTextFormat.Property
        LayoutDirection = ... # type: QTextFormat.Property
        OutlinePen = ... # type: QTextFormat.Property
        BackgroundBrush = ... # type: QTextFormat.Property
        ForegroundBrush = ... # type: QTextFormat.Property
        BlockAlignment = ... # type: QTextFormat.Property
        BlockTopMargin = ... # type: QTextFormat.Property
        BlockBottomMargin = ... # type: QTextFormat.Property
        BlockLeftMargin = ... # type: QTextFormat.Property
        BlockRightMargin = ... # type: QTextFormat.Property
        TextIndent = ... # type: QTextFormat.Property
        BlockIndent = ... # type: QTextFormat.Property
        BlockNonBreakableLines = ... # type: QTextFormat.Property
        BlockTrailingHorizontalRulerWidth = ... # type: QTextFormat.Property
        FontFamily = ... # type: QTextFormat.Property
        FontPointSize = ... # type: QTextFormat.Property
        FontSizeAdjustment = ... # type: QTextFormat.Property
        FontSizeIncrement = ... # type: QTextFormat.Property
        FontWeight = ... # type: QTextFormat.Property
        FontItalic = ... # type: QTextFormat.Property
        FontUnderline = ... # type: QTextFormat.Property
        FontOverline = ... # type: QTextFormat.Property
        FontStrikeOut = ... # type: QTextFormat.Property
        FontFixedPitch = ... # type: QTextFormat.Property
        FontPixelSize = ... # type: QTextFormat.Property
        TextUnderlineColor = ... # type: QTextFormat.Property
        TextVerticalAlignment = ... # type: QTextFormat.Property
        TextOutline = ... # type: QTextFormat.Property
        IsAnchor = ... # type: QTextFormat.Property
        AnchorHref = ... # type: QTextFormat.Property
        AnchorName = ... # type: QTextFormat.Property
        ObjectType = ... # type: QTextFormat.Property
        ListStyle = ... # type: QTextFormat.Property
        ListIndent = ... # type: QTextFormat.Property
        FrameBorder = ... # type: QTextFormat.Property
        FrameMargin = ... # type: QTextFormat.Property
        FramePadding = ... # type: QTextFormat.Property
        FrameWidth = ... # type: QTextFormat.Property
        FrameHeight = ... # type: QTextFormat.Property
        TableColumns = ... # type: QTextFormat.Property
        TableColumnWidthConstraints = ... # type: QTextFormat.Property
        TableCellSpacing = ... # type: QTextFormat.Property
        TableCellPadding = ... # type: QTextFormat.Property
        TableCellRowSpan = ... # type: QTextFormat.Property
        TableCellColumnSpan = ... # type: QTextFormat.Property
        ImageName = ... # type: QTextFormat.Property
        ImageWidth = ... # type: QTextFormat.Property
        ImageHeight = ... # type: QTextFormat.Property
        TextUnderlineStyle = ... # type: QTextFormat.Property
        TableHeaderRowCount = ... # type: QTextFormat.Property
        FullWidthSelection = ... # type: QTextFormat.Property
        PageBreakPolicy = ... # type: QTextFormat.Property
        TextToolTip = ... # type: QTextFormat.Property
        FrameTopMargin = ... # type: QTextFormat.Property
        FrameBottomMargin = ... # type: QTextFormat.Property
        FrameLeftMargin = ... # type: QTextFormat.Property
        FrameRightMargin = ... # type: QTextFormat.Property
        FrameBorderBrush = ... # type: QTextFormat.Property
        FrameBorderStyle = ... # type: QTextFormat.Property
        BackgroundImageUrl = ... # type: QTextFormat.Property
        TabPositions = ... # type: QTextFormat.Property
        FirstFontProperty = ... # type: QTextFormat.Property
        FontCapitalization = ... # type: QTextFormat.Property
        FontLetterSpacing = ... # type: QTextFormat.Property
        FontWordSpacing = ... # type: QTextFormat.Property
        LastFontProperty = ... # type: QTextFormat.Property
        TableCellTopPadding = ... # type: QTextFormat.Property
        TableCellBottomPadding = ... # type: QTextFormat.Property
        TableCellLeftPadding = ... # type: QTextFormat.Property
        TableCellRightPadding = ... # type: QTextFormat.Property
        FontStyleHint = ... # type: QTextFormat.Property
        FontStyleStrategy = ... # type: QTextFormat.Property
        FontKerning = ... # type: QTextFormat.Property
        LineHeight = ... # type: QTextFormat.Property
        LineHeightType = ... # type: QTextFormat.Property
        FontHintingPreference = ... # type: QTextFormat.Property
        ListNumberPrefix = ... # type: QTextFormat.Property
        ListNumberSuffix = ... # type: QTextFormat.Property
        FontStretch = ... # type: QTextFormat.Property
        FontLetterSpacingType = ... # type: QTextFormat.Property
        HeadingLevel = ... # type: QTextFormat.Property
        ImageQuality = ... # type: QTextFormat.Property
        FontFamilies = ... # type: QTextFormat.Property
        FontStyleName = ... # type: QTextFormat.Property
        BlockQuoteLevel = ... # type: QTextFormat.Property
        BlockCodeLanguage = ... # type: QTextFormat.Property
        BlockCodeFence = ... # type: QTextFormat.Property
        BlockMarker = ... # type: QTextFormat.Property
        TableBorderCollapse = ... # type: QTextFormat.Property
        TableCellTopBorder = ... # type: QTextFormat.Property
        TableCellBottomBorder = ... # type: QTextFormat.Property
        TableCellLeftBorder = ... # type: QTextFormat.Property
        TableCellRightBorder = ... # type: QTextFormat.Property
        TableCellTopBorderStyle = ... # type: QTextFormat.Property
        TableCellBottomBorderStyle = ... # type: QTextFormat.Property
        TableCellLeftBorderStyle = ... # type: QTextFormat.Property
        TableCellRightBorderStyle = ... # type: QTextFormat.Property
        TableCellTopBorderBrush = ... # type: QTextFormat.Property
        TableCellBottomBorderBrush = ... # type: QTextFormat.Property
        TableCellLeftBorderBrush = ... # type: QTextFormat.Property
        TableCellRightBorderBrush = ... # type: QTextFormat.Property
        ImageTitle = ... # type: QTextFormat.Property
        ImageAltText = ... # type: QTextFormat.Property
        UserProperty = ... # type: QTextFormat.Property

    class PageBreakFlag(int):
        PageBreak_Auto = ... # type: QTextFormat.PageBreakFlag
        PageBreak_AlwaysBefore = ... # type: QTextFormat.PageBreakFlag
        PageBreak_AlwaysAfter = ... # type: QTextFormat.PageBreakFlag

    class ObjectTypes(int):
        NoObject = ... # type: QTextFormat.ObjectTypes
        ImageObject = ... # type: QTextFormat.ObjectTypes
        TableObject = ... # type: QTextFormat.ObjectTypes
        TableCellObject = ... # type: QTextFormat.ObjectTypes
        UserObject = ... # type: QTextFormat.ObjectTypes

    class FormatType(int):
        InvalidFormat = ... # type: QTextFormat.FormatType
        BlockFormat = ... # type: QTextFormat.FormatType
        CharFormat = ... # type: QTextFormat.FormatType
        ListFormat = ... # type: QTextFormat.FormatType
        TableFormat = ... # type: QTextFormat.FormatType
        FrameFormat = ... # type: QTextFormat.FormatType
        UserFormat = ... # type: QTextFormat.FormatType

    class PageBreakFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __xor__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __ior__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __or__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __iand__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __and__(self, f: typing.Union['QTextFormat.PageBreakFlags', 'QTextFormat.PageBreakFlag']) -> 'QTextFormat.PageBreakFlags': ...
        def __invert__(self) -> 'QTextFormat.PageBreakFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: int) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QTextFormat') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def isEmpty(self) -> bool: ...
    def swap(self, other: 'QTextFormat') -> None: ...
    def toTableCellFormat(self) -> 'QTextTableCellFormat': ...
    def isTableCellFormat(self) -> bool: ...
    def propertyCount(self) -> int: ...
    def setObjectType(self, atype: int) -> None: ...
    def clearForeground(self) -> None: ...
    def foreground(self) -> QBrush: ...
    def setForeground(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def clearBackground(self) -> None: ...
    def background(self) -> QBrush: ...
    def setBackground(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def layoutDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setLayoutDirection(self, direction: QtCore.Qt.LayoutDirection) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def toImageFormat(self) -> 'QTextImageFormat': ...
    def toFrameFormat(self) -> 'QTextFrameFormat': ...
    def toTableFormat(self) -> 'QTextTableFormat': ...
    def toListFormat(self) -> 'QTextListFormat': ...
    def toCharFormat(self) -> 'QTextCharFormat': ...
    def toBlockFormat(self) -> 'QTextBlockFormat': ...
    def isTableFormat(self) -> bool: ...
    def isImageFormat(self) -> bool: ...
    def isFrameFormat(self) -> bool: ...
    def isListFormat(self) -> bool: ...
    def isBlockFormat(self) -> bool: ...
    def isCharFormat(self) -> bool: ...
    def objectType(self) -> int: ...
    def properties(self) -> typing.Dict[int, typing.Any]: ...
    def lengthVectorProperty(self, propertyId: int) -> typing.List[QTextLength]: ...
    def lengthProperty(self, propertyId: int) -> QTextLength: ...
    def brushProperty(self, propertyId: int) -> QBrush: ...
    def penProperty(self, propertyId: int) -> QPen: ...
    def colorProperty(self, propertyId: int) -> QColor: ...
    def stringProperty(self, propertyId: int) -> str: ...
    def doubleProperty(self, propertyId: int) -> float: ...
    def intProperty(self, propertyId: int) -> int: ...
    def boolProperty(self, propertyId: int) -> bool: ...
    def hasProperty(self, propertyId: int) -> bool: ...
    def clearProperty(self, propertyId: int) -> None: ...
    @typing.overload
    def setProperty(self, propertyId: int, value: typing.Any) -> None: ...
    @typing.overload
    def setProperty(self, propertyId: int, lengths: typing.Iterable[QTextLength]) -> None: ...
    def property(self, propertyId: int) -> typing.Any: ...
    def setObjectIndex(self, object: int) -> None: ...
    def objectIndex(self) -> int: ...
    def type(self) -> int: ...
    def isValid(self) -> bool: ...
    def merge(self, other: 'QTextFormat') -> None: ...


class QTextCharFormat(QTextFormat):

    class FontPropertiesInheritanceBehavior(int):
        FontPropertiesSpecifiedOnly = ... # type: QTextCharFormat.FontPropertiesInheritanceBehavior
        FontPropertiesAll = ... # type: QTextCharFormat.FontPropertiesInheritanceBehavior

    class UnderlineStyle(int):
        NoUnderline = ... # type: QTextCharFormat.UnderlineStyle
        SingleUnderline = ... # type: QTextCharFormat.UnderlineStyle
        DashUnderline = ... # type: QTextCharFormat.UnderlineStyle
        DotLine = ... # type: QTextCharFormat.UnderlineStyle
        DashDotLine = ... # type: QTextCharFormat.UnderlineStyle
        DashDotDotLine = ... # type: QTextCharFormat.UnderlineStyle
        WaveUnderline = ... # type: QTextCharFormat.UnderlineStyle
        SpellCheckUnderline = ... # type: QTextCharFormat.UnderlineStyle

    class VerticalAlignment(int):
        AlignNormal = ... # type: QTextCharFormat.VerticalAlignment
        AlignSuperScript = ... # type: QTextCharFormat.VerticalAlignment
        AlignSubScript = ... # type: QTextCharFormat.VerticalAlignment
        AlignMiddle = ... # type: QTextCharFormat.VerticalAlignment
        AlignTop = ... # type: QTextCharFormat.VerticalAlignment
        AlignBottom = ... # type: QTextCharFormat.VerticalAlignment
        AlignBaseline = ... # type: QTextCharFormat.VerticalAlignment

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextCharFormat') -> None: ...

    def fontStyleName(self) -> typing.Any: ...
    def setFontStyleName(self, styleName: str) -> None: ...
    def fontFamilies(self) -> typing.Any: ...
    def setFontFamilies(self, families: typing.Iterable[str]) -> None: ...
    def fontLetterSpacingType(self) -> QFont.SpacingType: ...
    def setFontLetterSpacingType(self, letterSpacingType: QFont.SpacingType) -> None: ...
    def setFontStretch(self, factor: int) -> None: ...
    def fontStretch(self) -> int: ...
    def fontHintingPreference(self) -> QFont.HintingPreference: ...
    def setFontHintingPreference(self, hintingPreference: QFont.HintingPreference) -> None: ...
    def fontKerning(self) -> bool: ...
    def setFontKerning(self, enable: bool) -> None: ...
    def fontStyleStrategy(self) -> QFont.StyleStrategy: ...
    def fontStyleHint(self) -> QFont.StyleHint: ...
    def setFontStyleStrategy(self, strategy: QFont.StyleStrategy) -> None: ...
    def setFontStyleHint(self, hint: QFont.StyleHint, strategy: QFont.StyleStrategy = ...) -> None: ...
    def fontWordSpacing(self) -> float: ...
    def setFontWordSpacing(self, spacing: float) -> None: ...
    def fontLetterSpacing(self) -> float: ...
    def setFontLetterSpacing(self, spacing: float) -> None: ...
    def fontCapitalization(self) -> QFont.Capitalization: ...
    def setFontCapitalization(self, capitalization: QFont.Capitalization) -> None: ...
    def anchorNames(self) -> typing.List[str]: ...
    def setAnchorNames(self, names: typing.Iterable[str]) -> None: ...
    def toolTip(self) -> str: ...
    def setToolTip(self, tip: str) -> None: ...
    def underlineStyle(self) -> 'QTextCharFormat.UnderlineStyle': ...
    def setUnderlineStyle(self, style: 'QTextCharFormat.UnderlineStyle') -> None: ...
    def textOutline(self) -> QPen: ...
    def setTextOutline(self, pen: typing.Union[QPen, QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def setTableCellColumnSpan(self, atableCellColumnSpan: int) -> None: ...
    def setTableCellRowSpan(self, atableCellRowSpan: int) -> None: ...
    def tableCellColumnSpan(self) -> int: ...
    def tableCellRowSpan(self) -> int: ...
    def anchorHref(self) -> str: ...
    def setAnchorHref(self, value: str) -> None: ...
    def isAnchor(self) -> bool: ...
    def setAnchor(self, anchor: bool) -> None: ...
    def verticalAlignment(self) -> 'QTextCharFormat.VerticalAlignment': ...
    def setVerticalAlignment(self, alignment: 'QTextCharFormat.VerticalAlignment') -> None: ...
    def fontFixedPitch(self) -> bool: ...
    def setFontFixedPitch(self, fixedPitch: bool) -> None: ...
    def underlineColor(self) -> QColor: ...
    def setUnderlineColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def fontStrikeOut(self) -> bool: ...
    def setFontStrikeOut(self, strikeOut: bool) -> None: ...
    def fontOverline(self) -> bool: ...
    def setFontOverline(self, overline: bool) -> None: ...
    def fontUnderline(self) -> bool: ...
    def setFontUnderline(self, underline: bool) -> None: ...
    def fontItalic(self) -> bool: ...
    def setFontItalic(self, italic: bool) -> None: ...
    def fontWeight(self) -> int: ...
    def setFontWeight(self, weight: int) -> None: ...
    def fontPointSize(self) -> float: ...
    def setFontPointSize(self, size: float) -> None: ...
    def fontFamily(self) -> str: ...
    def setFontFamily(self, family: str) -> None: ...
    def font(self) -> QFont: ...
    @typing.overload
    def setFont(self, font: QFont) -> None: ...
    @typing.overload
    def setFont(self, font: QFont, behavior: 'QTextCharFormat.FontPropertiesInheritanceBehavior') -> None: ...
    def isValid(self) -> bool: ...


class QTextBlockFormat(QTextFormat):

    class MarkerType(int):
        NoMarker = ... # type: QTextBlockFormat.MarkerType
        Unchecked = ... # type: QTextBlockFormat.MarkerType
        Checked = ... # type: QTextBlockFormat.MarkerType

    class LineHeightTypes(int):
        SingleHeight = ... # type: QTextBlockFormat.LineHeightTypes
        ProportionalHeight = ... # type: QTextBlockFormat.LineHeightTypes
        FixedHeight = ... # type: QTextBlockFormat.LineHeightTypes
        MinimumHeight = ... # type: QTextBlockFormat.LineHeightTypes
        LineDistanceHeight = ... # type: QTextBlockFormat.LineHeightTypes

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextBlockFormat') -> None: ...

    def marker(self) -> 'QTextBlockFormat.MarkerType': ...
    def setMarker(self, marker: 'QTextBlockFormat.MarkerType') -> None: ...
    def headingLevel(self) -> int: ...
    def setHeadingLevel(self, alevel: int) -> None: ...
    def lineHeightType(self) -> int: ...
    @typing.overload
    def lineHeight(self) -> float: ...
    @typing.overload
    def lineHeight(self, scriptLineHeight: float, scaling: float = ...) -> float: ...
    def setLineHeight(self, height: float, heightType: int) -> None: ...
    def tabPositions(self) -> typing.List['QTextOption.Tab']: ...
    def setTabPositions(self, tabs: typing.Iterable['QTextOption.Tab']) -> None: ...
    def pageBreakPolicy(self) -> QTextFormat.PageBreakFlags: ...
    def setPageBreakPolicy(self, flags: typing.Union[QTextFormat.PageBreakFlags, QTextFormat.PageBreakFlag]) -> None: ...
    def setIndent(self, aindent: int) -> None: ...
    def setAlignment(self, aalignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def nonBreakableLines(self) -> bool: ...
    def setNonBreakableLines(self, b: bool) -> None: ...
    def indent(self) -> int: ...
    def textIndent(self) -> float: ...
    def setTextIndent(self, margin: float) -> None: ...
    def rightMargin(self) -> float: ...
    def setRightMargin(self, margin: float) -> None: ...
    def leftMargin(self) -> float: ...
    def setLeftMargin(self, margin: float) -> None: ...
    def bottomMargin(self) -> float: ...
    def setBottomMargin(self, margin: float) -> None: ...
    def topMargin(self) -> float: ...
    def setTopMargin(self, margin: float) -> None: ...
    def alignment(self) -> QtCore.Qt.Alignment: ...
    def isValid(self) -> bool: ...


class QTextListFormat(QTextFormat):

    class Style(int):
        ListDisc = ... # type: QTextListFormat.Style
        ListCircle = ... # type: QTextListFormat.Style
        ListSquare = ... # type: QTextListFormat.Style
        ListDecimal = ... # type: QTextListFormat.Style
        ListLowerAlpha = ... # type: QTextListFormat.Style
        ListUpperAlpha = ... # type: QTextListFormat.Style
        ListLowerRoman = ... # type: QTextListFormat.Style
        ListUpperRoman = ... # type: QTextListFormat.Style

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextListFormat') -> None: ...

    def setNumberSuffix(self, ns: str) -> None: ...
    def setNumberPrefix(self, np: str) -> None: ...
    def numberSuffix(self) -> str: ...
    def numberPrefix(self) -> str: ...
    def setIndent(self, aindent: int) -> None: ...
    def setStyle(self, astyle: 'QTextListFormat.Style') -> None: ...
    def indent(self) -> int: ...
    def style(self) -> 'QTextListFormat.Style': ...
    def isValid(self) -> bool: ...


class QTextImageFormat(QTextCharFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextImageFormat') -> None: ...

    def setQuality(self, quality: int = ...) -> None: ...
    def setHeight(self, aheight: float) -> None: ...
    def setWidth(self, awidth: float) -> None: ...
    def setName(self, aname: str) -> None: ...
    def quality(self) -> int: ...
    def height(self) -> float: ...
    def width(self) -> float: ...
    def name(self) -> str: ...
    def isValid(self) -> bool: ...


class QTextFrameFormat(QTextFormat):

    class BorderStyle(int):
        BorderStyle_None = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Dotted = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Dashed = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Solid = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Double = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_DotDash = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_DotDotDash = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Groove = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Ridge = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Inset = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Outset = ... # type: QTextFrameFormat.BorderStyle

    class Position(int):
        InFlow = ... # type: QTextFrameFormat.Position
        FloatLeft = ... # type: QTextFrameFormat.Position
        FloatRight = ... # type: QTextFrameFormat.Position

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextFrameFormat') -> None: ...

    def setRightMargin(self, amargin: float) -> None: ...
    def setLeftMargin(self, amargin: float) -> None: ...
    def setBottomMargin(self, amargin: float) -> None: ...
    def setTopMargin(self, amargin: float) -> None: ...
    def rightMargin(self) -> float: ...
    def leftMargin(self) -> float: ...
    def bottomMargin(self) -> float: ...
    def topMargin(self) -> float: ...
    def borderStyle(self) -> 'QTextFrameFormat.BorderStyle': ...
    def setBorderStyle(self, style: 'QTextFrameFormat.BorderStyle') -> None: ...
    def borderBrush(self) -> QBrush: ...
    def setBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def pageBreakPolicy(self) -> QTextFormat.PageBreakFlags: ...
    def setPageBreakPolicy(self, flags: typing.Union[QTextFormat.PageBreakFlags, QTextFormat.PageBreakFlag]) -> None: ...
    @typing.overload
    def setHeight(self, aheight: float) -> None: ...
    @typing.overload
    def setHeight(self, aheight: QTextLength) -> None: ...
    def setPadding(self, apadding: float) -> None: ...
    def setMargin(self, amargin: float) -> None: ...
    def setBorder(self, aborder: float) -> None: ...
    def height(self) -> QTextLength: ...
    def width(self) -> QTextLength: ...
    @typing.overload
    def setWidth(self, length: QTextLength) -> None: ...
    @typing.overload
    def setWidth(self, awidth: float) -> None: ...
    def padding(self) -> float: ...
    def margin(self) -> float: ...
    def border(self) -> float: ...
    def position(self) -> 'QTextFrameFormat.Position': ...
    def setPosition(self, f: 'QTextFrameFormat.Position') -> None: ...
    def isValid(self) -> bool: ...


class QTextTableFormat(QTextFrameFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextTableFormat') -> None: ...

    def borderCollapse(self) -> bool: ...
    def setBorderCollapse(self, borderCollapse: bool) -> None: ...
    def headerRowCount(self) -> int: ...
    def setHeaderRowCount(self, count: int) -> None: ...
    def setAlignment(self, aalignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def setCellPadding(self, apadding: float) -> None: ...
    def setColumns(self, acolumns: int) -> None: ...
    def alignment(self) -> QtCore.Qt.Alignment: ...
    def cellPadding(self) -> float: ...
    def setCellSpacing(self, spacing: float) -> None: ...
    def cellSpacing(self) -> float: ...
    def clearColumnWidthConstraints(self) -> None: ...
    def columnWidthConstraints(self) -> typing.List[QTextLength]: ...
    def setColumnWidthConstraints(self, constraints: typing.Iterable[QTextLength]) -> None: ...
    def columns(self) -> int: ...
    def isValid(self) -> bool: ...


class QTextTableCellFormat(QTextCharFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextTableCellFormat') -> None: ...

    def setBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def rightBorderBrush(self) -> QBrush: ...
    def setRightBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def leftBorderBrush(self) -> QBrush: ...
    def setLeftBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def bottomBorderBrush(self) -> QBrush: ...
    def setBottomBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def topBorderBrush(self) -> QBrush: ...
    def setTopBorderBrush(self, brush: typing.Union[QBrush, QColor, QtCore.Qt.GlobalColor, QGradient]) -> None: ...
    def setBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def rightBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setRightBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def leftBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setLeftBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def bottomBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setBottomBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def topBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setTopBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def setBorder(self, width: float) -> None: ...
    def rightBorder(self) -> float: ...
    def setRightBorder(self, width: float) -> None: ...
    def leftBorder(self) -> float: ...
    def setLeftBorder(self, width: float) -> None: ...
    def bottomBorder(self) -> float: ...
    def setBottomBorder(self, width: float) -> None: ...
    def topBorder(self) -> float: ...
    def setTopBorder(self, width: float) -> None: ...
    def setPadding(self, padding: float) -> None: ...
    def rightPadding(self) -> float: ...
    def setRightPadding(self, padding: float) -> None: ...
    def leftPadding(self) -> float: ...
    def setLeftPadding(self, padding: float) -> None: ...
    def bottomPadding(self) -> float: ...
    def setBottomPadding(self, padding: float) -> None: ...
    def topPadding(self) -> float: ...
    def setTopPadding(self, padding: float) -> None: ...
    def isValid(self) -> bool: ...


class QTextInlineObject(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextInlineObject') -> None: ...

    def format(self) -> QTextFormat: ...
    def formatIndex(self) -> int: ...
    def textPosition(self) -> int: ...
    def setDescent(self, d: float) -> None: ...
    def setAscent(self, a: float) -> None: ...
    def setWidth(self, w: float) -> None: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def width(self) -> float: ...
    def rect(self) -> QtCore.QRectF: ...
    def isValid(self) -> bool: ...


class QTextLayout(PyQt5.sipsimplewrapper):

    class CursorMode(int):
        SkipCharacters = ... # type: QTextLayout.CursorMode
        SkipWords = ... # type: QTextLayout.CursorMode

    class FormatRange(PyQt5.sipsimplewrapper):

        format = ... # type: QTextCharFormat
        length = ... # type: int
        start = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextLayout.FormatRange') -> None: ...

        def __eq__(self, other: object): ...
        def __ne__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: str) -> None: ...
    @typing.overload
    def __init__(self, text: str, font: QFont, paintDevice: typing.Optional[QPaintDevice] = ...) -> None: ...
    @typing.overload
    def __init__(self, b: 'QTextBlock') -> None: ...

    def clearFormats(self) -> None: ...
    def formats(self) -> typing.List['QTextLayout.FormatRange']: ...
    def setFormats(self, overrides: typing.Iterable['QTextLayout.FormatRange']) -> None: ...
    def glyphRuns(self, from_: int = ..., length: int = ...) -> typing.List[QGlyphRun]: ...
    def rightCursorPosition(self, oldPos: int) -> int: ...
    def leftCursorPosition(self, oldPos: int) -> int: ...
    def cursorMoveStyle(self) -> QtCore.Qt.CursorMoveStyle: ...
    def setCursorMoveStyle(self, style: QtCore.Qt.CursorMoveStyle) -> None: ...
    def clearLayout(self) -> None: ...
    def maximumWidth(self) -> float: ...
    def minimumWidth(self) -> float: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def setPosition(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    def position(self) -> QtCore.QPointF: ...
    @typing.overload
    def drawCursor(self, p: QPainter, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], cursorPosition: int) -> None: ...
    @typing.overload
    def drawCursor(self, p: QPainter, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], cursorPosition: int, width: int) -> None: ...
    def draw(self, p: QPainter, pos: typing.Union[QtCore.QPointF, QtCore.QPoint], selections: typing.Iterable['QTextLayout.FormatRange'] = ..., clip: QtCore.QRectF = ...) -> None: ...
    def previousCursorPosition(self, oldPos: int, mode: 'QTextLayout.CursorMode' = ...) -> int: ...
    def nextCursorPosition(self, oldPos: int, mode: 'QTextLayout.CursorMode' = ...) -> int: ...
    def isValidCursorPosition(self, pos: int) -> bool: ...
    def lineForTextPosition(self, pos: int) -> 'QTextLine': ...
    def lineAt(self, i: int) -> 'QTextLine': ...
    def lineCount(self) -> int: ...
    def createLine(self) -> 'QTextLine': ...
    def endLayout(self) -> None: ...
    def beginLayout(self) -> None: ...
    def cacheEnabled(self) -> bool: ...
    def setCacheEnabled(self, enable: bool) -> None: ...
    def clearAdditionalFormats(self) -> None: ...
    def additionalFormats(self) -> typing.List['QTextLayout.FormatRange']: ...
    def setAdditionalFormats(self, overrides: typing.Iterable['QTextLayout.FormatRange']) -> None: ...
    def preeditAreaText(self) -> str: ...
    def preeditAreaPosition(self) -> int: ...
    def setPreeditArea(self, position: int, text: str) -> None: ...
    def textOption(self) -> 'QTextOption': ...
    def setTextOption(self, option: 'QTextOption') -> None: ...
    def text(self) -> str: ...
    def setText(self, string: str) -> None: ...
    def font(self) -> QFont: ...
    def setFont(self, f: QFont) -> None: ...


class QTextLine(PyQt5.sipsimplewrapper):

    class CursorPosition(int):
        CursorBetweenCharacters = ... # type: QTextLine.CursorPosition
        CursorOnCharacter = ... # type: QTextLine.CursorPosition

    class Edge(int):
        Leading = ... # type: QTextLine.Edge
        Trailing = ... # type: QTextLine.Edge

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextLine') -> None: ...

    def glyphRuns(self, from_: int = ..., length: int = ...) -> typing.List[QGlyphRun]: ...
    def horizontalAdvance(self) -> float: ...
    def leadingIncluded(self) -> bool: ...
    def setLeadingIncluded(self, included: bool) -> None: ...
    def leading(self) -> float: ...
    def position(self) -> QtCore.QPointF: ...
    def draw(self, painter: QPainter, position: typing.Union[QtCore.QPointF, QtCore.QPoint], selection: typing.Optional[QTextLayout.FormatRange] = ...) -> None: ...
    def lineNumber(self) -> int: ...
    def textLength(self) -> int: ...
    def textStart(self) -> int: ...
    def setPosition(self, pos: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def setNumColumns(self, columns: int) -> None: ...
    @typing.overload
    def setNumColumns(self, columns: int, alignmentWidth: float) -> None: ...
    def setLineWidth(self, width: float) -> None: ...
    def xToCursor(self, x: float, edge: 'QTextLine.CursorPosition' = ...) -> int: ...
    def cursorToX(self, cursorPos: int, edge: 'QTextLine.Edge' = ...) -> typing.Tuple[float, int]: ...
    def naturalTextRect(self) -> QtCore.QRectF: ...
    def naturalTextWidth(self) -> float: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def width(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def rect(self) -> QtCore.QRectF: ...
    def isValid(self) -> bool: ...


class QTextObject(QtCore.QObject):

    def __init__(self, doc: QTextDocument) -> None: ...

    def objectIndex(self) -> int: ...
    def document(self) -> QTextDocument: ...
    def formatIndex(self) -> int: ...
    def format(self) -> QTextFormat: ...
    def setFormat(self, format: QTextFormat) -> None: ...


class QTextBlockGroup(QTextObject):

    def __init__(self, doc: QTextDocument) -> None: ...

    def blockList(self) -> typing.List['QTextBlock']: ...
    def blockFormatChanged(self, block: 'QTextBlock') -> None: ...
    def blockRemoved(self, block: 'QTextBlock') -> None: ...
    def blockInserted(self, block: 'QTextBlock') -> None: ...


class QTextList(QTextBlockGroup):

    def __init__(self, doc: QTextDocument) -> None: ...

    def setFormat(self, aformat: QTextListFormat) -> None: ...
    def format(self) -> QTextListFormat: ...
    def add(self, block: 'QTextBlock') -> None: ...
    def remove(self, a0: 'QTextBlock') -> None: ...
    def removeItem(self, i: int) -> None: ...
    def itemText(self, a0: 'QTextBlock') -> str: ...
    def itemNumber(self, a0: 'QTextBlock') -> int: ...
    def item(self, i: int) -> 'QTextBlock': ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...


class QTextFrame(QTextObject):

    class iterator(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, o: 'QTextFrame.iterator') -> None: ...

        def __isub__(self, a0: int) -> 'QTextFrame.iterator': ...
        def __iadd__(self, a0: int) -> 'QTextFrame.iterator': ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def atEnd(self) -> bool: ...
        def currentBlock(self) -> 'QTextBlock': ...
        def currentFrame(self) -> 'QTextFrame': ...
        def parentFrame(self) -> 'QTextFrame': ...

    def __init__(self, doc: QTextDocument) -> None: ...

    def setFrameFormat(self, aformat: QTextFrameFormat) -> None: ...
    def end(self) -> 'QTextFrame.iterator': ...
    def begin(self) -> 'QTextFrame.iterator': ...
    def parentFrame(self) -> 'QTextFrame': ...
    def childFrames(self) -> typing.List['QTextFrame']: ...
    def lastPosition(self) -> int: ...
    def firstPosition(self) -> int: ...
    def lastCursorPosition(self) -> QTextCursor: ...
    def firstCursorPosition(self) -> QTextCursor: ...
    def frameFormat(self) -> QTextFrameFormat: ...


class QTextBlock(PyQt5.sip.wrapper):

    class iterator(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, o: 'QTextBlock.iterator') -> None: ...

        def __isub__(self, a0: int) -> 'QTextBlock.iterator': ...
        def __iadd__(self, a0: int) -> 'QTextBlock.iterator': ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def atEnd(self) -> bool: ...
        def fragment(self) -> 'QTextFragment': ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextBlock') -> None: ...

    def __ge__(self, o: 'QTextBlock') -> bool: ...
    def textFormats(self) -> typing.List[QTextLayout.FormatRange]: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def lineCount(self) -> int: ...
    def setLineCount(self, count: int) -> None: ...
    def firstLineNumber(self) -> int: ...
    def blockNumber(self) -> int: ...
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def setRevision(self, rev: int) -> None: ...
    def revision(self) -> int: ...
    def clearLayout(self) -> None: ...
    def setUserState(self, state: int) -> None: ...
    def userState(self) -> int: ...
    def setUserData(self, data: 'QTextBlockUserData') -> None: ...
    def userData(self) -> 'QTextBlockUserData': ...
    def previous(self) -> 'QTextBlock': ...
    def next(self) -> 'QTextBlock': ...
    def end(self) -> 'QTextBlock.iterator': ...
    def begin(self) -> 'QTextBlock.iterator': ...
    def textList(self) -> QTextList: ...
    def document(self) -> QTextDocument: ...
    def text(self) -> str: ...
    def charFormatIndex(self) -> int: ...
    def charFormat(self) -> QTextCharFormat: ...
    def blockFormatIndex(self) -> int: ...
    def blockFormat(self) -> QTextBlockFormat: ...
    def layout(self) -> QTextLayout: ...
    def contains(self, position: int) -> bool: ...
    def length(self) -> int: ...
    def position(self) -> int: ...
    def __lt__(self, o: 'QTextBlock') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QTextFragment(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextFragment') -> None: ...

    def __ge__(self, o: 'QTextFragment') -> bool: ...
    def glyphRuns(self, from_: int = ..., length: int = ...) -> typing.List[QGlyphRun]: ...
    def text(self) -> str: ...
    def charFormatIndex(self) -> int: ...
    def charFormat(self) -> QTextCharFormat: ...
    def contains(self, position: int) -> bool: ...
    def length(self) -> int: ...
    def position(self) -> int: ...
    def __lt__(self, o: 'QTextFragment') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QTextBlockUserData(PyQt5.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextBlockUserData') -> None: ...


class QTextOption(PyQt5.sipsimplewrapper):

    class TabType(int):
        LeftTab = ... # type: QTextOption.TabType
        RightTab = ... # type: QTextOption.TabType
        CenterTab = ... # type: QTextOption.TabType
        DelimiterTab = ... # type: QTextOption.TabType

    class Flag(int):
        IncludeTrailingSpaces = ... # type: QTextOption.Flag
        ShowTabsAndSpaces = ... # type: QTextOption.Flag
        ShowLineAndParagraphSeparators = ... # type: QTextOption.Flag
        AddSpaceForLineAndParagraphSeparators = ... # type: QTextOption.Flag
        SuppressColors = ... # type: QTextOption.Flag
        ShowDocumentTerminator = ... # type: QTextOption.Flag

    class WrapMode(int):
        NoWrap = ... # type: QTextOption.WrapMode
        WordWrap = ... # type: QTextOption.WrapMode
        ManualWrap = ... # type: QTextOption.WrapMode
        WrapAnywhere = ... # type: QTextOption.WrapMode
        WrapAtWordBoundaryOrAnywhere = ... # type: QTextOption.WrapMode

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __xor__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __ior__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __or__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __iand__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __and__(self, f: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> 'QTextOption.Flags': ...
        def __invert__(self) -> 'QTextOption.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class Tab(PyQt5.sipsimplewrapper):

        delimiter = ... # type: str
        position = ... # type: float
        type = ... # type: 'QTextOption.TabType'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, pos: float, tabType: 'QTextOption.TabType', delim: str = ...) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextOption.Tab') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, alignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextOption') -> None: ...

    def tabStopDistance(self) -> float: ...
    def setTabStopDistance(self, tabStopDistance: float) -> None: ...
    def tabs(self) -> typing.List['QTextOption.Tab']: ...
    def setTabs(self, tabStops: typing.Iterable['QTextOption.Tab']) -> None: ...
    def setTabStop(self, atabStop: float) -> None: ...
    def setFlags(self, flags: typing.Union['QTextOption.Flags', 'QTextOption.Flag']) -> None: ...
    def setAlignment(self, aalignment: typing.Union[QtCore.Qt.Alignment, QtCore.Qt.AlignmentFlag]) -> None: ...
    def useDesignMetrics(self) -> bool: ...
    def setUseDesignMetrics(self, b: bool) -> None: ...
    def tabArray(self) -> typing.List[float]: ...
    def setTabArray(self, tabStops: typing.Iterable[float]) -> None: ...
    def tabStop(self) -> float: ...
    def flags(self) -> 'QTextOption.Flags': ...
    def wrapMode(self) -> 'QTextOption.WrapMode': ...
    def setWrapMode(self, wrap: 'QTextOption.WrapMode') -> None: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setTextDirection(self, aDirection: QtCore.Qt.LayoutDirection) -> None: ...
    def alignment(self) -> QtCore.Qt.Alignment: ...


class QTextTableCell(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextTableCell') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def tableCellFormatIndex(self) -> int: ...
    def lastCursorPosition(self) -> QTextCursor: ...
    def firstCursorPosition(self) -> QTextCursor: ...
    def isValid(self) -> bool: ...
    def columnSpan(self) -> int: ...
    def rowSpan(self) -> int: ...
    def column(self) -> int: ...
    def row(self) -> int: ...
    def setFormat(self, format: QTextCharFormat) -> None: ...
    def format(self) -> QTextCharFormat: ...


class QTextTable(QTextFrame):

    def __init__(self, doc: QTextDocument) -> None: ...

    def appendColumns(self, count: int) -> None: ...
    def appendRows(self, count: int) -> None: ...
    def setFormat(self, aformat: QTextTableFormat) -> None: ...
    def format(self) -> QTextTableFormat: ...
    def rowEnd(self, c: QTextCursor) -> QTextCursor: ...
    def rowStart(self, c: QTextCursor) -> QTextCursor: ...
    @typing.overload
    def cellAt(self, row: int, col: int) -> QTextTableCell: ...
    @typing.overload
    def cellAt(self, position: int) -> QTextTableCell: ...
    @typing.overload
    def cellAt(self, c: QTextCursor) -> QTextTableCell: ...
    def columns(self) -> int: ...
    def rows(self) -> int: ...
    def splitCell(self, row: int, col: int, numRows: int, numCols: int) -> None: ...
    @typing.overload
    def mergeCells(self, row: int, col: int, numRows: int, numCols: int) -> None: ...
    @typing.overload
    def mergeCells(self, cursor: QTextCursor) -> None: ...
    def removeColumns(self, pos: int, num: int) -> None: ...
    def removeRows(self, pos: int, num: int) -> None: ...
    def insertColumns(self, pos: int, num: int) -> None: ...
    def insertRows(self, pos: int, num: int) -> None: ...
    def resize(self, rows: int, cols: int) -> None: ...


class QTouchDevice(PyQt5.sipsimplewrapper):

    class CapabilityFlag(int):
        Position = ... # type: QTouchDevice.CapabilityFlag
        Area = ... # type: QTouchDevice.CapabilityFlag
        Pressure = ... # type: QTouchDevice.CapabilityFlag
        Velocity = ... # type: QTouchDevice.CapabilityFlag
        RawPositions = ... # type: QTouchDevice.CapabilityFlag
        NormalizedPosition = ... # type: QTouchDevice.CapabilityFlag
        MouseEmulation = ... # type: QTouchDevice.CapabilityFlag

    class DeviceType(int):
        TouchScreen = ... # type: QTouchDevice.DeviceType
        TouchPad = ... # type: QTouchDevice.DeviceType

    class Capabilities(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __xor__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __ior__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __or__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __iand__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __and__(self, f: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> 'QTouchDevice.Capabilities': ...
        def __invert__(self) -> 'QTouchDevice.Capabilities': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTouchDevice') -> None: ...

    def setMaximumTouchPoints(self, max: int) -> None: ...
    def maximumTouchPoints(self) -> int: ...
    def setCapabilities(self, caps: typing.Union['QTouchDevice.Capabilities', 'QTouchDevice.CapabilityFlag']) -> None: ...
    def setType(self, devType: 'QTouchDevice.DeviceType') -> None: ...
    def setName(self, name: str) -> None: ...
    def capabilities(self) -> 'QTouchDevice.Capabilities': ...
    def type(self) -> 'QTouchDevice.DeviceType': ...
    def name(self) -> str: ...
    @staticmethod
    def devices() -> typing.List['QTouchDevice']: ...


class QTransform(PyQt5.sipsimplewrapper):

    class TransformationType(int):
        TxNone = ... # type: QTransform.TransformationType
        TxTranslate = ... # type: QTransform.TransformationType
        TxScale = ... # type: QTransform.TransformationType
        TxRotate = ... # type: QTransform.TransformationType
        TxShear = ... # type: QTransform.TransformationType
        TxProject = ... # type: QTransform.TransformationType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, m11: float, m12: float, m13: float, m21: float, m22: float, m23: float, m31: float, m32: float, m33: float = ...) -> None: ...
    @typing.overload
    def __init__(self, h11: float, h12: float, h13: float, h21: float, h22: float, h23: float) -> None: ...
    @typing.overload
    def __init__(self, other: 'QTransform') -> None: ...

    def __truediv__(self, a: 'QTransform', n: float) -> 'QTransform': ...
    def __add__(self, a: 'QTransform', n: float) -> 'QTransform': ...
    def __sub__(self, a: 'QTransform', n: float) -> 'QTransform': ...
    def __hash__(self) -> int: ...
    def __isub__(self, num: float) -> 'QTransform': ...
    def __iadd__(self, num: float) -> 'QTransform': ...
    def __itruediv__(self, div: float) -> 'QTransform': ...
    @staticmethod
    def fromScale(dx: float, dy: float) -> 'QTransform': ...
    @staticmethod
    def fromTranslate(dx: float, dy: float) -> 'QTransform': ...
    def dy(self) -> float: ...
    def dx(self) -> float: ...
    def m33(self) -> float: ...
    def m32(self) -> float: ...
    def m31(self) -> float: ...
    def m23(self) -> float: ...
    def m22(self) -> float: ...
    def m21(self) -> float: ...
    def m13(self) -> float: ...
    def m12(self) -> float: ...
    def m11(self) -> float: ...
    def determinant(self) -> float: ...
    def isTranslating(self) -> bool: ...
    def isRotating(self) -> bool: ...
    def isScaling(self) -> bool: ...
    def isInvertible(self) -> bool: ...
    def isIdentity(self) -> bool: ...
    def isAffine(self) -> bool: ...
    @typing.overload
    def mapRect(self, a0: QtCore.QRect) -> QtCore.QRect: ...
    @typing.overload
    def mapRect(self, a0: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapToPolygon(self, r: QtCore.QRect) -> QPolygon: ...
    @typing.overload
    def map(self, x: int, y: int) -> typing.Tuple[int, int]: ...
    @typing.overload
    def map(self, x: float, y: float) -> typing.Tuple[float, float]: ...
    @typing.overload
    def map(self, p: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def map(self, p: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    @typing.overload
    def map(self, l: QtCore.QLine) -> QtCore.QLine: ...
    @typing.overload
    def map(self, l: QtCore.QLineF) -> QtCore.QLineF: ...
    @typing.overload
    def map(self, a: QPolygonF) -> QPolygonF: ...
    @typing.overload
    def map(self, a: QPolygon) -> QPolygon: ...
    @typing.overload
    def map(self, r: QRegion) -> QRegion: ...
    @typing.overload
    def map(self, p: QPainterPath) -> QPainterPath: ...
    def reset(self) -> None: ...
    def __matmul__(self, o: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __mul__(self, o: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __mul__(self, a: 'QTransform', n: float) -> 'QTransform': ...
    def __imatmul__(self, a0: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __imul__(self, a0: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __imul__(self, num: float) -> 'QTransform': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def quadToQuad(one: QPolygonF, two: QPolygonF, result: 'QTransform') -> bool: ...
    @staticmethod
    def quadToSquare(quad: QPolygonF, result: 'QTransform') -> bool: ...
    @staticmethod
    def squareToQuad(square: QPolygonF, result: 'QTransform') -> bool: ...
    def rotateRadians(self, angle: float, axis: QtCore.Qt.Axis = ...) -> 'QTransform': ...
    def rotate(self, angle: float, axis: QtCore.Qt.Axis = ...) -> 'QTransform': ...
    def shear(self, sh: float, sv: float) -> 'QTransform': ...
    def scale(self, sx: float, sy: float) -> 'QTransform': ...
    def translate(self, dx: float, dy: float) -> 'QTransform': ...
    def transposed(self) -> 'QTransform': ...
    def adjoint(self) -> 'QTransform': ...
    def inverted(self) -> typing.Tuple['QTransform', bool]: ...
    def setMatrix(self, m11: float, m12: float, m13: float, m21: float, m22: float, m23: float, m31: float, m32: float, m33: float) -> None: ...
    def type(self) -> 'QTransform.TransformationType': ...


class QValidator(QtCore.QObject):

    class State(int):
        Invalid = ... # type: QValidator.State
        Intermediate = ... # type: QValidator.State
        Acceptable = ... # type: QValidator.State

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    changed: typing.ClassVar[QtCore.pyqtSignal]
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def fixup(self, a0: str) -> str: ...
    def validate(self, a0: str, a1: int) -> typing.Tuple['QValidator.State', str, int]: ...


class QIntValidator(QValidator):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, bottom: int, top: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def top(self) -> int: ...
    def bottom(self) -> int: ...
    def setRange(self, bottom: int, top: int) -> None: ...
    def setTop(self, a0: int) -> None: ...
    def setBottom(self, a0: int) -> None: ...
    def fixup(self, input: str) -> str: ...
    def validate(self, a0: str, a1: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QDoubleValidator(QValidator):

    class Notation(int):
        StandardNotation = ... # type: QDoubleValidator.Notation
        ScientificNotation = ... # type: QDoubleValidator.Notation

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, bottom: float, top: float, decimals: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def notation(self) -> 'QDoubleValidator.Notation': ...
    def setNotation(self, a0: 'QDoubleValidator.Notation') -> None: ...
    def decimals(self) -> int: ...
    def top(self) -> float: ...
    def bottom(self) -> float: ...
    def setDecimals(self, a0: int) -> None: ...
    def setTop(self, a0: float) -> None: ...
    def setBottom(self, a0: float) -> None: ...
    def setRange(self, minimum: float, maximum: float, decimals: int = ...) -> None: ...
    def validate(self, a0: str, a1: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QRegExpValidator(QValidator):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, rx: QtCore.QRegExp, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def regExp(self) -> QtCore.QRegExp: ...
    def setRegExp(self, rx: QtCore.QRegExp) -> None: ...
    def validate(self, input: str, pos: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QRegularExpressionValidator(QValidator):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, re: QtCore.QRegularExpression, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setRegularExpression(self, re: QtCore.QRegularExpression) -> None: ...
    def regularExpression(self) -> QtCore.QRegularExpression: ...
    def validate(self, input: str, pos: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QVector2D(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector2D') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __truediv__(self, vector: 'QVector2D', divisor: float) -> 'QVector2D': ...
    @typing.overload
    def __truediv__(self, vector: 'QVector2D', divisor: 'QVector2D') -> 'QVector2D': ...
    def __add__(self, v1: 'QVector2D', v2: 'QVector2D') -> 'QVector2D': ...
    def __sub__(self, v1: 'QVector2D', v2: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __mul__(self, factor: float, vector: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector2D', factor: float) -> 'QVector2D': ...
    @typing.overload
    def __mul__(self, v1: 'QVector2D', v2: 'QVector2D') -> 'QVector2D': ...
    def __neg__(self) -> 'QVector2D': ...
    def __getitem__(self, i: int) -> float: ...
    def distanceToLine(self, point: 'QVector2D', direction: 'QVector2D') -> float: ...
    def distanceToPoint(self, point: 'QVector2D') -> float: ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector2D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector2D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def __isub__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def __iadd__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def setY(self, aY: float) -> None: ...
    def setX(self, aX: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def toVector4D(self) -> 'QVector4D': ...
    def toVector3D(self) -> 'QVector3D': ...
    @staticmethod
    def dotProduct(v1: 'QVector2D', v2: 'QVector2D') -> float: ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector2D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __repr__(self) -> str: ...


class QVector3D(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector3D') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __truediv__(self, vector: 'QVector3D', divisor: float) -> 'QVector3D': ...
    @typing.overload
    def __truediv__(self, vector: 'QVector3D', divisor: 'QVector3D') -> 'QVector3D': ...
    def __add__(self, v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    def __sub__(self, v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector3D', matrix: QMatrix4x4) -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, factor: float, vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector3D', factor: float) -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    def __neg__(self) -> 'QVector3D': ...
    def unproject(self, modelView: QMatrix4x4, projection: QMatrix4x4, viewport: QtCore.QRect) -> 'QVector3D': ...
    def project(self, modelView: QMatrix4x4, projection: QMatrix4x4, viewport: QtCore.QRect) -> 'QVector3D': ...
    def __getitem__(self, i: int) -> float: ...
    def distanceToPoint(self, point: 'QVector3D') -> float: ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector3D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector3D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def __isub__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def __iadd__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def setZ(self, aZ: float) -> None: ...
    def setY(self, aY: float) -> None: ...
    def setX(self, aX: float) -> None: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def toVector4D(self) -> 'QVector4D': ...
    def toVector2D(self) -> QVector2D: ...
    def distanceToLine(self, point: 'QVector3D', direction: 'QVector3D') -> float: ...
    @typing.overload
    def distanceToPlane(self, plane: 'QVector3D', normal: 'QVector3D') -> float: ...
    @typing.overload
    def distanceToPlane(self, plane1: 'QVector3D', plane2: 'QVector3D', plane3: 'QVector3D') -> float: ...
    @typing.overload
    @staticmethod
    def normal(v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    @staticmethod
    def normal(v1: 'QVector3D', v2: 'QVector3D', v3: 'QVector3D') -> 'QVector3D': ...
    @staticmethod
    def crossProduct(v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    @staticmethod
    def dotProduct(v1: 'QVector3D', v2: 'QVector3D') -> float: ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector3D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __repr__(self) -> str: ...


class QVector4D(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float, zpos: float, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D, zpos: float, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector3D) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector3D, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector4D') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @typing.overload
    def __truediv__(self, vector: 'QVector4D', divisor: float) -> 'QVector4D': ...
    @typing.overload
    def __truediv__(self, vector: 'QVector4D', divisor: 'QVector4D') -> 'QVector4D': ...
    def __add__(self, v1: 'QVector4D', v2: 'QVector4D') -> 'QVector4D': ...
    def __sub__(self, v1: 'QVector4D', v2: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector4D', matrix: QMatrix4x4) -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, factor: float, vector: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector4D', factor: float) -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, v1: 'QVector4D', v2: 'QVector4D') -> 'QVector4D': ...
    def __neg__(self) -> 'QVector4D': ...
    def __getitem__(self, i: int) -> float: ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector4D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector4D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def __isub__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def __iadd__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def setW(self, aW: float) -> None: ...
    def setZ(self, aZ: float) -> None: ...
    def setY(self, aY: float) -> None: ...
    def setX(self, aX: float) -> None: ...
    def w(self) -> float: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def toVector3DAffine(self) -> QVector3D: ...
    def toVector3D(self) -> QVector3D: ...
    def toVector2DAffine(self) -> QVector2D: ...
    def toVector2D(self) -> QVector2D: ...
    @staticmethod
    def dotProduct(v1: 'QVector4D', v2: 'QVector4D') -> float: ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector4D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __repr__(self) -> str: ...


def qIsGray(rgb: int) -> bool: ...
@typing.overload
def qGray(r: int, g: int, b: int) -> int: ...
@typing.overload
def qGray(rgb: int) -> int: ...
def qRgba(r: int, g: int, b: int, a: int) -> int: ...
def qRgb(r: int, g: int, b: int) -> int: ...
@typing.overload
def qAlpha(rgb: QRgba64) -> int: ...
@typing.overload
def qAlpha(rgb: int) -> int: ...
@typing.overload
def qBlue(rgb: QRgba64) -> int: ...
@typing.overload
def qBlue(rgb: int) -> int: ...
@typing.overload
def qGreen(rgb: QRgba64) -> int: ...
@typing.overload
def qGreen(rgb: int) -> int: ...
@typing.overload
def qRed(rgb: QRgba64) -> int: ...
@typing.overload
def qRed(rgb: int) -> int: ...
@typing.overload
def qUnpremultiply(c: QRgba64) -> QRgba64: ...
@typing.overload
def qUnpremultiply(p: int) -> int: ...
@typing.overload
def qPremultiply(c: QRgba64) -> QRgba64: ...
@typing.overload
def qPremultiply(x: int) -> int: ...
@typing.overload
def qRgba64(r: int, g: int, b: int, a: int) -> QRgba64: ...
@typing.overload
def qRgba64(c: int) -> QRgba64: ...
def qPixelFormatAlpha(channelSize: int, typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatYuv(layout: QPixelFormat.YUVLayout, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., premultiplied: QPixelFormat.AlphaPremultiplied = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ..., byteOrder: QPixelFormat.ByteOrder = ...) -> QPixelFormat: ...
def qPixelFormatHsv(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatHsl(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatCmyk(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatGrayscale(channelSize: int, typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatRgba(red: int, green: int, blue: int, alfa: int, usage: QPixelFormat.AlphaUsage, position: QPixelFormat.AlphaPosition, premultiplied: QPixelFormat.AlphaPremultiplied = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
@typing.overload
def qFuzzyCompare(m1: QMatrix4x4, m2: QMatrix4x4) -> bool: ...
@typing.overload
def qFuzzyCompare(q1: QQuaternion, q2: QQuaternion) -> bool: ...
@typing.overload
def qFuzzyCompare(t1: QTransform, t2: QTransform) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector2D, v2: QVector2D) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector3D, v2: QVector3D) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector4D, v2: QVector4D) -> bool: ...
def qt_set_sequence_auto_mnemonic(b: bool) -> None: ...
