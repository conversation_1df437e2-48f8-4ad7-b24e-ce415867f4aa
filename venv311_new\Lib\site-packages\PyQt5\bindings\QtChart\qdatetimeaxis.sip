// qdatetimeaxis.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_1_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qdatetimeaxis.h>
%End

    class QDateTimeAxis : public QtCharts::QAbstractAxis
    {
%TypeHeaderCode
#include <qdatetimeaxis.h>
%End

    public:
        explicit QDateTimeAxis(QObject *parent /TransferThis/ = 0);
        virtual ~QDateTimeAxis();
        virtual QtCharts::QAbstractAxis::AxisType type() const;
        void setMin(QDateTime min);
        QDateTime min() const;
        void setMax(QDateTime max);
        QDateTime max() const;
        void setRange(QDateTime min, QDateTime max);
        void setFormat(QString format);
        QString format() const;
        void setTickCount(int count);
        int tickCount() const;

    signals:
        void minChanged(QDateTime min);
        void maxChanged(QDateTime max);
        void rangeChanged(QDateTime min, QDateTime max);
        void formatChanged(QString format);
%If (QtChart_1_2_0 -)
        void tickCountChanged(int tick);
%End
    };
};

%End
