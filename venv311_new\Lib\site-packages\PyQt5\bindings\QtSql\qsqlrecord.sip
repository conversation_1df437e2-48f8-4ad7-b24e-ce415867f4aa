// qsqlrecord.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlRecord
{
%TypeHeaderCode
#include <qsqlrecord.h>
%End

public:
    QSqlRecord();
    QSqlRecord(const QSqlRecord &other);
    ~QSqlRecord();
    bool operator==(const QSqlRecord &other) const;
    bool operator!=(const QSqlRecord &other) const;
    QVariant value(int i) const;
    QVariant value(const QString &name) const;
    void setValue(int i, const QVariant &val);
    void setValue(const QString &name, const QVariant &val);
    void setNull(int i);
    void setNull(const QString &name);
    bool isNull(int i) const;
    bool isNull(const QString &name) const;
    int indexOf(const QString &name) const;
    QString fieldName(int i) const;
    QSqlField field(int i) const;
    QSqlField field(const QString &name) const;
    bool isGenerated(int i) const;
    bool isGenerated(const QString &name) const;
    void setGenerated(const QString &name, bool generated);
    void setGenerated(int i, bool generated);
    void append(const QSqlField &field);
    void replace(int pos, const QSqlField &field);
    void insert(int pos, const QSqlField &field);
    void remove(int pos);
    bool isEmpty() const;
    bool contains(const QString &name) const;
    void clear();
    void clearValues();
    int count() const /__len__/;
%If (Qt_5_1_0 -)
    QSqlRecord keyValues(const QSqlRecord &keyFields) const;
%End
};
