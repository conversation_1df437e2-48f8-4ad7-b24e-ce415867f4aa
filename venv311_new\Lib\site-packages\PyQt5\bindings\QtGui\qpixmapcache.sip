// qpixmapcache.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPixmapCache
{
%TypeHeaderCode
#include <qpixmapcache.h>
%End

public:
    class Key
    {
%TypeHeaderCode
#include <qpixmapcache.h>
%End

    public:
        Key();
        Key(const QPixmapCache::Key &other);
        ~Key();
        bool operator==(const QPixmapCache::Key &key) const;
        bool operator!=(const QPixmapCache::Key &key) const;
%If (Qt_5_6_0 -)
        void swap(QPixmapCache::Key &other /Constrained/);
%End
%If (Qt_5_7_0 -)
        bool isValid() const;
%End
    };

    static int cacheLimit();
    static void clear();
    static QPixmap find(const QString &key);
%MethodCode
        sipRes = new QPixmap;
        
        if (!QPixmapCache::find(*a0, sipRes))
        {
            delete sipRes;
            sipRes = 0;
        }
%End

    static QPixmap find(const QPixmapCache::Key &key);
%MethodCode
        sipRes = new QPixmap;
        
        if (!QPixmapCache::find(*a0, sipRes))
        {
            delete sipRes;
            sipRes = 0;
        }
%End

    static bool insert(const QString &key, const QPixmap &);
    static QPixmapCache::Key insert(const QPixmap &pixmap);
    static void remove(const QString &key);
    static void remove(const QPixmapCache::Key &key);
    static bool replace(const QPixmapCache::Key &key, const QPixmap &pixmap);
    static void setCacheLimit(int);
};
