#!/usr/bin/env python3
"""
اختبار مباشر للمخزون المحدث
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار مباشر للمخزون المحدث...")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        # استيراد مباشر لوحدة المخزون
        print("📦 استيراد وحدة المخزون...")
        from gui.inventory import InventoryWidget
        
        # إنشاء نافذة رئيسية
        main_window = QMainWindow()
        main_window.setWindowTitle("اختبار المخزون المحدث")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # إنشاء widget المخزون
        inventory_widget = InventoryWidget(engine)
        main_window.setCentralWidget(inventory_widget)
        
        # عرض النافذة
        main_window.show()
        
        print("✅ تم فتح نافذة المخزون!")
        print("")
        print("🔍 ما يجب أن تشاهده:")
        print("")
        print("   📋 التبويبات:")
        print("     1️⃣ تبويب 'المخزون'")
        print("     2️⃣ تبويب 'إدارة الأصناف'")
        print("")
        print("   📊 الجداول:")
        print("     • عمود 'المكسب' في كلا الجدولين")
        print("     • لون أخضر فاتح لعمود المكسب")
        print("     • حسابات صحيحة للقيم")
        print("")
        print("   ➕ إضافة منتج:")
        print("     • حقول القيمة الإجمالية والمكسب")
        print("     • تحديث فوري عند تغيير الأرقام")
        print("     • ألوان مميزة للحقول")
        print("")
        print("   ✏️ تعديل منتج:")
        print("     • أزرار تعديل وحذف في تبويب 'إدارة الأصناف'")
        print("     • تحديث فوري للقيم المحسوبة")
        print("")
        print("🎯 اختبر الآن:")
        print("1️⃣ انقر على تبويب 'إدارة الأصناف'")
        print("2️⃣ لاحظ عمود 'المكسب' في الجدول")
        print("3️⃣ اضغط 'إضافة صنف جديد'")
        print("4️⃣ أدخل البيانات ولاحظ التحديث الفوري")
        print("5️⃣ احفظ المنتج")
        print("6️⃣ اضغط 'تعديل' لأي منتج")
        print("7️⃣ غير الأرقام ولاحظ التحديث")
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    
    print("🎯 انتهى الاختبار")

if __name__ == "__main__":
    main()
