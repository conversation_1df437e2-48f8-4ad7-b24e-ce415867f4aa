import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Controls.Universal 2.15'

Module {
    dependencies: ["QtQuick.Controls 2.0"]
    Component { name: "QQuickAttachedObject"; prototype: "QObject" }
    Component {
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        Enum {
            name: "Flags"
            values: {
                "ItemClipsChildrenToShape": 1,
                "ItemAcceptsInputMethod": 2,
                "ItemIsFocusScope": 4,
                "ItemHasContents": 8,
                "ItemAcceptsDrops": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickPaintedItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Enum {
            name: "RenderTarget"
            values: {
                "Image": 0,
                "FramebufferObject": 1,
                "InvertedYFramebufferObject": 2
            }
        }
        Enum {
            name: "PerformanceHints"
            values: {
                "FastFBOResizing": 1
            }
        }
        Property { name: "contentsSize"; type: "QSize" }
        Property { name: "fillColor"; type: "QColor" }
        Property { name: "contentsScale"; type: "double" }
        Property { name: "renderTarget"; type: "RenderTarget" }
        Property { name: "textureSize"; type: "QSize" }
    }
    Component {
        name: "QQuickUniversalBusyIndicator"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "count"; type: "int" }
        Property { name: "color"; type: "QColor" }
    }
    Component {
        name: "QQuickUniversalFocusRectangle"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.Universal.impl/FocusRectangle 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickUniversalProgressBar"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.Universal.impl/ProgressBarImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor" }
        Property { name: "progress"; type: "double" }
        Property { name: "indeterminate"; type: "bool" }
    }
    Component {
        name: "QQuickUniversalStyle"
        prototype: "QQuickAttachedObject"
        exports: ["QtQuick.Controls.Universal/Universal 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Theme"
            values: {
                "Light": 0,
                "Dark": 1,
                "System": 2
            }
        }
        Enum {
            name: "Color"
            values: {
                "Lime": 0,
                "Green": 1,
                "Emerald": 2,
                "Teal": 3,
                "Cyan": 4,
                "Cobalt": 5,
                "Indigo": 6,
                "Violet": 7,
                "Pink": 8,
                "Magenta": 9,
                "Crimson": 10,
                "Red": 11,
                "Orange": 12,
                "Amber": 13,
                "Yellow": 14,
                "Brown": 15,
                "Olive": 16,
                "Steel": 17,
                "Mauve": 18,
                "Taupe": 19
            }
        }
        Property { name: "theme"; type: "Theme" }
        Property { name: "accent"; type: "QVariant" }
        Property { name: "foreground"; type: "QVariant" }
        Property { name: "background"; type: "QVariant" }
        Property { name: "altHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "altLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "altMediumColor"; type: "QColor"; isReadonly: true }
        Property { name: "altMediumHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "altMediumLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "baseHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "baseLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "baseMediumColor"; type: "QColor"; isReadonly: true }
        Property { name: "baseMediumHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "baseMediumLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeAltLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeBlackHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeBlackLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeBlackMediumLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeBlackMediumColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeDisabledHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeDisabledLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeHighColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeMediumColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeMediumLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "chromeWhiteColor"; type: "QColor"; isReadonly: true }
        Property { name: "listLowColor"; type: "QColor"; isReadonly: true }
        Property { name: "listMediumColor"; type: "QColor"; isReadonly: true }
        Signal { name: "paletteChanged" }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
        }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Universal.impl/CheckIndicator 2.0"
        exports: ["QtQuick.Controls.Universal.impl/CheckIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "partiallyChecked"; type: "bool"; isReadonly: true }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Universal.impl/RadioIndicator 2.0"
        exports: ["QtQuick.Controls.Universal.impl/RadioIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QVariant" }
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Universal.impl/SwitchIndicator 2.0"
        exports: ["QtQuick.Controls.Universal.impl/SwitchIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
    }
}
