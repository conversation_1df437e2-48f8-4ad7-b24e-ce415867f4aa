#!/usr/bin/env python3
"""
إجبار تحديث ملف المخزون
"""

import os
import shutil
import sys

def force_update():
    print("🔄 إجبار تحديث ملف المخزون...")
    
    # حذف ملفات cache
    cache_dirs = [
        "gui/__pycache__",
        "__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✅ تم حذف {cache_dir}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {cache_dir}: {e}")
    
    # إنشاء ملف تحديث مؤقت
    update_code = '''
# تحديث مؤقت للمخزون
import importlib
import sys

# إعادة تحميل الوحدة
if "gui.inventory" in sys.modules:
    importlib.reload(sys.modules["gui.inventory"])

print("✅ تم تحديث وحدة المخزون")
'''
    
    with open("temp_update.py", "w", encoding="utf-8") as f:
        f.write(update_code)
    
    print("✅ تم إنشاء ملف التحديث المؤقت")
    
    # تشغيل التحديث
    try:
        exec(update_code)
    except Exception as e:
        print(f"⚠️ خطأ في التحديث: {e}")
    
    # حذف الملف المؤقت
    if os.path.exists("temp_update.py"):
        os.remove("temp_update.py")
    
    print("🎯 تم الانتهاء من التحديث")
    print("")
    print("📋 الآن قم بما يلي:")
    print("1️⃣ أغلق البرنامج تماماً")
    print("2️⃣ شغل البرنامج من جديد")
    print("3️⃣ اذهب لقسم المخزون")
    print("4️⃣ ابحث عن تبويب 'إدارة الأصناف'")
    print("5️⃣ لاحظ عمود 'المكسب' في الجداول")

if __name__ == "__main__":
    force_update()
