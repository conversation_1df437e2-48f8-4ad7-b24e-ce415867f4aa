// qmediatimerange.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaTimeInterval
{
%TypeHeaderCode
#include <qmediatimerange.h>
%End

public:
    QMediaTimeInterval();
    QMediaTimeInterval(qint64 start, qint64 end);
    QMediaTimeInterval(const QMediaTimeInterval &);
    qint64 start() const;
    qint64 end() const;
    bool contains(qint64 time) const;
    bool isNormal() const;
    QMediaTimeInterval normalized() const;
    QMediaTimeInterval translated(qint64 offset) const;
};

bool operator==(const QMediaTimeInterval &, const QMediaTimeInterval &);
bool operator!=(const QMediaTimeInterval &, const QMediaTimeInterval &);

class QMediaTimeRange
{
%TypeHeaderCode
#include <qmediatimerange.h>
%End

public:
    QMediaTimeRange();
    QMediaTimeRange(qint64 start, qint64 end);
    QMediaTimeRange(const QMediaTimeInterval &);
    QMediaTimeRange(const QMediaTimeRange &range);
    ~QMediaTimeRange();
    qint64 earliestTime() const;
    qint64 latestTime() const;
    QList<QMediaTimeInterval> intervals() const;
    bool isEmpty() const;
    bool isContinuous() const;
    bool contains(qint64 time) const;
    void addInterval(qint64 start, qint64 end);
    void addInterval(const QMediaTimeInterval &interval);
    void addTimeRange(const QMediaTimeRange &);
    void removeInterval(qint64 start, qint64 end);
    void removeInterval(const QMediaTimeInterval &interval);
    void removeTimeRange(const QMediaTimeRange &);
    QMediaTimeRange &operator+=(const QMediaTimeRange &);
    QMediaTimeRange &operator+=(const QMediaTimeInterval &);
    QMediaTimeRange &operator-=(const QMediaTimeRange &);
    QMediaTimeRange &operator-=(const QMediaTimeInterval &);
    void clear();
};

bool operator==(const QMediaTimeRange &, const QMediaTimeRange &);
bool operator!=(const QMediaTimeRange &, const QMediaTimeRange &);
QMediaTimeRange operator+(const QMediaTimeRange &, const QMediaTimeRange &);
QMediaTimeRange operator-(const QMediaTimeRange &, const QMediaTimeRange &);
