from __future__ import annotations

from typing import Any
import numpy as np

SEED_NONE = None
SEED_INT = 4579435749574957634658964293569
SEED_ARR: np.ndarray[Any, np.dtype[np.int64]] = np.array([1, 2, 3, 4], dtype=np.int64)
SEED_ARRLIKE: list[int] = [1, 2, 3, 4]
SEED_SEED_SEQ: np.random.SeedSequence = np.random.SeedSequence(0)
SEED_MT19937: np.random.MT19937 = np.random.MT19937(0)
SEED_PCG64: np.random.PCG64 = np.random.PCG64(0)
SEED_PHILOX: np.random.Philox = np.random.Philox(0)
SEED_SFC64: np.random.SFC64 = np.random.SFC64(0)

# default rng
np.random.default_rng()
np.random.default_rng(SEED_NONE)
np.random.default_rng(SEED_INT)
np.random.default_rng(SEED_ARR)
np.random.default_rng(SEED_ARRLIKE)
np.random.default_rng(SEED_SEED_SEQ)
np.random.default_rng(SEED_MT19937)
np.random.default_rng(SEED_PCG64)
np.random.default_rng(SEED_PHILOX)
np.random.default_rng(SEED_SFC64)

# Seed Sequence
np.random.SeedSequence(SEED_NONE)
np.random.SeedSequence(SEED_INT)
np.random.SeedSequence(SEED_ARR)
np.random.SeedSequence(SEED_ARRLIKE)

# Bit Generators
np.random.MT19937(SEED_NONE)
np.random.MT19937(SEED_INT)
np.random.MT19937(SEED_ARR)
np.random.MT19937(SEED_ARRLIKE)
np.random.MT19937(SEED_SEED_SEQ)

np.random.PCG64(SEED_NONE)
np.random.PCG64(SEED_INT)
np.random.PCG64(SEED_ARR)
np.random.PCG64(SEED_ARRLIKE)
np.random.PCG64(SEED_SEED_SEQ)

np.random.Philox(SEED_NONE)
np.random.Philox(SEED_INT)
np.random.Philox(SEED_ARR)
np.random.Philox(SEED_ARRLIKE)
np.random.Philox(SEED_SEED_SEQ)

np.random.SFC64(SEED_NONE)
np.random.SFC64(SEED_INT)
np.random.SFC64(SEED_ARR)
np.random.SFC64(SEED_ARRLIKE)
np.random.SFC64(SEED_SEED_SEQ)

seed_seq: np.random.bit_generator.SeedSequence = np.random.SeedSequence(SEED_NONE)
seed_seq.spawn(10)
seed_seq.generate_state(3)
seed_seq.generate_state(3, "u4")
seed_seq.generate_state(3, "uint32")
seed_seq.generate_state(3, "u8")
seed_seq.generate_state(3, "uint64")
seed_seq.generate_state(3, np.uint32)
seed_seq.generate_state(3, np.uint64)


def_gen: np.random.Generator = np.random.default_rng()

D_arr_0p1: np.ndarray[Any, np.dtype[np.float64]] = np.array([0.1])
D_arr_0p5: np.ndarray[Any, np.dtype[np.float64]] = np.array([0.5])
D_arr_0p9: np.ndarray[Any, np.dtype[np.float64]] = np.array([0.9])
D_arr_1p5: np.ndarray[Any, np.dtype[np.float64]] = np.array([1.5])
I_arr_10: np.ndarray[Any, np.dtype[np.int_]] = np.array([10], dtype=np.int_)
I_arr_20: np.ndarray[Any, np.dtype[np.int_]] = np.array([20], dtype=np.int_)
D_arr_like_0p1: list[float] = [0.1]
D_arr_like_0p5: list[float] = [0.5]
D_arr_like_0p9: list[float] = [0.9]
D_arr_like_1p5: list[float] = [1.5]
I_arr_like_10: list[int] = [10]
I_arr_like_20: list[int] = [20]
D_2D_like: list[list[float]] = [[1, 2], [2, 3], [3, 4], [4, 5.1]]
D_2D: np.ndarray[Any, np.dtype[np.float64]] = np.array(D_2D_like)

S_out: np.ndarray[Any, np.dtype[np.float32]] = np.empty(1, dtype=np.float32)
D_out: np.ndarray[Any, np.dtype[np.float64]] = np.empty(1)

def_gen.standard_normal()
def_gen.standard_normal(dtype=np.float32)
def_gen.standard_normal(dtype="float32")
def_gen.standard_normal(dtype="double")
def_gen.standard_normal(dtype=np.float64)
def_gen.standard_normal(size=None)
def_gen.standard_normal(size=1)
def_gen.standard_normal(size=1, dtype=np.float32)
def_gen.standard_normal(size=1, dtype="f4")
def_gen.standard_normal(size=1, dtype="float32", out=S_out)
def_gen.standard_normal(dtype=np.float32, out=S_out)
def_gen.standard_normal(size=1, dtype=np.float64)
def_gen.standard_normal(size=1, dtype="float64")
def_gen.standard_normal(size=1, dtype="f8")
def_gen.standard_normal(out=D_out)
def_gen.standard_normal(size=1, dtype="float64")
def_gen.standard_normal(size=1, dtype="float64", out=D_out)

def_gen.random()
def_gen.random(dtype=np.float32)
def_gen.random(dtype="float32")
def_gen.random(dtype="double")
def_gen.random(dtype=np.float64)
def_gen.random(size=None)
def_gen.random(size=1)
def_gen.random(size=1, dtype=np.float32)
def_gen.random(size=1, dtype="f4")
def_gen.random(size=1, dtype="float32", out=S_out)
def_gen.random(dtype=np.float32, out=S_out)
def_gen.random(size=1, dtype=np.float64)
def_gen.random(size=1, dtype="float64")
def_gen.random(size=1, dtype="f8")
def_gen.random(out=D_out)
def_gen.random(size=1, dtype="float64")
def_gen.random(size=1, dtype="float64", out=D_out)

def_gen.standard_cauchy()
def_gen.standard_cauchy(size=None)
def_gen.standard_cauchy(size=1)

def_gen.standard_exponential()
def_gen.standard_exponential(method="inv")
def_gen.standard_exponential(dtype=np.float32)
def_gen.standard_exponential(dtype="float32")
def_gen.standard_exponential(dtype="double")
def_gen.standard_exponential(dtype=np.float64)
def_gen.standard_exponential(size=None)
def_gen.standard_exponential(size=None, method="inv")
def_gen.standard_exponential(size=1, method="inv")
def_gen.standard_exponential(size=1, dtype=np.float32)
def_gen.standard_exponential(size=1, dtype="f4", method="inv")
def_gen.standard_exponential(size=1, dtype="float32", out=S_out)
def_gen.standard_exponential(dtype=np.float32, out=S_out)
def_gen.standard_exponential(size=1, dtype=np.float64, method="inv")
def_gen.standard_exponential(size=1, dtype="float64")
def_gen.standard_exponential(size=1, dtype="f8")
def_gen.standard_exponential(out=D_out)
def_gen.standard_exponential(size=1, dtype="float64")
def_gen.standard_exponential(size=1, dtype="float64", out=D_out)

def_gen.zipf(1.5)
def_gen.zipf(1.5, size=None)
def_gen.zipf(1.5, size=1)
def_gen.zipf(D_arr_1p5)
def_gen.zipf(D_arr_1p5, size=1)
def_gen.zipf(D_arr_like_1p5)
def_gen.zipf(D_arr_like_1p5, size=1)

def_gen.weibull(0.5)
def_gen.weibull(0.5, size=None)
def_gen.weibull(0.5, size=1)
def_gen.weibull(D_arr_0p5)
def_gen.weibull(D_arr_0p5, size=1)
def_gen.weibull(D_arr_like_0p5)
def_gen.weibull(D_arr_like_0p5, size=1)

def_gen.standard_t(0.5)
def_gen.standard_t(0.5, size=None)
def_gen.standard_t(0.5, size=1)
def_gen.standard_t(D_arr_0p5)
def_gen.standard_t(D_arr_0p5, size=1)
def_gen.standard_t(D_arr_like_0p5)
def_gen.standard_t(D_arr_like_0p5, size=1)

def_gen.poisson(0.5)
def_gen.poisson(0.5, size=None)
def_gen.poisson(0.5, size=1)
def_gen.poisson(D_arr_0p5)
def_gen.poisson(D_arr_0p5, size=1)
def_gen.poisson(D_arr_like_0p5)
def_gen.poisson(D_arr_like_0p5, size=1)

def_gen.power(0.5)
def_gen.power(0.5, size=None)
def_gen.power(0.5, size=1)
def_gen.power(D_arr_0p5)
def_gen.power(D_arr_0p5, size=1)
def_gen.power(D_arr_like_0p5)
def_gen.power(D_arr_like_0p5, size=1)

def_gen.pareto(0.5)
def_gen.pareto(0.5, size=None)
def_gen.pareto(0.5, size=1)
def_gen.pareto(D_arr_0p5)
def_gen.pareto(D_arr_0p5, size=1)
def_gen.pareto(D_arr_like_0p5)
def_gen.pareto(D_arr_like_0p5, size=1)

def_gen.chisquare(0.5)
def_gen.chisquare(0.5, size=None)
def_gen.chisquare(0.5, size=1)
def_gen.chisquare(D_arr_0p5)
def_gen.chisquare(D_arr_0p5, size=1)
def_gen.chisquare(D_arr_like_0p5)
def_gen.chisquare(D_arr_like_0p5, size=1)

def_gen.exponential(0.5)
def_gen.exponential(0.5, size=None)
def_gen.exponential(0.5, size=1)
def_gen.exponential(D_arr_0p5)
def_gen.exponential(D_arr_0p5, size=1)
def_gen.exponential(D_arr_like_0p5)
def_gen.exponential(D_arr_like_0p5, size=1)

def_gen.geometric(0.5)
def_gen.geometric(0.5, size=None)
def_gen.geometric(0.5, size=1)
def_gen.geometric(D_arr_0p5)
def_gen.geometric(D_arr_0p5, size=1)
def_gen.geometric(D_arr_like_0p5)
def_gen.geometric(D_arr_like_0p5, size=1)

def_gen.logseries(0.5)
def_gen.logseries(0.5, size=None)
def_gen.logseries(0.5, size=1)
def_gen.logseries(D_arr_0p5)
def_gen.logseries(D_arr_0p5, size=1)
def_gen.logseries(D_arr_like_0p5)
def_gen.logseries(D_arr_like_0p5, size=1)

def_gen.rayleigh(0.5)
def_gen.rayleigh(0.5, size=None)
def_gen.rayleigh(0.5, size=1)
def_gen.rayleigh(D_arr_0p5)
def_gen.rayleigh(D_arr_0p5, size=1)
def_gen.rayleigh(D_arr_like_0p5)
def_gen.rayleigh(D_arr_like_0p5, size=1)

def_gen.standard_gamma(0.5)
def_gen.standard_gamma(0.5, size=None)
def_gen.standard_gamma(0.5, dtype="float32")
def_gen.standard_gamma(0.5, size=None, dtype="float32")
def_gen.standard_gamma(0.5, size=1)
def_gen.standard_gamma(D_arr_0p5)
def_gen.standard_gamma(D_arr_0p5, dtype="f4")
def_gen.standard_gamma(0.5, size=1, dtype="float32", out=S_out)
def_gen.standard_gamma(D_arr_0p5, dtype=np.float32, out=S_out)
def_gen.standard_gamma(D_arr_0p5, size=1)
def_gen.standard_gamma(D_arr_like_0p5)
def_gen.standard_gamma(D_arr_like_0p5, size=1)
def_gen.standard_gamma(0.5, out=D_out)
def_gen.standard_gamma(D_arr_like_0p5, out=D_out)
def_gen.standard_gamma(D_arr_like_0p5, size=1)
def_gen.standard_gamma(D_arr_like_0p5, size=1, out=D_out, dtype=np.float64)

def_gen.vonmises(0.5, 0.5)
def_gen.vonmises(0.5, 0.5, size=None)
def_gen.vonmises(0.5, 0.5, size=1)
def_gen.vonmises(D_arr_0p5, 0.5)
def_gen.vonmises(0.5, D_arr_0p5)
def_gen.vonmises(D_arr_0p5, 0.5, size=1)
def_gen.vonmises(0.5, D_arr_0p5, size=1)
def_gen.vonmises(D_arr_like_0p5, 0.5)
def_gen.vonmises(0.5, D_arr_like_0p5)
def_gen.vonmises(D_arr_0p5, D_arr_0p5)
def_gen.vonmises(D_arr_like_0p5, D_arr_like_0p5)
def_gen.vonmises(D_arr_0p5, D_arr_0p5, size=1)
def_gen.vonmises(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.wald(0.5, 0.5)
def_gen.wald(0.5, 0.5, size=None)
def_gen.wald(0.5, 0.5, size=1)
def_gen.wald(D_arr_0p5, 0.5)
def_gen.wald(0.5, D_arr_0p5)
def_gen.wald(D_arr_0p5, 0.5, size=1)
def_gen.wald(0.5, D_arr_0p5, size=1)
def_gen.wald(D_arr_like_0p5, 0.5)
def_gen.wald(0.5, D_arr_like_0p5)
def_gen.wald(D_arr_0p5, D_arr_0p5)
def_gen.wald(D_arr_like_0p5, D_arr_like_0p5)
def_gen.wald(D_arr_0p5, D_arr_0p5, size=1)
def_gen.wald(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.uniform(0.5, 0.5)
def_gen.uniform(0.5, 0.5, size=None)
def_gen.uniform(0.5, 0.5, size=1)
def_gen.uniform(D_arr_0p5, 0.5)
def_gen.uniform(0.5, D_arr_0p5)
def_gen.uniform(D_arr_0p5, 0.5, size=1)
def_gen.uniform(0.5, D_arr_0p5, size=1)
def_gen.uniform(D_arr_like_0p5, 0.5)
def_gen.uniform(0.5, D_arr_like_0p5)
def_gen.uniform(D_arr_0p5, D_arr_0p5)
def_gen.uniform(D_arr_like_0p5, D_arr_like_0p5)
def_gen.uniform(D_arr_0p5, D_arr_0p5, size=1)
def_gen.uniform(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.beta(0.5, 0.5)
def_gen.beta(0.5, 0.5, size=None)
def_gen.beta(0.5, 0.5, size=1)
def_gen.beta(D_arr_0p5, 0.5)
def_gen.beta(0.5, D_arr_0p5)
def_gen.beta(D_arr_0p5, 0.5, size=1)
def_gen.beta(0.5, D_arr_0p5, size=1)
def_gen.beta(D_arr_like_0p5, 0.5)
def_gen.beta(0.5, D_arr_like_0p5)
def_gen.beta(D_arr_0p5, D_arr_0p5)
def_gen.beta(D_arr_like_0p5, D_arr_like_0p5)
def_gen.beta(D_arr_0p5, D_arr_0p5, size=1)
def_gen.beta(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.f(0.5, 0.5)
def_gen.f(0.5, 0.5, size=None)
def_gen.f(0.5, 0.5, size=1)
def_gen.f(D_arr_0p5, 0.5)
def_gen.f(0.5, D_arr_0p5)
def_gen.f(D_arr_0p5, 0.5, size=1)
def_gen.f(0.5, D_arr_0p5, size=1)
def_gen.f(D_arr_like_0p5, 0.5)
def_gen.f(0.5, D_arr_like_0p5)
def_gen.f(D_arr_0p5, D_arr_0p5)
def_gen.f(D_arr_like_0p5, D_arr_like_0p5)
def_gen.f(D_arr_0p5, D_arr_0p5, size=1)
def_gen.f(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.gamma(0.5, 0.5)
def_gen.gamma(0.5, 0.5, size=None)
def_gen.gamma(0.5, 0.5, size=1)
def_gen.gamma(D_arr_0p5, 0.5)
def_gen.gamma(0.5, D_arr_0p5)
def_gen.gamma(D_arr_0p5, 0.5, size=1)
def_gen.gamma(0.5, D_arr_0p5, size=1)
def_gen.gamma(D_arr_like_0p5, 0.5)
def_gen.gamma(0.5, D_arr_like_0p5)
def_gen.gamma(D_arr_0p5, D_arr_0p5)
def_gen.gamma(D_arr_like_0p5, D_arr_like_0p5)
def_gen.gamma(D_arr_0p5, D_arr_0p5, size=1)
def_gen.gamma(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.gumbel(0.5, 0.5)
def_gen.gumbel(0.5, 0.5, size=None)
def_gen.gumbel(0.5, 0.5, size=1)
def_gen.gumbel(D_arr_0p5, 0.5)
def_gen.gumbel(0.5, D_arr_0p5)
def_gen.gumbel(D_arr_0p5, 0.5, size=1)
def_gen.gumbel(0.5, D_arr_0p5, size=1)
def_gen.gumbel(D_arr_like_0p5, 0.5)
def_gen.gumbel(0.5, D_arr_like_0p5)
def_gen.gumbel(D_arr_0p5, D_arr_0p5)
def_gen.gumbel(D_arr_like_0p5, D_arr_like_0p5)
def_gen.gumbel(D_arr_0p5, D_arr_0p5, size=1)
def_gen.gumbel(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.laplace(0.5, 0.5)
def_gen.laplace(0.5, 0.5, size=None)
def_gen.laplace(0.5, 0.5, size=1)
def_gen.laplace(D_arr_0p5, 0.5)
def_gen.laplace(0.5, D_arr_0p5)
def_gen.laplace(D_arr_0p5, 0.5, size=1)
def_gen.laplace(0.5, D_arr_0p5, size=1)
def_gen.laplace(D_arr_like_0p5, 0.5)
def_gen.laplace(0.5, D_arr_like_0p5)
def_gen.laplace(D_arr_0p5, D_arr_0p5)
def_gen.laplace(D_arr_like_0p5, D_arr_like_0p5)
def_gen.laplace(D_arr_0p5, D_arr_0p5, size=1)
def_gen.laplace(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.logistic(0.5, 0.5)
def_gen.logistic(0.5, 0.5, size=None)
def_gen.logistic(0.5, 0.5, size=1)
def_gen.logistic(D_arr_0p5, 0.5)
def_gen.logistic(0.5, D_arr_0p5)
def_gen.logistic(D_arr_0p5, 0.5, size=1)
def_gen.logistic(0.5, D_arr_0p5, size=1)
def_gen.logistic(D_arr_like_0p5, 0.5)
def_gen.logistic(0.5, D_arr_like_0p5)
def_gen.logistic(D_arr_0p5, D_arr_0p5)
def_gen.logistic(D_arr_like_0p5, D_arr_like_0p5)
def_gen.logistic(D_arr_0p5, D_arr_0p5, size=1)
def_gen.logistic(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.lognormal(0.5, 0.5)
def_gen.lognormal(0.5, 0.5, size=None)
def_gen.lognormal(0.5, 0.5, size=1)
def_gen.lognormal(D_arr_0p5, 0.5)
def_gen.lognormal(0.5, D_arr_0p5)
def_gen.lognormal(D_arr_0p5, 0.5, size=1)
def_gen.lognormal(0.5, D_arr_0p5, size=1)
def_gen.lognormal(D_arr_like_0p5, 0.5)
def_gen.lognormal(0.5, D_arr_like_0p5)
def_gen.lognormal(D_arr_0p5, D_arr_0p5)
def_gen.lognormal(D_arr_like_0p5, D_arr_like_0p5)
def_gen.lognormal(D_arr_0p5, D_arr_0p5, size=1)
def_gen.lognormal(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.noncentral_chisquare(0.5, 0.5)
def_gen.noncentral_chisquare(0.5, 0.5, size=None)
def_gen.noncentral_chisquare(0.5, 0.5, size=1)
def_gen.noncentral_chisquare(D_arr_0p5, 0.5)
def_gen.noncentral_chisquare(0.5, D_arr_0p5)
def_gen.noncentral_chisquare(D_arr_0p5, 0.5, size=1)
def_gen.noncentral_chisquare(0.5, D_arr_0p5, size=1)
def_gen.noncentral_chisquare(D_arr_like_0p5, 0.5)
def_gen.noncentral_chisquare(0.5, D_arr_like_0p5)
def_gen.noncentral_chisquare(D_arr_0p5, D_arr_0p5)
def_gen.noncentral_chisquare(D_arr_like_0p5, D_arr_like_0p5)
def_gen.noncentral_chisquare(D_arr_0p5, D_arr_0p5, size=1)
def_gen.noncentral_chisquare(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.normal(0.5, 0.5)
def_gen.normal(0.5, 0.5, size=None)
def_gen.normal(0.5, 0.5, size=1)
def_gen.normal(D_arr_0p5, 0.5)
def_gen.normal(0.5, D_arr_0p5)
def_gen.normal(D_arr_0p5, 0.5, size=1)
def_gen.normal(0.5, D_arr_0p5, size=1)
def_gen.normal(D_arr_like_0p5, 0.5)
def_gen.normal(0.5, D_arr_like_0p5)
def_gen.normal(D_arr_0p5, D_arr_0p5)
def_gen.normal(D_arr_like_0p5, D_arr_like_0p5)
def_gen.normal(D_arr_0p5, D_arr_0p5, size=1)
def_gen.normal(D_arr_like_0p5, D_arr_like_0p5, size=1)

def_gen.triangular(0.1, 0.5, 0.9)
def_gen.triangular(0.1, 0.5, 0.9, size=None)
def_gen.triangular(0.1, 0.5, 0.9, size=1)
def_gen.triangular(D_arr_0p1, 0.5, 0.9)
def_gen.triangular(0.1, D_arr_0p5, 0.9)
def_gen.triangular(D_arr_0p1, 0.5, D_arr_like_0p9, size=1)
def_gen.triangular(0.1, D_arr_0p5, 0.9, size=1)
def_gen.triangular(D_arr_like_0p1, 0.5, D_arr_0p9)
def_gen.triangular(0.5, D_arr_like_0p5, 0.9)
def_gen.triangular(D_arr_0p1, D_arr_0p5, 0.9)
def_gen.triangular(D_arr_like_0p1, D_arr_like_0p5, 0.9)
def_gen.triangular(D_arr_0p1, D_arr_0p5, D_arr_0p9, size=1)
def_gen.triangular(D_arr_like_0p1, D_arr_like_0p5, D_arr_like_0p9, size=1)

def_gen.noncentral_f(0.1, 0.5, 0.9)
def_gen.noncentral_f(0.1, 0.5, 0.9, size=None)
def_gen.noncentral_f(0.1, 0.5, 0.9, size=1)
def_gen.noncentral_f(D_arr_0p1, 0.5, 0.9)
def_gen.noncentral_f(0.1, D_arr_0p5, 0.9)
def_gen.noncentral_f(D_arr_0p1, 0.5, D_arr_like_0p9, size=1)
def_gen.noncentral_f(0.1, D_arr_0p5, 0.9, size=1)
def_gen.noncentral_f(D_arr_like_0p1, 0.5, D_arr_0p9)
def_gen.noncentral_f(0.5, D_arr_like_0p5, 0.9)
def_gen.noncentral_f(D_arr_0p1, D_arr_0p5, 0.9)
def_gen.noncentral_f(D_arr_like_0p1, D_arr_like_0p5, 0.9)
def_gen.noncentral_f(D_arr_0p1, D_arr_0p5, D_arr_0p9, size=1)
def_gen.noncentral_f(D_arr_like_0p1, D_arr_like_0p5, D_arr_like_0p9, size=1)

def_gen.binomial(10, 0.5)
def_gen.binomial(10, 0.5, size=None)
def_gen.binomial(10, 0.5, size=1)
def_gen.binomial(I_arr_10, 0.5)
def_gen.binomial(10, D_arr_0p5)
def_gen.binomial(I_arr_10, 0.5, size=1)
def_gen.binomial(10, D_arr_0p5, size=1)
def_gen.binomial(I_arr_like_10, 0.5)
def_gen.binomial(10, D_arr_like_0p5)
def_gen.binomial(I_arr_10, D_arr_0p5)
def_gen.binomial(I_arr_like_10, D_arr_like_0p5)
def_gen.binomial(I_arr_10, D_arr_0p5, size=1)
def_gen.binomial(I_arr_like_10, D_arr_like_0p5, size=1)

def_gen.negative_binomial(10, 0.5)
def_gen.negative_binomial(10, 0.5, size=None)
def_gen.negative_binomial(10, 0.5, size=1)
def_gen.negative_binomial(I_arr_10, 0.5)
def_gen.negative_binomial(10, D_arr_0p5)
def_gen.negative_binomial(I_arr_10, 0.5, size=1)
def_gen.negative_binomial(10, D_arr_0p5, size=1)
def_gen.negative_binomial(I_arr_like_10, 0.5)
def_gen.negative_binomial(10, D_arr_like_0p5)
def_gen.negative_binomial(I_arr_10, D_arr_0p5)
def_gen.negative_binomial(I_arr_like_10, D_arr_like_0p5)
def_gen.negative_binomial(I_arr_10, D_arr_0p5, size=1)
def_gen.negative_binomial(I_arr_like_10, D_arr_like_0p5, size=1)

def_gen.hypergeometric(20, 20, 10)
def_gen.hypergeometric(20, 20, 10, size=None)
def_gen.hypergeometric(20, 20, 10, size=1)
def_gen.hypergeometric(I_arr_20, 20, 10)
def_gen.hypergeometric(20, I_arr_20, 10)
def_gen.hypergeometric(I_arr_20, 20, I_arr_like_10, size=1)
def_gen.hypergeometric(20, I_arr_20, 10, size=1)
def_gen.hypergeometric(I_arr_like_20, 20, I_arr_10)
def_gen.hypergeometric(20, I_arr_like_20, 10)
def_gen.hypergeometric(I_arr_20, I_arr_20, 10)
def_gen.hypergeometric(I_arr_like_20, I_arr_like_20, 10)
def_gen.hypergeometric(I_arr_20, I_arr_20, I_arr_10, size=1)
def_gen.hypergeometric(I_arr_like_20, I_arr_like_20, I_arr_like_10, size=1)

I_int64_100: np.ndarray[Any, np.dtype[np.int64]] = np.array([100], dtype=np.int64)

def_gen.integers(0, 100)
def_gen.integers(100)
def_gen.integers([100])
def_gen.integers(0, [100])

I_bool_low: np.ndarray[Any, np.dtype[np.bool]] = np.array([0], dtype=np.bool)
I_bool_low_like: list[int] = [0]
I_bool_high_open: np.ndarray[Any, np.dtype[np.bool]] = np.array([1], dtype=np.bool)
I_bool_high_closed: np.ndarray[Any, np.dtype[np.bool]] = np.array([1], dtype=np.bool)

def_gen.integers(2, dtype=bool)
def_gen.integers(0, 2, dtype=bool)
def_gen.integers(1, dtype=bool, endpoint=True)
def_gen.integers(0, 1, dtype=bool, endpoint=True)
def_gen.integers(I_bool_low_like, 1, dtype=bool, endpoint=True)
def_gen.integers(I_bool_high_open, dtype=bool)
def_gen.integers(I_bool_low, I_bool_high_open, dtype=bool)
def_gen.integers(0, I_bool_high_open, dtype=bool)
def_gen.integers(I_bool_high_closed, dtype=bool, endpoint=True)
def_gen.integers(I_bool_low, I_bool_high_closed, dtype=bool, endpoint=True)
def_gen.integers(0, I_bool_high_closed, dtype=bool, endpoint=True)

def_gen.integers(2, dtype=np.bool)
def_gen.integers(0, 2, dtype=np.bool)
def_gen.integers(1, dtype=np.bool, endpoint=True)
def_gen.integers(0, 1, dtype=np.bool, endpoint=True)
def_gen.integers(I_bool_low_like, 1, dtype=np.bool, endpoint=True)
def_gen.integers(I_bool_high_open, dtype=np.bool)
def_gen.integers(I_bool_low, I_bool_high_open, dtype=np.bool)
def_gen.integers(0, I_bool_high_open, dtype=np.bool)
def_gen.integers(I_bool_high_closed, dtype=np.bool, endpoint=True)
def_gen.integers(I_bool_low, I_bool_high_closed, dtype=np.bool, endpoint=True)
def_gen.integers(0, I_bool_high_closed, dtype=np.bool, endpoint=True)

I_u1_low: np.ndarray[Any, np.dtype[np.uint8]] = np.array([0], dtype=np.uint8)
I_u1_low_like: list[int] = [0]
I_u1_high_open: np.ndarray[Any, np.dtype[np.uint8]] = np.array([255], dtype=np.uint8)
I_u1_high_closed: np.ndarray[Any, np.dtype[np.uint8]] = np.array([255], dtype=np.uint8)

def_gen.integers(256, dtype="u1")
def_gen.integers(0, 256, dtype="u1")
def_gen.integers(255, dtype="u1", endpoint=True)
def_gen.integers(0, 255, dtype="u1", endpoint=True)
def_gen.integers(I_u1_low_like, 255, dtype="u1", endpoint=True)
def_gen.integers(I_u1_high_open, dtype="u1")
def_gen.integers(I_u1_low, I_u1_high_open, dtype="u1")
def_gen.integers(0, I_u1_high_open, dtype="u1")
def_gen.integers(I_u1_high_closed, dtype="u1", endpoint=True)
def_gen.integers(I_u1_low, I_u1_high_closed, dtype="u1", endpoint=True)
def_gen.integers(0, I_u1_high_closed, dtype="u1", endpoint=True)

def_gen.integers(256, dtype="uint8")
def_gen.integers(0, 256, dtype="uint8")
def_gen.integers(255, dtype="uint8", endpoint=True)
def_gen.integers(0, 255, dtype="uint8", endpoint=True)
def_gen.integers(I_u1_low_like, 255, dtype="uint8", endpoint=True)
def_gen.integers(I_u1_high_open, dtype="uint8")
def_gen.integers(I_u1_low, I_u1_high_open, dtype="uint8")
def_gen.integers(0, I_u1_high_open, dtype="uint8")
def_gen.integers(I_u1_high_closed, dtype="uint8", endpoint=True)
def_gen.integers(I_u1_low, I_u1_high_closed, dtype="uint8", endpoint=True)
def_gen.integers(0, I_u1_high_closed, dtype="uint8", endpoint=True)

def_gen.integers(256, dtype=np.uint8)
def_gen.integers(0, 256, dtype=np.uint8)
def_gen.integers(255, dtype=np.uint8, endpoint=True)
def_gen.integers(0, 255, dtype=np.uint8, endpoint=True)
def_gen.integers(I_u1_low_like, 255, dtype=np.uint8, endpoint=True)
def_gen.integers(I_u1_high_open, dtype=np.uint8)
def_gen.integers(I_u1_low, I_u1_high_open, dtype=np.uint8)
def_gen.integers(0, I_u1_high_open, dtype=np.uint8)
def_gen.integers(I_u1_high_closed, dtype=np.uint8, endpoint=True)
def_gen.integers(I_u1_low, I_u1_high_closed, dtype=np.uint8, endpoint=True)
def_gen.integers(0, I_u1_high_closed, dtype=np.uint8, endpoint=True)

I_u2_low: np.ndarray[Any, np.dtype[np.uint16]] = np.array([0], dtype=np.uint16)
I_u2_low_like: list[int] = [0]
I_u2_high_open: np.ndarray[Any, np.dtype[np.uint16]] = np.array([65535], dtype=np.uint16)
I_u2_high_closed: np.ndarray[Any, np.dtype[np.uint16]] = np.array([65535], dtype=np.uint16)

def_gen.integers(65536, dtype="u2")
def_gen.integers(0, 65536, dtype="u2")
def_gen.integers(65535, dtype="u2", endpoint=True)
def_gen.integers(0, 65535, dtype="u2", endpoint=True)
def_gen.integers(I_u2_low_like, 65535, dtype="u2", endpoint=True)
def_gen.integers(I_u2_high_open, dtype="u2")
def_gen.integers(I_u2_low, I_u2_high_open, dtype="u2")
def_gen.integers(0, I_u2_high_open, dtype="u2")
def_gen.integers(I_u2_high_closed, dtype="u2", endpoint=True)
def_gen.integers(I_u2_low, I_u2_high_closed, dtype="u2", endpoint=True)
def_gen.integers(0, I_u2_high_closed, dtype="u2", endpoint=True)

def_gen.integers(65536, dtype="uint16")
def_gen.integers(0, 65536, dtype="uint16")
def_gen.integers(65535, dtype="uint16", endpoint=True)
def_gen.integers(0, 65535, dtype="uint16", endpoint=True)
def_gen.integers(I_u2_low_like, 65535, dtype="uint16", endpoint=True)
def_gen.integers(I_u2_high_open, dtype="uint16")
def_gen.integers(I_u2_low, I_u2_high_open, dtype="uint16")
def_gen.integers(0, I_u2_high_open, dtype="uint16")
def_gen.integers(I_u2_high_closed, dtype="uint16", endpoint=True)
def_gen.integers(I_u2_low, I_u2_high_closed, dtype="uint16", endpoint=True)
def_gen.integers(0, I_u2_high_closed, dtype="uint16", endpoint=True)

def_gen.integers(65536, dtype=np.uint16)
def_gen.integers(0, 65536, dtype=np.uint16)
def_gen.integers(65535, dtype=np.uint16, endpoint=True)
def_gen.integers(0, 65535, dtype=np.uint16, endpoint=True)
def_gen.integers(I_u2_low_like, 65535, dtype=np.uint16, endpoint=True)
def_gen.integers(I_u2_high_open, dtype=np.uint16)
def_gen.integers(I_u2_low, I_u2_high_open, dtype=np.uint16)
def_gen.integers(0, I_u2_high_open, dtype=np.uint16)
def_gen.integers(I_u2_high_closed, dtype=np.uint16, endpoint=True)
def_gen.integers(I_u2_low, I_u2_high_closed, dtype=np.uint16, endpoint=True)
def_gen.integers(0, I_u2_high_closed, dtype=np.uint16, endpoint=True)

I_u4_low: np.ndarray[Any, np.dtype[np.uint32]] = np.array([0], dtype=np.uint32)
I_u4_low_like: list[int] = [0]
I_u4_high_open: np.ndarray[Any, np.dtype[np.uint32]] = np.array([4294967295], dtype=np.uint32)
I_u4_high_closed: np.ndarray[Any, np.dtype[np.uint32]] = np.array([4294967295], dtype=np.uint32)

def_gen.integers(4294967296, dtype="u4")
def_gen.integers(0, 4294967296, dtype="u4")
def_gen.integers(4294967295, dtype="u4", endpoint=True)
def_gen.integers(0, 4294967295, dtype="u4", endpoint=True)
def_gen.integers(I_u4_low_like, 4294967295, dtype="u4", endpoint=True)
def_gen.integers(I_u4_high_open, dtype="u4")
def_gen.integers(I_u4_low, I_u4_high_open, dtype="u4")
def_gen.integers(0, I_u4_high_open, dtype="u4")
def_gen.integers(I_u4_high_closed, dtype="u4", endpoint=True)
def_gen.integers(I_u4_low, I_u4_high_closed, dtype="u4", endpoint=True)
def_gen.integers(0, I_u4_high_closed, dtype="u4", endpoint=True)

def_gen.integers(4294967296, dtype="uint32")
def_gen.integers(0, 4294967296, dtype="uint32")
def_gen.integers(4294967295, dtype="uint32", endpoint=True)
def_gen.integers(0, 4294967295, dtype="uint32", endpoint=True)
def_gen.integers(I_u4_low_like, 4294967295, dtype="uint32", endpoint=True)
def_gen.integers(I_u4_high_open, dtype="uint32")
def_gen.integers(I_u4_low, I_u4_high_open, dtype="uint32")
def_gen.integers(0, I_u4_high_open, dtype="uint32")
def_gen.integers(I_u4_high_closed, dtype="uint32", endpoint=True)
def_gen.integers(I_u4_low, I_u4_high_closed, dtype="uint32", endpoint=True)
def_gen.integers(0, I_u4_high_closed, dtype="uint32", endpoint=True)

def_gen.integers(4294967296, dtype=np.uint32)
def_gen.integers(0, 4294967296, dtype=np.uint32)
def_gen.integers(4294967295, dtype=np.uint32, endpoint=True)
def_gen.integers(0, 4294967295, dtype=np.uint32, endpoint=True)
def_gen.integers(I_u4_low_like, 4294967295, dtype=np.uint32, endpoint=True)
def_gen.integers(I_u4_high_open, dtype=np.uint32)
def_gen.integers(I_u4_low, I_u4_high_open, dtype=np.uint32)
def_gen.integers(0, I_u4_high_open, dtype=np.uint32)
def_gen.integers(I_u4_high_closed, dtype=np.uint32, endpoint=True)
def_gen.integers(I_u4_low, I_u4_high_closed, dtype=np.uint32, endpoint=True)
def_gen.integers(0, I_u4_high_closed, dtype=np.uint32, endpoint=True)

I_u8_low: np.ndarray[Any, np.dtype[np.uint64]] = np.array([0], dtype=np.uint64)
I_u8_low_like: list[int] = [0]
I_u8_high_open: np.ndarray[Any, np.dtype[np.uint64]] = np.array([18446744073709551615], dtype=np.uint64)
I_u8_high_closed: np.ndarray[Any, np.dtype[np.uint64]] = np.array([18446744073709551615], dtype=np.uint64)

def_gen.integers(18446744073709551616, dtype="u8")
def_gen.integers(0, 18446744073709551616, dtype="u8")
def_gen.integers(18446744073709551615, dtype="u8", endpoint=True)
def_gen.integers(0, 18446744073709551615, dtype="u8", endpoint=True)
def_gen.integers(I_u8_low_like, 18446744073709551615, dtype="u8", endpoint=True)
def_gen.integers(I_u8_high_open, dtype="u8")
def_gen.integers(I_u8_low, I_u8_high_open, dtype="u8")
def_gen.integers(0, I_u8_high_open, dtype="u8")
def_gen.integers(I_u8_high_closed, dtype="u8", endpoint=True)
def_gen.integers(I_u8_low, I_u8_high_closed, dtype="u8", endpoint=True)
def_gen.integers(0, I_u8_high_closed, dtype="u8", endpoint=True)

def_gen.integers(18446744073709551616, dtype="uint64")
def_gen.integers(0, 18446744073709551616, dtype="uint64")
def_gen.integers(18446744073709551615, dtype="uint64", endpoint=True)
def_gen.integers(0, 18446744073709551615, dtype="uint64", endpoint=True)
def_gen.integers(I_u8_low_like, 18446744073709551615, dtype="uint64", endpoint=True)
def_gen.integers(I_u8_high_open, dtype="uint64")
def_gen.integers(I_u8_low, I_u8_high_open, dtype="uint64")
def_gen.integers(0, I_u8_high_open, dtype="uint64")
def_gen.integers(I_u8_high_closed, dtype="uint64", endpoint=True)
def_gen.integers(I_u8_low, I_u8_high_closed, dtype="uint64", endpoint=True)
def_gen.integers(0, I_u8_high_closed, dtype="uint64", endpoint=True)

def_gen.integers(18446744073709551616, dtype=np.uint64)
def_gen.integers(0, 18446744073709551616, dtype=np.uint64)
def_gen.integers(18446744073709551615, dtype=np.uint64, endpoint=True)
def_gen.integers(0, 18446744073709551615, dtype=np.uint64, endpoint=True)
def_gen.integers(I_u8_low_like, 18446744073709551615, dtype=np.uint64, endpoint=True)
def_gen.integers(I_u8_high_open, dtype=np.uint64)
def_gen.integers(I_u8_low, I_u8_high_open, dtype=np.uint64)
def_gen.integers(0, I_u8_high_open, dtype=np.uint64)
def_gen.integers(I_u8_high_closed, dtype=np.uint64, endpoint=True)
def_gen.integers(I_u8_low, I_u8_high_closed, dtype=np.uint64, endpoint=True)
def_gen.integers(0, I_u8_high_closed, dtype=np.uint64, endpoint=True)

I_i1_low: np.ndarray[Any, np.dtype[np.int8]] = np.array([-128], dtype=np.int8)
I_i1_low_like: list[int] = [-128]
I_i1_high_open: np.ndarray[Any, np.dtype[np.int8]] = np.array([127], dtype=np.int8)
I_i1_high_closed: np.ndarray[Any, np.dtype[np.int8]] = np.array([127], dtype=np.int8)

def_gen.integers(128, dtype="i1")
def_gen.integers(-128, 128, dtype="i1")
def_gen.integers(127, dtype="i1", endpoint=True)
def_gen.integers(-128, 127, dtype="i1", endpoint=True)
def_gen.integers(I_i1_low_like, 127, dtype="i1", endpoint=True)
def_gen.integers(I_i1_high_open, dtype="i1")
def_gen.integers(I_i1_low, I_i1_high_open, dtype="i1")
def_gen.integers(-128, I_i1_high_open, dtype="i1")
def_gen.integers(I_i1_high_closed, dtype="i1", endpoint=True)
def_gen.integers(I_i1_low, I_i1_high_closed, dtype="i1", endpoint=True)
def_gen.integers(-128, I_i1_high_closed, dtype="i1", endpoint=True)

def_gen.integers(128, dtype="int8")
def_gen.integers(-128, 128, dtype="int8")
def_gen.integers(127, dtype="int8", endpoint=True)
def_gen.integers(-128, 127, dtype="int8", endpoint=True)
def_gen.integers(I_i1_low_like, 127, dtype="int8", endpoint=True)
def_gen.integers(I_i1_high_open, dtype="int8")
def_gen.integers(I_i1_low, I_i1_high_open, dtype="int8")
def_gen.integers(-128, I_i1_high_open, dtype="int8")
def_gen.integers(I_i1_high_closed, dtype="int8", endpoint=True)
def_gen.integers(I_i1_low, I_i1_high_closed, dtype="int8", endpoint=True)
def_gen.integers(-128, I_i1_high_closed, dtype="int8", endpoint=True)

def_gen.integers(128, dtype=np.int8)
def_gen.integers(-128, 128, dtype=np.int8)
def_gen.integers(127, dtype=np.int8, endpoint=True)
def_gen.integers(-128, 127, dtype=np.int8, endpoint=True)
def_gen.integers(I_i1_low_like, 127, dtype=np.int8, endpoint=True)
def_gen.integers(I_i1_high_open, dtype=np.int8)
def_gen.integers(I_i1_low, I_i1_high_open, dtype=np.int8)
def_gen.integers(-128, I_i1_high_open, dtype=np.int8)
def_gen.integers(I_i1_high_closed, dtype=np.int8, endpoint=True)
def_gen.integers(I_i1_low, I_i1_high_closed, dtype=np.int8, endpoint=True)
def_gen.integers(-128, I_i1_high_closed, dtype=np.int8, endpoint=True)

I_i2_low: np.ndarray[Any, np.dtype[np.int16]] = np.array([-32768], dtype=np.int16)
I_i2_low_like: list[int] = [-32768]
I_i2_high_open: np.ndarray[Any, np.dtype[np.int16]] = np.array([32767], dtype=np.int16)
I_i2_high_closed: np.ndarray[Any, np.dtype[np.int16]] = np.array([32767], dtype=np.int16)

def_gen.integers(32768, dtype="i2")
def_gen.integers(-32768, 32768, dtype="i2")
def_gen.integers(32767, dtype="i2", endpoint=True)
def_gen.integers(-32768, 32767, dtype="i2", endpoint=True)
def_gen.integers(I_i2_low_like, 32767, dtype="i2", endpoint=True)
def_gen.integers(I_i2_high_open, dtype="i2")
def_gen.integers(I_i2_low, I_i2_high_open, dtype="i2")
def_gen.integers(-32768, I_i2_high_open, dtype="i2")
def_gen.integers(I_i2_high_closed, dtype="i2", endpoint=True)
def_gen.integers(I_i2_low, I_i2_high_closed, dtype="i2", endpoint=True)
def_gen.integers(-32768, I_i2_high_closed, dtype="i2", endpoint=True)

def_gen.integers(32768, dtype="int16")
def_gen.integers(-32768, 32768, dtype="int16")
def_gen.integers(32767, dtype="int16", endpoint=True)
def_gen.integers(-32768, 32767, dtype="int16", endpoint=True)
def_gen.integers(I_i2_low_like, 32767, dtype="int16", endpoint=True)
def_gen.integers(I_i2_high_open, dtype="int16")
def_gen.integers(I_i2_low, I_i2_high_open, dtype="int16")
def_gen.integers(-32768, I_i2_high_open, dtype="int16")
def_gen.integers(I_i2_high_closed, dtype="int16", endpoint=True)
def_gen.integers(I_i2_low, I_i2_high_closed, dtype="int16", endpoint=True)
def_gen.integers(-32768, I_i2_high_closed, dtype="int16", endpoint=True)

def_gen.integers(32768, dtype=np.int16)
def_gen.integers(-32768, 32768, dtype=np.int16)
def_gen.integers(32767, dtype=np.int16, endpoint=True)
def_gen.integers(-32768, 32767, dtype=np.int16, endpoint=True)
def_gen.integers(I_i2_low_like, 32767, dtype=np.int16, endpoint=True)
def_gen.integers(I_i2_high_open, dtype=np.int16)
def_gen.integers(I_i2_low, I_i2_high_open, dtype=np.int16)
def_gen.integers(-32768, I_i2_high_open, dtype=np.int16)
def_gen.integers(I_i2_high_closed, dtype=np.int16, endpoint=True)
def_gen.integers(I_i2_low, I_i2_high_closed, dtype=np.int16, endpoint=True)
def_gen.integers(-32768, I_i2_high_closed, dtype=np.int16, endpoint=True)

I_i4_low: np.ndarray[Any, np.dtype[np.int32]] = np.array([-2147483648], dtype=np.int32)
I_i4_low_like: list[int] = [-2147483648]
I_i4_high_open: np.ndarray[Any, np.dtype[np.int32]] = np.array([2147483647], dtype=np.int32)
I_i4_high_closed: np.ndarray[Any, np.dtype[np.int32]] = np.array([2147483647], dtype=np.int32)

def_gen.integers(2147483648, dtype="i4")
def_gen.integers(-2147483648, 2147483648, dtype="i4")
def_gen.integers(2147483647, dtype="i4", endpoint=True)
def_gen.integers(-2147483648, 2147483647, dtype="i4", endpoint=True)
def_gen.integers(I_i4_low_like, 2147483647, dtype="i4", endpoint=True)
def_gen.integers(I_i4_high_open, dtype="i4")
def_gen.integers(I_i4_low, I_i4_high_open, dtype="i4")
def_gen.integers(-2147483648, I_i4_high_open, dtype="i4")
def_gen.integers(I_i4_high_closed, dtype="i4", endpoint=True)
def_gen.integers(I_i4_low, I_i4_high_closed, dtype="i4", endpoint=True)
def_gen.integers(-2147483648, I_i4_high_closed, dtype="i4", endpoint=True)

def_gen.integers(2147483648, dtype="int32")
def_gen.integers(-2147483648, 2147483648, dtype="int32")
def_gen.integers(2147483647, dtype="int32", endpoint=True)
def_gen.integers(-2147483648, 2147483647, dtype="int32", endpoint=True)
def_gen.integers(I_i4_low_like, 2147483647, dtype="int32", endpoint=True)
def_gen.integers(I_i4_high_open, dtype="int32")
def_gen.integers(I_i4_low, I_i4_high_open, dtype="int32")
def_gen.integers(-2147483648, I_i4_high_open, dtype="int32")
def_gen.integers(I_i4_high_closed, dtype="int32", endpoint=True)
def_gen.integers(I_i4_low, I_i4_high_closed, dtype="int32", endpoint=True)
def_gen.integers(-2147483648, I_i4_high_closed, dtype="int32", endpoint=True)

def_gen.integers(2147483648, dtype=np.int32)
def_gen.integers(-2147483648, 2147483648, dtype=np.int32)
def_gen.integers(2147483647, dtype=np.int32, endpoint=True)
def_gen.integers(-2147483648, 2147483647, dtype=np.int32, endpoint=True)
def_gen.integers(I_i4_low_like, 2147483647, dtype=np.int32, endpoint=True)
def_gen.integers(I_i4_high_open, dtype=np.int32)
def_gen.integers(I_i4_low, I_i4_high_open, dtype=np.int32)
def_gen.integers(-2147483648, I_i4_high_open, dtype=np.int32)
def_gen.integers(I_i4_high_closed, dtype=np.int32, endpoint=True)
def_gen.integers(I_i4_low, I_i4_high_closed, dtype=np.int32, endpoint=True)
def_gen.integers(-2147483648, I_i4_high_closed, dtype=np.int32, endpoint=True)

I_i8_low: np.ndarray[Any, np.dtype[np.int64]] = np.array([-9223372036854775808], dtype=np.int64)
I_i8_low_like: list[int] = [-9223372036854775808]
I_i8_high_open: np.ndarray[Any, np.dtype[np.int64]] = np.array([9223372036854775807], dtype=np.int64)
I_i8_high_closed: np.ndarray[Any, np.dtype[np.int64]] = np.array([9223372036854775807], dtype=np.int64)

def_gen.integers(9223372036854775808, dtype="i8")
def_gen.integers(-9223372036854775808, 9223372036854775808, dtype="i8")
def_gen.integers(9223372036854775807, dtype="i8", endpoint=True)
def_gen.integers(-9223372036854775808, 9223372036854775807, dtype="i8", endpoint=True)
def_gen.integers(I_i8_low_like, 9223372036854775807, dtype="i8", endpoint=True)
def_gen.integers(I_i8_high_open, dtype="i8")
def_gen.integers(I_i8_low, I_i8_high_open, dtype="i8")
def_gen.integers(-9223372036854775808, I_i8_high_open, dtype="i8")
def_gen.integers(I_i8_high_closed, dtype="i8", endpoint=True)
def_gen.integers(I_i8_low, I_i8_high_closed, dtype="i8", endpoint=True)
def_gen.integers(-9223372036854775808, I_i8_high_closed, dtype="i8", endpoint=True)

def_gen.integers(9223372036854775808, dtype="int64")
def_gen.integers(-9223372036854775808, 9223372036854775808, dtype="int64")
def_gen.integers(9223372036854775807, dtype="int64", endpoint=True)
def_gen.integers(-9223372036854775808, 9223372036854775807, dtype="int64", endpoint=True)
def_gen.integers(I_i8_low_like, 9223372036854775807, dtype="int64", endpoint=True)
def_gen.integers(I_i8_high_open, dtype="int64")
def_gen.integers(I_i8_low, I_i8_high_open, dtype="int64")
def_gen.integers(-9223372036854775808, I_i8_high_open, dtype="int64")
def_gen.integers(I_i8_high_closed, dtype="int64", endpoint=True)
def_gen.integers(I_i8_low, I_i8_high_closed, dtype="int64", endpoint=True)
def_gen.integers(-9223372036854775808, I_i8_high_closed, dtype="int64", endpoint=True)

def_gen.integers(9223372036854775808, dtype=np.int64)
def_gen.integers(-9223372036854775808, 9223372036854775808, dtype=np.int64)
def_gen.integers(9223372036854775807, dtype=np.int64, endpoint=True)
def_gen.integers(-9223372036854775808, 9223372036854775807, dtype=np.int64, endpoint=True)
def_gen.integers(I_i8_low_like, 9223372036854775807, dtype=np.int64, endpoint=True)
def_gen.integers(I_i8_high_open, dtype=np.int64)
def_gen.integers(I_i8_low, I_i8_high_open, dtype=np.int64)
def_gen.integers(-9223372036854775808, I_i8_high_open, dtype=np.int64)
def_gen.integers(I_i8_high_closed, dtype=np.int64, endpoint=True)
def_gen.integers(I_i8_low, I_i8_high_closed, dtype=np.int64, endpoint=True)
def_gen.integers(-9223372036854775808, I_i8_high_closed, dtype=np.int64, endpoint=True)


def_gen.bit_generator

def_gen.bytes(2)

def_gen.choice(5)
def_gen.choice(5, 3)
def_gen.choice(5, 3, replace=True)
def_gen.choice(5, 3, p=[1 / 5] * 5)
def_gen.choice(5, 3, p=[1 / 5] * 5, replace=False)

def_gen.choice(["pooh", "rabbit", "piglet", "Christopher"])
def_gen.choice(["pooh", "rabbit", "piglet", "Christopher"], 3)
def_gen.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, p=[1 / 4] * 4)
def_gen.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, replace=True)
def_gen.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, replace=False, p=np.array([1 / 8, 1 / 8, 1 / 2, 1 / 4]))

def_gen.dirichlet([0.5, 0.5])
def_gen.dirichlet(np.array([0.5, 0.5]))
def_gen.dirichlet(np.array([0.5, 0.5]), size=3)

def_gen.multinomial(20, [1 / 6.0] * 6)
def_gen.multinomial(20, np.array([0.5, 0.5]))
def_gen.multinomial(20, [1 / 6.0] * 6, size=2)
def_gen.multinomial([[10], [20]], [1 / 6.0] * 6, size=(2, 2))
def_gen.multinomial(np.array([[10], [20]]), np.array([0.5, 0.5]), size=(2, 2))

def_gen.multivariate_hypergeometric([3, 5, 7], 2)
def_gen.multivariate_hypergeometric(np.array([3, 5, 7]), 2)
def_gen.multivariate_hypergeometric(np.array([3, 5, 7]), 2, size=4)
def_gen.multivariate_hypergeometric(np.array([3, 5, 7]), 2, size=(4, 7))
def_gen.multivariate_hypergeometric([3, 5, 7], 2, method="count")
def_gen.multivariate_hypergeometric(np.array([3, 5, 7]), 2, method="marginals")

def_gen.multivariate_normal([0.0], [[1.0]])
def_gen.multivariate_normal([0.0], np.array([[1.0]]))
def_gen.multivariate_normal(np.array([0.0]), [[1.0]])
def_gen.multivariate_normal([0.0], np.array([[1.0]]))

def_gen.permutation(10)
def_gen.permutation([1, 2, 3, 4])
def_gen.permutation(np.array([1, 2, 3, 4]))
def_gen.permutation(D_2D, axis=1)
def_gen.permuted(D_2D)
def_gen.permuted(D_2D_like)
def_gen.permuted(D_2D, axis=1)
def_gen.permuted(D_2D, out=D_2D)
def_gen.permuted(D_2D_like, out=D_2D)
def_gen.permuted(D_2D_like, out=D_2D)
def_gen.permuted(D_2D, axis=1, out=D_2D)

def_gen.shuffle(np.arange(10))
def_gen.shuffle([1, 2, 3, 4, 5])
def_gen.shuffle(D_2D, axis=1)

def_gen.__str__()
def_gen.__repr__()
def_gen.__setstate__(dict(def_gen.bit_generator.state))

# RandomState
random_st: np.random.RandomState = np.random.RandomState()

random_st.standard_normal()
random_st.standard_normal(size=None)
random_st.standard_normal(size=1)

random_st.random()
random_st.random(size=None)
random_st.random(size=1)

random_st.standard_cauchy()
random_st.standard_cauchy(size=None)
random_st.standard_cauchy(size=1)

random_st.standard_exponential()
random_st.standard_exponential(size=None)
random_st.standard_exponential(size=1)

random_st.zipf(1.5)
random_st.zipf(1.5, size=None)
random_st.zipf(1.5, size=1)
random_st.zipf(D_arr_1p5)
random_st.zipf(D_arr_1p5, size=1)
random_st.zipf(D_arr_like_1p5)
random_st.zipf(D_arr_like_1p5, size=1)

random_st.weibull(0.5)
random_st.weibull(0.5, size=None)
random_st.weibull(0.5, size=1)
random_st.weibull(D_arr_0p5)
random_st.weibull(D_arr_0p5, size=1)
random_st.weibull(D_arr_like_0p5)
random_st.weibull(D_arr_like_0p5, size=1)

random_st.standard_t(0.5)
random_st.standard_t(0.5, size=None)
random_st.standard_t(0.5, size=1)
random_st.standard_t(D_arr_0p5)
random_st.standard_t(D_arr_0p5, size=1)
random_st.standard_t(D_arr_like_0p5)
random_st.standard_t(D_arr_like_0p5, size=1)

random_st.poisson(0.5)
random_st.poisson(0.5, size=None)
random_st.poisson(0.5, size=1)
random_st.poisson(D_arr_0p5)
random_st.poisson(D_arr_0p5, size=1)
random_st.poisson(D_arr_like_0p5)
random_st.poisson(D_arr_like_0p5, size=1)

random_st.power(0.5)
random_st.power(0.5, size=None)
random_st.power(0.5, size=1)
random_st.power(D_arr_0p5)
random_st.power(D_arr_0p5, size=1)
random_st.power(D_arr_like_0p5)
random_st.power(D_arr_like_0p5, size=1)

random_st.pareto(0.5)
random_st.pareto(0.5, size=None)
random_st.pareto(0.5, size=1)
random_st.pareto(D_arr_0p5)
random_st.pareto(D_arr_0p5, size=1)
random_st.pareto(D_arr_like_0p5)
random_st.pareto(D_arr_like_0p5, size=1)

random_st.chisquare(0.5)
random_st.chisquare(0.5, size=None)
random_st.chisquare(0.5, size=1)
random_st.chisquare(D_arr_0p5)
random_st.chisquare(D_arr_0p5, size=1)
random_st.chisquare(D_arr_like_0p5)
random_st.chisquare(D_arr_like_0p5, size=1)

random_st.exponential(0.5)
random_st.exponential(0.5, size=None)
random_st.exponential(0.5, size=1)
random_st.exponential(D_arr_0p5)
random_st.exponential(D_arr_0p5, size=1)
random_st.exponential(D_arr_like_0p5)
random_st.exponential(D_arr_like_0p5, size=1)

random_st.geometric(0.5)
random_st.geometric(0.5, size=None)
random_st.geometric(0.5, size=1)
random_st.geometric(D_arr_0p5)
random_st.geometric(D_arr_0p5, size=1)
random_st.geometric(D_arr_like_0p5)
random_st.geometric(D_arr_like_0p5, size=1)

random_st.logseries(0.5)
random_st.logseries(0.5, size=None)
random_st.logseries(0.5, size=1)
random_st.logseries(D_arr_0p5)
random_st.logseries(D_arr_0p5, size=1)
random_st.logseries(D_arr_like_0p5)
random_st.logseries(D_arr_like_0p5, size=1)

random_st.rayleigh(0.5)
random_st.rayleigh(0.5, size=None)
random_st.rayleigh(0.5, size=1)
random_st.rayleigh(D_arr_0p5)
random_st.rayleigh(D_arr_0p5, size=1)
random_st.rayleigh(D_arr_like_0p5)
random_st.rayleigh(D_arr_like_0p5, size=1)

random_st.standard_gamma(0.5)
random_st.standard_gamma(0.5, size=None)
random_st.standard_gamma(0.5, size=1)
random_st.standard_gamma(D_arr_0p5)
random_st.standard_gamma(D_arr_0p5, size=1)
random_st.standard_gamma(D_arr_like_0p5)
random_st.standard_gamma(D_arr_like_0p5, size=1)
random_st.standard_gamma(D_arr_like_0p5, size=1)

random_st.vonmises(0.5, 0.5)
random_st.vonmises(0.5, 0.5, size=None)
random_st.vonmises(0.5, 0.5, size=1)
random_st.vonmises(D_arr_0p5, 0.5)
random_st.vonmises(0.5, D_arr_0p5)
random_st.vonmises(D_arr_0p5, 0.5, size=1)
random_st.vonmises(0.5, D_arr_0p5, size=1)
random_st.vonmises(D_arr_like_0p5, 0.5)
random_st.vonmises(0.5, D_arr_like_0p5)
random_st.vonmises(D_arr_0p5, D_arr_0p5)
random_st.vonmises(D_arr_like_0p5, D_arr_like_0p5)
random_st.vonmises(D_arr_0p5, D_arr_0p5, size=1)
random_st.vonmises(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.wald(0.5, 0.5)
random_st.wald(0.5, 0.5, size=None)
random_st.wald(0.5, 0.5, size=1)
random_st.wald(D_arr_0p5, 0.5)
random_st.wald(0.5, D_arr_0p5)
random_st.wald(D_arr_0p5, 0.5, size=1)
random_st.wald(0.5, D_arr_0p5, size=1)
random_st.wald(D_arr_like_0p5, 0.5)
random_st.wald(0.5, D_arr_like_0p5)
random_st.wald(D_arr_0p5, D_arr_0p5)
random_st.wald(D_arr_like_0p5, D_arr_like_0p5)
random_st.wald(D_arr_0p5, D_arr_0p5, size=1)
random_st.wald(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.uniform(0.5, 0.5)
random_st.uniform(0.5, 0.5, size=None)
random_st.uniform(0.5, 0.5, size=1)
random_st.uniform(D_arr_0p5, 0.5)
random_st.uniform(0.5, D_arr_0p5)
random_st.uniform(D_arr_0p5, 0.5, size=1)
random_st.uniform(0.5, D_arr_0p5, size=1)
random_st.uniform(D_arr_like_0p5, 0.5)
random_st.uniform(0.5, D_arr_like_0p5)
random_st.uniform(D_arr_0p5, D_arr_0p5)
random_st.uniform(D_arr_like_0p5, D_arr_like_0p5)
random_st.uniform(D_arr_0p5, D_arr_0p5, size=1)
random_st.uniform(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.beta(0.5, 0.5)
random_st.beta(0.5, 0.5, size=None)
random_st.beta(0.5, 0.5, size=1)
random_st.beta(D_arr_0p5, 0.5)
random_st.beta(0.5, D_arr_0p5)
random_st.beta(D_arr_0p5, 0.5, size=1)
random_st.beta(0.5, D_arr_0p5, size=1)
random_st.beta(D_arr_like_0p5, 0.5)
random_st.beta(0.5, D_arr_like_0p5)
random_st.beta(D_arr_0p5, D_arr_0p5)
random_st.beta(D_arr_like_0p5, D_arr_like_0p5)
random_st.beta(D_arr_0p5, D_arr_0p5, size=1)
random_st.beta(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.f(0.5, 0.5)
random_st.f(0.5, 0.5, size=None)
random_st.f(0.5, 0.5, size=1)
random_st.f(D_arr_0p5, 0.5)
random_st.f(0.5, D_arr_0p5)
random_st.f(D_arr_0p5, 0.5, size=1)
random_st.f(0.5, D_arr_0p5, size=1)
random_st.f(D_arr_like_0p5, 0.5)
random_st.f(0.5, D_arr_like_0p5)
random_st.f(D_arr_0p5, D_arr_0p5)
random_st.f(D_arr_like_0p5, D_arr_like_0p5)
random_st.f(D_arr_0p5, D_arr_0p5, size=1)
random_st.f(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.gamma(0.5, 0.5)
random_st.gamma(0.5, 0.5, size=None)
random_st.gamma(0.5, 0.5, size=1)
random_st.gamma(D_arr_0p5, 0.5)
random_st.gamma(0.5, D_arr_0p5)
random_st.gamma(D_arr_0p5, 0.5, size=1)
random_st.gamma(0.5, D_arr_0p5, size=1)
random_st.gamma(D_arr_like_0p5, 0.5)
random_st.gamma(0.5, D_arr_like_0p5)
random_st.gamma(D_arr_0p5, D_arr_0p5)
random_st.gamma(D_arr_like_0p5, D_arr_like_0p5)
random_st.gamma(D_arr_0p5, D_arr_0p5, size=1)
random_st.gamma(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.gumbel(0.5, 0.5)
random_st.gumbel(0.5, 0.5, size=None)
random_st.gumbel(0.5, 0.5, size=1)
random_st.gumbel(D_arr_0p5, 0.5)
random_st.gumbel(0.5, D_arr_0p5)
random_st.gumbel(D_arr_0p5, 0.5, size=1)
random_st.gumbel(0.5, D_arr_0p5, size=1)
random_st.gumbel(D_arr_like_0p5, 0.5)
random_st.gumbel(0.5, D_arr_like_0p5)
random_st.gumbel(D_arr_0p5, D_arr_0p5)
random_st.gumbel(D_arr_like_0p5, D_arr_like_0p5)
random_st.gumbel(D_arr_0p5, D_arr_0p5, size=1)
random_st.gumbel(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.laplace(0.5, 0.5)
random_st.laplace(0.5, 0.5, size=None)
random_st.laplace(0.5, 0.5, size=1)
random_st.laplace(D_arr_0p5, 0.5)
random_st.laplace(0.5, D_arr_0p5)
random_st.laplace(D_arr_0p5, 0.5, size=1)
random_st.laplace(0.5, D_arr_0p5, size=1)
random_st.laplace(D_arr_like_0p5, 0.5)
random_st.laplace(0.5, D_arr_like_0p5)
random_st.laplace(D_arr_0p5, D_arr_0p5)
random_st.laplace(D_arr_like_0p5, D_arr_like_0p5)
random_st.laplace(D_arr_0p5, D_arr_0p5, size=1)
random_st.laplace(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.logistic(0.5, 0.5)
random_st.logistic(0.5, 0.5, size=None)
random_st.logistic(0.5, 0.5, size=1)
random_st.logistic(D_arr_0p5, 0.5)
random_st.logistic(0.5, D_arr_0p5)
random_st.logistic(D_arr_0p5, 0.5, size=1)
random_st.logistic(0.5, D_arr_0p5, size=1)
random_st.logistic(D_arr_like_0p5, 0.5)
random_st.logistic(0.5, D_arr_like_0p5)
random_st.logistic(D_arr_0p5, D_arr_0p5)
random_st.logistic(D_arr_like_0p5, D_arr_like_0p5)
random_st.logistic(D_arr_0p5, D_arr_0p5, size=1)
random_st.logistic(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.lognormal(0.5, 0.5)
random_st.lognormal(0.5, 0.5, size=None)
random_st.lognormal(0.5, 0.5, size=1)
random_st.lognormal(D_arr_0p5, 0.5)
random_st.lognormal(0.5, D_arr_0p5)
random_st.lognormal(D_arr_0p5, 0.5, size=1)
random_st.lognormal(0.5, D_arr_0p5, size=1)
random_st.lognormal(D_arr_like_0p5, 0.5)
random_st.lognormal(0.5, D_arr_like_0p5)
random_st.lognormal(D_arr_0p5, D_arr_0p5)
random_st.lognormal(D_arr_like_0p5, D_arr_like_0p5)
random_st.lognormal(D_arr_0p5, D_arr_0p5, size=1)
random_st.lognormal(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.noncentral_chisquare(0.5, 0.5)
random_st.noncentral_chisquare(0.5, 0.5, size=None)
random_st.noncentral_chisquare(0.5, 0.5, size=1)
random_st.noncentral_chisquare(D_arr_0p5, 0.5)
random_st.noncentral_chisquare(0.5, D_arr_0p5)
random_st.noncentral_chisquare(D_arr_0p5, 0.5, size=1)
random_st.noncentral_chisquare(0.5, D_arr_0p5, size=1)
random_st.noncentral_chisquare(D_arr_like_0p5, 0.5)
random_st.noncentral_chisquare(0.5, D_arr_like_0p5)
random_st.noncentral_chisquare(D_arr_0p5, D_arr_0p5)
random_st.noncentral_chisquare(D_arr_like_0p5, D_arr_like_0p5)
random_st.noncentral_chisquare(D_arr_0p5, D_arr_0p5, size=1)
random_st.noncentral_chisquare(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.normal(0.5, 0.5)
random_st.normal(0.5, 0.5, size=None)
random_st.normal(0.5, 0.5, size=1)
random_st.normal(D_arr_0p5, 0.5)
random_st.normal(0.5, D_arr_0p5)
random_st.normal(D_arr_0p5, 0.5, size=1)
random_st.normal(0.5, D_arr_0p5, size=1)
random_st.normal(D_arr_like_0p5, 0.5)
random_st.normal(0.5, D_arr_like_0p5)
random_st.normal(D_arr_0p5, D_arr_0p5)
random_st.normal(D_arr_like_0p5, D_arr_like_0p5)
random_st.normal(D_arr_0p5, D_arr_0p5, size=1)
random_st.normal(D_arr_like_0p5, D_arr_like_0p5, size=1)

random_st.triangular(0.1, 0.5, 0.9)
random_st.triangular(0.1, 0.5, 0.9, size=None)
random_st.triangular(0.1, 0.5, 0.9, size=1)
random_st.triangular(D_arr_0p1, 0.5, 0.9)
random_st.triangular(0.1, D_arr_0p5, 0.9)
random_st.triangular(D_arr_0p1, 0.5, D_arr_like_0p9, size=1)
random_st.triangular(0.1, D_arr_0p5, 0.9, size=1)
random_st.triangular(D_arr_like_0p1, 0.5, D_arr_0p9)
random_st.triangular(0.5, D_arr_like_0p5, 0.9)
random_st.triangular(D_arr_0p1, D_arr_0p5, 0.9)
random_st.triangular(D_arr_like_0p1, D_arr_like_0p5, 0.9)
random_st.triangular(D_arr_0p1, D_arr_0p5, D_arr_0p9, size=1)
random_st.triangular(D_arr_like_0p1, D_arr_like_0p5, D_arr_like_0p9, size=1)

random_st.noncentral_f(0.1, 0.5, 0.9)
random_st.noncentral_f(0.1, 0.5, 0.9, size=None)
random_st.noncentral_f(0.1, 0.5, 0.9, size=1)
random_st.noncentral_f(D_arr_0p1, 0.5, 0.9)
random_st.noncentral_f(0.1, D_arr_0p5, 0.9)
random_st.noncentral_f(D_arr_0p1, 0.5, D_arr_like_0p9, size=1)
random_st.noncentral_f(0.1, D_arr_0p5, 0.9, size=1)
random_st.noncentral_f(D_arr_like_0p1, 0.5, D_arr_0p9)
random_st.noncentral_f(0.5, D_arr_like_0p5, 0.9)
random_st.noncentral_f(D_arr_0p1, D_arr_0p5, 0.9)
random_st.noncentral_f(D_arr_like_0p1, D_arr_like_0p5, 0.9)
random_st.noncentral_f(D_arr_0p1, D_arr_0p5, D_arr_0p9, size=1)
random_st.noncentral_f(D_arr_like_0p1, D_arr_like_0p5, D_arr_like_0p9, size=1)

random_st.binomial(10, 0.5)
random_st.binomial(10, 0.5, size=None)
random_st.binomial(10, 0.5, size=1)
random_st.binomial(I_arr_10, 0.5)
random_st.binomial(10, D_arr_0p5)
random_st.binomial(I_arr_10, 0.5, size=1)
random_st.binomial(10, D_arr_0p5, size=1)
random_st.binomial(I_arr_like_10, 0.5)
random_st.binomial(10, D_arr_like_0p5)
random_st.binomial(I_arr_10, D_arr_0p5)
random_st.binomial(I_arr_like_10, D_arr_like_0p5)
random_st.binomial(I_arr_10, D_arr_0p5, size=1)
random_st.binomial(I_arr_like_10, D_arr_like_0p5, size=1)

random_st.negative_binomial(10, 0.5)
random_st.negative_binomial(10, 0.5, size=None)
random_st.negative_binomial(10, 0.5, size=1)
random_st.negative_binomial(I_arr_10, 0.5)
random_st.negative_binomial(10, D_arr_0p5)
random_st.negative_binomial(I_arr_10, 0.5, size=1)
random_st.negative_binomial(10, D_arr_0p5, size=1)
random_st.negative_binomial(I_arr_like_10, 0.5)
random_st.negative_binomial(10, D_arr_like_0p5)
random_st.negative_binomial(I_arr_10, D_arr_0p5)
random_st.negative_binomial(I_arr_like_10, D_arr_like_0p5)
random_st.negative_binomial(I_arr_10, D_arr_0p5, size=1)
random_st.negative_binomial(I_arr_like_10, D_arr_like_0p5, size=1)

random_st.hypergeometric(20, 20, 10)
random_st.hypergeometric(20, 20, 10, size=None)
random_st.hypergeometric(20, 20, 10, size=1)
random_st.hypergeometric(I_arr_20, 20, 10)
random_st.hypergeometric(20, I_arr_20, 10)
random_st.hypergeometric(I_arr_20, 20, I_arr_like_10, size=1)
random_st.hypergeometric(20, I_arr_20, 10, size=1)
random_st.hypergeometric(I_arr_like_20, 20, I_arr_10)
random_st.hypergeometric(20, I_arr_like_20, 10)
random_st.hypergeometric(I_arr_20, I_arr_20, 10)
random_st.hypergeometric(I_arr_like_20, I_arr_like_20, 10)
random_st.hypergeometric(I_arr_20, I_arr_20, I_arr_10, size=1)
random_st.hypergeometric(I_arr_like_20, I_arr_like_20, I_arr_like_10, size=1)

random_st.randint(0, 100)
random_st.randint(100)
random_st.randint([100])
random_st.randint(0, [100])

random_st.randint(2, dtype=bool)
random_st.randint(0, 2, dtype=bool)
random_st.randint(I_bool_high_open, dtype=bool)
random_st.randint(I_bool_low, I_bool_high_open, dtype=bool)
random_st.randint(0, I_bool_high_open, dtype=bool)

random_st.randint(2, dtype=np.bool)
random_st.randint(0, 2, dtype=np.bool)
random_st.randint(I_bool_high_open, dtype=np.bool)
random_st.randint(I_bool_low, I_bool_high_open, dtype=np.bool)
random_st.randint(0, I_bool_high_open, dtype=np.bool)

random_st.randint(256, dtype="u1")
random_st.randint(0, 256, dtype="u1")
random_st.randint(I_u1_high_open, dtype="u1")
random_st.randint(I_u1_low, I_u1_high_open, dtype="u1")
random_st.randint(0, I_u1_high_open, dtype="u1")

random_st.randint(256, dtype="uint8")
random_st.randint(0, 256, dtype="uint8")
random_st.randint(I_u1_high_open, dtype="uint8")
random_st.randint(I_u1_low, I_u1_high_open, dtype="uint8")
random_st.randint(0, I_u1_high_open, dtype="uint8")

random_st.randint(256, dtype=np.uint8)
random_st.randint(0, 256, dtype=np.uint8)
random_st.randint(I_u1_high_open, dtype=np.uint8)
random_st.randint(I_u1_low, I_u1_high_open, dtype=np.uint8)
random_st.randint(0, I_u1_high_open, dtype=np.uint8)

random_st.randint(65536, dtype="u2")
random_st.randint(0, 65536, dtype="u2")
random_st.randint(I_u2_high_open, dtype="u2")
random_st.randint(I_u2_low, I_u2_high_open, dtype="u2")
random_st.randint(0, I_u2_high_open, dtype="u2")

random_st.randint(65536, dtype="uint16")
random_st.randint(0, 65536, dtype="uint16")
random_st.randint(I_u2_high_open, dtype="uint16")
random_st.randint(I_u2_low, I_u2_high_open, dtype="uint16")
random_st.randint(0, I_u2_high_open, dtype="uint16")

random_st.randint(65536, dtype=np.uint16)
random_st.randint(0, 65536, dtype=np.uint16)
random_st.randint(I_u2_high_open, dtype=np.uint16)
random_st.randint(I_u2_low, I_u2_high_open, dtype=np.uint16)
random_st.randint(0, I_u2_high_open, dtype=np.uint16)

random_st.randint(4294967296, dtype="u4")
random_st.randint(0, 4294967296, dtype="u4")
random_st.randint(I_u4_high_open, dtype="u4")
random_st.randint(I_u4_low, I_u4_high_open, dtype="u4")
random_st.randint(0, I_u4_high_open, dtype="u4")

random_st.randint(4294967296, dtype="uint32")
random_st.randint(0, 4294967296, dtype="uint32")
random_st.randint(I_u4_high_open, dtype="uint32")
random_st.randint(I_u4_low, I_u4_high_open, dtype="uint32")
random_st.randint(0, I_u4_high_open, dtype="uint32")

random_st.randint(4294967296, dtype=np.uint32)
random_st.randint(0, 4294967296, dtype=np.uint32)
random_st.randint(I_u4_high_open, dtype=np.uint32)
random_st.randint(I_u4_low, I_u4_high_open, dtype=np.uint32)
random_st.randint(0, I_u4_high_open, dtype=np.uint32)


random_st.randint(18446744073709551616, dtype="u8")
random_st.randint(0, 18446744073709551616, dtype="u8")
random_st.randint(I_u8_high_open, dtype="u8")
random_st.randint(I_u8_low, I_u8_high_open, dtype="u8")
random_st.randint(0, I_u8_high_open, dtype="u8")

random_st.randint(18446744073709551616, dtype="uint64")
random_st.randint(0, 18446744073709551616, dtype="uint64")
random_st.randint(I_u8_high_open, dtype="uint64")
random_st.randint(I_u8_low, I_u8_high_open, dtype="uint64")
random_st.randint(0, I_u8_high_open, dtype="uint64")

random_st.randint(18446744073709551616, dtype=np.uint64)
random_st.randint(0, 18446744073709551616, dtype=np.uint64)
random_st.randint(I_u8_high_open, dtype=np.uint64)
random_st.randint(I_u8_low, I_u8_high_open, dtype=np.uint64)
random_st.randint(0, I_u8_high_open, dtype=np.uint64)

random_st.randint(128, dtype="i1")
random_st.randint(-128, 128, dtype="i1")
random_st.randint(I_i1_high_open, dtype="i1")
random_st.randint(I_i1_low, I_i1_high_open, dtype="i1")
random_st.randint(-128, I_i1_high_open, dtype="i1")

random_st.randint(128, dtype="int8")
random_st.randint(-128, 128, dtype="int8")
random_st.randint(I_i1_high_open, dtype="int8")
random_st.randint(I_i1_low, I_i1_high_open, dtype="int8")
random_st.randint(-128, I_i1_high_open, dtype="int8")

random_st.randint(128, dtype=np.int8)
random_st.randint(-128, 128, dtype=np.int8)
random_st.randint(I_i1_high_open, dtype=np.int8)
random_st.randint(I_i1_low, I_i1_high_open, dtype=np.int8)
random_st.randint(-128, I_i1_high_open, dtype=np.int8)

random_st.randint(32768, dtype="i2")
random_st.randint(-32768, 32768, dtype="i2")
random_st.randint(I_i2_high_open, dtype="i2")
random_st.randint(I_i2_low, I_i2_high_open, dtype="i2")
random_st.randint(-32768, I_i2_high_open, dtype="i2")
random_st.randint(32768, dtype="int16")
random_st.randint(-32768, 32768, dtype="int16")
random_st.randint(I_i2_high_open, dtype="int16")
random_st.randint(I_i2_low, I_i2_high_open, dtype="int16")
random_st.randint(-32768, I_i2_high_open, dtype="int16")
random_st.randint(32768, dtype=np.int16)
random_st.randint(-32768, 32768, dtype=np.int16)
random_st.randint(I_i2_high_open, dtype=np.int16)
random_st.randint(I_i2_low, I_i2_high_open, dtype=np.int16)
random_st.randint(-32768, I_i2_high_open, dtype=np.int16)

random_st.randint(2147483648, dtype="i4")
random_st.randint(-2147483648, 2147483648, dtype="i4")
random_st.randint(I_i4_high_open, dtype="i4")
random_st.randint(I_i4_low, I_i4_high_open, dtype="i4")
random_st.randint(-2147483648, I_i4_high_open, dtype="i4")

random_st.randint(2147483648, dtype="int32")
random_st.randint(-2147483648, 2147483648, dtype="int32")
random_st.randint(I_i4_high_open, dtype="int32")
random_st.randint(I_i4_low, I_i4_high_open, dtype="int32")
random_st.randint(-2147483648, I_i4_high_open, dtype="int32")

random_st.randint(2147483648, dtype=np.int32)
random_st.randint(-2147483648, 2147483648, dtype=np.int32)
random_st.randint(I_i4_high_open, dtype=np.int32)
random_st.randint(I_i4_low, I_i4_high_open, dtype=np.int32)
random_st.randint(-2147483648, I_i4_high_open, dtype=np.int32)

random_st.randint(9223372036854775808, dtype="i8")
random_st.randint(-9223372036854775808, 9223372036854775808, dtype="i8")
random_st.randint(I_i8_high_open, dtype="i8")
random_st.randint(I_i8_low, I_i8_high_open, dtype="i8")
random_st.randint(-9223372036854775808, I_i8_high_open, dtype="i8")

random_st.randint(9223372036854775808, dtype="int64")
random_st.randint(-9223372036854775808, 9223372036854775808, dtype="int64")
random_st.randint(I_i8_high_open, dtype="int64")
random_st.randint(I_i8_low, I_i8_high_open, dtype="int64")
random_st.randint(-9223372036854775808, I_i8_high_open, dtype="int64")

random_st.randint(9223372036854775808, dtype=np.int64)
random_st.randint(-9223372036854775808, 9223372036854775808, dtype=np.int64)
random_st.randint(I_i8_high_open, dtype=np.int64)
random_st.randint(I_i8_low, I_i8_high_open, dtype=np.int64)
random_st.randint(-9223372036854775808, I_i8_high_open, dtype=np.int64)

bg: np.random.BitGenerator = random_st._bit_generator

random_st.bytes(2)

random_st.choice(5)
random_st.choice(5, 3)
random_st.choice(5, 3, replace=True)
random_st.choice(5, 3, p=[1 / 5] * 5)
random_st.choice(5, 3, p=[1 / 5] * 5, replace=False)

random_st.choice(["pooh", "rabbit", "piglet", "Christopher"])
random_st.choice(["pooh", "rabbit", "piglet", "Christopher"], 3)
random_st.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, p=[1 / 4] * 4)
random_st.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, replace=True)
random_st.choice(["pooh", "rabbit", "piglet", "Christopher"], 3, replace=False, p=np.array([1 / 8, 1 / 8, 1 / 2, 1 / 4]))

random_st.dirichlet([0.5, 0.5])
random_st.dirichlet(np.array([0.5, 0.5]))
random_st.dirichlet(np.array([0.5, 0.5]), size=3)

random_st.multinomial(20, [1 / 6.0] * 6)
random_st.multinomial(20, np.array([0.5, 0.5]))
random_st.multinomial(20, [1 / 6.0] * 6, size=2)

random_st.multivariate_normal([0.0], [[1.0]])
random_st.multivariate_normal([0.0], np.array([[1.0]]))
random_st.multivariate_normal(np.array([0.0]), [[1.0]])
random_st.multivariate_normal([0.0], np.array([[1.0]]))

random_st.permutation(10)
random_st.permutation([1, 2, 3, 4])
random_st.permutation(np.array([1, 2, 3, 4]))
random_st.permutation(D_2D)

random_st.shuffle(np.arange(10))
random_st.shuffle([1, 2, 3, 4, 5])
random_st.shuffle(D_2D)

np.random.RandomState(SEED_PCG64)
np.random.RandomState(0)
np.random.RandomState([0, 1, 2])
random_st.__str__()
random_st.__repr__()
random_st_state = random_st.__getstate__()
random_st.__setstate__(random_st_state)
random_st.seed()
random_st.seed(1)
random_st.seed([0, 1])
random_st_get_state = random_st.get_state()
random_st_get_state_legacy = random_st.get_state(legacy=True)
random_st.set_state(random_st_get_state)

random_st.rand()
random_st.rand(1)
random_st.rand(1, 2)
random_st.randn()
random_st.randn(1)
random_st.randn(1, 2)
random_st.random_sample()
random_st.random_sample(1)
random_st.random_sample(size=(1, 2))

random_st.tomaxint()
random_st.tomaxint(1)
random_st.tomaxint((1,))

np.random.mtrand.set_bit_generator(SEED_PCG64)
np.random.mtrand.get_bit_generator()
