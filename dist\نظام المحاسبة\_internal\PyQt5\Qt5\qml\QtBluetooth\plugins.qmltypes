import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtBluetooth 5.15'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: {
                "NoLayoutChangeHint": 0,
                "VerticalSortHint": 1,
                "HorizontalSortHint": 2
            }
        }
        Enum {
            name: "CheckIndexOption"
            values: {
                "NoOption": 0,
                "IndexIsValid": 1,
                "DoNotUseParent": 2,
                "ParentIsInvalid": 4
            }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractListModel"; prototype: "QAbstractItemModel" }
    Component {
        name: "QDeclarativeBluetoothDiscoveryModel"
        prototype: "QAbstractListModel"
        exports: [
            "QtBluetooth/BluetoothDiscoveryModel 5.0",
            "QtBluetooth/BluetoothDiscoveryModel 5.2"
        ]
        exportMetaObjectRevisions: [0, 0]
        Enum {
            name: "DiscoveryMode"
            values: {
                "MinimalServiceDiscovery": 0,
                "FullServiceDiscovery": 1,
                "DeviceDiscovery": 2
            }
        }
        Enum {
            name: "Error"
            values: {
                "NoError": 0,
                "InputOutputError": 1,
                "PoweredOffError": 2,
                "UnknownError": 3,
                "InvalidBluetoothAdapterError": 4
            }
        }
        Property { name: "error"; type: "Error"; isReadonly: true }
        Property { name: "discoveryMode"; type: "DiscoveryMode" }
        Property { name: "running"; type: "bool" }
        Property { name: "uuidFilter"; type: "string" }
        Property { name: "remoteAddress"; type: "string" }
        Signal {
            name: "serviceDiscovered"
            Parameter { name: "service"; type: "QDeclarativeBluetoothService"; isPointer: true }
        }
        Signal {
            name: "deviceDiscovered"
            Parameter { name: "device"; type: "string" }
        }
    }
    Component {
        name: "QDeclarativeBluetoothService"
        prototype: "QObject"
        exports: [
            "QtBluetooth/BluetoothService 5.0",
            "QtBluetooth/BluetoothService 5.2"
        ]
        exportMetaObjectRevisions: [0, 0]
        Enum {
            name: "Protocol"
            values: {
                "RfcommProtocol": 2,
                "L2CapProtocol": 1,
                "UnknownProtocol": 0
            }
        }
        Property { name: "deviceName"; type: "string"; isReadonly: true }
        Property { name: "deviceAddress"; type: "string" }
        Property { name: "serviceName"; type: "string" }
        Property { name: "serviceDescription"; type: "string" }
        Property { name: "serviceUuid"; type: "string" }
        Property { name: "serviceProtocol"; type: "Protocol" }
        Property { name: "registered"; type: "bool" }
        Signal { name: "detailsChanged" }
        Signal { name: "newClient" }
        Method { name: "nextClient"; type: "QDeclarativeBluetoothSocket*" }
        Method {
            name: "assignNextClient"
            Parameter { name: "dbs"; type: "QDeclarativeBluetoothSocket"; isPointer: true }
        }
    }
    Component {
        name: "QDeclarativeBluetoothSocket"
        prototype: "QObject"
        exports: [
            "QtBluetooth/BluetoothSocket 5.0",
            "QtBluetooth/BluetoothSocket 5.2"
        ]
        exportMetaObjectRevisions: [0, 0]
        Enum {
            name: "Error"
            values: {
                "NoError": -2,
                "UnknownSocketError": -1,
                "RemoteHostClosedError": 1,
                "HostNotFoundError": 2,
                "ServiceNotFoundError": 9,
                "NetworkError": 7,
                "UnsupportedProtocolError": 8
            }
        }
        Enum {
            name: "SocketState"
            values: {
                "Unconnected": 0,
                "ServiceLookup": 1,
                "Connecting": 2,
                "Connected": 3,
                "Bound": 4,
                "Closing": 6,
                "Listening": 5,
                "NoServiceSet": 100
            }
        }
        Property { name: "service"; type: "QDeclarativeBluetoothService"; isPointer: true }
        Property { name: "connected"; type: "bool" }
        Property { name: "error"; type: "Error"; isReadonly: true }
        Property { name: "socketState"; type: "SocketState"; isReadonly: true }
        Property { name: "stringData"; type: "string" }
        Signal { name: "stateChanged" }
        Signal { name: "dataAvailable" }
        Method {
            name: "setService"
            Parameter { name: "service"; type: "QDeclarativeBluetoothService"; isPointer: true }
        }
        Method {
            name: "setConnected"
            Parameter { name: "connected"; type: "bool" }
        }
        Method {
            name: "sendStringData"
            Parameter { name: "data"; type: "string" }
        }
    }
}
