// qchart.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qchart.h>
%End

    class QChart : public QGraphicsWidget
    {
%TypeHeaderCode
#include <qchart.h>
%End

    public:
        enum ChartTheme
        {
            ChartThemeLight,
            ChartThemeBlueCerulean,
            ChartThemeDark,
            ChartThemeBrownSand,
            ChartThemeBlueNcs,
            ChartThemeHighContrast,
            ChartThemeBlueIcy,
%If (QtChart_1_3_1 -)
            ChartThemeQt,
%End
        };

        enum AnimationOption
        {
            NoAnimation,
            GridAxisAnimations,
            SeriesAnimations,
            AllAnimations,
        };

        typedef QFlags<QtCharts::QChart::AnimationOption> AnimationOptions;
        QChart(QGraphicsItem *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
        virtual ~QChart();
        void addSeries(QtCharts::QAbstractSeries *series /Transfer/);
        void removeSeries(QtCharts::QAbstractSeries *series /TransferBack/);
        void removeAllSeries();
        QList<QtCharts::QAbstractSeries *> series() const;
        void setTheme(QtCharts::QChart::ChartTheme theme);
        QtCharts::QChart::ChartTheme theme() const;
        void setTitle(const QString &title);
        QString title() const;
        void setTitleFont(const QFont &font);
        QFont titleFont() const;
        void setTitleBrush(const QBrush &brush);
        QBrush titleBrush() const;
        void setBackgroundBrush(const QBrush &brush);
        QBrush backgroundBrush() const;
        void setBackgroundPen(const QPen &pen);
        QPen backgroundPen() const;
        void setBackgroundVisible(bool visible = true);
        bool isBackgroundVisible() const;
        void setAnimationOptions(QtCharts::QChart::AnimationOptions options);
        QtCharts::QChart::AnimationOptions animationOptions() const;
        void zoomIn();
        void zoomIn(const QRectF &rect);
        void zoomOut();
        void zoom(qreal factor);
        QtCharts::QLegend *legend() const;
%If (QtChart_1_1_0 - QtChart_2_0_0)
        void setMinimumMargins(const QMargins &margins);
%End
%If (- QtChart_1_1_0)
        void setMarginsMinimum(const QRectF &margins);
%End
%If (QtChart_1_1_0 - QtChart_2_0_0)
        QMargins minimumMargins() const;
%End
%If (- QtChart_1_1_0)
        QRectF margins() const;
%End
        void setAxisX(QtCharts::QAbstractAxis *axis /Transfer/, QtCharts::QAbstractSeries *series /Transfer/ = 0);
        void setAxisY(QtCharts::QAbstractAxis *axis /Transfer/, QtCharts::QAbstractSeries *series /Transfer/ = 0);
        QtCharts::QAbstractAxis *axisX(QtCharts::QAbstractSeries *series = 0) const;
        QtCharts::QAbstractAxis *axisY(QtCharts::QAbstractSeries *series = 0) const;
        void createDefaultAxes();
        void setDropShadowEnabled(bool enabled = true);
        bool isDropShadowEnabled() const;
        void scroll(qreal dx, qreal dy);
        QRectF plotArea() const;
%If (QtChart_1_2_0 -)
        void addAxis(QtCharts::QAbstractAxis *axis /Transfer/, Qt::Alignment alignment);
%End
%If (QtChart_1_2_0 -)
        void removeAxis(QtCharts::QAbstractAxis *axis /TransferBack/);
%End
%If (QtChart_1_2_0 -)
        QList<QtCharts::QAbstractAxis *> axes(Qt::Orientations orientation = Qt::Orientation::Horizontal | Qt::Orientation::Vertical, QtCharts::QAbstractSeries *series = 0) const;
%End
%If (QtChart_1_2_0 -)
        void setMargins(const QMargins &margins);
%End
%If (QtChart_1_2_0 -)
        QMargins margins() const;
%End
%If (QtChart_1_2_0 -)
        QPointF mapToValue(const QPointF &position, QtCharts::QAbstractSeries *series = 0);
%End
%If (QtChart_1_2_0 -)
        QPointF mapToPosition(const QPointF &value, QtCharts::QAbstractSeries *series = 0);
%End

    signals:
%If (- QtChart_1_1_0)
        void marginsChanged(QRectF newMargins);
%End

    public:
%If (QtChart_1_3_0 -)

        enum ChartType
        {
            ChartTypeUndefined,
            ChartTypeCartesian,
            ChartTypePolar,
        };

%End
%If (QtChart_1_3_0 -)
        void setBackgroundRoundness(qreal diameter);
%End
%If (QtChart_1_3_0 -)
        qreal backgroundRoundness() const;
%End
%If (QtChart_1_3_0 -)
        void zoomReset();
%End
%If (QtChart_1_3_0 -)
        bool isZoomed();
%End
%If (QtChart_5_12_0 -)
        void setPlotArea(const QRectF &rect);
%End
%If (QtChart_1_3_0 -)
        void setPlotAreaBackgroundBrush(const QBrush &brush);
%End
%If (QtChart_1_3_0 -)
        QBrush plotAreaBackgroundBrush() const;
%End
%If (QtChart_1_3_0 -)
        void setPlotAreaBackgroundPen(const QPen &pen);
%End
%If (QtChart_1_3_0 -)
        QPen plotAreaBackgroundPen() const;
%End
%If (QtChart_1_3_0 -)
        void setPlotAreaBackgroundVisible(bool visible = true);
%End
%If (QtChart_1_3_0 -)
        bool isPlotAreaBackgroundVisible() const;
%End
%If (QtChart_1_3_0 -)
        QtCharts::QChart::ChartType chartType() const;
%End
%If (QtChart_2_0_0 -)
        void setLocalizeNumbers(bool localize);
%End
%If (QtChart_2_0_0 -)
        bool localizeNumbers() const;
%End
%If (QtChart_2_0_0 -)
        void setLocale(const QLocale &locale);
%End
%If (QtChart_2_0_0 -)
        QLocale locale() const;
%End

    signals:
%If (QtChart_2_0_0 -)
        void plotAreaChanged(const QRectF &plotArea);
%End

    public:
%If (QtChart_2_1_0 -)
        void setAnimationDuration(int msecs);
%End
%If (QtChart_2_1_0 -)
        int animationDuration() const;
%End
%If (QtChart_2_1_0 -)
        void setAnimationEasingCurve(const QEasingCurve &curve);
%End
%If (QtChart_2_1_0 -)
        QEasingCurve animationEasingCurve() const;
%End
    };
};

QFlags<QtCharts::QChart::AnimationOption> operator|(QtCharts::QChart::AnimationOption f1, QtCharts::QChart::AnimationOption f2);
