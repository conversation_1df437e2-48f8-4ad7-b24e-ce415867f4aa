# 🚀 دليل تحضير الإصدار النهائي

## 📋 نظرة عامة

هذا الدليل يشرح كيفية تحضير البرنامج للإصدار النهائي وتوزيعه على العملاء بشكل نظيف وخالي من البيانات التجريبية.

---

## 🎯 الهدف

إنتاج نسخة نظيفة من البرنامج تحتوي على:
- ✅ **لوجو الشركة المطورة** (ثابت في شاشة الدخول)
- ✅ **حساب المدير الافتراضي** (sicoo/sicoo123)
- ✅ **هيكل قاعدة البيانات** (جداول فارغة)
- ✅ **إعدادات النظام الأساسية**

وخالية من:
- ❌ **البيانات التجريبية** (منتجات، فواتير، عملاء، موردين)
- ❌ **إعدادات الشركة التجريبية** (اسم، عنوان، لوجو العميل)
- ❌ **الملفات المؤقتة** والسجلات

---

## 🛠️ خطوات التحضير

### 1️⃣ **فحص الحالة الحالية**
```bash
python verify_clean_release.py
```
هذا السكريبت سيفحص:
- محتويات قاعدة البيانات
- إعدادات الشركة
- وجود الملفات الأساسية
- الملفات المؤقتة

### 2️⃣ **تنظيف البيانات**
```bash
python clean_for_release.py
```
هذا السكريبت سيقوم بـ:
- إنشاء نسخة احتياطية تلقائية
- حذف جميع البيانات التجريبية
- تنظيف إعدادات الشركة
- حذف الملفات المؤقتة
- الاحتفاظ بلوجو الشركة المطورة

### 3️⃣ **التحقق النهائي**
```bash
python verify_clean_release.py
```
للتأكد من نجاح عملية التنظيف

---

## 📊 تفاصيل التنظيف

### 🗑️ **البيانات المحذوفة:**

#### **قاعدة البيانات:**
- `products` - جميع المنتجات التجريبية
- `customers` - جميع العملاء التجريبيين  
- `suppliers` - جميع الموردين التجريبيين
- `transactions` - جميع الفواتير والمعاملات
- `transaction_items` - عناصر الفواتير
- `product_barcodes` - باركودات المنتجات
- `audit_log` - سجل العمليات التجريبي
- `notifications` - الإشعارات التجريبية

#### **إعدادات الشركة:**
- `company_name` - اسم الشركة
- `owner_name` - اسم المالك
- `phone` - رقم الهاتف
- `email` - البريد الإلكتروني
- `address` - العنوان
- `tax_number` - الرقم الضريبي
- `commercial_register` - السجل التجاري
- `company_logo` - لوجو شركة العميل

#### **الملفات المؤقتة:**
- `user_settings.json`
- `app_settings.json`
- `backup_settings.json`
- `temp_invoice.pdf`
- `debug.log`
- `error.log`
- مجلدات: `__pycache__`, `.pytest_cache`, `temp`, `logs`

### ✅ **البيانات المحفوظة:**

#### **أصول الشركة المطورة:**
- `assets/icons.ico` - لوجو الشركة المطورة (ثابت)
- شاشة الدخول مع لوجو الشركة المطورة

#### **إعدادات النظام:**
- حساب المدير الافتراضي (sicoo/sicoo123)
- إعدادات اللغة والمظهر الافتراضية
- هيكل قاعدة البيانات (جداول فارغة)

---

## 🎉 بعد التنظيف

### **العميل يمكنه:**
1. **تشغيل البرنامج** بحساب المدير الافتراضي
2. **إدخال بيانات شركته** (اسم، عنوان، لوجو)
3. **إضافة منتجاته** وأسعارها
4. **إضافة عملائه وموردينه**
5. **البدء في إنشاء الفواتير**
6. **تخصيص إعدادات النظام**

### **الشركة المطورة:**
- **لوجوها محفوظ** في شاشة الدخول
- **هويتها واضحة** كمطور للبرنامج
- **لا تتداخل** مع بيانات العميل

---

## ⚠️ تحذيرات مهمة

### **قبل التنظيف:**
- ✅ تأكد من حفظ أي بيانات مهمة
- ✅ السكريبت ينشئ نسخة احتياطية تلقائياً
- ✅ يمكن التراجع باستخدام النسخة الاحتياطية

### **بعد التنظيف:**
- ❌ لا تضع بيانات تجريبية جديدة
- ❌ لا تغير لوجو الشركة المطورة
- ✅ اختبر البرنامج للتأكد من عمله

---

## 🔧 استكشاف الأخطاء

### **إذا فشل التنظيف:**
1. تحقق من صلاحيات الملفات
2. أغلق البرنامج قبل التنظيف
3. تأكد من وجود قاعدة البيانات
4. استخدم النسخة الاحتياطية للاسترجاع

### **إذا كان البرنامج لا يعمل بعد التنظيف:**
1. تحقق من وجود الملفات الأساسية
2. تأكد من سلامة قاعدة البيانات
3. أعد تشغيل البرنامج
4. تحقق من سجل الأخطاء

---

## 📞 الدعم

في حالة وجود مشاكل:
1. راجع تقرير الفحص المُنشأ
2. تحقق من النسخة الاحتياطية
3. تواصل مع فريق التطوير

---

## ✅ قائمة التحقق النهائية

قبل توزيع البرنامج، تأكد من:

- [ ] تم تشغيل `clean_for_release.py` بنجاح
- [ ] تم تشغيل `verify_clean_release.py` وأظهر "جاهز للإصدار"
- [ ] لوجو الشركة المطورة موجود في شاشة الدخول
- [ ] يمكن الدخول بحساب sicoo/sicoo123
- [ ] قاعدة البيانات فارغة من البيانات التجريبية
- [ ] لا توجد إعدادات شركة مُدخلة مسبقاً
- [ ] البرنامج يعمل بشكل طبيعي
- [ ] تم اختبار الوظائف الأساسية

🎯 **عند اكتمال جميع النقاط، البرنامج جاهز للتوزيع!**
