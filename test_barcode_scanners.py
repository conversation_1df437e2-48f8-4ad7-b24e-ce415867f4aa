#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نوافذ مسح الباركود المختلفة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نوافذ مسح الباركود")
        self.setGeometry(100, 100, 500, 400)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # العنوان
        title = QLabel("🧪 اختبار نوافذ مسح الباركود")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2C3E50;
                padding: 20px;
                text-align: center;
                background-color: #ECF0F1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # زر اختبار الكاميرا
        camera_btn = QPushButton("📷 اختبار مسح الباركود بالكاميرا")
        camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        camera_btn.clicked.connect(self.test_camera_scanner)
        layout.addWidget(camera_btn)
        
        # زر اختبار الجهاز
        device_btn = QPushButton("🔍 اختبار مسح الباركود بالجهاز")
        device_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        device_btn.clicked.connect(self.test_device_scanner)
        layout.addWidget(device_btn)
        
        # النتيجة
        self.result_label = QLabel("اضغط على أحد الأزرار لاختبار النوافذ")
        self.result_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7F8C8D;
                padding: 20px;
                text-align: center;
                background-color: #F8F9FA;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        self.result_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.result_label)
        
    def test_camera_scanner(self):
        """اختبار نافذة مسح الباركود بالكاميرا"""
        try:
            from gui.barcode_scanner import BarcodeScannerDialog
            scanner = BarcodeScannerDialog(self)
            scanner.setWindowTitle("📷 مسح الباركود بالكاميرا")
            
            if scanner.exec_():
                if hasattr(scanner, 'scanned_code') and scanner.scanned_code:
                    self.result_label.setText(f"✅ نافذة الكاميرا تعمل!\nالباركود: {scanner.scanned_code}")
                    self.result_label.setStyleSheet("""
                        QLabel {
                            font-size: 16px;
                            color: #27AE60;
                            padding: 20px;
                            text-align: center;
                            background-color: #E8F6F3;
                            border: 2px solid #1ABC9C;
                            border-radius: 8px;
                            margin: 10px;
                        }
                    """)
                else:
                    self.result_label.setText("⚠️ تم إلغاء مسح الكاميرا")
            else:
                self.result_label.setText("❌ تم إلغاء نافذة الكاميرا")
                
        except Exception as e:
            self.result_label.setText(f"❌ خطأ في نافذة الكاميرا:\n{str(e)}")
            self.result_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    color: #E74C3C;
                    padding: 20px;
                    text-align: center;
                    background-color: #FADBD8;
                    border: 2px solid #E74C3C;
                    border-radius: 8px;
                    margin: 10px;
                }
            """)
    
    def test_device_scanner(self):
        """اختبار نافذة مسح الباركود بالجهاز"""
        try:
            from gui.device_barcode_scanner import DeviceBarcodeScannerDialog
            scanner = DeviceBarcodeScannerDialog(self)
            
            if scanner.exec_():
                if hasattr(scanner, 'scanned_code') and scanner.scanned_code:
                    self.result_label.setText(f"✅ نافذة الجهاز تعمل!\nالباركود: {scanner.scanned_code}")
                    self.result_label.setStyleSheet("""
                        QLabel {
                            font-size: 16px;
                            color: #27AE60;
                            padding: 20px;
                            text-align: center;
                            background-color: #E8F6F3;
                            border: 2px solid #1ABC9C;
                            border-radius: 8px;
                            margin: 10px;
                        }
                    """)
                else:
                    self.result_label.setText("⚠️ تم إلغاء مسح الجهاز")
            else:
                self.result_label.setText("❌ تم إلغاء نافذة الجهاز")
                
        except Exception as e:
            self.result_label.setText(f"❌ خطأ في نافذة الجهاز:\n{str(e)}")
            self.result_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    color: #E74C3C;
                    padding: 20px;
                    text-align: center;
                    background-color: #FADBD8;
                    border: 2px solid #E74C3C;
                    border-radius: 8px;
                    margin: 10px;
                }
            """)

def main():
    print("🧪 اختبار نوافذ مسح الباركود")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    print("✅ تم إنشاء النوافذ التالية:")
    print("")
    print("   📷 نافذة مسح الباركود بالكاميرا:")
    print("     • ملف: gui/barcode_scanner.py")
    print("     • كلاس: BarcodeScannerDialog")
    print("     • الوظيفة: مسح بالكاميرا")
    print("")
    print("   🔍 نافذة مسح الباركود بالجهاز:")
    print("     • ملف: gui/device_barcode_scanner.py")
    print("     • كلاس: DeviceBarcodeScannerDialog")
    print("     • الوظيفة: مسح بالجهاز المخصص")
    print("")
    print("   🔧 تم تعديل inventory.py:")
    print("     • زر الكاميرا 📷 → نافذة الكاميرا")
    print("     • زر الجهاز 🔍 → نافذة الجهاز")
    print("     • نوافذ مختلفة تماماً!")
    print("")
    
    # إنشاء نافذة الاختبار
    window = TestWindow()
    window.show()
    
    print("🚀 تم فتح نافذة الاختبار!")
    print("")
    print("🎯 اختبر الآن:")
    print("   1️⃣ اضغط زر 'مسح الباركود بالكاميرا'")
    print("   2️⃣ اضغط زر 'مسح الباركود بالجهاز'")
    print("   3️⃣ لاحظ النوافذ المختلفة!")
    print("")
    print("✅ المتوقع:")
    print("   📷 الكاميرا: نافذة بكاميرا ومعاينة")
    print("   🔍 الجهاز: نافذة بحقل إدخال وتعليمات")
    print("")
    
    app.exec_()
    
    print("🎉 انتهى الاختبار!")
    print("=" * 50)

if __name__ == "__main__":
    main()
