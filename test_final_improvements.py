#!/usr/bin/env python3
"""
اختبار التحسينات النهائية للترويسة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🎯 اختبار التحسينات النهائية للترويسة...")
    print("=" * 60)
    print("✅ التحسينات المطبقة:")
    print("   • تنزيل رقم الهاتف مع مسافة إضافية (5px)")
    print("   • إضافة الرقم الضريبي مع أيقونة 🏢")
    print("   • محاذاة يمنى صحيحة لجميع النصوص")
    print("   • إحداثيات صحيحة داخل الترويسة")
    print("   • ارتفاع ترويسة 180px")
    print("   • لوجو 120x120")
    print("   • خط اسم الشركة 18px")
    print("   • خط التفاصيل 13px")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    
    try:
        print("🧾 اختبار فاتورة رقم 1 مع التحسينات النهائية...")
        from utils.advanced_invoice_printer import show_advanced_print_dialog
        show_advanced_print_dialog(engine, 1, None)
        print("✅ تم فتح نافذة الطباعة!")
        print("🎯 تحقق من الترويسة:")
        print("   • هل رقم الهاتف منفصل عن العنوان؟")
        print("   • هل الرقم الضريبي ظاهر؟")
        print("   • هل جميع النصوص محاذاة يمنى؟")
        print("   • هل النصوص داخل الترويسة الخضراء؟")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
        # اختبار بديل
        try:
            print("🔄 محاولة اختبار بديل...")
            from utils.advanced_invoice_printer import AdvancedInvoicePrinter
            
            # إنشاء نافذة الطباعة مباشرة
            dialog = AdvancedInvoicePrinter(engine, 1, None)
            dialog.show()
            print("✅ تم فتح نافذة الطباعة البديلة!")
            
            app.exec_()
            
        except Exception as e2:
            print(f"❌ خطأ في الاختبار البديل: {e2}")
    
    print("🎯 انتهى الاختبار")
    print("=" * 60)
    print("📊 النتيجة المتوقعة:")
    print("   ✅ ترويسة احترافية مع:")
    print("      🖼️ لوجو الشركة (120x120)")
    print("      📝 اسم الشركة (18px غامق)")
    print("      📍 عنوان مفصل")
    print("      📞 رقم الهاتف (منفصل)")
    print("      📧 بريد إلكتروني")
    print("      🏢 رقم ضريبي")
    print("   ✅ محاذاة يمنى صحيحة")
    print("   ✅ مسافات مناسبة")
    print("   ✅ نصوص كاملة وواضحة")

if __name__ == "__main__":
    main()
