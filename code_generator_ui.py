#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة مولد أكواد التجديد (للمطور فقط)
License Code Generator UI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyperclip
import webbrowser
from datetime import datetime
import json
from license_manager import LicenseCodeGenerator

class CodeGeneratorApp:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("🔑 مولد أكواد التجديد - للمطور فقط")
        self.window.geometry("600x500")
        self.window.configure(bg='#2c3e50')
        
        # تطبيق الثيم الداكن
        self.setup_styles()
        self.create_widgets()
        
    def setup_styles(self):
        """إعداد الأنماط"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الألوان
        style.configure('Title.TLabel', 
                       background='#2c3e50', 
                       foreground='#ecf0f1', 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Heading.TLabel', 
                       background='#2c3e50', 
                       foreground='#3498db', 
                       font=('Arial', 12, 'bold'))
        
        style.configure('Info.TLabel', 
                       background='#2c3e50', 
                       foreground='#bdc3c7', 
                       font=('Arial', 10))
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.window, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🔑 مولد أكواد التجديد",
            font=("Arial", 18, "bold"),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="للمطور فقط - Developer Only",
            font=("Arial", 10),
            bg='#2c3e50',
            fg='#e74c3c'
        )
        subtitle_label.pack()
        
        # إطار المدخلات
        input_frame = tk.LabelFrame(
            self.window,
            text="بيانات العميل",
            font=("Arial", 12, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        input_frame.pack(pady=20, padx=30, fill='x')
        
        # كود العميل
        tk.Label(
            input_frame,
            text="📝 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#3498db'
        ).grid(row=0, column=0, sticky='e', padx=10, pady=8)
        
        self.customer_code_entry = tk.Entry(
            input_frame,
            font=("Arial", 11),
            width=25,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.customer_code_entry.grid(row=0, column=1, padx=10, pady=8)
        
        # رقم الجهاز
        tk.Label(
            input_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#3498db'
        ).grid(row=1, column=0, sticky='e', padx=10, pady=8)
        
        self.machine_id_entry = tk.Entry(
            input_frame,
            font=("Arial", 11),
            width=25,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.machine_id_entry.grid(row=1, column=1, padx=10, pady=8)
        
        # سنة التجديد
        tk.Label(
            input_frame,
            text="📅 سنة التجديد:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#3498db'
        ).grid(row=2, column=0, sticky='e', padx=10, pady=8)
        
        self.year_var = tk.StringVar(value=str(datetime.now().year + 1))
        year_frame = tk.Frame(input_frame, bg='#34495e')
        year_frame.grid(row=2, column=1, padx=10, pady=8, sticky='w')
        
        self.year_entry = tk.Entry(
            year_frame,
            textvariable=self.year_var,
            font=("Arial", 11),
            width=10,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.year_entry.pack(side='left')
        
        # أزرار سنوات سريعة
        for i, year in enumerate([2024, 2025, 2026, 2027]):
            btn = tk.Button(
                year_frame,
                text=str(year),
                font=("Arial", 9),
                bg='#3498db',
                fg='white',
                padx=8,
                pady=2,
                command=lambda y=year: self.year_var.set(str(y))
            )
            btn.pack(side='left', padx=2)
        
        # مدة الترخيص
        tk.Label(
            input_frame,
            text="⏳ مدة الترخيص:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#3498db'
        ).grid(row=3, column=0, sticky='e', padx=10, pady=8)
        self.duration_var = tk.StringVar(value="12 شهر")
        self.duration_combo = ttk.Combobox(
            input_frame,
            textvariable=self.duration_var,
            values=["7 أيام", "شهر", "6 شهور", "12 شهر", "تاريخ مخصص"],
            state="readonly",
            font=("Arial", 11)
        )
        self.duration_combo.grid(row=3, column=1, padx=10, pady=8, sticky='w')
        self.duration_combo.bind("<<ComboboxSelected>>", self.on_duration_change)
        # حقل تاريخ الانتهاء المخصص
        self.custom_expiry_var = tk.StringVar()
        self.custom_expiry_entry = tk.Entry(
            input_frame,
            textvariable=self.custom_expiry_var,
            font=("Arial", 11),
            width=12,
            bg='#ecf0f1',
            fg='#2c3e50',
            state='disabled'
        )
        self.custom_expiry_entry.grid(row=4, column=1, padx=10, pady=8, sticky='w')
        tk.Label(
            input_frame,
            text="تاريخ الانتهاء (YYYY-MM-DD):",
            font=("Arial", 10),
            bg='#34495e',
            fg='#7f8c8d'
        ).grid(row=4, column=0, sticky='e', padx=10, pady=8)
        
        # زر توليد الكود
        generate_btn = tk.Button(
            input_frame,
            text="🔄 توليد الكود",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=10,
            command=self.generate_code
        )
        generate_btn.grid(row=5, column=0, columnspan=2, pady=20)
        
        # إطار النتيجة
        result_frame = tk.LabelFrame(
            self.window,
            text="الكود المولد",
            font=("Arial", 12, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        result_frame.pack(pady=20, padx=30, fill='x')
        
        # عرض الكود
        self.code_text = tk.Text(
            result_frame,
            height=3,
            font=("Courier New", 12, "bold"),
            bg='#2c3e50',
            fg='#e74c3c',
            wrap='word',
            state='disabled'
        )
        self.code_text.pack(fill='x', pady=10)
        
        # أزرار العمليات
        actions_frame = tk.Frame(result_frame, bg='#34495e')
        actions_frame.pack(fill='x', pady=10)
        
        # زر النسخ
        copy_btn = tk.Button(
            actions_frame,
            text="📋 نسخ الكود",
            font=("Arial", 11, "bold"),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=8,
            command=self.copy_code
        )
        copy_btn.pack(side='left', padx=5)
        
        # زر إرسال واتساب
        whatsapp_btn = tk.Button(
            actions_frame,
            text="💬 إرسال واتساب",
            font=("Arial", 11, "bold"),
            bg='#25d366',
            fg='white',
            padx=20,
            pady=8,
            command=self.send_whatsapp
        )
        whatsapp_btn.pack(side='left', padx=5)
        
        # زر حفظ في ملف
        save_btn = tk.Button(
            actions_frame,
            text="💾 حفظ في ملف",
            font=("Arial", 11, "bold"),
            bg='#f39c12',
            fg='white',
            padx=20,
            pady=8,
            command=self.save_to_file
        )
        save_btn.pack(side='left', padx=5)
        
        # زر مسح
        clear_btn = tk.Button(
            actions_frame,
            text="🗑️ مسح",
            font=("Arial", 11, "bold"),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=8,
            command=self.clear_all
        )
        clear_btn.pack(side='right', padx=5)
        
        # معلومات إضافية
        info_frame = tk.Frame(self.window, bg='#2c3e50')
        info_frame.pack(pady=10)
        
        info_text = "💡 نصيحة: احفظ أكواد العملاء في ملف آمن للرجوع إليها لاحقاً"
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=("Arial", 9),
            bg='#2c3e50',
            fg='#f39c12'
        )
        info_label.pack()
        
        # متغيرات
        self.generated_code = ""
        self.customer_phone = ""
    
    def on_duration_change(self, event=None):
        """تحديث تاريخ الانتهاء بناءً على المدة المختارة"""
        from datetime import datetime, timedelta
        duration = self.duration_var.get()
        today = datetime.now().date()
        if duration == "7 أيام":
            expiry = today + timedelta(days=7)
            self.custom_expiry_var.set(str(expiry))
            self.custom_expiry_entry.config(state='disabled')
        elif duration == "شهر":
            from dateutil.relativedelta import relativedelta
            expiry = today + relativedelta(months=1)
            self.custom_expiry_var.set(str(expiry))
            self.custom_expiry_entry.config(state='disabled')
        elif duration == "6 شهور":
            from dateutil.relativedelta import relativedelta
            expiry = today + relativedelta(months=6)
            self.custom_expiry_var.set(str(expiry))
            self.custom_expiry_entry.config(state='disabled')
        elif duration == "12 شهر":
            from dateutil.relativedelta import relativedelta
            expiry = today + relativedelta(months=12)
            self.custom_expiry_var.set(str(expiry))
            self.custom_expiry_entry.config(state='disabled')
        elif duration == "تاريخ مخصص":
            self.custom_expiry_entry.config(state='normal')
            self.custom_expiry_var.set("")
        else:
            self.custom_expiry_var.set("")
            self.custom_expiry_entry.config(state='disabled')
    
    def generate_code(self):
        """توليد كود التجديد مع تاريخ انتهاء فعلي"""
        customer_code = self.customer_code_entry.get().strip()
        machine_id = self.machine_id_entry.get().strip()
        expiry_date = self.custom_expiry_var.get().strip()
        # التحقق من المدخلات
        if not customer_code:
            messagebox.showerror("خطأ", "يرجى إدخال كود العميل")
            return
        if not machine_id:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الجهاز")
            return
        if not expiry_date:
            messagebox.showerror("خطأ", "يرجى تحديد أو إدخال تاريخ الانتهاء")
            return
        try:
            from datetime import datetime
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
            if expiry < datetime.now():
                messagebox.showerror("خطأ", "تاريخ الانتهاء يجب أن يكون في المستقبل")
                return
        except Exception:
            messagebox.showerror("خطأ", "صيغة تاريخ الانتهاء غير صحيحة (YYYY-MM-DD)")
            return
        try:
            self.generated_code = LicenseCodeGenerator.generate_renewal_code(
                customer_code, machine_id, expiry_date
            )
            self.code_text.config(state='normal')
            self.code_text.delete(1.0, tk.END)
            self.code_text.insert(1.0, f"كود التجديد:\n{self.generated_code}\n\nصالح حتى: {expiry_date}")
            self.code_text.config(state='disabled')
            messagebox.showinfo("نجح التوليد", "تم توليد كود التجديد بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد الكود: {e}")
    
    def copy_code(self):
        """نسخ الكود إلى الحافظة"""
        if not self.generated_code:
            messagebox.showerror("خطأ", "لا يوجد كود لنسخه")
            return
        
        try:
            pyperclip.copy(self.generated_code)
            messagebox.showinfo("تم النسخ", "تم نسخ الكود إلى الحافظة")
        except:
            messagebox.showinfo("الكود", f"كود التجديد:\n{self.generated_code}")
    
    def send_whatsapp(self):
        """إرسال الكود عبر واتساب"""
        if not self.generated_code:
            messagebox.showerror("خطأ", "لا يوجد كود للإرسال")
            return
        
        # طلب رقم الهاتف
        phone_dialog = PhoneDialog(self.window)
        phone_number = phone_dialog.get_phone_number()
        
        if phone_number:
            year = self.year_var.get()
            message = f"🔑 كود تجديد البرنامج\n\nكود التجديد: {self.generated_code}\n📅 صالح حتى: 31/12/{year}\n\n📝 ادخل الكود في البرنامج لتفعيل الترخيص"
            
            # تشفير الرسالة للرابط
            import urllib.parse
            encoded_message = urllib.parse.quote(message)
            
            # فتح واتساب
            whatsapp_url = f"https://wa.me/{phone_number}?text={encoded_message}"
            try:
                webbrowser.open(whatsapp_url)
            except:
                messagebox.showerror("خطأ", "فشل في فتح واتساب")
    
    def save_to_file(self):
        """حفظ الكود في ملف"""
        if not self.generated_code:
            messagebox.showerror("خطأ", "لا يوجد كود للحفظ")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")],
            title="حفظ كود التجديد"
        )
        
        if filename:
            try:
                customer_code = self.customer_code_entry.get().strip()
                machine_id = self.machine_id_entry.get().strip()
                year = self.year_var.get()
                
                data = {
                    "customer_code": customer_code,
                    "machine_id": machine_id,
                    "renewal_code": self.generated_code,
                    "year": year,
                    "generated_date": datetime.now().isoformat(),
                    "expiry_date": self.custom_expiry_var.get()
                }
                
                if filename.endswith('.json'):
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                else:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"كود تجديد البرنامج\n")
                        f.write(f"=" * 30 + "\n\n")
                        f.write(f"كود العميل: {customer_code}\n")
                        f.write(f"رقم الجهاز: {machine_id}\n")
                        f.write(f"كود التجديد: {self.generated_code}\n")
                        f.write(f"سنة التجديد: {year}\n")
                        f.write(f"تاريخ التوليد: {datetime.now().strftime('%d/%m/%Y %H:%M')}\n")
                        f.write(f"صالح حتى: {self.custom_expiry_var.get()}\n")
                
                messagebox.showinfo("تم الحفظ", f"تم حفظ الكود في:\n{filename}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {e}")
    
    def clear_all(self):
        """مسح جميع البيانات"""
        self.customer_code_entry.delete(0, tk.END)
        self.machine_id_entry.delete(0, tk.END)
        self.year_var.set(str(datetime.now().year + 1))
        self.duration_var.set("12 شهر")
        self.custom_expiry_var.set("")
        
        self.code_text.config(state='normal')
        self.code_text.delete(1.0, tk.END)
        self.code_text.config(state='disabled')
        
        self.generated_code = ""
    
    def run(self):
        """تشغيل التطبيق"""
        self.window.mainloop()

class PhoneDialog:
    """نافذة إدخال رقم الهاتف"""
    
    def __init__(self, parent):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("رقم الهاتف")
        self.dialog.geometry("300x150")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg='#34495e')
        
        # جعل النافذة في المقدمة
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # العنوان
        tk.Label(
            self.dialog,
            text="📱 أدخل رقم هاتف العميل",
            font=("Arial", 12, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(pady=20)
        
        # إدخال الرقم
        self.phone_entry = tk.Entry(
            self.dialog,
            font=("Arial", 12),
            width=20,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.phone_entry.pack(pady=10)
        self.phone_entry.focus()
        
        # الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#34495e')
        buttons_frame.pack(pady=10)
        
        tk.Button(
            buttons_frame,
            text="إرسال",
            font=("Arial", 10, "bold"),
            bg='#27ae60',
            fg='white',
            padx=20,
            command=self.ok_clicked
        ).pack(side='left', padx=10)
        
        tk.Button(
            buttons_frame,
            text="إلغاء",
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            padx=20,
            command=self.cancel_clicked
        ).pack(side='left', padx=10)
        
        # ربط مفتاح Enter
        self.phone_entry.bind('<Return>', lambda e: self.ok_clicked())
    
    def ok_clicked(self):
        phone = self.phone_entry.get().strip()
        if phone:
            # تنظيف رقم الهاتف
            phone = phone.replace('+', '').replace('-', '').replace(' ', '')
            if phone.startswith('0'):
                phone = '2' + phone[1:]  # تحويل للرقم المصري الدولي
            elif not phone.startswith('2'):
                phone = '2' + phone
            
            self.result = phone
        self.dialog.destroy()
    
    def cancel_clicked(self):
        self.dialog.destroy()
    
    def get_phone_number(self):
        self.dialog.wait_window()
        return self.result

# تشغيل التطبيق
if __name__ == "__main__":
    app = CodeGeneratorApp()
    app.run()
