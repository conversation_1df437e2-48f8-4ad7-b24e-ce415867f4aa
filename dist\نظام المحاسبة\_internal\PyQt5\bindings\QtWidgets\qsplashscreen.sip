// qsplashscreen.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSplashScreen : public QWidget
{
%TypeHeaderCode
#include <qsplashscreen.h>
%End

public:
    QSplashScreen(const QPixmap &pixmap = QPixmap(), Qt::WindowFlags flags = Qt::WindowFlags());
    QSplashScreen(QWidget *parent /TransferThis/, const QPixmap &pixmap = QPixmap(), Qt::WindowFlags flags = Qt::WindowFlags());
%If (Qt_5_15_0 -)
    QSplashScreen(QScreen *screen, const QPixmap &pixmap = QPixmap(), Qt::WindowFlags flags = Qt::WindowFlags());
%End
    virtual ~QSplashScreen();
    void setPixmap(const QPixmap &pixmap);
    const QPixmap pixmap() const;
    void finish(QWidget *w);
    void repaint();
%If (Qt_5_2_0 -)
    QString message() const;
%End

public slots:
    void showMessage(const QString &message, int alignment = Qt::AlignLeft, const QColor &color = Qt::black);
    void clearMessage();

signals:
    void messageChanged(const QString &message);

protected:
    virtual void drawContents(QPainter *painter);
    virtual bool event(QEvent *e);
    virtual void mousePressEvent(QMouseEvent *);
};
