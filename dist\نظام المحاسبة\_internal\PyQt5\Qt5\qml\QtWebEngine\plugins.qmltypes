import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -defaultplatform -dependencies dependencies.json QtWebEngine 1.10'

Module {
    dependencies: ["QtQuick 2.8"]
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: {
                "NoLayoutChangeHint": 0,
                "VerticalSortHint": 1,
                "HorizontalSortHint": 2
            }
        }
        Enum {
            name: "CheckIndexOption"
            values: {
                "NoOption": 0,
                "IndexIsValid": 1,
                "DoNotUseParent": 2,
                "ParentIsInvalid": 4
            }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractListModel"; prototype: "QAbstractItemModel" }
    Component {
        name: "QQuickWebEngineAction"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineAction 1.8"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "iconName"; type: "string"; isReadonly: true }
        Property { name: "enabled"; type: "bool"; isReadonly: true }
        Signal { name: "triggered" }
        Method { name: "trigger" }
    }
    Component {
        name: "QQuickWebEngineAuthenticationDialogRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/AuthenticationDialogRequest 1.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "AuthenticationType"
            values: {
                "AuthenticationTypeHTTP": 0,
                "AuthenticationTypeProxy": 1
            }
        }
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "realm"; type: "string"; isReadonly: true }
        Property { name: "proxyHost"; type: "string"; isReadonly: true }
        Property { name: "type"; type: "AuthenticationType"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "dialogAccept"
            Parameter { name: "user"; type: "string" }
            Parameter { name: "password"; type: "string" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        name: "QQuickWebEngineCertificateError"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineCertificateError 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Error"
            values: {
                "SslPinnedKeyNotInCertificateChain": -150,
                "CertificateCommonNameInvalid": -200,
                "CertificateDateInvalid": -201,
                "CertificateAuthorityInvalid": -202,
                "CertificateContainsErrors": -203,
                "CertificateNoRevocationMechanism": -204,
                "CertificateUnableToCheckRevocation": -205,
                "CertificateRevoked": -206,
                "CertificateInvalid": -207,
                "CertificateWeakSignatureAlgorithm": -208,
                "CertificateNonUniqueName": -210,
                "CertificateWeakKey": -211,
                "CertificateNameConstraintViolation": -212,
                "CertificateValidityTooLong": -213,
                "CertificateTransparencyRequired": -214
            }
        }
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "error"; type: "Error"; isReadonly: true }
        Property { name: "description"; type: "string"; isReadonly: true }
        Property { name: "overridable"; type: "bool"; isReadonly: true }
        Method { name: "defer" }
        Method { name: "ignoreCertificateError" }
        Method { name: "rejectCertificate" }
    }
    Component {
        name: "QQuickWebEngineClientCertificateOption"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineClientCertificateOption 1.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "issuer"; type: "string"; isReadonly: true }
        Property { name: "subject"; type: "string"; isReadonly: true }
        Property { name: "effectiveDate"; type: "QDateTime"; isReadonly: true }
        Property { name: "expiryDate"; type: "QDateTime"; isReadonly: true }
        Property { name: "isSelfSigned"; type: "bool"; isReadonly: true }
        Method { name: "select" }
    }
    Component {
        name: "QQuickWebEngineClientCertificateSelection"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineClientCertificateSelection 1.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "host"; type: "QUrl"; isReadonly: true }
        Property {
            name: "certificates"
            type: "QQuickWebEngineClientCertificateOption"
            isList: true
            isReadonly: true
        }
        Method {
            name: "select"
            Parameter { name: "idx"; type: "int" }
        }
        Method {
            name: "select"
            Parameter {
                name: "certificate"
                type: "const QQuickWebEngineClientCertificateOption"
                isPointer: true
            }
        }
        Method { name: "selectNone" }
    }
    Component {
        name: "QQuickWebEngineColorDialogRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/ColorDialogRequest 1.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "dialogAccept"
            Parameter { name: "color"; type: "QColor" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        name: "QQuickWebEngineContextMenuRequest"
        prototype: "QObject"
        exports: [
            "QtWebEngine/ContextMenuRequest 1.4",
            "QtWebEngine/ContextMenuRequest 1.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "MediaType"
            values: {
                "MediaTypeNone": 0,
                "MediaTypeImage": 1,
                "MediaTypeVideo": 2,
                "MediaTypeAudio": 3,
                "MediaTypeCanvas": 4,
                "MediaTypeFile": 5,
                "MediaTypePlugin": 6
            }
        }
        Enum {
            name: "MediaFlags"
            values: {
                "MediaInError": 1,
                "MediaPaused": 2,
                "MediaMuted": 4,
                "MediaLoop": 8,
                "MediaCanSave": 16,
                "MediaHasAudio": 32,
                "MediaCanToggleControls": 64,
                "MediaControls": 128,
                "MediaCanPrint": 256,
                "MediaCanRotate": 512
            }
        }
        Enum {
            name: "EditFlags"
            values: {
                "CanUndo": 1,
                "CanRedo": 2,
                "CanCut": 4,
                "CanCopy": 8,
                "CanPaste": 16,
                "CanDelete": 32,
                "CanSelectAll": 64,
                "CanTranslate": 128,
                "CanEditRichly": 256
            }
        }
        Property { name: "x"; type: "int"; isReadonly: true }
        Property { name: "y"; type: "int"; isReadonly: true }
        Property { name: "selectedText"; type: "string"; isReadonly: true }
        Property { name: "linkText"; type: "string"; isReadonly: true }
        Property { name: "linkUrl"; type: "QUrl"; isReadonly: true }
        Property { name: "mediaUrl"; type: "QUrl"; isReadonly: true }
        Property { name: "mediaType"; type: "MediaType"; isReadonly: true }
        Property { name: "isContentEditable"; type: "bool"; isReadonly: true }
        Property { name: "misspelledWord"; type: "string"; isReadonly: true }
        Property { name: "spellCheckerSuggestions"; type: "QStringList"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Property { name: "mediaFlags"; revision: 1; type: "MediaFlags"; isReadonly: true }
        Property { name: "editFlags"; revision: 1; type: "EditFlags"; isReadonly: true }
    }
    Component {
        name: "QQuickWebEngineDownloadItem"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineDownloadItem 1.1",
            "QtWebEngine/WebEngineDownloadItem 1.10",
            "QtWebEngine/WebEngineDownloadItem 1.2",
            "QtWebEngine/WebEngineDownloadItem 1.3",
            "QtWebEngine/WebEngineDownloadItem 1.4",
            "QtWebEngine/WebEngineDownloadItem 1.5",
            "QtWebEngine/WebEngineDownloadItem 1.6",
            "QtWebEngine/WebEngineDownloadItem 1.7",
            "QtWebEngine/WebEngineDownloadItem 1.8"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 10, 1, 2, 3, 4, 5, 6, 7]
        Enum {
            name: "DownloadState"
            values: {
                "DownloadRequested": 0,
                "DownloadInProgress": 1,
                "DownloadCompleted": 2,
                "DownloadCancelled": 3,
                "DownloadInterrupted": 4
            }
        }
        Enum {
            name: "SavePageFormat"
            values: {
                "UnknownSaveFormat": -1,
                "SingleHtmlSaveFormat": 0,
                "CompleteHtmlSaveFormat": 1,
                "MimeHtmlSaveFormat": 2
            }
        }
        Enum {
            name: "DownloadInterruptReason"
            values: {
                "NoReason": 0,
                "FileFailed": 1,
                "FileAccessDenied": 2,
                "FileNoSpace": 3,
                "FileNameTooLong": 5,
                "FileTooLarge": 6,
                "FileVirusInfected": 7,
                "FileTransientError": 10,
                "FileBlocked": 11,
                "FileSecurityCheckFailed": 12,
                "FileTooShort": 13,
                "FileHashMismatch": 14,
                "NetworkFailed": 20,
                "NetworkTimeout": 21,
                "NetworkDisconnected": 22,
                "NetworkServerDown": 23,
                "NetworkInvalidRequest": 24,
                "ServerFailed": 30,
                "ServerBadContent": 33,
                "ServerUnauthorized": 34,
                "ServerCertProblem": 35,
                "ServerForbidden": 36,
                "ServerUnreachable": 37,
                "UserCanceled": 40
            }
        }
        Enum {
            name: "DownloadType"
            values: {
                "Attachment": 0,
                "DownloadAttribute": 1,
                "UserRequested": 2,
                "SavePage": 3
            }
        }
        Property { name: "id"; type: "uint"; isReadonly: true }
        Property { name: "state"; type: "DownloadState"; isReadonly: true }
        Property { name: "savePageFormat"; revision: 2; type: "SavePageFormat" }
        Property { name: "totalBytes"; type: "qlonglong"; isReadonly: true }
        Property { name: "receivedBytes"; type: "qlonglong"; isReadonly: true }
        Property { name: "mimeType"; revision: 1; type: "string"; isReadonly: true }
        Property { name: "path"; type: "string" }
        Property { name: "type"; revision: 3; type: "DownloadType"; isReadonly: true }
        Property {
            name: "interruptReason"
            revision: 4
            type: "DownloadInterruptReason"
            isReadonly: true
        }
        Property { name: "interruptReasonString"; revision: 4; type: "string"; isReadonly: true }
        Property { name: "isFinished"; revision: 5; type: "bool"; isReadonly: true }
        Property { name: "isPaused"; revision: 5; type: "bool"; isReadonly: true }
        Property { name: "isSavePageDownload"; revision: 6; type: "bool"; isReadonly: true }
        Property {
            name: "view"
            revision: 7
            type: "QQuickWebEngineView"
            isReadonly: true
            isPointer: true
        }
        Property { name: "url"; revision: 10; type: "QUrl"; isReadonly: true }
        Property { name: "suggestedFileName"; revision: 10; type: "string"; isReadonly: true }
        Property { name: "downloadDirectory"; revision: 10; type: "string" }
        Property { name: "downloadFileName"; revision: 10; type: "string" }
        Signal { name: "savePageFormatChanged"; revision: 2 }
        Signal { name: "mimeTypeChanged"; revision: 1 }
        Signal { name: "typeChanged"; revision: 3 }
        Signal { name: "interruptReasonChanged"; revision: 4 }
        Signal { name: "isFinishedChanged"; revision: 5 }
        Signal { name: "isPausedChanged"; revision: 5 }
        Signal { name: "downloadDirectoryChanged"; revision: 10 }
        Signal { name: "downloadFileNameChanged"; revision: 10 }
        Method { name: "accept" }
        Method { name: "cancel" }
        Method { name: "pause" }
        Method { name: "resume" }
    }
    Component {
        name: "QQuickWebEngineFileDialogRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/FileDialogRequest 1.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "FileMode"
            values: {
                "FileModeOpen": 0,
                "FileModeOpenMultiple": 1,
                "FileModeUploadFolder": 2,
                "FileModeSave": 3
            }
        }
        Property { name: "defaultFileName"; type: "string"; isReadonly: true }
        Property { name: "acceptedMimeTypes"; type: "QStringList"; isReadonly: true }
        Property { name: "mode"; type: "FileMode"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "dialogAccept"
            Parameter { name: "files"; type: "QStringList" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        name: "QQuickWebEngineFormValidationMessageRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/FormValidationMessageRequest 1.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RequestType"
            values: {
                "Show": 0,
                "Hide": 1,
                "Move": 2
            }
        }
        Property { name: "anchor"; type: "QRect"; isReadonly: true }
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "subText"; type: "string"; isReadonly: true }
        Property { name: "type"; type: "RequestType"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        name: "QQuickWebEngineFullScreenRequest"
        exports: ["QtWebEngine/FullScreenRequest 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QUrl"; isReadonly: true }
        Property { name: "toggleOn"; type: "bool"; isReadonly: true }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        name: "QQuickWebEngineHistory"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtWebEngine/NavigationHistory 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property {
            name: "items"
            type: "QQuickWebEngineHistoryListModel"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "backItems"
            type: "QQuickWebEngineHistoryListModel"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "forwardItems"
            type: "QQuickWebEngineHistoryListModel"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QQuickWebEngineHistoryListModel"
        prototype: "QAbstractListModel"
        exports: ["QtWebEngine/NavigationHistoryListModel 1.1"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuickWebEngineJavaScriptDialogRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/JavaScriptDialogRequest 1.4"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "DialogType"
            values: {
                "DialogTypeAlert": 0,
                "DialogTypeConfirm": 1,
                "DialogTypePrompt": 2,
                "DialogTypeBeforeUnload": 3
            }
        }
        Property { name: "message"; type: "string"; isReadonly: true }
        Property { name: "defaultText"; type: "string"; isReadonly: true }
        Property { name: "title"; type: "string"; isReadonly: true }
        Property { name: "type"; type: "DialogType"; isReadonly: true }
        Property { name: "securityOrigin"; type: "QUrl"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "dialogAccept"
            Parameter { name: "text"; type: "string" }
        }
        Method { name: "dialogAccept" }
        Method { name: "dialogReject" }
    }
    Component {
        name: "QQuickWebEngineLoadRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineLoadRequest 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "status"; type: "QQuickWebEngineView::LoadStatus"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "errorDomain"; type: "QQuickWebEngineView::ErrorDomain"; isReadonly: true }
        Property { name: "errorCode"; type: "int"; isReadonly: true }
    }
    Component {
        name: "QQuickWebEngineNavigationRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineNavigationRequest 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "isMainFrame"; type: "bool"; isReadonly: true }
        Property { name: "action"; type: "QQuickWebEngineView::NavigationRequestAction" }
        Property {
            name: "navigationType"
            type: "QQuickWebEngineView::NavigationType"
            isReadonly: true
        }
    }
    Component {
        name: "QQuickWebEngineNewViewRequest"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineNewViewRequest 1.1",
            "QtWebEngine/WebEngineNewViewRequest 1.5"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Property {
            name: "destination"
            type: "QQuickWebEngineView::NewViewDestination"
            isReadonly: true
        }
        Property { name: "requestedUrl"; revision: 1; type: "QUrl"; isReadonly: true }
        Property { name: "userInitiated"; type: "bool"; isReadonly: true }
        Method {
            name: "openIn"
            Parameter { name: "view"; type: "QQuickWebEngineView"; isPointer: true }
        }
    }
    Component {
        name: "QQuickWebEngineProfile"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineProfile 1.1",
            "QtWebEngine/WebEngineProfile 1.2",
            "QtWebEngine/WebEngineProfile 1.3",
            "QtWebEngine/WebEngineProfile 1.4",
            "QtWebEngine/WebEngineProfile 1.5",
            "QtWebEngine/WebEngineProfile 1.9"
        ]
        exportMetaObjectRevisions: [0, 1, 2, 3, 4, 5]
        Enum {
            name: "HttpCacheType"
            values: {
                "MemoryHttpCache": 0,
                "DiskHttpCache": 1,
                "NoCache": 2
            }
        }
        Enum {
            name: "PersistentCookiesPolicy"
            values: {
                "NoPersistentCookies": 0,
                "AllowPersistentCookies": 1,
                "ForcePersistentCookies": 2
            }
        }
        Property { name: "storageName"; type: "string" }
        Property { name: "offTheRecord"; type: "bool" }
        Property { name: "persistentStoragePath"; type: "string" }
        Property { name: "cachePath"; type: "string" }
        Property { name: "httpUserAgent"; type: "string" }
        Property { name: "httpCacheType"; type: "HttpCacheType" }
        Property { name: "httpAcceptLanguage"; revision: 1; type: "string" }
        Property { name: "persistentCookiesPolicy"; type: "PersistentCookiesPolicy" }
        Property { name: "httpCacheMaximumSize"; type: "int" }
        Property { name: "spellCheckLanguages"; revision: 3; type: "QStringList" }
        Property { name: "spellCheckEnabled"; revision: 3; type: "bool" }
        Property {
            name: "userScripts"
            revision: 4
            type: "QQuickWebEngineScript"
            isList: true
            isReadonly: true
        }
        Property { name: "useForGlobalCertificateVerification"; revision: 5; type: "bool" }
        Property { name: "downloadPath"; revision: 5; type: "string" }
        Signal { name: "httpAcceptLanguageChanged"; revision: 1 }
        Signal { name: "spellCheckLanguagesChanged"; revision: 3 }
        Signal { name: "spellCheckEnabledChanged"; revision: 3 }
        Signal { name: "useForGlobalCertificateVerificationChanged"; revision: 5 }
        Signal { name: "downloadPathChanged"; revision: 5 }
        Signal {
            name: "downloadRequested"
            Parameter { name: "download"; type: "QQuickWebEngineDownloadItem"; isPointer: true }
        }
        Signal {
            name: "downloadFinished"
            Parameter { name: "download"; type: "QQuickWebEngineDownloadItem"; isPointer: true }
        }
        Signal {
            name: "presentNotification"
            revision: 5
            Parameter { name: "notification"; type: "QWebEngineNotification"; isPointer: true }
        }
        Method { name: "clearHttpCache"; revision: 2 }
    }
    Component {
        name: "QQuickWebEngineScript"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineScript 1.1"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "InjectionPoint"
            values: {
                "Deferred": 0,
                "DocumentReady": 1,
                "DocumentCreation": 2
            }
        }
        Enum {
            name: "ScriptWorldId"
            values: {
                "MainWorld": 0,
                "ApplicationWorld": 1,
                "UserWorld": 2
            }
        }
        Property { name: "name"; type: "string" }
        Property { name: "sourceUrl"; type: "QUrl" }
        Property { name: "sourceCode"; type: "string" }
        Property { name: "injectionPoint"; type: "InjectionPoint" }
        Property { name: "worldId"; type: "ScriptWorldId" }
        Property { name: "runOnSubframes"; type: "bool" }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "sourceUrlChanged"
            Parameter { name: "url"; type: "QUrl" }
        }
        Signal {
            name: "sourceCodeChanged"
            Parameter { name: "code"; type: "string" }
        }
        Signal {
            name: "injectionPointChanged"
            Parameter { name: "injectionPoint"; type: "InjectionPoint" }
        }
        Signal {
            name: "worldIdChanged"
            Parameter { name: "scriptWorldId"; type: "ScriptWorldId" }
        }
        Signal {
            name: "runOnSubframesChanged"
            Parameter { name: "on"; type: "bool" }
        }
        Method { name: "toString"; type: "string" }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setSourceUrl"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "setSourceCode"
            Parameter { name: "code"; type: "string" }
        }
        Method {
            name: "setInjectionPoint"
            Parameter { name: "injectionPoint"; type: "InjectionPoint" }
        }
        Method {
            name: "setWorldId"
            Parameter { name: "scriptWorldId"; type: "ScriptWorldId" }
        }
        Method {
            name: "setRunOnSubframes"
            Parameter { name: "on"; type: "bool" }
        }
    }
    Component {
        name: "QQuickWebEngineSettings"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineSettings 1.1",
            "QtWebEngine/WebEngineSettings 1.2",
            "QtWebEngine/WebEngineSettings 1.3",
            "QtWebEngine/WebEngineSettings 1.4",
            "QtWebEngine/WebEngineSettings 1.5",
            "QtWebEngine/WebEngineSettings 1.6",
            "QtWebEngine/WebEngineSettings 1.7",
            "QtWebEngine/WebEngineSettings 1.8",
            "QtWebEngine/WebEngineSettings 1.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 2, 3, 4, 5, 6, 7, 8]
        Enum {
            name: "UnknownUrlSchemePolicy"
            values: {
                "DisallowUnknownUrlSchemes": 1,
                "AllowUnknownUrlSchemesFromUserInteraction": 2,
                "AllowAllUnknownUrlSchemes": 3
            }
        }
        Property { name: "autoLoadImages"; type: "bool" }
        Property { name: "javascriptEnabled"; type: "bool" }
        Property { name: "javascriptCanOpenWindows"; type: "bool" }
        Property { name: "javascriptCanAccessClipboard"; type: "bool" }
        Property { name: "linksIncludedInFocusChain"; type: "bool" }
        Property { name: "localStorageEnabled"; type: "bool" }
        Property { name: "localContentCanAccessRemoteUrls"; type: "bool" }
        Property { name: "spatialNavigationEnabled"; type: "bool" }
        Property { name: "localContentCanAccessFileUrls"; type: "bool" }
        Property { name: "hyperlinkAuditingEnabled"; type: "bool" }
        Property { name: "errorPageEnabled"; type: "bool" }
        Property { name: "pluginsEnabled"; type: "bool" }
        Property { name: "fullScreenSupportEnabled"; revision: 1; type: "bool" }
        Property { name: "defaultTextEncoding"; type: "string" }
        Property { name: "screenCaptureEnabled"; revision: 2; type: "bool" }
        Property { name: "webGLEnabled"; revision: 2; type: "bool" }
        Property { name: "accelerated2dCanvasEnabled"; revision: 2; type: "bool" }
        Property { name: "autoLoadIconsForPage"; revision: 2; type: "bool" }
        Property { name: "touchIconsEnabled"; revision: 2; type: "bool" }
        Property { name: "focusOnNavigationEnabled"; revision: 3; type: "bool" }
        Property { name: "printElementBackgrounds"; revision: 3; type: "bool" }
        Property { name: "allowRunningInsecureContent"; revision: 3; type: "bool" }
        Property { name: "allowGeolocationOnInsecureOrigins"; revision: 4; type: "bool" }
        Property { name: "allowWindowActivationFromJavaScript"; revision: 5; type: "bool" }
        Property { name: "showScrollBars"; revision: 5; type: "bool" }
        Property { name: "unknownUrlSchemePolicy"; revision: 6; type: "UnknownUrlSchemePolicy" }
        Property { name: "playbackRequiresUserGesture"; revision: 6; type: "bool" }
        Property { name: "webRTCPublicInterfacesOnly"; revision: 6; type: "bool" }
        Property { name: "javascriptCanPaste"; revision: 6; type: "bool" }
        Property { name: "dnsPrefetchEnabled"; revision: 7; type: "bool" }
        Property { name: "pdfViewerEnabled"; revision: 8; type: "bool" }
        Signal { name: "fullScreenSupportEnabledChanged"; revision: 1 }
        Signal { name: "screenCaptureEnabledChanged"; revision: 2 }
        Signal { name: "webGLEnabledChanged"; revision: 2 }
        Signal { name: "accelerated2dCanvasEnabledChanged"; revision: 2 }
        Signal { name: "autoLoadIconsForPageChanged"; revision: 2 }
        Signal { name: "touchIconsEnabledChanged"; revision: 2 }
        Signal { name: "focusOnNavigationEnabledChanged"; revision: 3 }
        Signal { name: "printElementBackgroundsChanged"; revision: 3 }
        Signal { name: "allowRunningInsecureContentChanged"; revision: 3 }
        Signal { name: "allowGeolocationOnInsecureOriginsChanged"; revision: 4 }
        Signal { name: "allowWindowActivationFromJavaScriptChanged"; revision: 5 }
        Signal { name: "showScrollBarsChanged"; revision: 5 }
        Signal { name: "unknownUrlSchemePolicyChanged"; revision: 6 }
        Signal { name: "playbackRequiresUserGestureChanged"; revision: 6 }
        Signal { name: "webRTCPublicInterfacesOnlyChanged"; revision: 6 }
        Signal { name: "javascriptCanPasteChanged"; revision: 6 }
        Signal { name: "dnsPrefetchEnabledChanged"; revision: 7 }
        Signal { name: "pdfViewerEnabledChanged"; revision: 8 }
    }
    Component {
        name: "QQuickWebEngineSingleton"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngine 1.1"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Property { name: "settings"; type: "QQuickWebEngineSettings"; isReadonly: true; isPointer: true }
        Property {
            name: "defaultProfile"
            revision: 1
            type: "QQuickWebEngineProfile"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QQuickWebEngineTooltipRequest"
        prototype: "QObject"
        exports: ["QtWebEngine/TooltipRequest 1.10"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RequestType"
            values: {
                "Show": 0,
                "Hide": 1
            }
        }
        Property { name: "x"; type: "int"; isReadonly: true }
        Property { name: "y"; type: "int"; isReadonly: true }
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "type"; type: "RequestType"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        name: "QQuickWebEngineView"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtWebEngine/WebEngineView 1.0",
            "QtWebEngine/WebEngineView 1.1",
            "QtWebEngine/WebEngineView 1.10",
            "QtWebEngine/WebEngineView 1.2",
            "QtWebEngine/WebEngineView 1.3",
            "QtWebEngine/WebEngineView 1.4",
            "QtWebEngine/WebEngineView 1.5",
            "QtWebEngine/WebEngineView 1.6",
            "QtWebEngine/WebEngineView 1.7",
            "QtWebEngine/WebEngineView 1.8",
            "QtWebEngine/WebEngineView 1.9"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 2, 3, 4, 5, 6, 7, 8, 9]
        Enum {
            name: "NavigationRequestAction"
            values: {
                "AcceptRequest": 0,
                "IgnoreRequest": 255
            }
        }
        Enum {
            name: "NavigationType"
            values: {
                "LinkClickedNavigation": 0,
                "TypedNavigation": 1,
                "FormSubmittedNavigation": 2,
                "BackForwardNavigation": 3,
                "ReloadNavigation": 4,
                "OtherNavigation": 5,
                "RedirectNavigation": 6
            }
        }
        Enum {
            name: "LoadStatus"
            values: {
                "LoadStartedStatus": 0,
                "LoadStoppedStatus": 1,
                "LoadSucceededStatus": 2,
                "LoadFailedStatus": 3
            }
        }
        Enum {
            name: "ErrorDomain"
            values: {
                "NoErrorDomain": 0,
                "InternalErrorDomain": 1,
                "ConnectionErrorDomain": 2,
                "CertificateErrorDomain": 3,
                "HttpErrorDomain": 4,
                "FtpErrorDomain": 5,
                "DnsErrorDomain": 6
            }
        }
        Enum {
            name: "NewViewDestination"
            values: {
                "NewViewInWindow": 0,
                "NewViewInTab": 1,
                "NewViewInDialog": 2,
                "NewViewInBackgroundTab": 3
            }
        }
        Enum {
            name: "Feature"
            values: {
                "MediaAudioCapture": 0,
                "MediaVideoCapture": 1,
                "MediaAudioVideoCapture": 2,
                "Geolocation": 3,
                "DesktopVideoCapture": 4,
                "DesktopAudioVideoCapture": 5,
                "Notifications": 6
            }
        }
        Enum {
            name: "WebAction"
            values: {
                "NoWebAction": -1,
                "Back": 0,
                "Forward": 1,
                "Stop": 2,
                "Reload": 3,
                "Cut": 4,
                "Copy": 5,
                "Paste": 6,
                "Undo": 7,
                "Redo": 8,
                "SelectAll": 9,
                "ReloadAndBypassCache": 10,
                "PasteAndMatchStyle": 11,
                "OpenLinkInThisWindow": 12,
                "OpenLinkInNewWindow": 13,
                "OpenLinkInNewTab": 14,
                "CopyLinkToClipboard": 15,
                "DownloadLinkToDisk": 16,
                "CopyImageToClipboard": 17,
                "CopyImageUrlToClipboard": 18,
                "DownloadImageToDisk": 19,
                "CopyMediaUrlToClipboard": 20,
                "ToggleMediaControls": 21,
                "ToggleMediaLoop": 22,
                "ToggleMediaPlayPause": 23,
                "ToggleMediaMute": 24,
                "DownloadMediaToDisk": 25,
                "InspectElement": 26,
                "ExitFullScreen": 27,
                "RequestClose": 28,
                "Unselect": 29,
                "SavePage": 30,
                "ViewSource": 31,
                "ToggleBold": 32,
                "ToggleItalic": 33,
                "ToggleUnderline": 34,
                "ToggleStrikethrough": 35,
                "AlignLeft": 36,
                "AlignCenter": 37,
                "AlignRight": 38,
                "AlignJustified": 39,
                "Indent": 40,
                "Outdent": 41,
                "InsertOrderedList": 42,
                "InsertUnorderedList": 43,
                "WebActionCount": 44
            }
        }
        Enum {
            name: "JavaScriptConsoleMessageLevel"
            values: {
                "InfoMessageLevel": 0,
                "WarningMessageLevel": 1,
                "ErrorMessageLevel": 2
            }
        }
        Enum {
            name: "RenderProcessTerminationStatus"
            values: {
                "NormalTerminationStatus": 0,
                "AbnormalTerminationStatus": 1,
                "CrashedTerminationStatus": 2,
                "KilledTerminationStatus": 3
            }
        }
        Enum {
            name: "FindFlags"
            values: {
                "FindBackward": 1,
                "FindCaseSensitively": 2
            }
        }
        Enum {
            name: "PrintedPageSizeId"
            values: {
                "A4": 0,
                "B5": 1,
                "Letter": 2,
                "Legal": 3,
                "Executive": 4,
                "A0": 5,
                "A1": 6,
                "A2": 7,
                "A3": 8,
                "A5": 9,
                "A6": 10,
                "A7": 11,
                "A8": 12,
                "A9": 13,
                "B0": 14,
                "B1": 15,
                "B10": 16,
                "B2": 17,
                "B3": 18,
                "B4": 19,
                "B6": 20,
                "B7": 21,
                "B8": 22,
                "B9": 23,
                "C5E": 24,
                "Comm10E": 25,
                "DLE": 26,
                "Folio": 27,
                "Ledger": 28,
                "Tabloid": 29,
                "Custom": 30,
                "A10": 31,
                "A3Extra": 32,
                "A4Extra": 33,
                "A4Plus": 34,
                "A4Small": 35,
                "A5Extra": 36,
                "B5Extra": 37,
                "JisB0": 38,
                "JisB1": 39,
                "JisB2": 40,
                "JisB3": 41,
                "JisB4": 42,
                "JisB5": 43,
                "JisB6": 44,
                "JisB7": 45,
                "JisB8": 46,
                "JisB9": 47,
                "JisB10": 48,
                "AnsiC": 49,
                "AnsiD": 50,
                "AnsiE": 51,
                "LegalExtra": 52,
                "LetterExtra": 53,
                "LetterPlus": 54,
                "LetterSmall": 55,
                "TabloidExtra": 56,
                "ArchA": 57,
                "ArchB": 58,
                "ArchC": 59,
                "ArchD": 60,
                "ArchE": 61,
                "Imperial7x9": 62,
                "Imperial8x10": 63,
                "Imperial9x11": 64,
                "Imperial9x12": 65,
                "Imperial10x11": 66,
                "Imperial10x13": 67,
                "Imperial10x14": 68,
                "Imperial12x11": 69,
                "Imperial15x11": 70,
                "ExecutiveStandard": 71,
                "Note": 72,
                "Quarto": 73,
                "Statement": 74,
                "SuperA": 75,
                "SuperB": 76,
                "Postcard": 77,
                "DoublePostcard": 78,
                "Prc16K": 79,
                "Prc32K": 80,
                "Prc32KBig": 81,
                "FanFoldUS": 82,
                "FanFoldGerman": 83,
                "FanFoldGermanLegal": 84,
                "EnvelopeB4": 85,
                "EnvelopeB5": 86,
                "EnvelopeB6": 87,
                "EnvelopeC0": 88,
                "EnvelopeC1": 89,
                "EnvelopeC2": 90,
                "EnvelopeC3": 91,
                "EnvelopeC4": 92,
                "EnvelopeC6": 93,
                "EnvelopeC65": 94,
                "EnvelopeC7": 95,
                "Envelope9": 96,
                "Envelope11": 97,
                "Envelope12": 98,
                "Envelope14": 99,
                "EnvelopeMonarch": 100,
                "EnvelopePersonal": 101,
                "EnvelopeChou3": 102,
                "EnvelopeChou4": 103,
                "EnvelopeInvite": 104,
                "EnvelopeItalian": 105,
                "EnvelopeKaku2": 106,
                "EnvelopeKaku3": 107,
                "EnvelopePrc1": 108,
                "EnvelopePrc2": 109,
                "EnvelopePrc3": 110,
                "EnvelopePrc4": 111,
                "EnvelopePrc5": 112,
                "EnvelopePrc6": 113,
                "EnvelopePrc7": 114,
                "EnvelopePrc8": 115,
                "EnvelopePrc9": 116,
                "EnvelopePrc10": 117,
                "EnvelopeYou4": 118,
                "LastPageSize": 118,
                "NPageSize": 118,
                "NPaperSize": 118,
                "AnsiA": 2,
                "AnsiB": 28,
                "EnvelopeC5": 24,
                "EnvelopeDL": 26,
                "Envelope10": 25
            }
        }
        Enum {
            name: "PrintedPageOrientation"
            values: {
                "Portrait": 0,
                "Landscape": 1
            }
        }
        Enum {
            name: "LifecycleState"
            values: {
                "Active": 0,
                "Frozen": 1,
                "Discarded": 2
            }
        }
        Property { name: "url"; type: "QUrl" }
        Property { name: "icon"; type: "QUrl"; isReadonly: true }
        Property { name: "loading"; type: "bool"; isReadonly: true }
        Property { name: "loadProgress"; type: "int"; isReadonly: true }
        Property { name: "title"; type: "string"; isReadonly: true }
        Property { name: "canGoBack"; type: "bool"; isReadonly: true }
        Property { name: "canGoForward"; type: "bool"; isReadonly: true }
        Property { name: "isFullScreen"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "zoomFactor"; revision: 1; type: "double" }
        Property { name: "profile"; revision: 1; type: "QQuickWebEngineProfile"; isPointer: true }
        Property {
            name: "settings"
            revision: 1
            type: "QQuickWebEngineSettings"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "navigationHistory"
            revision: 1
            type: "QQuickWebEngineHistory"
            isReadonly: true
            isPointer: true
        }
        Property { name: "webChannel"; revision: 1; type: "QQmlWebChannel"; isPointer: true }
        Property {
            name: "userScripts"
            revision: 1
            type: "QQuickWebEngineScript"
            isList: true
            isReadonly: true
        }
        Property { name: "activeFocusOnPress"; revision: 2; type: "bool" }
        Property { name: "backgroundColor"; revision: 2; type: "QColor" }
        Property { name: "contentsSize"; revision: 3; type: "QSizeF"; isReadonly: true }
        Property { name: "scrollPosition"; revision: 3; type: "QPointF"; isReadonly: true }
        Property { name: "audioMuted"; revision: 3; type: "bool" }
        Property { name: "recentlyAudible"; revision: 3; type: "bool"; isReadonly: true }
        Property { name: "webChannelWorld"; revision: 3; type: "uint" }
        Property { name: "inspectedView"; revision: 7; type: "QQuickWebEngineView"; isPointer: true }
        Property { name: "devToolsView"; revision: 7; type: "QQuickWebEngineView"; isPointer: true }
        Property { name: "lifecycleState"; revision: 10; type: "LifecycleState" }
        Property { name: "recommendedState"; revision: 10; type: "LifecycleState"; isReadonly: true }
        Property { name: "renderProcessId"; revision: 11; type: "qint64"; isReadonly: true }
        Signal {
            name: "loadingChanged"
            Parameter { name: "loadRequest"; type: "QQuickWebEngineLoadRequest"; isPointer: true }
        }
        Signal {
            name: "linkHovered"
            Parameter { name: "hoveredUrl"; type: "QUrl" }
        }
        Signal {
            name: "navigationRequested"
            Parameter { name: "request"; type: "QQuickWebEngineNavigationRequest"; isPointer: true }
        }
        Signal {
            name: "javaScriptConsoleMessage"
            Parameter { name: "level"; type: "JavaScriptConsoleMessageLevel" }
            Parameter { name: "message"; type: "string" }
            Parameter { name: "lineNumber"; type: "int" }
            Parameter { name: "sourceID"; type: "string" }
        }
        Signal {
            name: "certificateError"
            revision: 1
            Parameter { name: "error"; type: "QQuickWebEngineCertificateError"; isPointer: true }
        }
        Signal {
            name: "fullScreenRequested"
            revision: 1
            Parameter { name: "request"; type: "QQuickWebEngineFullScreenRequest" }
        }
        Signal { name: "isFullScreenChanged"; revision: 1 }
        Signal {
            name: "featurePermissionRequested"
            revision: 1
            Parameter { name: "securityOrigin"; type: "QUrl" }
            Parameter { name: "feature"; type: "Feature" }
        }
        Signal {
            name: "newViewRequested"
            revision: 1
            Parameter { name: "request"; type: "QQuickWebEngineNewViewRequest"; isPointer: true }
        }
        Signal {
            name: "zoomFactorChanged"
            revision: 1
            Parameter { name: "arg"; type: "double" }
        }
        Signal { name: "profileChanged"; revision: 1 }
        Signal { name: "webChannelChanged"; revision: 1 }
        Signal {
            name: "activeFocusOnPressChanged"
            revision: 2
            Parameter { type: "bool" }
        }
        Signal { name: "backgroundColorChanged"; revision: 2 }
        Signal {
            name: "renderProcessTerminated"
            revision: 2
            Parameter { name: "terminationStatus"; type: "RenderProcessTerminationStatus" }
            Parameter { name: "exitCode"; type: "int" }
        }
        Signal { name: "windowCloseRequested"; revision: 2 }
        Signal {
            name: "contentsSizeChanged"
            revision: 3
            Parameter { name: "size"; type: "QSizeF" }
        }
        Signal {
            name: "scrollPositionChanged"
            revision: 3
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "audioMutedChanged"
            revision: 3
            Parameter { name: "muted"; type: "bool" }
        }
        Signal {
            name: "recentlyAudibleChanged"
            revision: 3
            Parameter { name: "recentlyAudible"; type: "bool" }
        }
        Signal {
            name: "webChannelWorldChanged"
            revision: 3
            Parameter { type: "uint" }
        }
        Signal {
            name: "contextMenuRequested"
            revision: 4
            Parameter { name: "request"; type: "QQuickWebEngineContextMenuRequest"; isPointer: true }
        }
        Signal {
            name: "authenticationDialogRequested"
            revision: 4
            Parameter {
                name: "request"
                type: "QQuickWebEngineAuthenticationDialogRequest"
                isPointer: true
            }
        }
        Signal {
            name: "javaScriptDialogRequested"
            revision: 4
            Parameter { name: "request"; type: "QQuickWebEngineJavaScriptDialogRequest"; isPointer: true }
        }
        Signal {
            name: "colorDialogRequested"
            revision: 4
            Parameter { name: "request"; type: "QQuickWebEngineColorDialogRequest"; isPointer: true }
        }
        Signal {
            name: "fileDialogRequested"
            revision: 4
            Parameter { name: "request"; type: "QQuickWebEngineFileDialogRequest"; isPointer: true }
        }
        Signal {
            name: "formValidationMessageRequested"
            revision: 4
            Parameter {
                name: "request"
                type: "QQuickWebEngineFormValidationMessageRequest"
                isPointer: true
            }
        }
        Signal {
            name: "pdfPrintingFinished"
            revision: 5
            Parameter { name: "filePath"; type: "string" }
            Parameter { name: "success"; type: "bool" }
        }
        Signal {
            name: "quotaRequested"
            revision: 7
            Parameter { name: "request"; type: "QWebEngineQuotaRequest" }
        }
        Signal {
            name: "geometryChangeRequested"
            revision: 7
            Parameter { name: "geometry"; type: "QRect" }
            Parameter { name: "frameGeometry"; type: "QRect" }
        }
        Signal { name: "inspectedViewChanged"; revision: 7 }
        Signal { name: "devToolsViewChanged"; revision: 7 }
        Signal {
            name: "registerProtocolHandlerRequested"
            revision: 7
            Parameter { name: "request"; type: "QWebEngineRegisterProtocolHandlerRequest" }
        }
        Signal { name: "printRequested"; revision: 8 }
        Signal {
            name: "selectClientCertificate"
            revision: 9
            Parameter {
                name: "clientCertSelection"
                type: "QQuickWebEngineClientCertificateSelection"
                isPointer: true
            }
        }
        Signal {
            name: "tooltipRequested"
            revision: 10
            Parameter { name: "request"; type: "QQuickWebEngineTooltipRequest"; isPointer: true }
        }
        Signal {
            name: "lifecycleStateChanged"
            revision: 10
            Parameter { name: "state"; type: "LifecycleState" }
        }
        Signal {
            name: "recommendedStateChanged"
            revision: 10
            Parameter { name: "state"; type: "LifecycleState" }
        }
        Signal {
            name: "findTextFinished"
            revision: 10
            Parameter { name: "result"; type: "QWebEngineFindTextResult" }
        }
        Signal {
            name: "renderProcessPidChanged"
            revision: 11
            Parameter { name: "pid"; type: "qint64" }
        }
        Method {
            name: "runJavaScript"
            Parameter { type: "string" }
            Parameter { type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            Parameter { type: "string" }
        }
        Method {
            name: "runJavaScript"
            revision: 3
            Parameter { type: "string" }
            Parameter { name: "worldId"; type: "uint" }
            Parameter { type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            revision: 3
            Parameter { type: "string" }
            Parameter { name: "worldId"; type: "uint" }
        }
        Method {
            name: "loadHtml"
            Parameter { name: "html"; type: "string" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method {
            name: "loadHtml"
            Parameter { name: "html"; type: "string" }
        }
        Method { name: "goBack" }
        Method { name: "goForward" }
        Method {
            name: "goBackOrForward"
            revision: 1
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reload" }
        Method { name: "reloadAndBypassCache"; revision: 1 }
        Method { name: "stop" }
        Method {
            name: "findText"
            revision: 1
            Parameter { name: "subString"; type: "string" }
            Parameter { name: "options"; type: "FindFlags" }
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "findText"
            revision: 1
            Parameter { name: "subString"; type: "string" }
            Parameter { name: "options"; type: "FindFlags" }
        }
        Method {
            name: "findText"
            revision: 1
            Parameter { name: "subString"; type: "string" }
        }
        Method { name: "fullScreenCancelled"; revision: 1 }
        Method {
            name: "grantFeaturePermission"
            revision: 1
            Parameter { name: "securityOrigin"; type: "QUrl" }
            Parameter { type: "Feature" }
            Parameter { name: "granted"; type: "bool" }
        }
        Method {
            name: "setActiveFocusOnPress"
            revision: 2
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "triggerWebAction"
            revision: 2
            Parameter { name: "action"; type: "WebAction" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "filePath"; type: "string" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
            Parameter { name: "orientation"; type: "PrintedPageOrientation" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "filePath"; type: "string" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "filePath"; type: "string" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
            Parameter { name: "orientation"; type: "PrintedPageOrientation" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
        }
        Method {
            name: "printToPdf"
            revision: 3
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "replaceMisspelledWord"
            revision: 4
            Parameter { name: "replacement"; type: "string" }
        }
        Method {
            name: "action"
            revision: 8
            type: "QQuickWebEngineAction*"
            Parameter { name: "action"; type: "WebAction" }
        }
    }
    Component {
        name: "QWebEngineFindTextResult"
        exports: ["QtWebEngine/FindTextResult 1.10"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "numberOfMatches"; type: "int"; isReadonly: true }
        Property { name: "activeMatchOrdinal"; type: "int"; isReadonly: true }
    }
    Component {
        name: "QWebEngineNotification"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineNotification 1.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QUrl"; isReadonly: true }
        Property { name: "title"; type: "string"; isReadonly: true }
        Property { name: "message"; type: "string"; isReadonly: true }
        Property { name: "tag"; type: "string"; isReadonly: true }
        Property { name: "language"; type: "string"; isReadonly: true }
        Property { name: "direction"; type: "Qt::LayoutDirection"; isReadonly: true }
        Signal { name: "closed" }
        Method { name: "show" }
        Method { name: "click" }
        Method { name: "close" }
    }
    Component {
        name: "QWebEngineQuotaRequest"
        exports: ["QtWebEngine/QuotaRequest 1.7"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QUrl"; isReadonly: true }
        Property { name: "requestedSize"; type: "qlonglong"; isReadonly: true }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        name: "QWebEngineRegisterProtocolHandlerRequest"
        exports: ["QtWebEngine/RegisterProtocolHandlerRequest 1.7"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QUrl"; isReadonly: true }
        Property { name: "scheme"; type: "string"; isReadonly: true }
        Method { name: "accept" }
        Method { name: "reject" }
    }
}
