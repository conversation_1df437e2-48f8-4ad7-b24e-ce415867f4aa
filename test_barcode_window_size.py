#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حجم نوافذ مسح الباركود الجديدة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(__file__))

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نوافذ مسح الباركود")
        self.setGeometry(100, 100, 400, 300)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # زر اختبار مسح الباركود بالكاميرا
        camera_btn = QPushButton("📷 اختبار مسح الباركود بالكاميرا")
        camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        camera_btn.clicked.connect(self.test_camera_scanner)
        layout.addWidget(camera_btn)
        
        # زر اختبار مسح الباركود بالجهاز
        device_btn = QPushButton("🔍 اختبار مسح الباركود بالجهاز")
        device_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        device_btn.clicked.connect(self.test_device_scanner)
        layout.addWidget(device_btn)
        
        # زر اختبار إضافة منتج (للمقارنة)
        product_btn = QPushButton("📦 اختبار نافذة إضافة منتج")
        product_btn.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #D35400;
            }
        """)
        product_btn.clicked.connect(self.test_product_dialog)
        layout.addWidget(product_btn)
        
        # معلومات
        info_label = QPushButton("ℹ️ معلومات الاختبار")
        info_label.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                font-size: 14px;
                padding: 10px;
                border: none;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        info_label.clicked.connect(self.show_info)
        layout.addWidget(info_label)
        
    def test_camera_scanner(self):
        """اختبار نافذة مسح الباركود بالكاميرا"""
        try:
            from gui.barcode_scanner import BarcodeScannerDialog
            
            dialog = BarcodeScannerDialog(self)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح الاختبار",
                    f"تم اختبار نافذة مسح الباركود بالكاميرا بنجاح!\n"
                    f"الحجم الجديد: {dialog.width()} × {dialog.height()}\n"
                    f"الحجم المطلوب: 750 × 1400 (طولي)")
            else:
                QMessageBox.information(self, "تم الإلغاء", "تم إلغاء اختبار مسح الباركود بالكاميرا")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار مسح الباركود بالكاميرا:\n{str(e)}")
    
    def test_device_scanner(self):
        """اختبار نافذة مسح الباركود بالجهاز"""
        try:
            from gui.device_barcode_scanner import DeviceBarcodeScannerDialog
            
            dialog = DeviceBarcodeScannerDialog(self)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح الاختبار",
                    f"تم اختبار نافذة مسح الباركود بالجهاز بنجاح!\n"
                    f"الحجم الجديد: {dialog.width()} × {dialog.height()}\n"
                    f"الحجم المطلوب: 750 × 1400 (طولي)\n"
                    f"الباركود المدخل: {getattr(dialog, 'scanned_code', 'لا يوجد')}")
            else:
                QMessageBox.information(self, "تم الإلغاء", "تم إلغاء اختبار مسح الباركود بالجهاز")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار مسح الباركود بالجهاز:\n{str(e)}")
    
    def test_product_dialog(self):
        """اختبار نافذة إضافة منتج للمقارنة"""
        try:
            # إنشاء محرك قاعدة بيانات وهمي للاختبار
            from sqlalchemy import create_engine
            engine = create_engine('sqlite:///:memory:', echo=False)
            
            from gui.inventory import ProductDialog
            
            dialog = ProductDialog(engine, parent=self)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح الاختبار",
                    f"تم اختبار نافذة إضافة منتج بنجاح!\n"
                    f"الحجم الجديد: {dialog.width()} × {dialog.height()}\n"
                    f"الحجم المطلوب: 1200 × 1400 (طولي)")
            else:
                QMessageBox.information(self, "تم الإلغاء", "تم إلغاء اختبار نافذة إضافة منتج")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء اختبار نافذة إضافة منتج:\n{str(e)}")
    
    def show_info(self):
        """عرض معلومات الاختبار"""
        info_text = """
🎯 هدف الاختبار:
التأكد من أن نوافذ مسح الباركود أصبحت بالحجم المطلوب

📏 الأحجام الجديدة المخصصة:
• نوافذ الباركود والكاميرا: 750 × 1400 بكسل (طولي)
• نافذة إضافة منتج: 1200 × 1400 بكسل (طولي)
• منطقة الكاميرا: 600 × 700 بكسل

✅ التحسينات المطبقة:
• أحجام مخصصة حسب نوع النافذة
• نافذة مسح الباركود بالكاميرا: 750×1400
• نافذة مسح الباركود بالجهاز: 750×1400
• نافذة إضافة منتج: 1200×1400 (أكبر للحقول الكثيرة)
• الحفاظ على الخطوط والأزرار الأصلية
• تحسين التخطيط للشكل الطولي
• دعم تكبير/تصغير النوافذ

🔧 كيفية الاختبار:
1. اضغط على أزرار الاختبار
2. تحقق من أحجام النوافذ المخصصة (750×1400 و 1200×1400)
3. تأكد من وضوح النصوص
4. اختبر وظائف التكبير/التصغير
5. تأكد من التخطيط الطولي المناسب
        """
        
        QMessageBox.information(self, "معلومات الاختبار", info_text)

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق نمط عربي بسيط
    app.setStyleSheet("""
        QWidget {
            font-family: Arial;
            font-size: 14px;
        }
        QMainWindow {
            background-color: #F8F9FA;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
