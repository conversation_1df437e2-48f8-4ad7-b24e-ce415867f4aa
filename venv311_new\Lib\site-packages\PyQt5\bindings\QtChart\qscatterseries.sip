// qscatterseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qscatterseries.h>
%End

    class QScatterSeries : public QtCharts::QXYSeries
    {
%TypeHeaderCode
#include <qscatterseries.h>
%End

    public:
        enum MarkerShape
        {
            MarkerShapeCircle,
            MarkerShapeRectangle,
        };

        explicit QScatterSeries(QObject *parent /TransferThis/ = 0);
        virtual ~QScatterSeries();
        virtual QtCharts::QAbstractSeries::SeriesType type() const;
        QtCharts::QScatterSeries::MarkerShape markerShape() const;
        void setMarkerShape(QtCharts::QScatterSeries::MarkerShape shape);
        qreal markerSize() const;
        void setMarkerSize(qreal size);
        virtual void setPen(const QPen &pen);
%If (QtChart_1_4_0 -)
        QBrush brush() const;
%End
        virtual void setBrush(const QBrush &brush);
        virtual void setColor(const QColor &color);
        virtual QColor color() const;
        void setBorderColor(const QColor &color);
        QColor borderColor() const;

    signals:
        void colorChanged(QColor color);
        void borderColorChanged(QColor color);
%If (QtChart_2_1_0 -)
        void markerShapeChanged(QtCharts::QScatterSeries::MarkerShape shape /ScopesStripped=2/);
%End
%If (QtChart_2_1_0 -)
        void markerSizeChanged(qreal size);
%End
    };
};
