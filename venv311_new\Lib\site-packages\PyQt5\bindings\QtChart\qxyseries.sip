// qxyseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qxyseries.h>
%End

    class QXYSeries : public QtCharts::QAbstractSeries /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qxyseries.h>
%End

    public:
        virtual ~QXYSeries();
        void append(qreal x, qreal y);
        void append(const QPointF &point);
        void append(const QList<QPointF> &points);
        void replace(qreal oldX, qreal oldY, qreal newX, qreal newY);
        void replace(const QPointF &oldPoint, const QPointF &newPoint);
%If (QtChart_1_1_0 -)
        void replace(QList<QPointF> points);
%End
%If (QtChart_1_3_0 -)
        void replace(int index, qreal newX, qreal newY);
%End
%If (QtChart_1_3_0 -)
        void replace(int index, const QPointF &newPoint);
%End
        void remove(qreal x, qreal y);
        void remove(const QPointF &point);
%If (QtChart_1_3_0 -)
        void remove(int index);
%End
        void insert(int index, const QPointF &point);
        void clear();
        int count() const /__len__/;
        QList<QPointF> points() const;
        QtCharts::QXYSeries &operator<<(const QPointF &point);
        QtCharts::QXYSeries &operator<<(const QList<QPointF> &points);
        virtual void setPen(const QPen &pen);
        QPen pen() const;
        virtual void setBrush(const QBrush &brush);
        QBrush brush() const;
        virtual void setColor(const QColor &color);
        virtual QColor color() const;
        void setPointsVisible(bool visible = true);
        bool pointsVisible() const;
%If (QtChart_1_3_0 -)
        const QPointF &at(int index) const;
%End

    signals:
        void clicked(const QPointF &point);
        void colorChanged(QColor color);
        void pointReplaced(int index);
        void pointRemoved(int index);
        void pointAdded(int index);
%If (QtChart_1_1_0 -)
        void pointsReplaced();
%End
%If (QtChart_1_2_0 -)
        void hovered(const QPointF &point, bool state);
%End

    public:
%If (QtChart_1_4_0 -)
        void setPointLabelsFormat(const QString &format);
%End
%If (QtChart_1_4_0 -)
        QString pointLabelsFormat() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsVisible(bool visible = true);
%End
%If (QtChart_1_4_0 -)
        bool pointLabelsVisible() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsFont(const QFont &font);
%End
%If (QtChart_1_4_0 -)
        QFont pointLabelsFont() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsColor(const QColor &color);
%End
%If (QtChart_1_4_0 -)
        QColor pointLabelsColor() const;
%End

    signals:
%If (QtChart_1_4_0 -)
        void pointLabelsFormatChanged(const QString &format);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsVisibilityChanged(bool visible);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsFontChanged(const QFont &font);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsColorChanged(const QColor &color);
%End
%If (QtChart_2_0_0 -)
        void pressed(const QPointF &point);
%End
%If (QtChart_2_0_0 -)
        void released(const QPointF &point);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(const QPointF &point);
%End

    public:
%If (QtChart_2_1_0 -)
        void removePoints(int index, int count);
%End
%If (QtChart_2_1_0 -)
        QVector<QPointF> pointsVector() const;
%End
%If (QtChart_2_1_0 -)
        void setPointLabelsClipping(bool enable = true);
%End
%If (QtChart_2_1_0 -)
        bool pointLabelsClipping() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void pointLabelsClippingChanged(bool clipping);
%End
%If (QtChart_2_1_0 -)
        void pointsRemoved(int index, int count);
%End
%If (QtChart_2_1_0 -)
        void penChanged(const QPen &pen);
%End
    };
};
